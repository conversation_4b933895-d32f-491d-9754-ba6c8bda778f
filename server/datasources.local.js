const LOCALHOST = '127.0.0.1',
	{
		NODE_ENV,
		DB_HOST = NODE_ENV ? 'mongodb' : LOCALHOST,
		DB_USERNAME = '',
		DB_PASSWORD = '',
		DB_AUTH = 'admin',
		DB_SET = '',
		ACTION_HOST = '',
		SERVICE_HOST = LOCALHOST,
		PLACE_HOST = SERVICE_HOST,
		PERSON_HOST = SERVICE_HOST,
		MEMBERSHIP_HOST = SERVICE_HOST,
		IMAGE_HOST = SERVICE_HOST,
		PAYMENT_HOST = SERVICE_HOST,
		ACCOUNT_HOST = SERVICE_HOST,
		SALES_HOST = SERVICE_HOST,
		PRODUCT_HOST = SERVICE_HOST,
		CAMPAIGN_HOST = SERVICE_HOST,
		OFFER_HOST = SERVICE_HOST,
	} = process.env

module.exports = {
	trap: {
		name: 'trap',
		connector: 'mongodb',
		url: 'mongodb://' + (DB_USERNAME ? `${DB_USERNAME}:${DB_PASSWORD}@` : '') + `${DB_HOST}/trap?authSource=${DB_AUTH}` + (DB_SET ? `&replicaSet=${DB_SET}&readPreference=primaryPreferred&slaveOk=true` : '') + '&useNewUrlParser=true&useUnifiedTopology=true&keepAlive=false&maxPoolSize=5&socketTimeoutMS=360000&connectTimeoutMS=10000&maxIdleTimeMS=300000',
		allowExtendedOperators: true,
		enableOptimisedfindOrCreate: true,
	},
	shared: {
		name: 'shared',
		connector: 'mongodb',
		url: 'mongodb://' + (DB_USERNAME ? `${DB_USERNAME}:${DB_PASSWORD}@` : '') + `${DB_HOST}/shared?authSource=${DB_AUTH}` + (DB_SET ? `&replicaSet=${DB_SET}&readPreference=primaryPreferred&slaveOk=true` : ''),
		allowExtendedOperators: true,
		enableOptimisedfindOrCreate: true,
		options: {
			useNewUrlParser: true,
			useUnifiedTopology: true,
			maxPoolSize: 10,
			minPoolSize: 2,
			maxIdleTimeMS: 30000,
			connectTimeoutMS: 10000,
			socketTimeoutMS: 360000,
			serverSelectionTimeoutMS: 5000,
			heartbeatFrequencyMS: 10000,
			keepAlive: true,
			autoReconnect: true,
			validateConnection: true
		}
	},
	authRemote: {
		name: 'authRemote',
		connector: 'remote',
		url: `http://${ACCOUNT_HOST}:3060/api`,
	},
	personRemote: {
		name: 'personRemote',
		connector: 'remote',
		url: `http://${PERSON_HOST}:3101/api`,
	},
	membershipRemote: {
		name: 'membershipRemote',
		connector: 'remote',
		url: `http://${MEMBERSHIP_HOST}:3103/api`,
	},
	imageRemote: {
		name: 'imageRemote',
		connector: 'remote',
		url: `http://${IMAGE_HOST}:3031/api`,
	},
	paymentRemote: {
		name: 'paymentRemote',
		connector: 'remote',
		url: `http://${PAYMENT_HOST}:3126/api`,
	},
	salesRemote: {
		name: 'salesRemote',
		connector: 'remote',
		url: `http://${SALES_HOST}:3102/api`,
	},
	productRemote: {
		name: 'productRemote',
		connector: 'remote',
		url: `http://${PRODUCT_HOST}:3122/api`,
	},
	placeRemote: {
		name: 'placeRemote',
		connector: 'remote',
		url: `http://${PLACE_HOST}:3115/api`,
	},
	campaignRemote: {
		name: 'campaignRemote',
		connector: 'remote',
		url: `http://${CAMPAIGN_HOST}:3013/api`,
	},
	offerRemote: {
		name: 'offerRemote',
		connector: 'remote',
		url: `http://${OFFER_HOST}:3125/api`,
	},
	actionRemote: {
		name: 'actionRemote',
		connector: 'rest',
		options: {
			headers: {
				accepts: 'application/json',
				'content-type': 'application/json',
			},
		},
		operations: [
			{
				functions: {
					createRest: [ 'data', 'token' ],
				},
				template: {
					method: 'POST',
					url: `https://${ACTION_HOST}/Actions`,
					headers: {
						'x-access-token': '{token}',
					},
					body: '{data:object}',
					responsePath: '$'
				},
			}
		],
	}
}
