/**
 *  @module Model:BusinessProvider
 */
const { REGISTRY } = require('@perkd/event-registry-crm'),
	{ Service } = require('@perkd/errors')

const EXTERNAL = {
		BUSINESS_DELETED: 'business.deleted',
	},
	{ business: Event } = REGISTRY,
	BUSINESS = {
		CREATED: Event.business.created,
		UPDATED: Event.business.updated,
		DELETED: Event.business.deleted,
	},
	{ EVENT_NOT_HANDLED, HANDLER_NOT_IMPLEMENTED } = Service.PROVIDER

module.exports = function (BusinessProvider) {

	BusinessProvider.providerExternalEvents = async function(name, data = {}) {
		const { id } = data

		return this.queue(`providerExternalEvents_${id}`, async () => {
			switch (name) {
			case EXTERNAL.BUSINESS_DELETED:
				return this.handleExtBusinessDeleted(data)

			default:
				appLog(EVENT_NOT_HANDLED, { name, data })
			}
		})
	}

	BusinessProvider.providerInternalEvents = async function(name, data = {}) {
		const { id } = data

		return this.queue(`providerInternalEvents_${id}`, async () => {
			switch (name) {
			case BUSINESS.CREATED:
				return this.handleBusinessCreated(data)

			case BUSINESS.UPDATED:
				return this.handleBusinessUpdated(data)

			case BUSINESS.DELETED:
				return this.handleBusinessDeleted(data)

			default:
				appNotify(EVENT_NOT_HANDLED, { name, data })
			}
		})
	}

	// ----  Provider-specific methods  ----

	BusinessProvider.handleBusinessCreated = function (person) {
		return this.rejectErr(HANDLER_NOT_IMPLEMENTED)
	}

	BusinessProvider.handleBusinessUpdated = function(person) {
		return this.rejectErr(HANDLER_NOT_IMPLEMENTED)
	}

	BusinessProvider.handleBusinessDeleted = function (person) {
		return this.rejectErr(HANDLER_NOT_IMPLEMENTED)
	}

	// external event
	BusinessProvider.handleExtBusinessDeleted = function (person) {
		return this.rejectErr(HANDLER_NOT_IMPLEMENTED)
	}
}
