/**
 *  @module Model:Provider  (only Business have access to this)
 */

module.exports = function(Provider) {

	/**
	 * Get Provider by name (Tenanted and Shared)
	 * @param	{String} name of provider
	 * @return	{Promise<Provider|null>} instance
	 */
	Provider.getByName = async function(name) {
		const { SharedProvider } = this.app.models,
			filter = { where: { name } }

		try {
			const [ local, shared ] = await Promise.all([
				this.find(filter),
				SharedProvider.find(filter),
			])

			if (!local.length) return shared
			if (!shared.length) return local

			// inherit shared credentials
			const [ { credentials } ] = shared
			for (const p of local) {
				p.credentials = { ...credentials, ...p.credentials }
			}

			return local
		}
		catch (err) {
			appEcho(`getByName() - Provider '${name}' not found`, err)
		}
	}

	/**
	 * Get Providers by service (Tenanted only)
	 * @param	{String} service name
	 * @return	{Promise<Provider[]>} providers
	 */
	Provider.getByService = async function(service) {
		try {
			const filter = {
					where: { services: service }
				},
				providers = await this.find(filter)

			return providers
		}
		catch (err) {
			appEcho(`getByService() - Provider for service '${service}' not found`, err)
		}
	}

	// -----  Instance Methods  -----

	Provider.prototype.enable = async function(toEnable) {
		const { enabled } = this

		if (enabled === toEnable) return { enabled }

		const res = await this.updateAttributes({ enabled: toEnable })
		return { enabled: res.enabled }
	}

	Provider.prototype.enableModule = async function(name) {
		const { module, settings } = await this.moduleSettings(name)
		return this._enableModule(module, settings)
	}

	Provider.prototype.disableModule = async function(name) {
		const { module, settings } = await this.moduleSettings(name)
		return this._disableModule(module, settings)
	}

	// -----  Private Methods  -----

	Provider.prototype._enableModule = async function(module, settings) {
		const { modules } = this,
			{ options } = settings,
			found = modules.find(m => (m.name === module) ? (m.enabled = true) : false)

		if (!found) modules.push({ name: module, options, enabled: true })
		else found.options = options	// always set to latest options

		return this.updateAttributes({ modules })
	}

	Provider.prototype._disableModule = async function(module, settings) {
		const { modules } = this,
			{ options } = settings,
			found = modules.find(m => (m.name === module) ? (m.enabled = false) || true : false)

		if (!found) {
			modules.push({ name: module, options, enabled: false })
		}
		return this.updateAttributes({ modules })
	}

	Provider.prototype.moduleSettings = async function(name) {
		const { Service } = this.constructor.app,
			module = name.charAt(0).toUpperCase() + name.toLowerCase().slice(1),	// initial caps
			{ providers } = Service.provider._settings,
			settings = providers[this.name][module]

		if (!settings) throw 'Module not found'

		return { module, settings }
	}

	// -----  Remote Methods  -----

	Provider.remoteMethod('prototype.enableModule', {
		description: 'Enable a module',
		http: { path: '/module/enable', verb: 'post' },
		accepts: [
			{ arg: 'name', type: 'string', required: true, description: 'Name of module' },
		],
		returns: { type: 'Provider', root: true },
	})

	Provider.remoteMethod('prototype.disableModule', {
		description: 'Disable a module',
		http: { path: '/module/disable', verb: 'post' },
		accepts: [
			{ arg: 'name', type: 'string', required: true, description: 'Name of module' },
		],
		returns: { type: 'Provider', root: true },
	})
}
