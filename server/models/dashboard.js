/**
 *  @module Model:Dashboard
 */

const { Providers } = require('@crm/types'),
	{ DASHBOARD: DASHBOARD_ERR } = require('@perkd/errors/dist/service')

const { AWS } = Providers.PROVIDER,
	{ NOT_AVAILABLE } = DASHBOARD_ERR

module.exports = function(Dashboard) {

	Dashboard.prototype.url = async function(arn) {
		const { id } = this,
			api = await Dashboard.getProvider(AWS)

		if (!api) return this.rejectErr(NOT_AVAILABLE, { reason: 'provider not found' }, true)

		const url = await api.dashboards.get(id.toString(), arn)
		return { url }
	}
}
