{"name": "Assignment", "plural": "Assignments", "base": "PersistedModel", "idInjection": true, "strict": false, "mixins": {"Multitenant": true}, "options": {"validateUpsert": true}, "properties": {"start": {"type": "Date"}, "end": {"type": "Date"}, "visible": {"type": "Boolean", "default": true}}, "validations": [], "relations": {"staff": {"type": "belongsTo", "model": "Staff", "foreignKey": "staffId"}, "place": {"type": "belongsTo", "model": "Place", "foreignKey": "placeId"}}, "acls": [], "indexes": {}, "scopes": {}, "methods": []}