{"name": "Account", "plural": "Accounts", "base": "PersistedModel", "idInjection": true, "strict": false, "mixins": {}, "options": {}, "properties": {"id": {"type": "String", "id": true, "generated": true}}, "validations": [], "relations": {}, "acls": [], "indexes": {}, "scopes": {}, "methods": {"provision": {"description": "Provision an account", "http": {"path": "/crm/provision", "verb": "post"}, "accepts": [{"arg": "businessName", "type": "string", "required": true, "description": "Registered name"}, {"arg": "registration", "type": "string", "required": true, "description": "Business registration number"}, {"arg": "address", "type": "string", "required": true, "description": "Full postal address"}, {"arg": "email", "type": "string", "required": true}, {"arg": "locale", "type": "object", "required": true, "description": "{ languages, currency, precision, country, timezone }"}, {"arg": "commerce", "type": "object", "required": true, "description": "{ taxIncluded, taxes }"}, {"arg": "tags", "type": "Tag"}, {"arg": "options", "type": "object", "required": true, "description": "{ tenantCode, apps }"}], "returns": {"type": "object", "root": true}}, "register": {"description": "Register an account", "http": {"path": "/register", "verb": "post"}, "accepts": [{"arg": "data", "type": "object", "http": {"source": "body"}, "required": true}, {"arg": "req", "type": "object", "http": {"source": "req"}}], "returns": {"type": "object", "root": true}}, "deleteById": {"description": "Delete account", "http": {"path": "/:id", "verb": "delete"}, "accepts": [{"arg": "id", "type": "string", "http": {"source": "path"}, "required": true}], "returns": {"type": "object", "root": true}}}}