/**
 *  @module Model:Provision
 */
const http = require('node:http'),
	https = require('node:https'),
	FormData = require('form-data'),
	{ Context } = require('@perkd/multitenant-context'),
	{ Providers, Settings, Businesses, Contacts, Contents, Forms } = require('@crm/types'),
	{ Currencies, generateTenantCode } = require('@perkd/utils'),
	{ Provider: ProviderRedis } = require('@perkd/providers'),
	{ HttpErrors } = require('@perkd/errors')

const { PERKD } = Providers.PROVIDER,
	{ PROVIDER } = Settings.Name,
	{ NATIONAL, REGISTRATION } = Businesses.Identities,
	{ SIGNUP } = Businesses.UrlKind,
	{ MOBILE, WORK } = Contacts.Type,
	{ CONTRACT_START, CONTRACT_END } = Contacts.Dates,
	{ DEFAULT: THEME } = Contents.Theme,
	{ IMAGE } = Forms.FileType

module.exports = function(Provision) {

	/**
	 * Create a CRM Business
	 * @param {Object} props
	 *			{String} name
	 *			{String} registration - number
	 *			{String} address - full, formatted
	 *			{Object} owner - { id, mobile }
	 *			{Object} contract - { startTime, endTime }
	 *			{Object} profile - { brand, email, locale, urls }
	 *			{String[]} tags
	 *			{Boolean} isMain
	 * @param {http.IncomingMessage} req
	 * @param {Object} files
	 * @returns
	 */
	Provision.createBusiness = async function (props = {}, req, files) {
		const { Business } = Provision.app.models,
			{ name, registration, address: formatted, owner = {}, contract, profile = {}, tags, isMain } = props,
			{ brand: short = name, email, locale = {}, urls = [] } = profile,
			{ mobile = {}, id: ownerId } = owner,
			{ countryCode, number } = mobile,
			{ startTime, endTime } = contract ?? {},
			{ country } = locale,
			signup = urls.find(u => u.kind === SIGNUP),
			address = {
				type: WORK,
				formatted,
				optIn: true,
			},
			phone = {
				type: MOBILE,
				countryCode,
				number,
				optIn: true
			},
			emailList = email ? [ { type: WORK, address: email, optIn: true } ] : undefined,
			data = {
				name,
				brand: { short },
				merchantCode: generateTenantCode(name, country),
				locale,
				urls: signup ? [ signup ] : [],
				phoneList: [ phone ],
				addressList: [ address ],
				emailList,
				tags,
				isMain,
				ownerId
			}

		if (registration) {
			const identity = {
				identity: `${NATIONAL}.${REGISTRATION}.${registration}`,
				provider: NATIONAL,
				type: REGISTRATION,
				externalId: registration,
				visible: true,
			}

			data.identityList = [ identity ]
		}
		if (contract) {
			const start = {
					name: CONTRACT_START,
					date: new Date(startTime)
				},
				end = {
					name: CONTRACT_END,
					date: new Date(endTime)
				}

			data.dateList = [ start, end ]
		}

		const business = await Business.doUpsert(data),
			logo = await this.createUploadLogoImage(business.id, req, files)
				.catch(err => console.log(err))

		return business
	}

	Provision.createUploadLogoImage = async function (ownerId, req, files = {}) {
		const { LogoImage } = Provision.app.models,
			{ pluralModelName } = LogoImage,
			logo = await LogoImage.create({ name: THEME, ownerId }),
			imageId = String(logo.id),
			{ url: apiBase } = LogoImage.getDataSource().connector,
			isHttps = apiBase.startsWith('https'),
			{ request: fetch } = isHttps ? https : http,
			{ hostname, pathname, port } = new URL(`${apiBase}/${pluralModelName}/${imageId}/upload`),
			image = files[IMAGE],
			{ file, filename, mimeType: contentType } = image ?? {},
			form = new FormData()

		if (!file || !req) return { imageId }

		form.append(IMAGE, file, { filename, contentType })

		const headers = {
				...req.headers,
				...form.getHeaders(),
			},
			request = fetch({
				method: 'post',
				host: hostname,
				port,
				path: pathname,
				headers
			})

		form.pipe(request)

		return new Promise((resolve, reject) => {
			request.on('response', res => resolve({ imageId }))
			request.on('error', err => reject(err))
		})
	}

	/**
	 * Provision Business in Perkd (X)
	 * @param {Business} business instance
	 * @param {String} tenant code
	 * @return {Business} instance (CRM)
	 */
	Provision.createPerkdBusiness = async function (business, tenant) {
		const { Perkd } = appModule('perkd'),
			{ type, name, brand, style, locale, tags, globalize, phoneList, addressList } = business.toJSON(),
			profile = { type, name, brand, style, locale, tags, globalize, phoneList, addressList, tenant },
			pBusiness = await Perkd.businesses.create(profile),
			{ id: businessId } = pBusiness ?? {},
			updates = {
				external: { [PERKD]: { businessId } }
			}

		// TODO
		// LogoImage

		return pBusiness
			? business.updateAttributes(updates)
			: business
	}

	/**
	 * Create Provider for tenant
	 * @param {String} name - provider key
	 * @param {Object} credentials
	 * @param {Boolean} liveMode
	 * @param {Boolean} enabled
	 * @param {String[]} [services]
	 * @param {Object[]} [modules]
	 * @param {Object} [options]
	 * @param {String} [shop] - multi-merchant only
	 * @return Promise<void>
	 */
	Provision.createProvider = async function (name, credentials, liveMode, enabled = true, services = [], modules = [], options = {}, shop) {
		const { Provider, Business } = Provision.app.models,
			providers = new ProviderRedis(name),
			config = {
				name, credentials, liveMode, enabled,
				services, modules, options, shop
			},
			api = await appModule(PROVIDER).getAPI(config),
			{ accountId } = api,
			filter = {
				where: { name, shop }
			},
			[ instance, isNew ] = await Provider.findOrCreate(filter, config)

		if (!isNew) throw HttpErrors.BadRequest('Already provisioned')

		await Promise.allSettled([
			accountId && providers.tenants.add(accountId, Context.tenant),		// may not apply for some providers
			Business.updateSetting(PROVIDER, { [name]: true }, true)
		])
		await providers.end()
	}

	/**
	 * @param {String} businessId
	 * @return {Object} - { currency, profile }
	 */
	Provision.getBusinessProfile = async function (businessId) {
		const { Business } = Provision.app.models,
			business = await Business.findById(businessId),
			{ locale = {}, phoneList = [] } = business ?? {},
			{ currency: code } = locale,
			{ decimals: precision } = Currencies.currency(code),
			currency = { code, precision },
			[ phone ] = phoneList,
			profile = { mobile: phone?.fullNumber }

		if (!business) throw HttpErrors.NotFound('Business not found')

		return { currency, profile }
	}

	Provision.getTemplate = function(name) {
		try {
			const file = process.cwd() + `/templates/${name}.json`,
				config = require(file)

			return config
		}
		catch (err) {
			throw new Error(`template '${name}' not found`)
		}
	}
}