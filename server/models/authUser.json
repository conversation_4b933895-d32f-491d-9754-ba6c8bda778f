{"name": "AuthUser", "plural": "users", "base": "PersistedModel", "idInjection": true, "strict": false, "mixins": {"MultitenantRemote": true}, "properties": {"id": {"type": "String", "id": true, "generated": true}}, "validations": [], "relations": {}, "acls": [], "methods": {"getByStaffId": {"description": "Get user & roles by staff Id", "http": {"path": "/staff", "verb": "get"}, "accepts": [{"arg": "staffId", "type": "string", "required": true}], "returns": {"type": "object", "root": true}}, "getStaffCardToken": {"description": "Get token for staff card", "http": {"path": "/staff/card/token", "verb": "post"}, "accepts": [{"arg": "id", "type": "string", "required": true}, {"arg": "payload", "type": "object"}], "returns": {"type": "object", "root": true}}, "prototype.destroy": {"http": {"verb": "delete", "path": "/"}, "accepts": [{"arg": "fk", "type": "any", "description": "Foreign key for user", "required": true, "http": {"source": "path"}}], "description": "Delete a related item by id for user.", "returns": []}}}