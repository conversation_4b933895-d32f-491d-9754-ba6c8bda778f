/**
 *  @module Model:SharedProvider  (only Business have access to this)
 */

module.exports = function(SharedProvider) {

	/**
	 * Get Provider by name
	 * @param	{String} name of provider
	 * @return	{SharedProvider} instance
	 */
	SharedProvider.getByName = function(name) {
		return this.findOne({ where: { name } })
	}

	/**
	 * Get Provider by service name
	 * @param	{String} name of service
	 * @return	{SharedProvider} instance
	 */
	SharedProvider.getByService = function(name) {
		const filter = {
			where: { services: { inq: [name] } }
		}

		return this.findOne(filter)
	}
}
