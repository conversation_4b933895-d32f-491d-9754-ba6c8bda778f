{"name": "Provider", "plural": "Providers", "base": "PersistedModel", "idInjection": true, "strict": true, "mixins": {"Multitenant": true, "Timestamp": true, "Errors": true, "DisableAllRemotes": {"create": true, "upsert": false, "updateAll": false, "prototype.patchAttributes": true, "find": true, "findById": true, "findOne": true, "findOrCreate": false, "createChangeStream": false, "deleteById": true, "confirm": false, "count": true, "exists": false, "replaceById": false, "replaceOrCreate": false, "upsertWithWhere": true}}, "properties": {"name": {"type": "string", "max": 32, "required": true}, "services": {"type": [{"type": "string", "enum": ["push", "sms", "email", "voice", "whatsapp", "rich", "offer", "notify", "order", "fulfillment", "product", "customer", "discount"]}], "default": []}, "credentials": {"type": "object", "default": {}, "description": "Also inherit from <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> of identical name (if exists)"}, "liveMode": {"type": "boolean", "default": false, "description": "If service is in production mode"}, "enabled": {"type": "boolean", "default": false}, "modules": {"type": [{"type": {"name": {"type": "string", "max": "32"}, "enabled": {"type": "boolean", "default": false}, "options": {"type": "object", "default": {}, "description": "Module specific custom options"}}}], "default": []}, "shop": {"type": "string", "max": 128, "description": "MultiMerchant - shop-specific provider"}, "options": {"type": "Object", "default": {}, "description": "provider specific custom data"}, "demo": {"type": "boolean", "description": "Enable use of test credentials in production env, & vice versa"}, "createdAt": {"type": "Date"}, "modifiedAt": {"type": "Date"}}, "hidden": [], "validations": [], "relations": {}, "acls": [], "indexes": {"name": {"keys": {"name": 1}}}, "scopes": {}, "methods": {"getByName": {"description": "Get providers (including credentials) by name", "http": {"path": "/name", "verb": "get"}, "accepts": [{"arg": "name", "type": "string", "required": true}], "returns": {"type": ["Provider"], "root": true}}, "getByService": {"description": "Get providers (including credentials) by service name", "http": {"path": "/service", "verb": "get"}, "accepts": [{"arg": "service", "type": "string", "required": true}], "returns": {"type": ["Provider"], "root": true}}}}