{"name": "SharedProvider", "plural": "SharedProviders", "base": "Provider", "idInjection": true, "strict": false, "mixins": {"Multitenant": false, "DisableAllRemotes": {"find": true, "findOne": true}}, "properties": {}, "validations": [], "relations": {}, "acls": [], "scopes": {}, "methods": {"getByService": {"description": "Get credentials by service name", "http": {"path": "/service", "verb": "get"}, "accepts": [{"arg": "name", "type": "string", "required": true}], "returns": {"type": "object", "root": true}}, "getByName": {"description": "Get credentials by provider name", "http": {"path": "/provider", "verb": "get"}, "accepts": [{"arg": "name", "type": "string", "required": true}], "returns": {"type": "object", "root": true}}}}