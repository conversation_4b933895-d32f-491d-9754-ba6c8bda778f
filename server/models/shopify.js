/**
 *  @module Model:shopify - BusinessProvider
 */

const { Persons, Providers } = require('@crm/types'),
	{ shortId } = require('@perkd/utils')

const { CUSTOMER, NATIONAL, REGISTRATION, SMARTCOLLECTION } = Persons.Identities,
	{ CUSTOMERS, SMARTCOLLECTIONS } = Providers.Module

const CREATED_DESC = 'created-desc',
	VENDOR = 'vendor',
	EQUALS = 'equals'

module.exports = function(Shopify) {

	// ---  CRM events
	Shopify.handleBusinessCreated = async function (business) {
		return this.upsertBusiness(business)
	}

	Shopify.handleBusinessUpdated = async function (business) {
		return this.upsertBusiness(business)
	}

	Shopify.handleBusinessDeleted = async function (business) {
		// return this.deleteBusiness(business)
	}

	// --- Shopify ---

	/**
	 * @param	{Object} business
	 * @param	{Object} options force: update when there's no changes
	 */
	Shopify.upsertBusiness = async function (business = {}, options = {}) {
		const { name: SHOPIFY, app } = this,
			{ Business } = app.models,
			{ id: businessId, name, phoneList, emailList, addressList, identityList = [], tags, merchantCode } = business,
			registrationNumber = identityList.filter(({ provider, type }) => provider === NATIONAL && type === REGISTRATION ),
			data = { givenName: name, phoneList, emailList, addressList, tags, identityList: registrationNumber, merchantCode },
			api = await this.getProvider(SHOPIFY, CUSTOMERS),
			identity = identityList.reduce((p, c) => {
				const { provider, type, createdAt } = c
				return (provider === SHOPIFY && type === CUSTOMER && (!p || p.createdAt > createdAt))
					? c
					: p
			}, undefined),
			{ externalId } = identity ?? {}

		if (!api) return

		// update
		if (externalId) {
			return api.customers.update(externalId, data)
				.catch(err => {
					appLog('shopify_customer_update_fail', { err, externalId })
				})
		}

		// create
		const NOW = new Date(),
			[ customer, instance ] = await Promise.all([
				api.customers.create(data),
				Business.findById(businessId)
			])
				.catch(err => {
					appLog('shopify_customer_create_fail', { err, businessId })
				})

		await instance.updateAttributes({	// MUST by-pass business update to prevent cyclical update events
			$push: {
				identityList: {
					id: shortId(5),
					identity: `${SHOPIFY}.${CUSTOMER}.${customer.id}`,
					provider: SHOPIFY,
					type: CUSTOMER,
					domain: api.shopName,
					externalId: customer.id.toString(),
					createdAt: NOW,
					modifiedAt: NOW,
				},
			},
		}).catch(err => {
			console.log('debug [identities.build-error] %j', { data, err })
		})

		return customer
	}

	Shopify.upsertSmartCollection = async function(business) {
		const { name: SHOPIFY } = this,
			{ name: title, merchantCode, identityList } = business,
			identity = identityList.find(({ provider, type }) => provider === SHOPIFY && type === SMARTCOLLECTION),
			{ externalId: collectionId } = identity || {},
			config = {
				title,
				handle: merchantCode,
				sortOrder: CREATED_DESC,
				rules: [ {
					column: VENDOR,
					relation: EQUALS,
					condition: merchantCode
				} ]
			},
			api = await this.getProvider(SHOPIFY, SMARTCOLLECTIONS)

		if (!api) return

		if (collectionId) {
			config.id = collectionId
			return api.smartCollections.update(collectionId, config)
		}

		const collection = await api.smartCollections.create(config),
			{ id: smartCollectionId } = collection,
			NOW = new Date()

		await business.updateAttributes({
			$push: {
				identityList: {
					id: shortId(5),
					identity: `${SHOPIFY}.${SMARTCOLLECTION}.${smartCollectionId}`,
					provider: SHOPIFY,
					type: SMARTCOLLECTION,
					externalId: smartCollectionId.toString(),
					createdAt: NOW,
					modifiedAt: NOW,
				},
			},
		})
		return collection
	}
}
