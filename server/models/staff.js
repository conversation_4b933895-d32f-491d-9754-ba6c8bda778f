/**
 * @module Model:Staff
 */
const { Memberships, Touchpoints, Settings, Apis, Notify } = require('@crm/types'),
	{ widgetsFor, hasPermission } = require('@perkd/accesscontrol'),
	{ spotName } = require('@perkd/fulfillments'),
	{ currency: getCurrencyFormat } = require('@perkd/utils'),
	{ STAFF: STAFF_ERR, MEMBERSHIP: MEMBERSHIP_ERR } = require('@perkd/errors/dist/service')

const { MANUAL } = Memberships.QualifyMethod,
	{ CRM } = Touchpoints.Type,
	{ DEFAULT } = Settings,
	{ STAFF } = Settings.Name,
	{ X_ACCESS_TOKEN } = Apis.Headers,
	{ Templates } = Notify,
	{ NOT_FOUND } = STAFF_ERR,
	{ MEMBERSHIP_NOT_FOUND } = MEMBERSHIP_ERR

module.exports = function(Staff) {

	Staff.getCardId = async function(staffId = null) {
		const staff = await Staff.findById(staffId)

		if (!staff) throw NOT_FOUND

		const membership = await staff.membership.get(),
			{ digitalCard = {} } = membership || {},
			{ id = '' } = digitalCard

		if (!membership) throw MEMBERSHIP_NOT_FOUND

		return id
	}

	Staff.findOrCreateProviderStaff = async function(provider, staffId) {
		const filter = {
				where: {
					'identity.provider': provider,
					'identity.externalId': staffId
				},
			},
			data = {
				identityList: [ { provider, externalId: staffId } ],
			},
			[ result, isNew ] = await Staff.findOrCreate(filter, data)

		return result
	}

	/**
	 * Find staff assigned to place, optionally of specific roles
	 * @param	{String} placeId
	 * @param	{String[]} [roles]
	 * @return	{Staff+[]} - with 'roles' injected into instances
	 */
	Staff.assignedTo = async function(placeId = null, roles = []) {
		const { Assignment, AuthUser } = Staff.app.models,
			NOW = new Date(),
			filter = {
				where: {
					placeId,
					start: { lte: NOW },
					or: [
						{ end: { gte: NOW } },
						{ end: { exists: false } },
						{ end: null }
					]
				},
				fields: [ 'staffId' ]
			},
			assignments = await Assignment.find(filter),
			staffIds = assignments.map(a => a.staffId),
			staffFilter = {
				where: {
					id: { inq: staffIds }
				}
			},
			staffs = await Staff.find(staffFilter)

		for (const staff of staffs) {
			const { id } = staff,
				user = await AuthUser.getByStaffId(String(id))

			staff.roles = user?.roles
		}

		const assigned = roles.length
			? staffs.filter(s => roles.some(r => s.roles.includes(r)))
			: staffs

		return assigned
	}

	/**
	 * Find assigned places for a staff
	 * @param	{String} staffId
	 * @return	{String[]} - list of placeIds
	 */
	Staff.getAssignedPlaces = async function(staffId) {
		const { Assignment } = Staff.app.models,
			NOW = new Date(),
			filter = {
				where: {
					staffId,
					start: { lte: NOW },
					or: [
						{ end: { gte: NOW } },
						{ end: { exists: false } },
						{ end: null }
					]
				},
				fields: [ 'placeId' ]
			},
			assignments = await Assignment.find(filter),
			placeIds = assignments.map(a => a.placeId)

		return placeIds
	}

	// ----  Notify Event handlers

	/**
	 * Fulfillment notifications
	 * @param	{Object} event
	 *			{String} name
	 *			{Object} data - fulfillment
	 */
	Staff.handleFulfillmentNotify = async function(event) {
		try {
			const { data: fulfillment } = event,
				template = notifyTemplate(event),
				{ receipt = {}, placeId, itemList, scheduled } = fulfillment,
				{ queue = '' } = receipt,
				count = itemList.reduce((total, item) => total + item.quantity, 0),
				personalize = { queue, count },
				{ roles } = template ?? {},
				staffs = await Staff.assignedTo(placeId, roles)

			// personalize data
			if (fulfillment.minTime) {
				const minTime = new Date(fulfillment.minTime),
					scheduledTime = new Date(scheduled.minTime),
					at = minTime > scheduledTime ? minTime : scheduledTime

				personalize.scheduledAt = at
			}

			for (const staff of staffs) {
				staff.notify(template, personalize)
			}
		}
		catch (err) {
			appNotify('[handleFulfillmentNotify]', err, 'error')
		}
	}

	/**
	 * Service Request notifications
	 * @param	{Object} event
	 *			{String} name
	 *			{Object} data
	 *				{String} userId
	 *				{String} cardId
	 *				{Object} spot - { type, placeId, name, position: [{ key, value }] }
	 *				{String} note
	 */
	Staff.handleServceNotify = async function (event) {
		try {
			const { Event, models } = Staff.app,
				{ Person } = models,
				{ data } = event,
				{ userId, cardId, note, spot = {} } = data,
				{ placeId } = spot,
				position = spotName(spot),
				template = notifyTemplate(event),
				{ roles } = template ?? {},
				[ person, staffs ] = await Promise.all([
					Person.findOneByUserId(userId),
					Staff.assignedTo(placeId, roles),
				]),
				{ id: pId, profileImageUrl: image } = person ?? {},
				personId = String(pId),
				personalize = { position, note },
				[ first = {} ] = staffs,		// assume FIRST staff accepted (for now)
				{ id } = first,
				staffId = String(id)

			for (const staff of staffs) {
				staff.notify(template, personalize, { image })
			}

			appEmit(Event.applet.service.accepted, { staffId, position, placeId, personId, userId, cardId, note })
		}
		catch (err) {
			appNotify('[handleServceNotify]', err, 'error')
		}
	}

	/**
	 * Manual Order notifications
	 * @param {Object} event
	 *        {String} name
	 *        {Object} data - order
	 */
	Staff.handleManualOrderNotify = async function(event) {
		try {
			const { data: order } = event,
				{ storeId, amount = 0, currency, acquired = {} } = order,
				{ location = {}, attributedTo } = acquired

			if (attributedTo?.type === STAFF) return // skip if it is guest order, took by staff

			const { position = [] } = location,
				template = {
					name: 'staff:kitchen:manualOrderCreated',
					roles: [ 'cashier', 'admin' ]
				},
				{ decimals = 2, locale } = getCurrencyFormat(currency)

			// Format position info if available
			let positionText = ''
			if (position && position.length > 0) {
				positionText = position.map(p => `${p.key}${p.value}`).join(', ') + ' - '
			}

			const personalize = {
				amount,
				formatParams: {
					amount: {
						locale,
						currency,
						maximumFractionDigits: decimals,
						minimumFractionDigits: decimals
					}
				},
				position: positionText
			}

			const staffs = await Staff.assignedTo(storeId, template.roles)

			for (const staff of staffs) {
				staff.notify(template, personalize)
			}
		}
		catch (err) {
			appNotify('[handleManualOrderNotify]', err, 'error')
		}
	}

	// ---- Instance Methods

	/**
	 * Issue card for staff
	 * @param	{String} programId
	 * @param	{Number} tierLevel
	 * @param	{Object} options
	 *			{Object} at
	 *			{Boolean} noNotify
	 *			{Boolean} noSMS
	 *     		{Boolean} isNewMembership - new membership or existing membership issue card
	 * 			{Object} through - {type: 'crm'}
	 *			{Object} notification
	 * @return	{Membership}
	 */
	Staff.prototype.issueCard = async function(programId, tierLevel, options = {}) {
		const { id, personId } = this,
			{ AuthUser, Person, Program } = Staff.app.models,
			staffId = id.toString(),
			[ user, person, program ] = await Promise.all([
				AuthUser.getByStaffId(staffId),
				Person.findById(personId),
				Program.findById(programId),
			]),
			{ memberId } = person ?? {},
			{ roles = [] } = user ?? {},
			userId = String(user?.id),
			tier = program.tierList.find(t => (t.level === tierLevel)),
			{ roles: requiredRoles = [] } = tier ?? {},
			noRole = options.noRole && roles.length === 0

		if (!memberId) throw new Error('Person does not have a memberId')		// TODO return 'error code'?
		if (!user) throw new Error('User does not exist')
		if (!tier) throw new Error('Tier not found')

		// allow to issue staff card without roles
		if (!noRole && !requiredRoles.some(required => hasPermission(roles, required))) throw new Error('No permission')

		const keys = widgetsFor(roles),
			placeIds = await Staff.getAssignedPlaces(staffId),
			places = placeIds.length > 0 ? placeIds : undefined,
			payload = { places, widgets: keys },
			{ token } = await AuthUser.getStaffCardToken(userId, payload),
			credentials = {
				remote: {
					perkd: { [X_ACCESS_TOKEN]: token },
				},
			},
			formData = { staffId },
			through = {
				type: CRM,
				attributedTo: {},
				touchedAt: new Date(),
			},
			opt = {
				qualifyMethod: MANUAL,
				newCard: true,
				formData,
				through,
				credentials,
				staffId,
				widgets: buildWidgets(keys),
				...options,
			}

		return Program.joinMembership(programId, memberId, tierLevel, opt)
	}

	/**
	 * Refresh Staff's credential
	 * @return {Promise<void>}
	 */
	Staff.prototype.refreshCredential = async function() {
		const { Perkd } = appModule('perkd'),
			{ AuthUser } = Staff.app.models,
			staffId = String(this.id),
			[ user, membership ] = await Promise.all([
				AuthUser.getByStaffId(staffId),
				this.membership.get()
			]),
			{ roles } = user ?? {},
			userId = String(user?.id),
			keys = widgetsFor(roles),
			payload = { widgets: keys },
			{ token } = await AuthUser.getStaffCardToken(userId, payload),
			credentials = { [ X_ACCESS_TOKEN ]: token },
			{ digitalCard } = membership,
			{ id: cardId } = digitalCard

		return cardId
			? Perkd.cards.setCredentials(cardId, 'remote', 'perkd', credentials)
			: undefined
	}

	/**
	 * Send Push Notification to staff's card
	 * @param {Object} template - { name, widget? }
	 * @param {Object} [personalize] data
	 * @param {Object} [options]
	 */
	Staff.prototype.notify = async function(template, personalize, options) {
		const membership = await this.membership.get()
		return Staff.appNotify(membership, template, personalize, options)
	}

	// ---  Private functions

	/**
	 * Customise staff/instance level widget config:
	 * 	1. visible
	 * 	2. startTime
	 * 	3. endTime
	 * 	4. param
	 */
	function buildWidgets(keys = []) {
		const widgets = []

		for (const key of keys) {
			widgets.push({ key, visible: true })
		}
		return widgets
	}

	/**
	 * For push notifications
	 */
	function notifyTemplate(event = {}) {
		const { name } = event,
			{ app } = Staff,
			{ sales, applet } = app.Event,
			{ Kitchen, Service } = Templates.Staff,
			{ TEMPLATES = {} } = DEFAULT.STAFF,
			{ templates = TEMPLATES } = app.getSettings(STAFF),
			{ kitchen } = sales.fulfillment

		let key

		switch (name) {
		case kitchen.requested:
			key = Kitchen.REQUESTED
			break

		case kitchen.packed:
			key = Kitchen.PACKED
			break

		case applet.service.request:
			key = Service.REQUEST
			break
		}

		return templates[key]
	}

	// -----  Remote & Operation Hooks  -----

	Staff.observe('after delete', async ({ where }) => {
		const { AuthUser } = Staff.app.models,
			staffId = where?.id || null

		if (staffId) {
			try {
				const user = await AuthUser.findOne({ where: { staffId } })
				if (user) AuthUser.destroyById(user.id)
			}
			catch (err) {
				return null
			}
		}
	})
}
