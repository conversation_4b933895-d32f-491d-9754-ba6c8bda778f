{"name": "Provision", "plural": "Provisions", "base": "Model", "strict": true, "mixins": {"Provision": true, "Deprovision": true, "ProvisionL2": true, "ProvisionL4": true, "DeprovisionL2": true, "MarketPlace": true}, "properties": {}, "acls": [], "scopes": {}, "methods": {"account": {"description": "Provision CRM Account", "http": {"path": "/account", "verb": "post"}, "accepts": [{"arg": "profile", "type": "object", "required": true, "description": "{ name, registration, country, address, categories, taxIncluded, taxes }"}, {"arg": "owner", "type": "object", "required": true, "description": "Owner info: { name, email, phone: { countryCode, number }}"}, {"arg": "options", "type": "object", "description": "{ tenantCode }"}], "returns": {"type": "object", "root": true}}, "status": {"description": "Provision status", "http": {"path": "/status", "verb": "get"}, "accepts": [{"arg": "businessId", "type": "string"}], "returns": {"type": "object", "root": true}}, "createProvider": {"description": "Create a new Provider for business (used by Shopify app)", "http": {"path": "/provider", "verb": "post"}, "accepts": [{"arg": "name", "type": "string", "required": true, "description": "Key of provider"}, {"arg": "credentials", "type": "object", "required": true, "description": "provider specific"}, {"arg": "liveMode", "type": "Boolean", "required": true}, {"arg": "enabled", "type": "Boolean", "required": true}, {"arg": "services", "type": ["string"], "default": []}, {"arg": "modules", "type": ["object"], "default": []}, {"arg": "option", "type": "object", "description": "provider specific", "default": {}}, {"arg": "shop", "type": "String", "description": "Shopify shop name, multi-merchant only"}], "returns": {"type": "object", "root": true}}, "paymentProvider": {"description": "Payment Provider for business", "http": {"path": "/provider/payment", "verb": "post"}, "accepts": [{"arg": "businessId", "type": "string", "required": true}, {"arg": "provider", "type": "string", "required": true, "description": "key of provider"}, {"arg": "credentials", "type": "object", "required": true, "description": "provider specific"}, {"arg": "option", "type": "object", "required": true, "description": "provider specific"}, {"arg": "restrict", "type": ["string"], "description": "limit to these payment types"}], "returns": {"type": ["string"], "root": true}}, "orderProvider": {"description": "Order Provider for tenant", "http": {"path": "/provider/order", "verb": "post"}, "accepts": [{"arg": "name", "type": "string", "required": true, "description": "key of provider"}, {"arg": "credentials", "type": "object", "required": true, "description": "provider specific"}, {"arg": "liveMode", "type": "boolean"}, {"arg": "enabled", "type": "boolean"}, {"arg": "services", "type": ["string"], "required": true}, {"arg": "modules", "type": ["object"]}, {"arg": "option", "type": "object", "description": "provider specific"}], "returns": {"type": "object", "root": true}}, "orderProviderForPlace": {"description": "Order Provider for Place", "http": {"path": "/provider/order/place", "verb": "post"}, "accepts": [{"arg": "placeId", "type": "string", "required": true}, {"arg": "shop", "type": "string", "description": "Provider's shop name/id"}, {"arg": "name", "type": "string", "required": true, "description": "key of provider"}, {"arg": "credentials", "type": "object", "required": true, "description": "provider specific"}, {"arg": "liveMode", "type": "boolean"}, {"arg": "enabled", "type": "boolean"}, {"arg": "services", "type": ["string"], "required": true}, {"arg": "modules", "type": ["object"]}, {"arg": "option", "type": "object", "description": "provider specific"}], "returns": {"type": "object", "root": true}}, "printProvider": {"description": "Print Provider for tenant", "http": {"path": "/provider/print", "verb": "post"}, "accepts": [{"arg": "name", "type": "string", "required": true, "description": "key of provider"}, {"arg": "credentials", "type": "object", "required": true, "description": "provider specific"}, {"arg": "liveMode", "type": "boolean"}, {"arg": "enabled", "type": "boolean"}, {"arg": "services", "type": ["string"], "required": true}, {"arg": "modules", "type": ["object"]}, {"arg": "option", "type": "object", "description": "provider specific"}], "returns": {"type": "object", "root": true}}, "invoiceProvider": {"description": "Invoice Provider for tenant", "http": {"path": "/provider/invoice", "verb": "post"}, "accepts": [{"arg": "name", "type": "string", "required": true, "description": "key of provider"}, {"arg": "credentials", "type": "object", "required": true, "description": "provider specific"}, {"arg": "liveMode", "type": "boolean"}, {"arg": "enabled", "type": "boolean"}, {"arg": "services", "type": ["string"], "required": true}, {"arg": "modules", "type": ["object"]}, {"arg": "option", "type": "object", "description": "provider specific"}], "returns": {"type": "object", "root": true}}, "program": {"description": "Program for business", "http": {"path": "/program", "verb": "post"}, "accepts": [{"arg": "businessId", "type": "string", "required": true}, {"arg": "type", "type": "string", "required": true}, {"arg": "name", "type": "string", "required": true}, {"arg": "shortName", "type": "string", "required": true}, {"arg": "levels", "type": ["object"], "required": true, "description": "List of levels: { level, type, name, description, numbering } + other props"}, {"arg": "data", "type": "object", "description": "Program data"}], "returns": {"type": "Program", "root": true}}, "storedValue": {"description": "Stored Value for Tier of Program", "http": {"path": "/program/storedvalue", "verb": "post"}, "accepts": [{"arg": "programId", "type": "string", "required": true}, {"arg": "tierLevel", "type": "number", "required": true}, {"arg": "provider", "type": "object", "required": true, "description": "{ name, credentials, options, external }"}, {"arg": "topup", "type": "object", "description": "{ denominations: [], variable: { min, max, increment } }"}, {"arg": "preload", "type": "object", "description": "{ denominations: [] }"}, {"arg": "currency", "type": "object", "description": "{ code, symbol, precision, exchangeRate }"}, {"arg": "style", "type": "object", "description": "{ color, format, fontSize }"}, {"arg": "singleUse", "type": "boolean", "description": "giftcard only"}, {"arg": "sharedOnly", "type": "boolean", "description": "giftcard only"}], "returns": {"type": "object", "root": true}}, "staffRoles": {"description": "Roles for Tier of Staff Program", "http": {"path": "/program/staff/roles", "verb": "post"}, "accepts": [{"arg": "programId", "type": "string", "required": true}, {"arg": "level", "type": "number", "required": true}, {"arg": "roles", "type": ["string"], "required": true, "description": "Staff with ANY of the roles qualifies for this tier"}], "returns": {"type": "object", "root": true}}, "printer": {"description": "Printer for place", "http": {"path": "/place/:id/printer", "verb": "post"}, "accepts": [{"arg": "id", "type": "string", "http": {"source": "path"}, "required": true}, {"arg": "name", "type": "string", "description": "Name of printer"}, {"arg": "purpose", "type": "string", "enum": ["kitchen", "receipt", "delivery"], "default": "kitchen"}, {"arg": "credentials", "type": "string", "required": true, "description": "scan qr-code on printer"}], "returns": {"type": "object", "root": true}}, "pickup": {"description": "PIckup (fulfillment)", "http": {"path": "/pickup", "verb": "post"}, "accepts": [{"arg": "ticketTemplate", "type": "string"}, {"arg": "type", "type": "string", "enum": ["store", "vending", "convenience_store"]}, {"arg": "custom", "type": "Pickup"}], "returns": {"type": "object", "root": true}}, "deliver": {"description": "Deliver (fulfillment)", "http": {"path": "/deliver", "verb": "post"}, "accepts": [{"arg": "ticketTemplate", "type": "string"}, {"arg": "custom", "type": "Deliver"}], "returns": {"type": "object", "root": true}}, "store": {"description": "Store (fulfillment)", "http": {"path": "/store", "verb": "post"}, "accepts": [{"arg": "ticketTemplate", "type": "string"}, {"arg": "custom", "type": "Store"}], "returns": {"type": "object", "root": true}}, "kitchen": {"description": "Kitchen (fulfillment)", "http": {"path": "/kitchen", "verb": "post"}, "accepts": [{"arg": "ticketTemplate", "type": "string"}, {"arg": "maxScheduleWindow", "type": "number"}, {"arg": "prepareLookahead", "type": "number"}, {"arg": "minTimer", "type": "number"}, {"arg": "autoPrepare", "type": "boolean"}, {"arg": "autoPack", "type": "boolean"}], "returns": {"type": "object", "root": true}}, "vending": {"description": "Vending machine (fulfillment)", "http": {"path": "/vending", "verb": "post"}, "accepts": [{"arg": "businessId", "type": "string"}], "returns": {"type": "object", "root": true}}, "action": {"description": "Preset Action", "http": {"path": "/action", "verb": "post"}, "accepts": [{"arg": "key", "type": "string", "required": true, "description": "ActionKey"}, {"arg": "data", "type": "object", "description": "Action data"}], "returns": {"type": "object", "root": true}}, "decommission": {"description": "Purge CRM Account permanently", "http": {"path": "/account", "verb": "delete"}, "accepts": [{"arg": "accountId", "type": "string", "required": true}], "returns": {"type": "object", "root": true}}, "deprovisionPaymentProvider": {"description": "De-provision Payment Provider for business", "http": {"path": "/provider/payment", "verb": "delete"}, "accepts": [{"arg": "businessId", "type": "string", "required": true}, {"arg": "provider", "type": "string", "required": true, "description": "key of provider"}], "returns": {"type": ["string"], "root": true}}, "deprovisionOrderProvider": {"description": "De-provision Order Provider for tenant", "http": {"path": "/provider/order", "verb": "delete"}, "accepts": [{"arg": "name", "type": "string", "required": true, "description": "Key of provider"}, {"arg": "shop", "type": "string", "description": "Multi-merchants only"}], "returns": {"type": "object", "root": true}}, "deprovisionProgram": {"description": "Deprovision Program", "http": {"path": "/program", "verb": "delete"}, "accepts": [{"arg": "programId", "type": "string", "required": true}], "returns": {"type": "object", "root": true}}, "deprovisionStoredValue": {"description": "Deprovision Stored Value for Tier of Program", "http": {"path": "/program/storedvalue", "verb": "delete"}, "accepts": [{"arg": "programId", "type": "string", "required": true}, {"arg": "tierLevel", "type": "number", "required": true}], "returns": {"type": "object", "root": true}}, "deprovisionStaffRoles": {"description": "Deprovision Roles for Tier of Staff Program", "http": {"path": "/program/staff/roles", "verb": "delete"}, "accepts": [{"arg": "programId", "type": "string", "required": true}, {"arg": "tierLevel", "type": "number", "required": true}], "returns": {"type": "object", "root": true}}, "deprovisionFulfillment": {"description": "Deprovision fulfillment type", "http": {"path": "/fulfillment/:type", "verb": "delete"}, "accepts": [{"arg": "type", "http": {"source": "path"}, "type": "string", "enum": ["deliver", "pickup", "store", "vending"]}, {"arg": "businessId", "type": "string"}], "returns": {"type": "object", "root": true}}}}