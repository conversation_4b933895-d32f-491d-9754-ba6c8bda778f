/**
 * @module Model:Business
 */

const applyFilter = require('loopback-filters'),
	{ Settings, Businesses, Contents, Products, Contacts } = require('@crm/types'),
	{ DASHBOARD: DASHBOARD_ERR } = require('@perkd/errors/dist/service')

const { SIGNUP } = Businesses.UrlKind,
	{ PAYMENT } = Settings.Name,
	{ MENU } = Products.Channel,
	{ DEFAULT } = Contents.Theme,
	{ CONTRACT_START, CONTRACT_END } = Contacts.Dates,
	QUICKSIGHT = 'quicksight',
	{ NOT_AVAILABLE } = DASHBOARD_ERR,
	EN = 'en'

module.exports = function(Business) {

	/**
	 * Get main business
	 * @return {Business}
	 */
	Business.getMain = function() {
		return this.findOne({ where: { isMain: true } })
	}

	/**
	 * get all the operating stores between 'from' (optional) and 'to' (optional)
		eg.	getStores()			all currently operating stores
			getStores(from)		operating since 'from'  (still operating)
			getStores(null, to)	operating as at 'to'
			getStores(from, to)	operating within from - to

	* @param	{String} from	start of period (optional)
	* @param	{String} to		end of period (optional)
	* @return	{Array}			list of Stores
	*/
	Business.getStores = async function(from, to) {
		const { Place } = Business.app.models,
			filter = {
				where: { permanentlyClosed: false },
			},
			condition = {
				where: { and: [ checkStart, checkEnd ] },
			},
			places = await Place.find(filter)

		return applyFilter(places, condition)

		function checkStart(place) {
			if (!from) return true

			const { dateList = [] } = place,
				start = dateList.find(d => (d.name === CONTRACT_START))

			if (!start) return true
			return new Date(start.date) >= new Date(from)
		}

		function checkEnd(place) {
			if (!to) return true

			const { dateList = [] } = place,
				end = dateList.find(d => (d.name === CONTRACT_END))

			if (!end) return true
			return new Date(end.date) <= new Date(to)
		}
	}

	/**
	 * get Staff of given Role(s) during the (optional) Period
	 * rules for 'from' and 'to' same as getStores()
	 * @param	{String/Array} roles	role names (optional)
	 * @param	{String} from			start of period (optional)
	 * @param	{String} to				end of period (optional)
	 * @return	{Array}					list of Staff
	*/
	Business.getStaff = async function(roles = [], from, to) {
		const { Staff } = Business.app.models,
			allStaff = [],
			staffIds = []

		if (!Array.isArray(roles)) roles = [ roles ]

		for (const role of roles) {
			const filter = { where: { roles: role } },
				staffs = await Staff.find(filter)

			for (const staff of staffs) {
				if (staffIds.indexOf(staff.id) < 0) {
					allStaff.push(staff)
					staffIds.push(staff.id)
				}
			}
		}

		return allStaff
	}

	/**
	 * get Assignements (to stores) of given Staff(s) during the (optional) Period
	 * @param	{String/Array} staffList	list of staff (ids)
	 * @param	{String} from				start of period (optional)
	 * @param	{String} to					end of period (optional)
	 * @return	{Array}						list of Assignments
	 */
	Business.getAssignment = function(staffList, from, to) {
		const { Assignment } = Business.app.models,
			startDate = from ? new Date(from) : new Date(),
			endDate = to ? new Date(to) : new Date()

		if (!Array.isArray(staffList)) staffList = [ staffList ]

		const filter = {
			where: {
				staffId: { inq: staffList },
				start: { lte: startDate },
				or: [
					{ end: { gte: endDate } },
					{ end: { eq: null } },
					{ end: { exists: false } }
				],
			},
			include: [
				{
					relation: 'staff',
					scope: {
						fields: [ 'title', 'personId' ],
						include: {
							relation: 'person',
							scope: { fields: [ 'fullName' ] },
						},
					},
				}, {
					relation: 'place',
					scope: { fields: [ 'name', 'permanentlyClosed' ] },
				},
			],
		}

		return Assignment.find(filter)
	}

	/**
	 * Get payment setting required by payment provider, eg. merchantId of sub-merchants
	 * @param	{String} storeId
	 * @return	{Object|void} payment setting
	*/
	Business.getStorePaymentSetting = async function(storeId) {
		if (!storeId) return

		const { Place } = Business.app.models,
			place = await Place.findById(storeId),
			settings = place?.getSettingsByName(PAYMENT)

		return settings
	}

	Business.prototype.getLogoUrl = async function (name = DEFAULT) {
		const { id } = this,
			{ LogoImage } = Business.app.models

		return LogoImage.getUrlByOwnerId(String(id), { name })
	}

	Business.prototype.getDashboardUrl = async function(dashboardId) {
		const { Dashboard } = Business.app.models,
			{ externalId: arn } = this.identityList.find(({ provider }) => provider === QUICKSIGHT) || {}

		if (!arn) {
			return this.rejectErr(NOT_AVAILABLE, { reason: 'arn not found' }, true)
		}

		try {
			const dashboard = await Dashboard.getInstance(dashboardId)
			return dashboard.url(arn)
		}
		catch (err) {
			appNotify('getDashboardUrl', { err })
		}
	}

	// -----  Operation Hooks  -----

	function isMenuBusiness(data = {}) {
		const { tags = {} } = data,
			{ system = [] } = tags

		return system.includes(MENU)
	}

	/**
	 * Build Menu event data
	 */
	Business.buildMenuEvent = async function (instance, placeId) {
		const { name, brand, locale = {}, urls = [] } = instance.toJSON(),
			{ currency, languages = [] } = locale,
			[ language = EN ] = languages,
			{ uri: signupUrl } = urls.find(u => u.kind === SIGNUP) ?? {},
			logoUrl = await instance.getLogoUrl()

		return { placeId, name, brand, currency, language, logoUrl, signupUrl }
	}

	// defaultSettingList: isMain business' default settingList
	Business.observe('before save', async ({ currentInstance, data, instance, isNewInstance, hookState }) => {
		const changes = instance || data,
			{ name, brand, locale, urls } = changes

		if (name || brand || locale || urls) {
			hookState.menuChanges = true
		}
	})

	Business.observe('after save', async ({ instance, isNewInstance, hookState }) => {
		const { Event, models } = Business.app,
			{ Place } = models,
			{ id } = instance,
			{ menuChanges } = hookState,
			filter = {
				where: { ownerId: String(id) }
			}

		if (menuChanges && isMenuBusiness(instance)) {
			const places = await Place.find(filter)

			for (const place of places) {
				const { id: placeId } = place,
					data = await Business.buildMenuEvent(instance, placeId)

				if (isNewInstance) {
					appEmit(Event.place.menu.created, data)
				}
				else {
					appEmit(Event.place.menu.updated, data)
				}
			}
		}
	})
}
