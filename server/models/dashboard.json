{"name": "Dashboard", "plural": "Dashboards", "base": "PersistedModel", "idInjection": true, "strict": true, "mixins": {"Multitenant": true, "Timestamp": true, "Common": true, "Errors": true, "Provider": true}, "properties": {"key": {"type": "String", "required": true}, "createdAt": {"type": "Date"}, "modifiedAt": {"type": "Date"}, "deletedAt": {"type": "Date"}}, "validations": [], "relations": {"business": {"type": "belongsTo", "model": "Business", "foreignKey": "businessId"}}, "acls": [], "indexes": {}, "scopes": {}, "methods": {}}