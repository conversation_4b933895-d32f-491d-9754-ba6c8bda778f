{"initial": {"compression": {}, "cors": {"params": {"origin": true, "credentials": true, "maxAge": 86400}}, "loopback#favicon": {}, "nocache": {}}, "session": {}, "auth": {}, "auth:after": {"./lib/common/middleware/multitenant": {"name": "multitenant", "paths": ["/api/Businesses", "/api/Staff", "/api/Assignments", "/api/Activities", "/api/Providers", "/api/StaffActivities", "/api/Provisions"], "params": {"tenant-code": "trap"}, "enabled": true}}, "parse:before": {"./lib/common/middleware/multitenant-ds": {"name": "multitenant-ds", "paths": ["/api/Businesses", "/api/Staff", "/api/Assignments", "/api/Activities", "/api/Providers", "/api/StaffActivities", "/api/Provisions"], "enabled": true}}, "parse": {}, "routes": {"loopback#rest": {"paths": ["${restApiRoot}"]}}, "files": {}, "final": {"loopback#urlNotFound": {}}, "final:after": {"./lib/common/middleware/error-handler": {"name": "error-handler", "paths": ["${restApiRoot}"], "enabled": true}, "strong-error-handler": {"params": {"debug": true, "log": true, "safeFields": ["code", "details"]}}}}