
const LOCALHOST = '127.0.0.1',
	{
		NODE_ENV,
		ENV_REGION,

		PERKD_SECRET_KEY = '',

		WATCHDOG_KEY_ID = '',
		WATCHDOG_SECRET = '',

		REDIS_HOST = LOCALHOST,
		REDIS_PORT = 6379,
		REDIS_USERNAME,
		REDIS_PASSWORD,

		REDIS_CLOUD_HOST = LOCALHOST,
		REDIS_CLOUD_PORT = 6379,
		REDIS_CLOUD_USERNAME,
		REDIS_CLOUD_PASSWORD,

		CONFIG_REDIS_HOST = REDIS_CLOUD_HOST,
		CONFIG_REDIS_PORT = REDIS_CLOUD_PORT,
		CONFIG_REDIS_USERNAME = REDIS_CLOUD_USERNAME,
		CONFIG_REDIS_PASSWORD = REDIS_CLOUD_PASSWORD,

		EVENTBUS_HOST = REDIS_HOST || (NODE_ENV ? 'eventbus' : LOCALHOST),
		EVENTBUS_PORT = REDIS_PORT,
		EVENTBUS_USERNAME = REDIS_USERNAME,
		EVENTBUS_PASSWORD = REDIS_PASSWORD,

		STATS_HOST = NODE_ENV ? 'mission' : LOCALHOST,
		STATS_PORT = 8125,
	} = process.env

module.exports = {
	service: { secretKey: PERKD_SECRET_KEY }, // TODO: rotate; other best practice?
	modules: {
		watchdog: {
			cloudWatch: {
				enabled: WATCHDOG_KEY_ID && WATCHDOG_SECRET,
				awsAccessKeyId: WATCHDOG_KEY_ID,
				awsSecretKey: WATCHDOG_SECRET,
				awsRegion: ENV_REGION,
			},
		}, // TODO: rotate
		eventbus: {
			host: EVENTBUS_HOST,
			port: EVENTBUS_PORT,
		},
		tenants: {
			host: CONFIG_REDIS_HOST,
			port: CONFIG_REDIS_PORT,
			username: CONFIG_REDIS_USERNAME,
			password: CONFIG_REDIS_PASSWORD,
		},
		checkin: {
			host: 'mission', // TODO: in use?
			port: 8000,
		},
		stats: {
			host: STATS_HOST,
			port: STATS_PORT,
		},
	},
}
