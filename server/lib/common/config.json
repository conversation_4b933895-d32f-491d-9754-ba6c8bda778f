{"service": {}, "modules": {"watchdog": {"enabled": true, "level": "warning", "console": {"enabled": false, "colorize": true}, "cloudWatch": {"enabled": true, "level": "info", "logGroupName": "", "logStreamName": "", "jsonMessage": true}, "memory": {"enabled": true, "interval": 30, "alert": 300, "echo": false, "log": false}}, "eventbus": {"enabled": false, "pub": {"enabled": true, "log": false}, "sub": {"enabled": true, "log": false}}, "checkin": {"enabled": false, "apiRoot": "/api", "timeout": 10000, "retryInterval": 30000}, "metrics": {"enabled": false, "push": false, "heartbeat": false, "prefix": ""}, "ometrics": {"options": {"definitions": "lib/common/Metrics.json", "maxListeners": 0, "aggregates": {"persist": true, "persistInterval": 10}, "timeseries": {"persist": true, "persistInterval": 10}}}, "behavior": {}, "activity": {"options": {"maxListeners": 0}}, "i18n": {"namespaces": [], "languages": ["en", "zh-Hans", "zh-Han<PERSON>", "zh-Hant-HK", "ko", "ja", "ms", "id"], "fallbacks": {"zh-Hant-HK": ["zh-Hant-HK", "zh-Han<PERSON>", "en"], "zh-Hant": ["zh-Han<PERSON>", "zh-Hant-HK", "zh-Hans", "en"], "zh-Hans": ["zh-Hans", "zh-Han<PERSON>", "zh-Hant-HK", "en"], "ms": ["ms", "id", "en"], "id": ["id", "ms", "en"], "ja": ["ja", "en"], "ko": ["ko", "en"], "default": ["en"]}}}}