/**
 *  @module global
 */

const path = require('node:path'),
	{ Context } = require('@perkd/multitenant-context')

global._ = require('lodash')

module.exports = function(app) {
	global.appModule = function(name) {
		return name ? app.Service[name] : app.Service
	}

	global.appPath = function(dirname) {
		return path.resolve(__dirname, '../..', dirname || '')
	}

	global.appSettings = function(name = 'settings', path) {
		return require('./config-loader').loadAppSettings(path || global.appPath() + '/config', name)
	}

	global.appRequire = function(filePath) {
		return require(global.appPath(filePath))
	}

	global.appEcho = function(...param) {
		app.echo(...param)
	}

	global.appMetric = function(metric, value, options = {}) {
		const Metric = appModule('metrics'),
			{ prefix, child, tags = {}, error } = options,
			{ tenant } = Context

		process.nextTick(() => Metric.sendMetric({ prefix, metric, child, value, tags }, error, tenant))
	}

	global.appLog = async function(message, details, level = 'info', logId) {
		// process.nextTick(() => app.emit('watchdog.log', [message, details, level, logId]));
		// return logId;
		await app.Service.watchdog.log(message, details, level, logId)
	}

	global.appDebug = function(message, data, type, to, stack) {
		if (isProduction()) return null
		console.log('[DEBUG] %s - %j', message, data)
		// return app.Service.watchdog.notify(message, data, type, to, stack); // invoke directly to trace stack
	}

	global.appNotify = async function(message, data, type, to, stack) {
		await app.Service.watchdog.notify(message, data, type, to, stack) // invoke directly to trace stack
	}

	global.appEmit = function(name, ...objs) {
		const evtData = {}

		if (!name) console.error('[appEmit] missing event name', objs)

		objs.forEach(obj => Object.assign(evtData, obj.toJSON ? obj.toJSON() : obj))
		app.emit(name, evtData)
	}

	global.appOn = function(name, func) {
		if (name) {
			app.on(name, func)
		}
		else {
			global.appNotify('appOn_missing_event_name', null, 'error', null, true)
		}
	}

	global.appEnv = function() {
		return app.get('env')
	}

	global.isProduction = function() {
		return process.env.NODE_ENV === 'production'
	}
}
