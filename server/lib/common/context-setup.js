/**
 * @module Context Setup
 * Bootstrap script to ensure proper context setup at application startup
 */

const debug = require('debug')('multitenant:setup')

// Apply strict mode early
process.env.CONTEXT_MODE = 'strict'

// Print startup banner for context mode
debug('===== CONTEXT MODE: STRICT =====')
debug('Strict mode enabled to prevent context mixing in parallel requests')
debug('Hybrid/domain mode is completely disabled')

// Export a setup function for application startup
module.exports = function setupContext(app) {
    // Log the context mode at startup
    app.on('started', () => {
        debug('Application started with STRICT context mode')
    })
    
    // Add a health check route to verify context mode
    app.get('/health/context-mode', (req, res) => {
        res.json({
            contextMode: process.env.CONTEXT_MODE,
            isStrict: process.env.CONTEXT_MODE === 'strict',
            time: new Date().toISOString()
        })
    })
    
    return app
} 