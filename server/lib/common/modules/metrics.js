/**
 *  @module Metrics
 * 		- send to Prometheus (https://www.npmjs.com/package/hot-shots)
 * 		- push to clients over WebSockets
 *
 * ENVIRONMENT
 * 	-- metrics-push:
 * 	PROMETHEUS_HOST
 *	PROMETHEUS_PORT
 *	PROMETHEUS_USERNAME
 *	PROMETHEUS_PASSWORD
 *	WS_PORT
 */
const { Metrics } = require('@perkd/metrics'),
	{ MetricsPush } = require('@perkd/metrics-push')

class MetricsModule {
	/**
	 * @param	{Object} app - emitter
	 * @param	{Object} settings
	 * 			{Boolean} enabled
	 * 			{String} prefix - of metrics (name)
	 * 			{<PERSON>olean} push - enable push
	 * 			{<PERSON>olean} http - allow connect with http => inject http server
	 * 			{Object} heartbeat - { interval }
	 * 			{Object} credentials - for prometheus (should use ENV)
	 * 			{Object} options - { prometheus, websockets } (see metrics-push)
	 */
	constructor(app, settings) {
		const { prefix, enabled } = settings

		this.app = app
		this.metrics = new Metrics(prefix)
		this.settings = settings
		this.service = app.service
		this.enabled = enabled
		this._metric = { prefix }		// extend Metrics according to 'stats.metrics' in App's config
		this.heartbeatInterval = null   // Store the interval ID for cleanup

		this.app.Metric = this.metrics.registry	// complete list of metrics
	}

	async init() {
		const { enabled, settings } = this,
			{ heartbeat } = settings

		if (enabled) {
			if (heartbeat) {
				const { interval = 1000 } = heartbeat
				let x = true

				// Store the interval ID for cleanup during termination
				this.heartbeatInterval = setInterval(() => this.heartbeat(x = !x), interval)
			}
		}
	}

	async start() {
		const { enabled, settings, app, metrics } = this,
			{ push, http, credentials, options } = settings,
			server = http === true ? app.server : undefined,
			{ eventbus } = app.Service,
			pushOptions = { ...options, server, eventbus }

		if (!enabled) return

		if (push) {
			this.metricsPush = new MetricsPush(credentials, pushOptions, metrics)
			console.info(`[metrics] websocket port: ${this.metricsPush.port} ${http ? '(http)' : '(ws)'}`)
		}
	}

	async pause() {
		const { metricsPush } = this

		if (metricsPush) {
			metricsPush.pause()
		}
	}

	async resume() {
		const { metricsPush } = this

		if (metricsPush) {
			metricsPush.resume()
		}
	}

	async terminate() {
		const { metrics, metricsPush } = this

		// Clear the heartbeat interval to prevent further metrics sending
		if (this.heartbeatInterval) {
			clearInterval(this.heartbeatInterval)
			this.heartbeatInterval = null
		}

		// Close metrics connections
		await Promise.all([
			metricsPush?.close(),
			metrics?.close()
		])
	}

	sendMetric(params, error, tenant) {
		const { metrics } = this,
			{ metric } = params

		if (metrics.isTenanted(metric)) {
			params.tags ||= {}
			params.tags.tenant = tenant		// inject tenant code
		}
		if (error) {
			error.at = new Date()
			params.tags ||= {}
			try {
				params.tags.error = JSON.stringify(error)
			}
			catch (err) {
				params.tags.error = error.message
			}
		}

		metrics.send(params)
	}

	heartbeat(beat) {
		const { metrics } = this,
			{ registry } = metrics

		metrics.send({
			metric: registry.heartbeat.alive,
			value: beat ? 1 : 0,
		})
	}
}

module.exports = exports = MetricsModule
