/**
 *  @module Behavior module
 */
const EventEmitter = require('node:events'),
	{ Settings } = require('@perkd/behaviors')

class BehaviorModule extends EventEmitter {
	/**
	 * @param	{Object} app - emitter
	 * @param	{Object} settings
	 * 			{<PERSON><PERSON><PERSON>} enabled
	 * 			{Object} presets - from config/behavior.json/presets
	 */
	constructor(app, settings) {
		const { options = {}, presets = {}, enabled } = settings

		super()
		this.app = app
		this.settings = settings
		this.enabled = enabled
		this.options = options
		this.presets = presets

		this.setMaxListeners(Infinity)
	}

	async ready() {
		const { presets } = this

		for (const model in presets) {
			for (const behavior in presets[model]) {
				this.handleBehavior(model, behavior)
			}
		}
	}

	/***
	 * @param	{String} model name of model
	 * @param	{String} behavior name of behavior to listen & handle
	 */
	handleBehavior(model, behavior) {
		const { app, presets } = this,
			Model = app.models[model],
			{ events = [], rollbackEvents = [] } = Settings[behavior] ?? {},
			{ foreignKey = '' } = presets[model]?.[behavior] ?? {}

		for (const event of events) {
			this.on(event, handleEvent)		// handleEvent is (must be) a Closure!  (don't move out of this method)
		}

		for (const event of rollbackEvents) {
			this.on(event, handleEvent)
		}

		function handleEvent(e) {
			const { data } = e,
				id = data[foreignKey],
				preset = presets[model]?.[behavior]

			if (id) {
				Model.updateBehavior(id, behavior, preset, e)
					.catch(err => {
						console.log('[updateBehavior]', { err, data, preset })
					})
			}
		}
	}
}

module.exports = BehaviorModule
