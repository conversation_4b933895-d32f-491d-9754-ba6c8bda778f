/**
 *  @module Providers - module
 * 		- on init() load configured shared providers into memory
 */
const pLimit = require('p-limit'),
	assert = require('node:assert'),
	{ Settings, Providers: Provdr } = require('@crm/types'),
	{ Context } = require('@perkd/multitenant-context'),
	{ MESSAGE } = require('@perkd/settings')

const { PROVIDER } = Settings.Name,
	{ SHARED, TENANTED, INSTANCE } = Provdr.Type,
	MODULE = '[provider] '.blue,
	EVENT_HANDLER = 'handleProviderEvents',
	rateLimit = pLimit(500)	// concurrency

class Providers {

	constructor(app, settings) {
		this.app = app
		this.settings = settings
		this.service = app.service
		this.providers = {}
	}

	async init() {
		const { settings } = this,
			{ enabled, shared = [] } = settings

		if (!enabled) return

		// ----  Load Provider APIs (by service name) in memory  ----
		for (const providerName of shared) {
			await this.cacheProvider(providerName)
		}
	}

	async start() {
		const { app, settings } = this,
			{ Service, models } = app,
			{ SETTINGS } = models.Service,
			{ enabled, tenanted = {} } = settings,
			providerNames = Object.keys(tenanted),
			subscribed = {}

		if (!enabled) return

		// ----  Handle provider events  ----
		for (const name of providerNames) {
			for (const module of Object.keys(tenanted[name])) {
				if (tenanted[name][module].enabled === true) {
					const { events = [] } = tenanted[name][module]
					subscribed[name] ||= []
					subscribed[name].push(...events)
				}
			}
		}

		for (const [ name, events ] of Object.entries(subscribed)) {
			await this.handleEvents(name, events)
		}

		// only handle events sub/unsub when settings added, to avoid the wrong order of pub/sub events
		// to remove a tenant's "provider" settings, MUST set its value to false, instead of remove the "provider" settings
		SETTINGS.on(MESSAGE.setting.ADDED, async ({ code, setting = {} }) => {
			const { name: key, value: provider } = setting,
				listeners = Service.eventbus.subEventsListeners

			if (key !== PROVIDER) return

			for (const [ name, events ] of Object.entries(subscribed)) {
				const model = models[name] || {},
					handler = model[EVENT_HANDLER]

				for (const event of events) {
					const existingHandlers = listeners[event]?.[code] || [],
						handlerExists = existingHandlers.find(h => h.handler === handler)

					if (provider[name] && handlerExists) continue
					if (!provider[name] && !handlerExists) continue

					await provider[name] ? Service.eventbus.subscribe(event, code, handler)
						: Service.eventbus.unsubscribe(event, code, handler)
				}
			}
		})
	}

	// --- Public methods

	/**
	 * Get API by service (Shared first, fallback to Tenanted)
	 * @param	{string} service name
	 * @param	{string} [module]
	 * @param	{string} [shop] name - select shop-specific tenanted provider, fallback to first
	 * @return	{Promise<API | void>} provider api
	 */
	async getByService(service, module, shop) {
		const { providers, app } = this,
			{ Provider } = app.models

		if (providers[service]) return providers[service]

		for (const name in providers) {
			if (providers[name].supports(service)) return providers[name]
		}

		// fallback to Tenanted provider
		const list = await Provider.getByService(service),
			config = this.qualifyOne(list, module, shop)

		if (config) return this.getAPI(config, module, service)
	}

	/**
	 * Get API by Name of provider (Shared or Tenanted providers)
	 * @param	{String} name of provider
	 * @param	{String} [module] name
	 * @param	{string} [shop] name - select shop-specific tenanted provider, fallback to first
	 * @return	{Promise<API | void>} provider api
	 */
	async getByName(name, module, shop) {
		const { models } = this.app,
			{ SharedProvider, Provider } = models,
			type = this.typeOf(name)

		switch (type) {
		case SHARED: {
			if (this.providers[name]) return this.providers[name]

			assert(typeof SharedProvider !== 'undefined', '[Provider] SharedProvider model required in model-config')

			const provider = await SharedProvider.getByName(name)
			if (!provider) throw new Error(`Shared Provider '${name}' not found`)

			const { options } = provider,
				{ aggregates } = options,
				aggregated = {}

			// non-aggregated
			if (!aggregates) return this.getAPI(provider, module)

			// aggregator of multiple service providers
			for (const providerName of aggregates) {
				const api = await this.getByName(providerName)
				if (api) aggregated[providerName] = api
			}

			provider.options.aggregated = aggregated	// inject aggregated apis
			return this.getAPI(provider)
		}

		case TENANTED: {
			const list = await Provider.getByName(name),
				config = this.qualifyOne(list, module, shop)

			if (config) return this.getAPI(config, module)
			break
		}

		default:
			throw new Error(`Provider '${name}' not configured`)
		}
	}

	/**
	 * Get API for provider - load & init SDK
	 * @param {Object} config of provider - may be Shared, Tenanted OR Instance (payment)
	 * 		Shared/Tenanted - load by 'name' property
	 * 		Instance - load by 'provider' property. { type, provider, credentials, options } (rest NOT present)
	 * @param {String} [module] name
	 * @param {String} [service] name
	 * @return {Object} api
	 */
	async getAPI(config, module, service) {
		const { app } = this,
			{ Service, models } = app,
			{ metrics } = Service.metrics,
			{ provider, type, name, services = [], liveMode, enabled, modules, credentials = {}, options = {}, demo } = config,
			{ injectToken } = credentials

		if (!provider && !enabled) {	// instance provider always enabled
			console.log(MODULE + ` ${name}\t[disabled]`.red)
			return
		}

		const providerName = provider || name,
			mod = this.loadSdk(providerName),
			{ tenant } = Context

		if (mod instanceof Error) throw mod

		const { default: SDK } = mod,
			opts = {
				...options,
				liveMode,			// omitted by Instance providers (for now)
				modules,			// omitted by Instance providers (for now)
				tenant,
				demo				// true => skip env checks, ie. allow test credentials in live env & vice versa
			}

		service || (service = type || services[0])

		// fetch & inject access token if applicable
		if (injectToken) {
			const tenantProvider = await this.getByName(provider)
			await tenantProvider.injectToken(credentials)
		}

		const emitter = app,
			api = new SDK(service, credentials, opts, metrics, models, emitter)

		if (!module || api.enabled(module)) return api
	}

	/**
	 * Init and load provider api in memory
	 * @param {string} name of provider
	 */
	async cacheProvider(name) {
		const { providers } = this

		try {
			const api = await this.getByName(name)
			if (!api) return

			providers[name] = api

			if (api.aggregated) {
				console.log(MODULE + `${api.name}  (${Object.keys(api.aggregated).join(', ')})`.green)
			}
			else {
				console.log(MODULE + `${api.name}  (${api.supportedTypes.join(', ')})`.green)
			}
		}
		catch (err) {
			console.log(MODULE + `${name} - ${err.message || err}`.red)
		}
	}

	/**
	 * Reset in-memory Provider APIs (to force refresh of credentials & config)
	 * @param {String} [service] name, optional. if omitted, reset all providers
	 */
	async refreshProvider(service) {
		const { settings } = this,
			{ tenanted = {}, shared = [] } = settings

		if (service) {
			for (const name in tenanted) {
				if (tenanted[name].supports(service)) {
					await this.cacheProvider(name)
					return
				}
			}
			console.error('Provider/resetAPI', { svcName: service, err: { code: 'provider_not_found' } })
			throw new Error(`Provider for service '${service}' not found`)
		}

		for (const name of shared) {
			await this.cacheProvider(name)
		}
	}

	// -----  Private methods  ----

	/**
	 * Qualify ONE (tenanted) config
	 * @param	{Object[]} list of provider configs
	 * @param	{String} [module] name (lowercase)
	 * @param	{String} [shop] name
	 * @return	{Object} config
	 */
	qualifyOne(list = [], module, shop) {
		const hasModule = module
				? list.filter(t => t.modules.some(m => m.name.toLowerCase() === module && m.enabled))
				: list,
			[ first ] = hasModule,
			config = shop
				? hasModule.find(p => p.shop === shop) || hasModule.find(p => !p.shop)
				: first

		return config
	}

	/**
	 * Get type of provider
	 * @param	{String} name of provider
	 * @return	{String} type
	 */
	typeOf(name) {
		const { settings } = this,
			{ shared = [], tenanted = {} } = settings

		if (shared.includes(name)) return SHARED
		if (tenanted[name]) return TENANTED
		return INSTANCE
	}

	loadSdk(name) {
		try {
			const module = `@provider/${name}`,
				Sdk = require(module)

			return Sdk
		}
		catch (err) {
			console.error(`[loadSdk] name: ${name}`, err)
			return err
		}
	}

	/**
	 * Handle Provider webhook (events) defined in provider.json
	 * @param {string} name of provider
	 * @param {String[]} events subscribed
	 */
	async handleEvents(name, events = []) {
		const { app } = this,
			{ Service, models } = app,
			model = models[name] || {},
			handler = model[EVENT_HANDLER],
			tenants = app.allTenantCodes()

		assert(typeof model !== 'undefined', `[Provider] model-config missing ${name}`)

		if (!events.length) return

		assert(typeof handler === 'function', `[Provider] model ${name} '${EVENT_HANDLER}' method not config`)
		const subscribePromises = []
		for (const tenant of tenants) {
			const provider = app.getTenantSettings(tenant, PROVIDER) // only subscribe necessary events // TODO: to get the config one time
			if (provider[name]) {
				subscribePromises.push(rateLimit(() => Service.eventbus.batchSubscribe(events, tenant, handler)))
			}
		}
		await Promise.all(subscribePromises)
	}
}

module.exports = exports = Providers
