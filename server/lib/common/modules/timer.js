/**
 * @module Timer
 */

const { Context } = require('@perkd/multitenant-context'),
	{ parseTime } = require('@perkd/utils'),
	{ getBaseUrl } = require('@perkd/api-request')

const TRIGGER_SETUP_CONCURRENCY = 5

class Timer {
	constructor(app, settings) {
		this.app = app
		this.settings = settings
	}

	async start() {
		const { app, settings = {} } = this,
			{ timers = [] } = settings

		for (const timer of timers) {
			const { processor, tenants, excludeTenants = [] } = timer,
				{ model, method } = processor,
				{ [model]: Model } = app.models,
				tenantList = tenants || [ ...app.allTenantCodes() ],
				timerMethod = `${method}_timerCallback`

			// 1. inject timerCallback() to model, executed when trigger callback
			Model[timerMethod] = async function(body) {
				// if (app.service.state.now === Service.State.STARTED) // TODO: app.service.state.now not latest
				Model[method](...Object.values(body))
			}

			// 2. inject timer callback endpoint
			Model.remoteMethod(timerMethod, {
				description: 'Callback for timer module (reserved)',
				http: { path: '/timer', verb: 'post' },
				accepts: [ { arg: 'payload', type: 'object', http: { source: 'body' } } ],
				returns: { type: 'object', root: true },
			})

			// 3. setup timer triggers for each tenant
			for (const tenant of excludeTenants) {
				const index = tenantList.findIndex(t => t === tenant)
				if (index > -1) tenantList.splice(index, 1)
			}

			for (const tenant of tenantList) {
				Context.runAsTenant(tenant, () => {
					this.setUpTrigger(tenant, timer)
				}, app.connectionManager)
			}

			// TODO: 4. clean up triggers
		}
	}

	async setUpTrigger(tenant, timer) {
		const { app, settings = {} } = this,
			{ delay = 0 } = settings,
			{ config } = app.service,
			{ nextRun, trigger, processor, payload } = timer,
			{ model, method } = processor,
			{ Trigger, [model]: Model } = app.models,
			{ repeatInterval } = trigger,
			name = `${Model.name}.${method}-timer-reserved`,	// to manual update trigger.name in DB if changed
			url = `${getBaseUrl(config)}/${Model.pluralModelName}/timer`,
			when = new Date((nextRun ? parseTime(nextRun) : new Date()).getTime() + delay),
			callback = {
				method: 'post',
				url,
				payload,
				headers: { 'tenant-code': tenant },
			},
			filter = {
				where: { name: `[${tenant}] ${name}` },
			},
			data = { ...trigger, callback }

		try {
			const existing = await Trigger.findOne(filter)

			// TODO: Trigger service does not consider findOrCreate in operation hook
			// Trigger.findOrCreate(filter, data).then(trigger =>
			// 	trigger.updateAttributes(data).then(trigger =>
			// 		(trigger.active) ? Promise.resolve() : trigger.schedule(when)))

			if (existing) {
				const toUpdate = existing.nextRunAt.toString() !== when.toString()
					|| existing.repeatInterval !== repeatInterval

				if (toUpdate) {
					await existing.updateAttributes(data)
					await existing.schedule(when)
				}
			}
			else {
				const trigger = await Trigger.create({ ...data, name })
				await trigger.schedule(when)
			}
		}
		catch (err) {
			console.error(`[Timer] for '${tenant}'`, err.message)
		}
	}
}

module.exports = exports = Timer
