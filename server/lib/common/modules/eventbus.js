/**
 *  @module EventBus
 */
const pLimit = require('p-limit'),
	{ Bus, Utils } = require('@perkd/eventbus'),
	{ Context } = require('@perkd/multitenant-context'),
	{ merge, isEmptyObj, shortId, camelcase, findPath, cloneDeep } = require('@perkd/utils')

const { deepObjectValues, match } = Utils,
	NO_PUB_SUB = { subscribe: [], publish: [] },
	rateLimit = pLimit(500)	// concurrency

class EventBus extends Bus {
	/**
	 * @param	{Object} app - emitter
	 * @param	{Object} options
	 * 			{String} definitions - relative file path to event registry
	 * 			{Boolean} enabled
	 * 			{Object} pub - { enabled, log }
	 * 			{Object} sub - { enabled, log }
	 * 			{Object} tenant - events
	 * 				{String[]} subscribe - list of (remote) event name/pattern to listen from Eventbus
	 * 				{String[]} publish - list of (local) event name/pattern to be published onto Eventbus
	 * 			{Object[]} mapping - mapping of (subscribed) event name to alias (i.e. [{ from, to }])
	 * redis config, override defaults:
	 * 			{String} host
	 * 			{Number} port
	 * 			{Number} MAXLEN
	 * 			{Number} LIMIT
	 * 			{Object} limitConsumer { tokensPerInterval, interval }
	 * 			{Object} limitBus { tokensPerInterval, interval }
	 */
	constructor(app, options) {
		const { enabled, host, port, username, password, pub, sub, tenant = NO_PUB_SUB, mapping } = options,
			{ MAXLEN, LIMIT, limitConsumer, limitBus } = options,
			service = { name: app.service.name.toLowerCase() },
			settings = { service, tenant, mapping },
			config = {
				redis: { host, port, username, password }
			}

		if (MAXLEN !== undefined) config.MAXLEN = MAXLEN
		if (LIMIT !== undefined) config.LIMIT = LIMIT
		if (limitConsumer !== undefined) config.limitConsumer = limitConsumer
		if (limitBus !== undefined) config.limitBus = limitBus

		super(app, settings, config)

		this._settings = settings
		this._config = { enabled, pub, sub }

		this.registry = deepObjectValues(app.EventRegistry)
		this.multitenancy = app.service.multitenancy

		// ---  Globals: this.app, Events & Metrics
		this.app = app
		this.domain = camelcase(app.service.domain.trim()).toLowerCase()

		/*
		{
			"person.person.updated": {
				"mytenant": [{ "handler": "person.person.updated", "count": 1 }],
				"...": "..."
			},
			"sales.order.paid": {
				"mytenant": [{ "handler": [Metrics], "filter": null, "count", 1 }, { "handler": [Behavior], "filter": null, "count", 1 }],
				"...": "..."
			}
		}
		*/
		this.subEventsListeners = {}
	}

	/**
	 * Connect to EventBus then create Consumer
	 */
	async init() {
		const { app, _settings, _config } = this,
			{ enabled } = _config,
			subscribe = [ ..._settings.tenant.subscribe ],
			publish = this.getMatchedEventNames([ ..._settings.tenant.publish ]),
			events = [ ...subscribe, ...publish ],
			tenants = app.allTenantCodes()
		let eventRegistry = {}

		if (!enabled) return

		// Add to app.Event
		for (const event of events) {
			eventRegistry = merge(eventRegistry, findPath(app.EventRegistry, event))
		}
		this.app.Event = merge(this.app.Event, eventRegistry)

		await Bus.prototype.init.call(this, tenants)
	}

	/**
	 * Create Event Stream then enable sub & pub events
	 */
	async start() {
		const { app, tenants, _settings, _config } = this,
			{ service, Event } = app,
			{ tenant, mapping = [] } = _settings,
			{ enabled, pub = {}, sub = {} } = _config,
			{ subscribe = [], publish = [] } = tenant,
			toPublish = this.getMatchedEventNames(publish)

		if (!enabled) return

		const subMapped = subscribe.map(sub => mapping.find(map => map.from === sub)?.to ?? sub),
			setSub = new Set(subMapped),
			intersect = toPublish.filter(event => setSub.has(event))

		if (intersect.length > 0) {
			return Promise.reject(`[eventbus] Eventbus Start Failed. Events can't both be published and subscribed to. \n ${intersect}`)
		}

		if (pub.enabled) {
			for (const event of toPublish) {
				Bus.prototype.publish.call(this, event)
			}
		}
		else {
			appEcho('[eventbus] ⚠️ tenant events pubClient disabled')
		}

		if (sub.enabled) {
			const subscribes = []
			for (const tenantCode of tenants) {
				subscribes.push(rateLimit(() => this.batchSubscribe(subscribe, tenantCode)))
			}
			await Promise.all(subscribes)
		}
		else {
			appEcho('[eventbus] ⚠️ tenant events subClient disabled')
		}

		await Bus.prototype.start.call(this)
		app.emit(Event.eventbus.STARTED)
	}

	async terminate() {
		const { app } = this,
			{ Event } = app

		await this.end()
		app.emit(Event.eventbus.STOPPED)
	}

	/**
	 * Pub event onto EventBus
	 * @param	{String} name
	 * @param	{Object} body
	 * @param	{String} [tenantCode]
	 * @return	{Promise<String>} event id
	 */
	publish(name, body = {}, tenantCode) {
		const { app, _config, multitenancy } = this,
			{ service } = app,
			{ pub = {} } = _config,
			tenant = tenantCode || (multitenancy ? Context.tenant : service.tenantCode),
			data = { ...(typeof body.toJSON === 'function' ? body.toJSON() : body) },
			metadata = { timezone: Context.timezone, timestamp: Date.now() }

		if (pub.log) {
			console.log('[eventbus]', { type: 'pub', tenant, name, data, metadata })
		}
		return Bus.prototype.putEvent.call(this, name, data, tenant, metadata)
	}

	/**
	 * Subscribe to event(s) for a tenant
	 * @param	{String} name
	 * @param	{String} tenantCode
	 * @param	{Function} handler
	 * @return	{Promise<String|void>} subscribed event
	 */
	async subscribe(name, tenantCode, handler) {
		console.log('🆘🆘🆘 %s', name)
		if (!this.subEnabled()) return

		if (Array.isArray(name)) { // TODO: temp, to remove @wilson
			appNotify('eventbus_wrong_subscribe_param', name, 'error')
			return
		}

		this.subEventsListeners[name] = this.subEventsListeners[name] || {}

		const listeners = this.subEventsListeners[name][tenantCode] = this.subEventsListeners[name][tenantCode] || [],
			toSubscribe = listeners.length === 0

		let i = 0
		for (i = 0; i < listeners.length; i++) {
			if (listeners[i].handler === handler) {
				listeners[i].count++
				break
			}
		}

		if (i >= listeners.length) { // 1st subscription
			listeners.push({ handler, count: 1 })
		}

		return (toSubscribe) ? Bus.prototype.subscribe.call(this, name, tenantCode).then(() => name) : name
	}

	/**
	 * Subscribe to multiple events at once for a tenant
	 * @param	{String[]} eventNames - Array of event names to subscribe to
	 * @param	{String} tenantCode - Tenant code
	 * @param	{Function} handler - Optional event handler
	 * @return	{Promise<String[]>} subscribed events
	 */
	async batchSubscribe(eventNames, tenantCode, handler) {
		if (!this.subEnabled()) return []

		if (!Array.isArray(eventNames)) {
			appNotify('eventbus_wrong_batchSubscribe_param', eventNames, 'error')
			return []
		}

		// If no events to subscribe, return empty array
		if (eventNames.length === 0) return []

		// Setup listeners for each event
		const toSubscribe = []

		for (const name of eventNames) {
			this.subEventsListeners[name] = this.subEventsListeners[name] || {}

			const listeners = this.subEventsListeners[name][tenantCode] = this.subEventsListeners[name][tenantCode] || []
			let i = 0
			for (i = 0; i < listeners.length; i++) {
				if (listeners[i].handler === handler) {
					listeners[i].count++
					break
				}
			}

			if (i >= listeners.length) { // 1st subscription
				listeners.push({ handler, count: 1 })
			}

			// If this is the first listener for this event and tenant, mark for subscription
			if (listeners.length > 0 && !handler) {
				// When no handler is provided, we're just setting up the subscription
				// This is used during service startup to subscribe to all events
			}

			// Add to list of events to subscribe via batchSubscribe
			toSubscribe.push(name)
		}

		// Use the parent Bus batchSubscribe method to efficiently subscribe to all events at once
		if (toSubscribe.length > 0) {
			await Bus.prototype.batchSubscribe.call(this, toSubscribe, tenantCode)
		}

		return toSubscribe
	}

	async unsubscribe(name, tenantCode, handler) {
		this.subEventsListeners[name] = this.subEventsListeners[name] || {}

		if (!this.subEnabled()) return false

		const listeners = this.subEventsListeners[name][tenantCode] = this.subEventsListeners[name][tenantCode] || []

		for (let i = 0; i < listeners.length; i++) {
			const listener = listeners[i]

			if (listener.handler === handler && --listener.count <= 0) {
				listeners.splice(i, 1)
				break
			}
		}

		if (listeners.length === 0) {
			delete this.subEventsListeners[name][tenantCode]

			if (isEmptyObj(this.subEventsListeners[name])) {
				delete this.subEventsListeners[name]
				await Bus.prototype.unsubscribe.call(this, name, tenantCode)
			}
		}
		return false
	}

	async unsubAllTenants(eventName) {
		const { tenants } = this

		if (!this.subEnabled()) return

		// tenant events - run in parallel
		await Promise.all(
			Array.from(tenants).map(tenant =>
				this.unsubscribe(eventName, tenant)
			)
		)
	}

	async pause() {
		const { tenants } = this

		if (!this._config.enabled) return

		// tenant events - run in parallel
		await Promise.all(
			Array.from(tenants).map(tenant =>
				Bus.prototype.pause.call(this, tenant)
			)
		)
	}

	async resume() {
		const { tenants } = this

		if (!this._config.enabled) return

		// tenant events - run in parallel
		await Promise.all(
			Array.from(tenants).map(tenant =>
				Bus.prototype.resume.call(this, tenant)
			)
		)
	}

	createEvent(eventName, eventData, tenantCode) {
		const { app, multitenancy } = this,
			{ timezone } = Context,
			name = eventName.trim(),
			names = name.split('.'),
			domain = names.shift(),
			actor = names.shift(),
			action = names.join('.'),
			data = typeof eventData.toJSON === 'function' ? eventData.toJSON() : eventData

		return {
			id: shortId(),
			name,
			domain,
			actor,
			action,
			data,
			timezone: multitenancy ? timezone : 'Asia/Singapore',
			timestamp: Date.now(),
			tenantCode: tenantCode || (multitenancy ? Context.tenant : app.service.tenantCode)
		}
	}

	getSubscribeStatus(tenantCode, eventName) {
		const listeners = cloneDeep(this.subEventsListeners),
			subscribed = {}

		for (const evt of Object.keys(listeners)) {
			if (eventName && evt !== eventName) continue

			for (const tenant of Object.keys(listeners[evt])) {
				if (tenantCode && tenant !== tenantCode) continue
				subscribed[tenant] = subscribed[tenant] || {}
				subscribed[tenant][evt] = listeners[evt][tenant].length
			}
		}
		return subscribed
	}

	// -----  Private functions  -----

	getMatchedEventNames(events) {
		const { registry } = this

		return registry.reduce((list, name) => {
			const event = events.find(pattern => match(name, pattern))
			if (event) list.push(name)
			return list
		}, [])
	}

	// Emit event with emitter (app)
	async invokeListeners(name, data, tenant, metadata) {
		const { app, _config, subEventsListeners } = this,
			{ Event } = app.models,
			{ sub = {} } = _config,
			{ name: eventName, timezone } = metadata,
			subscribed = subEventsListeners[eventName] || subEventsListeners[name]

		if (!eventName || !subscribed) {
			console.log('invalid event', { name, data, tenant, metadata })
			return undefined
		}

		const listeners = subscribed[tenant] || [],
			event = buildv3Event(data, tenant, metadata) // backward compat for LB3

		return Context.runAsTenant(tenant, async () => {
			if (timezone) Context.timezone = timezone
			for (const listener of listeners) {
				try {
					if (typeof listener.handler === 'function') {
						Promise.resolve(listener.handler(event, tenant, metadata))
							.catch(err => handlerError(err, event, tenant))
					}
					else if (listener.handler && typeof listener.handler.emit === 'function') {
						Promise.resolve(listener.handler.emit(name, event, tenant, metadata))
							.catch(err => handlerError(err, event, tenant))
					}
					else {
						app.emit(name, event, tenant, metadata)
					}
				}
				catch (err) {
					handlerError(err, event, tenant)
				}
			}

			if (sub.log) {
				Event.create({ type: 'sub', tenant, name, data, metadata })
			}
		}, app.connectionManager)
	}

	pubEnabled() {
		return this._config.enabled && this._config.pub.enabled
	}

	subEnabled() {
		return this._config.enabled && this._config.sub.enabled
	}

	// ✅ PHASE 3: Enhanced Debugging Methods

	/**
	 * Get detailed subscription status for debugging
	 * @param {string} [tenantCode] - Optional tenant filter
	 * @param {string} [eventName] - Optional event filter
	 * @returns {Object} Detailed subscription information
	 */
	getDetailedSubscriptionStatus(tenantCode, eventName) {
		const { subEventsListeners } = this
		const status = {
			summary: {
				totalEvents: Object.keys(subEventsListeners).length,
				totalTenants: new Set(),
				totalSubscriptions: 0
			},
			events: {},
			tenants: {},
			errors: []
		}

		try {
			for (const [ event, tenantListeners ] of Object.entries(subEventsListeners)) {
				if (eventName && event !== eventName) continue

				status.events[event] = {
					tenantCount: Object.keys(tenantListeners).length,
					tenants: {}
				}

				for (const [ tenant, listeners ] of Object.entries(tenantListeners)) {
					if (tenantCode && tenant !== tenantCode) continue

					status.summary.totalTenants.add(tenant)
					status.summary.totalSubscriptions += listeners.length

					status.events[event].tenants[tenant] = {
						listenerCount: listeners.length,
						listeners: listeners.map(l => ({
							handlerType: typeof l.handler,
							handlerName: l.handler?.constructor?.name || 'Unknown',
							count: l.count
						}))
					}

					if (!status.tenants[tenant]) {
						status.tenants[tenant] = {
							eventCount: 0,
							events: []
						}
					}
					status.tenants[tenant].eventCount++
					status.tenants[tenant].events.push(event)
				}
			}

			status.summary.totalTenants = status.summary.totalTenants.size
		}
		catch (err) {
			status.errors.push({
				type: 'analysis_error',
				message: err.message,
				stack: err.stack
			})
		}

		return status
	}

	/**
	 * Get subscription health status
	 * @returns {Object} Health information
	 */
	getSubscriptionHealth() {
		const { _config, subEventsListeners } = this
		const health = {
			status: 'HEALTHY',
			timestamp: new Date().toISOString(),
			config: {
				enabled: _config.enabled,
				subEnabled: _config.sub.enabled,
				pubEnabled: _config.pub.enabled
			},
			metrics: {
				totalEvents: Object.keys(subEventsListeners).length,
				totalTenants: 0,
				totalSubscriptions: 0,
				averageSubscriptionsPerEvent: 0
			},
			issues: []
		}

		try {
			const tenants = new Set()
			let totalSubscriptions = 0

			for (const [ event, tenantListeners ] of Object.entries(subEventsListeners)) {
				for (const [ tenant, listeners ] of Object.entries(tenantListeners)) {
					tenants.add(tenant)
					totalSubscriptions += listeners.length

					// Check for potential issues
					if (listeners.length === 0) {
						health.issues.push({
							type: 'empty_listeners',
							event,
							tenant,
							message: 'Event has empty listeners array'
						})
					}

					if (listeners.length > 10) {
						health.issues.push({
							type: 'high_listener_count',
							event,
							tenant,
							count: listeners.length,
							message: 'Event has unusually high listener count'
						})
					}
				}
			}

			health.metrics.totalTenants = tenants.size
			health.metrics.totalSubscriptions = totalSubscriptions
			health.metrics.averageSubscriptionsPerEvent = health.metrics.totalEvents > 0
				? (totalSubscriptions / health.metrics.totalEvents).toFixed(2)
				: 0

			// Determine overall health status
			if (health.issues.length > 0) {
				health.status = health.issues.some(i => i.type === 'empty_listeners') ? 'DEGRADED' : 'WARNING'
			}

			if (!_config.enabled) {
				health.status = 'DISABLED'
				health.issues.push({
					type: 'disabled',
					message: 'EventBus is disabled'
				})
			}

		}
		catch (err) {
			health.status = 'ERROR'
			health.issues.push({
				type: 'health_check_error',
				message: err.message,
				stack: err.stack
			})
		}

		return health
	}

	/**
	 * Add batch unsubscribe method for cleanup
	 * @param {String[]} eventNames - Array of event names to unsubscribe from
	 * @param {String} tenantCode - Tenant code
	 * @param {Function} handler - Event handler
	 * @return {Promise<String[]>} unsubscribed events
	 */
	async batchUnsubscribe(eventNames, tenantCode, handler) {
		if (!this.subEnabled()) return []

		if (!Array.isArray(eventNames)) {
			appNotify('eventbus_wrong_batchUnsubscribe_param', eventNames, 'error')
			return []
		}

		const unsubscribed = []

		for (const name of eventNames) {
			try {
				const result = await this.unsubscribe(name, tenantCode, handler)
				if (result !== false) {
					unsubscribed.push(name)
				}
			}
			catch (err) {
				appNotify('eventbus_batchUnsubscribe_error', {
					event: name,
					tenant: tenantCode,
					error: err.message
				}, 'error')
			}
		}

		return unsubscribed
	}

	/**
	 * Get status for health checks
	 * @returns {Object} Status object
	 */
	getStatus() {
		const health = this.getSubscriptionHealth()
		return {
			status: health.status,
			enabled: this._config.enabled,
			subscriptions: health.metrics.totalSubscriptions,
			events: health.metrics.totalEvents,
			tenants: health.metrics.totalTenants,
			issues: health.issues.length
		}
	}
}

EventBus.prototype.putEvent = EventBus.prototype.publish

module.exports = EventBus

function buildv3Event(data, tenantCode, metadata) {
	const { id, domain, actor, action, name, timezone, timestamp } = metadata
	return { id, domain, actor, action, name, data, tenantCode, timezone, timestamp }
}

function handlerError(err, event, tenant) {
	console.error('[EventBus] Handler', {
		event,
		tenant,
		error: err.stack
	})
	// Consider adding metrics/appNotify here
}
