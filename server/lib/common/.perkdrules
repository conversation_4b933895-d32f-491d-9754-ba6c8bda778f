You are an expert in Node.js, Typescript and Loopback v3

# Tools
- use yarn, not npm

# Service Configurations
1. `server/config`
    - events published and subscribed to are configured in `eventbus.json`
    - enabled providers and subscribed provider events are in `providers.json`
2. `datasources.json`
    - datasources used by the service are configured here
3. `model-config.json` - model configurations
4. `middleware`
    - shared across services: `/server/lib/common/middleware`
    - service specific: `/server/middleware`
5. `component-config.json`
    - loopback components settings

# Service Modules
- found in `server/lib/common/modules`

# Models
- Schema & API endpoints definitions:
    - shared across services (partial definitions):
        - `/server/lib/common/models`
        - `/server/lib/perkd/models`
    - service specific (complete): `/server/models`
- Schema details: `README.md`
- API endpoints details: `README.md` & `/docs/API.md`
- Mixins extend model behaviors and endpoints:
    - shared across services:
        - `/server/lib/common/mixins`
        - `/server/lib/perkd/mixins`
    - service specific: `/server/mixins`

# Events
1. Use the Event Registry found in `@perkd/event-registry-perkd` package for event names
2. Use full event names in the Event Registry, code uses shortened names without domains mostly
3. Subscribed events:
    - service events found in `eventbus.json`
    - provider events found in `providers.json`
4. Exhaustive list of events published to be discovered in codebase
5. Event data structure needs to be disovered in codebase
6. Use the Event Bus to publish and subscribe to events

# Testing
1. Use the test suite found in `tests` folder
2. When fixing failed tests:
  - always analyse and understand the test cases thoroughly
  - understand the expected outcomes
  - understand the actual outcomes
  - be able to explain the root cause of the failures
  - reference past fixes, consider reverting previous fixes if they are not needed
  - before fixing them

# Documentation
1. All documentation is found in the `docs` folder, except for the README.md file
2. Package documentation:
  - all: always check README of the package in node_modules first
  - public: if necessary, additionally check using context7 mcp when available, then check online sources
3. Do not include Service Configuration in the documentation unless explicitly asked
4. Do not include common understanding of the application in the documentation unless asked

# Ignore files
*.env
*.pem
dist/
references/
