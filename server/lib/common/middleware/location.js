/**
 *  @module location	middleware for calls initiated by wallet app
 *
 *  Setup perkd-location in LB Context from request Header
 */

const { Context } = require('@perkd/multitenant-context'),
	{ location } = require('@perkd/wallet')

module.exports = function() {

	return function injectLocation(req, res, next) {
		const payload = location(req.headers)

		if (payload) {
			Context.location = payload
		}

		next()
	}
}
