/**
 *  @module Install	middleware for calls initiated by wallet app
 *
 *  Setup perkd-install in LB Context from request Header
 */

const { Context } = require('@perkd/multitenant-context'),
	{ languageOf } = require('@perkd/utils'),
	{ installation } = require('@perkd/wallet')

module.exports = function() {

	return function injectInstall(req, res, next) {
		const install = installation(req.headers),
			{ locale = {} } = install ?? {},
			language = languageOf(locale)

		if (install) {
			Context.installation = install
			Context.language = language
		}

		next()
	}
}
