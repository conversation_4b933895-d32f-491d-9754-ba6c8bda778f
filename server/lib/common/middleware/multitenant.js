/**
 *  @module Multitenant	middleware    (alternative to auth.js)
 *
 *  Setup tenant-code in LB Context from request Header or query params
 *
 *	Token could be from:
 *		queryString: access_token=jwt_token or tenant_code=demo
 *		header: {
 *			'authorization': 'Bearer JWT token'
 *			'x-access-token': 'JWT token',
 *			'tenant-code': 'demo',
 *		}
 */

const { LRUCache } = require('lru-cache'),
	debug = require('debug')('multitenant:multitenant-ds'),
	{ Apis } = require('@crm/types'),
	{ SHARED } = require('@crm/loopback'),
	{ Context } = require('@perkd/multitenant-context')

const { Headers, Parameters } = Apis,
	{ AUTHORIZATION, X_ACCESS_TOKEN, TENANT, USER, TIMEZONE } = Headers,
	{ ACCESS_TOKEN, TENANT: TENANTCODE } = Parameters,
	TOKENS_TTL = 1000 * 60 * 15,		// 15 minutes
	BEARER = 'Bearer '

// Cache for tenant tokens with TTL
const tokenCache = new LRUCache({
	max: 1000,
	ttl: TOKENS_TTL,
	// Add automatic pruning to avoid stale entries
	updateAgeOnGet: true,
	// Add TTL jitter to avoid cache stampedes
	ttlAutopurge: true
})

function parseAuthorization(string) {
	return string.replace(BEARER, '').replace(`${X_ACCESS_TOKEN} `, '') //TODO: compat for both Bearer and x-access-token, remove x-access-token after UI deploy
}

// Do not use runWithContext for direct context operations that don't involve async work
function getTenantAndSetContext(token, secretKey) {
	const cacheKey = `${token}:${secretKey}`
	let tenant = tokenCache.get(cacheKey)

	if (!tenant) {
		const result = Context.setWithToken(token, secretKey)

		if (!(result instanceof Error)) {
			const { tenant: tenantCode, user, app, timezone } = result

			tenant = { tenantCode, user, app, timezone }

			// Use a dedicated lock for this cache entry to make it thread-safe
			tokenCache.set(cacheKey, tenant)
		}
		else {
			debug('Token validation failed:', result.message)
		}
	}
	else {
		// Set context values when using cached tenant
		Context.accessToken = token
		const { tenantCode, user, timezone } = tenant
		// Don't wrap this in runWithContext to avoid context nesting issues
		Context.setValues(tenantCode, user, timezone)
	}
	return tenant
}

module.exports = function() {
	return async function injectTenant(req, res, next) {
		const { app, headers = {}, query = {} } = req,
			{ secretKey } = app.service
		// Create a fresh context for this request
		const requestContext = Context.createContext({})

		// Run the entire request handling in the isolated context
		try {
			await Context.runInContext(requestContext, async () => {
				if (!Context.getCurrentContext()) {
					const error = new Error('Failed to get context')
					appLog('multitenant/context', { err: { reason: 'failed to get context' } })
					appEcho('❌ Multitenant middleware: failed to get context')
					return next(error)
				}
				// Bypass tenant validation for shared datasource
				if (req.model?.dataSource?.name === SHARED) {
					return next()
				}

				// Handle token-based authentication
				if (headers[AUTHORIZATION] || headers[X_ACCESS_TOKEN] || query[ACCESS_TOKEN]) {
					const accessToken = headers[AUTHORIZATION]
						? parseAuthorization(headers[AUTHORIZATION])
						: (headers[X_ACCESS_TOKEN] || query[ACCESS_TOKEN])

					// Don't use runWithContext here as it may cause nesting issues
					const tenant = getTenantAndSetContext(accessToken, secretKey),
						userId = tenant?.user?.id || null

					if (!tenant) {
						const error = new Error('Invalid token')
						error.status = 401
						return next(error)
					}

					req.accessToken = { userId }
					return next()
				}

				// Handle direct tenant code
				if (headers[TENANT] || query[TENANTCODE]) {
					const tenantCode = headers[TENANT] || query[TENANTCODE],
						timeZone = headers[TIMEZONE] || Context.timezone

					let user = headers[USER] || Context.user || null

					if (typeof user === 'string') {
						try {
							user = JSON.parse(user)
						}
						catch (error) {
							appNotify('[multitenant]injectTenant', { error, user })
							user = undefined
						}
					}

					// Direct context access for setting values - avoid wrapping this in runWithContext
					Context.setValues(tenantCode, user, timeZone)
					req.accessToken = { userId: user?.id || null }
					return next()
				}

				// No tenant information provided
				const error = new Error('Tenant information required')
				error.statusCode = 401
				return next(error)
			})
		}
		catch (error) {
			error.statusCode = error.statusCode || 500
			next(error)
		}
	}
}
