/**
 *  @module Multitenant-ds middleware
 */

const debug = require('debug')('multitenant:multitenant-ds'),
	{ Context } = require('@perkd/multitenant-context')

const config = require('../tenant/config/tenant.config'),
	{ connection } = config,
	{ initTimeout, maxRetries } = connection

module.exports = function () {
	return async function ensureTenantConnection(req, res, next) {
		const { app } = req,
			{ connectionManager } = req.app

		if (!Context.tenant) {
			return next()
		}

		let retries = 0
		const waitForInitialization = async () => {
			if (!connectionManager) {
				throw new Error('Connection manager not available')
			}

			if (connectionManager.isInitialized) {
				return undefined
			}

			return new Promise((resolve, reject) => {
				const timeout = setTimeout(() => {
					cleanup()
					if (retries < maxRetries) {
						retries++
						debug(`Retrying initialization (attempt ${retries}/${maxRetries})`)
						waitForInitialization()
							.then(resolve)
							.catch(reject)
					}
					else {
						reject(new Error('Connection manager initialization timeout'))
					}
				}, initTimeout)

				const onReady = () => {
					cleanup()
					resolve()
				}

				const onFail = err => {
					cleanup()
					reject(err)
				}

				const cleanup = () => {
					clearTimeout(timeout)
					app.removeListener('connectionManagerReady', onReady)
					app.removeListener('connectionManagerFailed', onFail)
				}

				app.once('connectionManagerReady', onReady)
				app.once('connectionManagerFailed', onFail)
			})
		}

		try {
			// Ensure we're using the current tenant from context, not a stored variable
			const currentTenant = Context.tenant

			// Skip the runWithContext wrapper for this simple operation
			// to avoid any potential context nesting issues
			await waitForInitialization()

			// Always get the tenant directly from context at the time of access
			await connectionManager.ensureConnection(currentTenant)

			next()
		}
		catch (err) {
			debug('Error ensuring tenant connection:', err)
			next(err)
		}
	}
}
