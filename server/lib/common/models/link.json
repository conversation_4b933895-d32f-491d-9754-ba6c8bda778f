{"name": "Link", "plural": "Links", "description": "", "base": "Model", "idInjection": true, "strict": false, "mixins": {}, "options": {}, "properties": {"id": {"type": "String", "id": true, "defaultFn": "nanoid"}, "name": {"type": "String", "required": true, "max": 32}, "type": {"type": "string", "description": "enum {web, buy, about}"}, "label": {"type": "String"}, "auth": {"type": {"jwt": {"type": {"header": {"type": "object"}, "payload": {"type": "object"}, "secret": {"type": "string"}}}}}, "data": {"type": "any", "description": "Defines the data which is used to do authentication"}, "url": {"type": "any", "android": {"link": {"type": "string"}, "mode": {"type": "number", "default": 1, "description": "1 = embeded webview, 2 = openURL, 3 = intent"}}, "ios": {"link": {"type": "string"}, "mode": {"type": "number", "default": 1}}}, "failOverUrl": {"type": {"android": {"type": {"link": {"type": "string"}, "mode": {"type": "number", "default": 1}}}, "ios": {"type": {"link": {"type": "string"}, "mode": {"type": "number", "default": 1}}}}}}, "validations": [], "relations": {}, "acls": [], "indexes": {}, "scopes": {}, "methods": {}}