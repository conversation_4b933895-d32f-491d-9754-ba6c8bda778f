{"name": "FulfillPrice", "description": "", "idInjection": false, "strict": false, "options": {}, "properties": {"id": false, "lookup": {"type": "string", "description": "API for cost & ETA estimates"}, "zones": [{"type": {"id": {"type": "string", "description": "Of fulfillment provider (if required)"}, "name": {"type": "string"}, "price": {"type": "number", "description": "Price of delivery"}, "taxes": {"type": [{"type": {"title": {"type": "string", "description": "Name of tax"}, "rate": {"type": "number", "description": "Tax rate in fraction"}}}]}, "minTime": {"type": "number", "description": "Minimum fulfillment time (in minutes)"}, "maxTime": {"type": "number", "description": "Maximum fulfillment time (in minutes)"}, "qualifier": {"type": "string"}, "countryCode": {"type": "string", "description": "ISO 3166-1 alpha-2 country codes in uppercase"}, "states": [{"type": "string", "max": 16, "description": "States/province short code, eg. CA"}], "postCodes": [{"type": "string", "max": 16, "description": "Postal/zip codes"}]}, "description": "Pricing zones"}]}, "validations": [], "scopes": {}, "methods": []}