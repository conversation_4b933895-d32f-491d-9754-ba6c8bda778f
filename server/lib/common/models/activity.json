{"name": "Activity", "plural": "Activities", "base": "PersistedModel", "idInjection": true, "strict": false, "mixins": {"Common": true, "Timestamp": true, "DisableAllRemotes": {"find": true, "findOne": true}}, "options": {"validateUpsert": true}, "properties": {"name": {"type": "string", "max": 32, "description": "The verb (action) name of Activity"}, "actor": {"type": "object", "description": "Entity carrying out Activity"}, "instrument": {"type": "object", "description": "Object used in the completion of Activity"}, "location": {"type": {"type": "", "id": {"type": "string"}, "name": {"type": "string", "max": 80}, "longitude": {"type": "number"}, "latitude": {"type": "number"}}, "description": "Place of enagement"}, "attributedTo": {"type": {"type": "", "id": {"type": "string"}, "name": {"type": "string", "max": 80}}, "description": "Person, staff, campaign, system functions"}, "content": {"type": "string", "description": "The content or textual representation of the Activity encoded as a JSON string. By default, the value of content is HTML. (The `mediaType` property can be used in the activity to indicate a different content type)"}, "startTime": {"type": "date", "description": "Specifies the moment the activity began or is scheduled to begin."}, "endTime": {"type": "date", "description": "Specifies the moment the activity concluded or is expected to conclude."}, "occurredAt": {"type": "date", "description": "Time at which Activity occurred"}, "event": {"type": "string", "description": "The unique event id which generated this activity"}, "undo": {"type": "", "enabled": {"type": "boolean", "description": "If the activity can be undone."}, "handler": {"type": "string", "description": "The function name which is used to undo the activity."}}, "object": {"type": "object"}, "target": {"type": "object"}, "origin": {"type": "object", "description": "An indirect object of the activity from which Activity is directed"}, "result": {"type": "object"}, "audience": {"type": "object"}, "mediaType": {"description": "MIME media type of the value of the content property.", "type": "string"}, "image": {"type": "object"}, "custom": {"type": "object", "description": "To store custom params for the activity"}, "level": {"type": "number", "default": 1}, "visible": {"type": "boolean", "default": true}, "globalize": {"type": "Globalize", "default": {}}, "recordedAt": {"description": "The date and time at which the activity was recorded.", "type": "date"}, "createdAt": {"description": "The date and time at which the activity was created.", "type": "date"}, "modifiedAt": {"description": "The date and time at which the activity was updated.", "type": "date"}, "revokedAt": {"description": "The date and time at when thw activity was revoked.", "type": "date"}, "deletedAt": {"description": "The date and time at which the activity was deleted.", "type": "date"}}, "validations": [], "relations": {}, "acls": [], "indexes": {"ownerId_index": {"keys": {"ownerId": 1}}, "ownerId_name_index": {"keys": {"ownerId": 1, "name": 1}}, "occurredAt_index": {"keys": {"occurredAt": 1}}}, "scopes": {}, "methods": {"prototype.executeUndo": {"description": "Undo the activity", "http": {"path": "/undo", "verb": "post"}, "accepts": [{"arg": "options", "type": "object", "http": {"source": "body"}}], "returns": {"type": "object", "root": true}}}}