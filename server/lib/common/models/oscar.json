{"name": "<PERSON>", "plural": "<PERSON>", "base": "PersistedModel", "idInjection": false, "strict": false, "mixins": {}, "properties": {}, "validations": [], "relations": {}, "acls": [], "indexes": {}, "scopes": {}, "methods": {"clean": {"description": "Cleanse data", "http": {"path": "/clean", "verb": "post"}, "accepts": [{"arg": "person", "type": "object"}, {"arg": "options", "type": "object"}], "returns": {"type": "object", "root": true}}}}