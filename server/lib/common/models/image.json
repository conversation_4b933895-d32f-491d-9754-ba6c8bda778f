{"name": "Image", "plural": "Images", "base": "PersistedModel", "idInjection": true, "strict": false, "mixins": {"MultitenantRemote": true}, "options": {"validateUpsert": true}, "properties": {"id": {"type": "String", "id": true, "generated": true}, "createdAt": {"type": "Date"}, "modifiedAt": {"type": "Date"}}, "validations": [], "relations": {}, "acls": [], "indexes": {}, "scopes": {}, "methods": {"cluster": {"description": "Group images provided into visually identical clusters", "http": {"path": "/cluster", "verb": "post"}, "accepts": [{"arg": "images", "type": "array", "required": true}, {"arg": "existingClusters", "type": "array"}], "returns": {"type": "array", "root": true}}, "fetch": {"description": "Get image file from url, store in server-side (tenant) folder", "http": {"path": "/fetch", "verb": "post"}, "accepts": [{"arg": "url", "type": "string", "required": true}, {"arg": "options", "type": "object", "description": "makeUnique = true, to generate unique local file name"}], "returns": {"type": "object", "root": true}}, "getPHash": {"description": "Compute pHash of image file", "http": {"path": "/getPHash", "verb": "post"}, "accepts": [{"arg": "req", "type": "object", "http": {"source": "req"}}, {"arg": "res", "type": "object", "http": {"source": "res"}}, {"arg": "options", "type": "object", "description": "filename, imageUrl"}], "returns": {"type": "object", "root": true}}, "similarByPHash": {"description": "Shortlist similar pHash from list", "http": {"path": "/similarByPHash", "verb": "post"}, "accepts": [{"arg": "pHash", "type": "String", "required": true}, {"arg": "pHashList", "type": "array", "required": true}, {"arg": "options", "type": "object"}], "returns": {"type": "array", "root": true}}, "getInfo": {"description": "Get info of image file", "http": {"path": "/getInfo", "verb": "get"}, "accepts": [{"arg": "fileName", "type": "string", "required": true}], "returns": {"type": "object", "root": true}}, "download": {"description": "Download image file", "http": {"path": "/download", "verb": "post"}, "accepts": [{"arg": "bucket", "type": "string", "required": true}, {"arg": "key", "type": "string", "required": true}], "returns": {"type": "object", "root": true}}, "getUrlByOwnerId": {"description": "Get url of original image by ownerId", "http": {"path": "/url", "verb": "get"}, "accepts": [{"arg": "ownerId", "type": "string", "required": true}, {"arg": "where", "type": "object"}], "returns": {"type": "string", "root": true}}, "prototype.upload": {"description": "Upload image file & generate versions", "http": {"path": "/upload", "verb": "post"}, "accepts": [{"arg": "options", "type": "object", "description": "filename, imageUrl, cleanup"}, {"arg": "req", "type": "object", "http": {"source": "req"}}, {"arg": "res", "type": "object", "http": {"source": "res"}}], "returns": {"type": "object", "root": true}}, "prototype.clone": {"description": "Create a copy of image", "http": {"path": "/clone", "verb": "post"}, "accepts": [{"arg": "ownerId", "type": "any"}], "returns": {"type": "object", "root": true}}, "prototype.isSimilar": {"description": "Check if image is visually similar", "http": {"path": "/isS<PERSON><PERSON>r", "verb": "get"}, "accepts": [{"arg": "imageId", "type": "any", "required": true}, {"arg": "options", "type": "object"}], "returns": {"type": "object", "root": true}}}}