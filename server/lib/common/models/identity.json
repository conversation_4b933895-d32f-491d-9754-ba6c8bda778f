{"name": "Identity", "plural": "Identities", "base": "Model", "idInjection": true, "strict": false, "strictObjectIDCoercion": true, "options": {}, "mixins": {"Timestamp": true}, "properties": {"id": {"type": "String", "id": true, "defaultFn": "nanoid"}, "identity": {"type": "string"}, "provider": {"type": "string", "required": true, "comments": "national, perkd, facebook, google, twitter"}, "type": {"type": "string", "description": "Provider specific type/name"}, "externalId": {"type": "string", "required": true, "comments": "Provider specific id"}, "domain": {"type": "string", "description": "Provider specific domain, e.g. shopName for shopify"}, "country": {"type": "string", "length": 2, "description": "ISO 3166-1 alpha-2"}, "data": {"type": "Object", "description": "provider specific custom data"}, "authScheme": {"type": "string", "description": "oAuth, oAuth 2.0, OpenID, OpenID Connect"}, "credentials": {"type": "object"}, "valid": {"type": {"start": {"type": "Date"}, "end": {"type": "Date"}}}, "visible": {"type": "boolean", "default": true}, "createdAt": {"type": "Date"}, "modifiedAt": {"type": "Date"}}, "validations": [], "relations": {}, "acls": [], "indexes": {}, "scopes": {}, "methods": []}