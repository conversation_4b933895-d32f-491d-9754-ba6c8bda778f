{"name": "Task", "plural": "Tasks", "base": "PersistedModel", "idInjection": true, "strict": false, "mixins": {"MultitenantRemote": true}, "options": {}, "properties": {"id": {"type": "String", "id": true, "generated": true}}, "validations": [], "relations": {}, "acls": [], "indexes": {}, "scopes": {}, "methods": {"prototype.start": {"http": {"verb": "post"}, "accepts": [{"arg": "when", "type": "string"}, {"arg": "enforced", "type": "boolean"}], "returns": {"type": "object", "root": true}}, "prototype.pause": {"http": {"verb": "post"}, "accepts": [{"arg": "when", "type": "string"}, {"arg": "enforced", "type": "boolean"}], "returns": {"type": "object", "root": true}}, "prototype.resume": {"http": {"verb": "post"}, "accepts": [{"arg": "when", "type": "string"}, {"arg": "enforced", "type": "boolean"}], "returns": {"type": "object", "root": true}}, "prototype.stop": {"http": {"verb": "post"}, "accepts": [{"arg": "when", "type": "string"}, {"arg": "enforced", "type": "boolean"}], "returns": {"type": "object", "root": true}}}}