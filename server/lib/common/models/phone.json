{"name": "Phone", "plural": "Phones", "base": "Model", "idInjection": true, "strict": true, "mixins": {}, "options": {"validateUpsert": true}, "properties": {"id": {"type": "string", "id": true, "defaultFn": "nanoid"}, "fullNumber": {"type": "String", "required": true, "max": 15, "description": "see E.164 standard"}, "type": {"type": "string", "required": true, "max": 16, "description": "mobile, home, work, fax, others"}, "countryCode": {"type": "String", "min": 1, "max": 3}, "areaCode": {"type": "String", "max": 4}, "number": {"type": "String", "max": 14}, "regionCode": {"type": "String", "length": 2, "description": "ISO 3166-1 alpha-2"}, "lineType": {"type": "String", "max": 32}, "carrier": {"type": "String"}, "geo": {"type": "Geometry"}, "optIn": {"type": "Boolean"}, "valid": {"type": "boolean"}}, "validations": [], "relations": {}, "acls": [], "indexes": {}, "scopes": {}, "methods": []}