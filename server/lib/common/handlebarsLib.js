/**
 *  @module Handlebars library
 * 	formats supported:
 * 		simple:		{{person.givenName}}
 * 		formatted:	{{Date person.birthDate "DD MMM"}}
 * 					{{Image background width=888 height=88 format=jpg}}		TODO:
 * 		special:	{{Instance program.tier "<ID>"}}	TODO:
 *
 * 	references:	https://www.npmjs.com/package/mustache
 * 				https://github.com/janl/mustache.js/blob/master/mustache.js
 * 				http://numeral.handlebars.solidgoldpig.com/index.html
 */

const mustache = require('mustache'),
	numeral = require('numeral'),
	set = require('set-value'),
	get = require('get-value'),
	{ Languages } = require('@crm/types'),
	{ dayjs } = require('@perkd/utils'),
	{ DEFAULT_LANGUAGE } = Languages

require('numeral/locales')

const DATE = 'date',
	NUMBER = 'number',
	CURRENCY = 'currency',
	TYPES = [ DATE, NUMBER, CURRENCY ],
	EJS_TAGS = [ '<#-', '#>' ]

module.exports = exports = {
	parse,
	getFields,
	getProperties,
	format,
	render,
}
/**
 * Parses and the given `template`
 * returns the array of tokens
 * that is generated from the parse.
 */
function parse(template) {
	const tokens = mustache.parse(template)
	mustache.clearCache() // no cache
	return tokens
}

/**
 * Extract merge fields with (any) formatting definition (<field>: {} if no type/format defined)
 * @param	{Array} tokens list of tokens generated by Mustache.parse() - mutated, prefix removed from property
 * @param	{Object} alias maps shorthand to full field names, eg. getPerkd: 'perkd.downloadUrl'
 * @param	{String} prefix only process fields with this prefix (optional, for skipping runtime fields, eg. vue.js)
 * @return	{Object} { <property name>: { type: <data type>, format: <formatting string> } }
 */
function getFields(tokens, alias = {}, prefix = '') {
	return tokens.reduce((res, token, ndx) => {
		let [ symbol, value ] = token

		if (symbol === 'name') {
			if (prefix && !value.startsWith(prefix)) return res	// skip if not prefixed

			// remove prefix
			value = value.substring(prefix.length)
			tokens[ndx][1] = value

			if (alias[value]) value = alias[value] 	// replace alias with actual field name

			const params = value.match(/(["'].*?["']|[^"'\s]+)+(?=\s*|\s*$)/gi),
				[ type, property, format ] = params.length === 1 ? [ null, params[0], null ] : params

			res[property] = res[property] || {}
			if (TYPES.includes(type)) {
				if (!res[property].format) {
					res[property].type = type
					res[property].format = format ? format.replace(/^['"]|['"]$/gi, '') : null
				}
				token[1] = property	// strip (value) of type and format
			}
		} return res	// ignore all others
	}, {})
}

/**
 * Get models and properties of (merge) fields
 * @param	{Object} fields collection keyed by 'model.property'
 * @return	{Object} { <model>: [property] }
 */
function getProperties(fields) {
	const res = {}

	for (const name in fields) {
		const [ model, ...rest ] = name.split('.'),
			property = rest.join('.')
		if (model && property) {
			res[model] = res[model] || []
			res[model].push(property)
		}
	}
	return res
}

/**
 * Generate EJS template - converts handlerbars to EJS tags
 * @param	{Array} tokens list of tokens generated by Mustache.parse()
 * @param	{Object} fields collection of fields (name) to convert
 * @param	{Object} alias mapping of alias to field names
 * @param	{Array} tags (optional) custom EJS delimiters
 * @return	{String} EJS template (html)
 */
function render(tokens, fields = {}, alias = {}, tags = EJS_TAGS) {
	const skipValue = v => mustache.tags[0] + v + mustache.tags[1],
		propertyValue = v => tags[0] + v + tags[1]

	let buffer = ''
	for (let i = 0, numTokens = tokens.length; i < numTokens; ++i) {
		const token = tokens[i],
			[ symbol, value ] = token

		if (symbol !== 'name') buffer += value
		else {
			const field = alias[value] || value
			buffer += (!fields || fields[field]) ? propertyValue(field) : skipValue(field)
		}
	}
	return buffer
}

/**
 * Apply formatting (if any) to merge data
 * @param	{Object} data (mutated) personalize data (of a recipient)
 * @param	{String} formatting rules generated by getFormatting()
 * @param	{Object} locale of recipient
 * @return	{Object}
 */
function format(data, formatting, { timeZone, languages }) {
	const language = languages[0] || DEFAULT_LANGUAGE

	for (const property in formatting) {
		const { type, format } = formatting[property]		// TODO: default format?

		if (type) {
			const value = get(data, property)

			numeral.locale(numeral.locales[language] ? language : DEFAULT_LANGUAGE)

			let formatted = value
			if (type === DATE) {
				const parsedDate = dayjs(value)
				if (!parsedDate.isValid()) {
					console.warn(`Invalid date value for property ${property}: ${value}`)
					continue // Skip this property or handle it as needed
				}
				formatted = parsedDate.tz(timeZone).locale(language).format(format)
			}
			else if (type === NUMBER) formatted = numeral(value).format(format)
			else if (type === CURRENCY) formatted = numeral(value).format(format)

			set(data, property, formatted)
		}
	}
	return data
}
