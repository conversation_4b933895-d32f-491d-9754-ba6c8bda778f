//  Module Dependencies
const
	EventEmitter = require('events'),
	redis = require('redis'),
	{ Context } = require('@perkd/multitenant-context')

class EventBus extends EventEmitter {
	constructor(app, settings) {
		super()

		const self = this

		this.app = app
		this.settings = settings || {}
		const address = (settings && settings.address) || 'eventbus.perkd.intranet'
		this.Bus = redis.createClient(6379, address, {
			retry_strategy(options) {
				if (options.error && options.error.code === 'ECONNREFUSED') {
					// End reconnecting on a specific error and flush all commands with a individual error
					return new Error('The server refused the connection')
				}
				if (options.total_retry_time > 1000 * 60 * 60) {
					// End reconnecting after a specific timeout and flush all commands with a individual error
					return new Error('Retry time exhausted')
				}
				if (options.times_connected > 10) {
					console.log(options.times_connected)
					// End reconnecting with built in error
					return undefined
				}
				// reconnect after
				console.log('attempt: ', options.attempt, options.times_connected)

				return Math.min(options.attempt * 100, 3000)
			},
		})

		self.Bus.on('ready', () => {
			console.log('\n--- miniBus ready ---')

			if (self.settings.verbose) {
				self.Bus.on('pmessage', (pattern, channel, message) => {
					console.log('pmessage: ', JSON.parse(message))
				})

				self.Bus.on('message', (channel, message) => {
					console.log('message: ', JSON.parse(message))
				})
			}
			self.Bus.on('subscribe', pattern => {
				console.log('subscribed: ' + pattern)
			})

			self.Bus.on('psubscribe', pattern => {
				console.log('psubscribed: ' + (pattern === '*' ? 'all events' : pattern))
			})

			self.Bus.on('error', err => {
				console.log('eventBus error: ', err)
			})

			self.Bus.on('end', () => {
				console.log('eventBus ended')
			})
		})
	}

	subscribe(eventList, target) {
		const self = this

		self.Bus.on('pmessage', (pattern, channel, message) => {
			notify(target, JSON.parse(message))
		})
		self.Bus.on('message', (channel, message) => {
			notify(target, JSON.parse(message))
		})
		eventList.forEach(event => {
			self.Bus.psubscribe('*.' + event)
		})

		function notify(target, event) {
			Context.runAsTenant(event.tenantCode, err => {
				const data = {
					tenantCode: event.tenantCode,
					name: event.domain + '.' + event.actor + '.' + event.action,
					data: event.data,
					published: event.published,
				}
				target.emit(data.name, data)
			})
		}
	}
}

module.exports = exports = EventBus

