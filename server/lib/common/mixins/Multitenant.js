/**
 * @module Mixin:Multitenant
 * Handles multi-tenancy with dynamic MongoDB connection management
 */

const debug = require('debug')('multitenant'),
	debugModels = require('debug')('multitenant:models'),
	{ ConnectionManager, TRAP } = require('@crm/loopback'),
	{ Context } = require('@perkd/multitenant-context'),
	{ TenantIsolationError } = require('@perkd/errors')

const config = require('../tenant/config/tenant.config')

/**
 * Enhanced connection cleanup
 * @param {DataSource} connection - The datasource connection to clean up
 */
const cleanupConnection = async connection => {
	debug('Cleaning up connection')
	try {
		// Skip if this is the base trap connection
		if (connection && connection.name === 'TRAP') {
			debug('Skipping cleanup for base TRAP connection')
			return
		}

		// First, clean up MongoDB client
		const client = connection?.connector?.dataSource?.connector?.client
		if (client) {
			try {
				// Explicitly end all sessions before closing
				if (client.topology && client.topology.sessions) {
					const sessions = Array.from(client.topology.sessions.values())
					for (const session of sessions) {
						try {
							if (session && session.endSession) {
								await session.endSession()
							}
						}
						catch (sessionErr) {
							debug('Error ending session:', sessionErr)
						}
					}
				}

				// Close the client with force option
				await client.close(true)
				debug(`MongoDB client closed for connection ${connection.name}`)
			}
			catch (closeErr) {
				debug('Error closing MongoDB client:', closeErr)
			}

			// Explicitly null out topology references
			if (client.topology) {
				client.topology.s = null
				client.topology = null
			}
		}

		// Then, disconnect the connector
		if (connection?.connector?.disconnect) {
			try {
				await connection.connector.disconnect()
				debug(`Connector disconnected for ${connection.name}`)
			}
			catch (disconnectErr) {
				debug('Error disconnecting connector:', disconnectErr)
			}
		}

		if (connection.app && connection.name) {
			// Extract tenant ID from connection name (the name is the tenant ID)
			// We don't need to do anything with it here, but it's useful for debugging
			debug(`Cleaning up connection for tenant: ${connection.name}`)
		}

		// Finally, clean up event listeners and references
		if (connection && typeof connection.removeAllListeners === 'function') {
			connection.removeAllListeners()
			debug(`Removed all listeners from connection ${connection.name}`)
		}

		if (connection.connector) {
			if (connection.connector.dataSource) {
				connection.connector.dataSource = null
			}
		}

		debug(`Connection ${connection.name || 'unknown'} cleanup completed`)

		// Force garbage collection after significant cleanup
		if (global.gc) {
			global.gc()
			debug('Forced garbage collection after connection cleanup')
		}
	}
	catch (err) {
		debug('Error during connection cleanup:', err)
		// No need to return anything, this is an async function
	}
}

const validateConnection = async connection => {
	try {
		await connection.db.command({ ping: 1 })
		return true
	}
	catch (err) {
		debug('Connection validation failed:', err)
		return false
	}
}

const isTransactionSupported = () =>
	// Simply check if DB_SET environment variable is set, only replica set support transaction
	!!process.env.DB_SET

/**
 * Ensures that critical built-in models are always available in the registry
 * @param {Object} app - The application instance
 */
const ensureCriticalModelsExist = app => {
	if (!app || !app.registry || !app.registry.modelBuilder || !app.registry.modelBuilder.models) {
		debug('Cannot ensure critical models - app registry not available')
		return
	}

	// List of critical models that must be available
	const criticalModels = [
		'AccessToken', 'User', 'Role', 'RoleMapping', 'ACL'
	]

	// Check if each critical model exists
	let restoredCount = 0
	for (const modelName of criticalModels) {
		if (!app.registry.modelBuilder.models[modelName] && app.models[modelName]) {
			// Model exists in app.models but not in registry - restore it
			app.registry.modelBuilder.models[modelName] = app.models[modelName]
			restoredCount++
			debug(`Restored critical model ${modelName} to registry`)
		}
	}

	if (restoredCount > 0) {
		debug(`Ensured ${restoredCount} critical models are available in registry`)
	}
}

// Add setAppReference method to ConnectionManager.prototype if it doesn't exist
if (typeof ConnectionManager.prototype.setAppReference !== 'function') {
	ConnectionManager.prototype.setAppReference = function(app) {
		debug('Setting app reference for connection manager')
		this.app = app

		// We need to access the connections from the connection manager
		// Since getAllConnections might not be available, use getExistingConnection for each tenant
		if (this.tenants && Array.isArray(this.tenants)) {
			// If tenants array is available, use it to get connections for each tenant
			this.tenants.forEach(tenant => {
				const connection = this.getExistingConnection(tenant)
				if (connection) {
					connection.app = app
					debug(`Set app reference on connection for tenant ${tenant}`)
				}
			})
		}
		else if (this._connections || this.connections) {
			// If _connections or connections object is available, use it directly
			const connectionsObj = this._connections || this.connections || {}
			Object.values(connectionsObj).forEach(connection => {
				if (connection) {
					connection.app = app
					debug(`Set app reference on connection ${connection.name || 'unknown'}`)
				}
			})
		}
		else {
			debug('Unable to access connections directly from connection manager')
		}
	}
}

/**
 * @param {Model} Model - The model to be mixed in
 * @param {Object} mixinOptions - allowNonTenant: boolean
 */
module.exports = function(Model, mixinOptions = {}) {
	const originalDataSource = Model.dataSource

	// Override the instance getDataSource method to ensure it always uses current tenant context
	Model.prototype.getDataSource = function() {
		// Use the class-level method which properly checks tenant context
		// This ensures we always use the current tenant's datasource, not the one at instance creation time
		return this.constructor.getDataSource()
	}

	// Initialize connection manager if not exists
	Model.on('attached', async () => {
		const { app } = Model,
			{ maxPoolSize } = config.connection ?? {},
			ds = app.dataSources[TRAP],
			options = {
				validateConnection,
				cleanupConnection,
				connectionFactory: async tenant => {
					const { tenantModels } = app,
						settings = {
							...ds.settings,
							name: tenant,
							tenant,
							url: ds.settings.url.replace(`/${TRAP}?`, `/${tenant}?`),
							maxPoolSize,
							// Add connection lifecycle hooks
							onConnect: () => {
								debug(`Connected to tenant ${tenant}`)
							},
							onDisconnect: () => {
								debug(`Disconnected from tenant ${tenant}`)
							}
						},
						pool = app.registry.createDataSource(settings)

					// Set app reference for proper model cleanup
					pool.app = app

					// Ensure proper model attachment
					if (tenantModels) {
						for (const model of tenantModels) {
							pool.attach(model)
						}
					}

					// Wait for connection to be established
					await new Promise((resolve, reject) => {
						if (pool.connected) {
							resolve()
						}
						else {
							pool.once('connected', resolve)
							pool.once('error', reject)
						}
					})

					return pool
				}
			}

		// Track model for later attachment to tenant datasources
		if (!app.tenantModels) {
			app.tenantModels = new Set()
		}
		app.tenantModels.add(Model)

		// Initialize connection manager immediately
		if (!app.connectionManager) {
			app.connectionManager = new ConnectionManager(config, options)
			await app.connectionManager.initialize()

			// Ensure connections have reference to app for proper model cleanup
			app.connectionManager.setAppReference(app)

			// Add global cleanup capability
			app.cleanupAllConnections = () => cleanupAllConnections(app)

			// Ensure critical models are properly registered
			ensureCriticalModelsExist(app)

			debug('Connection manager initialized')
		}
	})

	/**
	 * Get datasource for the current tenant with retry logic
	 * @returns {DataSource}
	 * @throws {TenantIsolationError} When tenant isolation cannot be guaranteed
	 */
	Model.getDataSource = function() {
		const { app } = Model,
			{ connectionManager } = app,
			{ tenant } = Context

		if (!tenant) {
			return originalDataSource
		}

		if (!connectionManager) {
			throw new TenantIsolationError('Connection manager not initialized')
		}

		// Check initialization state from connection manager
		if (connectionManager.isInitializing) {
			debug('Waiting for connection manager initialization')
			throw new TenantIsolationError('Connection manager still initializing')
		}

		const { isError } = connectionManager
		if (isError) {
			debug('Connection manager failed:', isError)
			throw isError
		}

		// Try to get existing connection
		const pool = connectionManager.getExistingConnection(tenant)
		if (!pool) {
			debug('No existing connection for tenant:', tenant)
			throw new TenantIsolationError(`No connection available for tenant: ${tenant}`)
		}

		return pool
	}

	// Enhanced operation hooks with transaction support
	Model.observe('before save', async ({ options = {} }) => {
		const { connectionManager } = Model.app,
			{ tenant } = Context,
			{ mode } = config.session

		if (!tenant || options.session) return

		if (!connectionManager) {
			throw new TenantIsolationError('Connection manager not available')
		}

		const pool = await getConnectionPool()
		if (!pool.connector?.dataSource?.connector?.client) {
			debug('No MongoDB client available')
			// We need to modify the instance or data directly instead of returning
			return
		}

		// Check if transactions are supported based on DB_SET environment variable
		if (!isTransactionSupported() || mode === 'disabled') {
			debug('Transactions not supported - running in standalone mode')
			// We need to modify the instance or data directly instead of returning
			return
		}

		const client = pool.connector.dataSource.connector.client
		const session = client.startSession({
			defaultTransactionOptions: Context.get('txOptions')
		})
		options.session = session

		if (session) {
			await session.startTransaction()
		}

		// No need to return anything, we've modified options directly
	})

	Model.observe('after save', async ({ options = {} }) => {
		const { connectionManager } = Model.app,
			{ tenant } = Context,
			{ session, parentSession } = options

		if (!tenant || !session) return

		try {
			if (!parentSession && session && session.inTransaction()) {
				await session.commitTransaction()
			}
		}
		catch (err) {
			debug('Error ending session:', err)
			if (session && session.inTransaction()) {
				try {
					await session.abortTransaction()
				}
				catch (abortErr) {
					debug('Error aborting transaction:', abortErr)
				}
			}
			throw err
		}
		finally {
			try {
				await session.endSession()
			}
			catch (endErr) {
				debug('Error ending session:', endErr)
			}
			delete options.session
			connectionManager.recordCompletion(tenant)
		}
	})

	// Add transaction helper methods
	Model.withTransaction = async function(operation) {
		const { connectionManager } = Model.app,
			{ tenant } = Context

		if (!tenant) {
			if (!mixinOptions.allowNonTenant) {
				throw new TenantIsolationError('No tenant context for transaction')
			}
			return operation()
		}

		return connectionManager.withTransaction(tenant, operation)
	}

	Model.ensureConnection = async function() {
		const { connectionManager } = Model.app,
			{ tenant } = Context

		if (!tenant) {
			if (!mixinOptions.allowNonTenant) {
				throw new TenantIsolationError('No tenant context for connection')
			}
			return originalDataSource
		}

		return connectionManager.ensureConnection(tenant)
	}

	async function getConnectionPool() {
		const { tenant } = Context

		if (!tenant) {
			throw new TenantIsolationError('No tenant in context')
		}

		trackConnectionUsage()
		assertTenantContext()

		const { app } = Model
		if (!app.connectionManager) {
			throw new TenantIsolationError('Connection manager not initialized')
		}

		return app.connectionManager.ensureConnection(tenant)
	}

	function assertTenantContext() {
		if (process.env.CONTEXT_MODE === 'strict') {
			const { tenant } = Context
			if (!tenant) {
				throw new TenantIsolationError(
					'Strict context mode requires explicit tenant - '
					+ 'Add context middleware earlier in the chain'
				)
			}
		}
	}

	function trackConnectionUsage() {
		const ctx = Context.getCurrentContext()
		if (ctx.metrics) {	// FIXME
			ctx.metrics.connectionCount = (ctx.metrics.connectionCount || 0) + 1
		}
	}

	// Add global cleanup function for all connections
	const cleanupAllConnections = async app => {
		if (!app || !app.connectionManager) return

		try {
			// Get connections from the connection manager using available methods
			const connectionManager = app.connectionManager
			let connections = []

			// Try different ways to access connections
			if (typeof connectionManager.getAllConnections === 'function') {
				// If getAllConnections is available, use it
				connections = connectionManager.getAllConnections() || []
				debug(`Found ${connections.length} connections using getAllConnections`)
			}
			else if (connectionManager.tenants && Array.isArray(connectionManager.tenants)) {
				// If tenants array is available, get connections for each tenant
				for (const tenant of connectionManager.tenants) {
					const connection = connectionManager.getExistingConnection(tenant)
					if (connection) {
						connections.push(connection)
					}
				}
				debug(`Found ${connections.length} connections by iterating tenants`)
			}
			else if (connectionManager._connections || connectionManager.connections) {
				// If _connections or connections object is available, use it directly
				const connectionsObj = connectionManager._connections || connectionManager.connections || {}
				connections = Object.values(connectionsObj).filter(Boolean)
				debug(`Found ${connections.length} connections from internal connection store`)
			}
			else {
				debug('Unable to access connections from connection manager')
				return
			}

			let totalCleaned = 0

			for (const connection of connections) {
				if (connection && connection.name !== 'TRAP') {
					const cleaned = await cleanupConnection(connection)
					totalCleaned += cleaned || 0
				}
			}

			// Force garbage collection after global cleanup
			if (global.gc) {
				global.gc()
				debug('Forced garbage collection after global cleanup')
			}
		}
		catch (err) {
			debug('Error during global cleanup:', err)
		}
	}

	return Model
}
