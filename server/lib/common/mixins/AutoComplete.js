/**
 *  @module Mixin:AutoComplete of Address
 */
const { Providers, Settings, Languages } = require('@crm/types'),
	{ pickObj, completeAddress } = require('@perkd/utils')

const { GOOGLE } = Providers.PROVIDER,
	{ LOCALE } = Settings.Name,
	{ Code } = Languages

module.exports = function(Model) {

	/**
	 * AutoComplete addressList using formatted (address), to be used in 'before save' hook
	 * @param	{Object} ctx { Model: 'Place', instance: {}, currentInstance: undefined, data: undefined }
	 * @param	{Object} options { language: 'en', fields }
	 */
	Model.autoComplete = async function(ctx, options = {}) {
		const { app } = Model,
			{ Model: modelFrom, instance, data } = ctx,
			updates = instance || data,
			{ addressList = [], ownerId, locale, geo } = updates,
			defaultLocale = app.getSettings(LOCALE),
			{ languages = [] } = defaultLocale,
			{ language = languages[0] || Code.en, fields } = options,
			google = await modelFrom.getProvider(GOOGLE),
			// { concurrency = 5 } = google?.options || {},
			queries = []

		for (let i = 0; i < addressList.length; i++) {
			const address = addressList[i],
				{ street, city, country, formatted, short, geo } = address,
				incomplete = [ street, city, country, formatted, short, geo ].some(cond => cond === undefined)

			if (incomplete && formatted) {
				queries.push(
					google.maps.geocode(formatted, Code[language]).then(geocoding => {
						if (geocoding) {
							const completed = completeAddress(address, geocoding),
								result = fields ? pickObj(completed, fields) : completed

							Object.assign(addressList[i], result)
						}
					})
				)
			}
		}

		await Promise.all(queries)

		if (!geo && addressList[0]) {
			updates.geo = addressList[0].geo
		}
		if (!locale) {
			if (!ownerId) {
				updates.locale = defaultLocale || {}
			}
			else {
				const { Business } = app.models,
					business = await Business.findById(ownerId)

				updates.locale = business.locale || defaultLocale || {}
			}
		}
	}

	// -----  Operation Hooks  -----

	Model.observe('before save', async ctx => Model.autoComplete(ctx))
}
