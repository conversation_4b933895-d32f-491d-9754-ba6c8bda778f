/**
 *  @module Mixin:Upload2Local
 */
const { sep } = require('node:path'),
	{ rm } = require('node:fs/promises'),
	Zip = require('node-stream-zip'),
	{ Context } = require('@perkd/multitenant-context'),
	{ Forms } = require('@crm/types'),
	{ formData, FormFileFormat } = require('@perkd/forms'),
	{ TenantStorage } = require('@perkd/local-storage'),
	{ ensureDirectoryExists } = TenantStorage

const { FileType } = Forms,
	{ FILE } = FormFileFormat

module.exports = function(Model) {

	/**
	 * Decompress uploaded file, calls processor function
	 * @param {Req} req
	 * @param {Function} processor
	 * @return {Any} return value of processor
	 */
	Model.prototype.unzip2Local = async function(req, processor) {
		// Get the tenant and create a storage instance
		const storage = new TenantStorage(Context.tenant)

		// Get the upload directory path
		const uploadDir = this.tmpFilePath(storage)

		await ensureDirectoryExists(uploadDir)

		const { files } = await formData(req, FILE, { uploadDir }),
			{ file } = files[FileType.FILE],	// path to file
			zip = new Zip.async({ file })

		await zip.extract(null, uploadDir)

		const res = await processor(uploadDir)
		await rm(uploadDir, { recursive: true })
		return res
	}

	Model.prototype.tmpFilePath = function(storage, folderName, noSeparator) {
		const { id } = this,
			// Get the path to the tenant directory and append the ID
			fullPath = storage.getPath(String(id) + (folderName ? sep + folderName : ''), noSeparator)

		return fullPath
	}
}
