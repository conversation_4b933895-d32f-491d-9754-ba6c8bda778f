/**
 *  @module Mixin:Timeseries
 */
const { isEmptyObj, cloneDeep, flatten, ObjectId, TIMEZONE, dayjs } = require('@perkd/utils'),
	TimeSeries = appRequire('lib/common/modules/ometrics/timeseries')

module.exports = function(Model, options) {

	// -----  Static Methods  -----

	Model.getDbScale = function(scale, granularity) {
		return Object.keys(granularity).find(key => granularity[key].includes(scale))
	}

	if (!Model.getMetricValues) {
		Model.getMetricValues = async function(key, dimensions, dValues, startTs, endTs, scale, granularity, timezone) {
			const startTimeslots = TimeSeries.timeslots(startTs, timezone),
				endTimeslots = TimeSeries.timeslots(endTs, timezone),
				dbScale = Model.getDbScale(scale, granularity) || scale,
				initedValues = Model.initMetricValues(startTs, endTs, scale, granularity, timezone),
				allData = {},
				query = {
					key: { $regex: `^${key}` },
					dimensions: { $eq: dimensions },
					scale: _.findIndex(TimeSeries.SCALE, s => s === dbScale),
					startTS: { $in: Object.keys(initedValues).map(Number) },
				}

			if (dValues) {
				const dVal = dValues.map(d => (/^[0-9a-f]{24}$/.test(d) ? ObjectId(d) : d))
				query.dValues = { $eq: dVal }
			}

			const instances = await Model.query({ find: query }, { instance: true })

			for (const instance of instances) {
				const { key: k, startTS: sTS, dValues: dVal } = instance

				allData[k] = allData[k] || { dValues: dVal, values: cloneDeep(initedValues) }
				allData[k].values[sTS] = instance.getValues(scale)
					.slice(0, allData[k].values[sTS].length)		// get actual daysInMonth
			}

			if (isEmptyObj(allData)) {
				allData[key] = { dValues, values: cloneDeep(initedValues) }
			}

			const keys = Object.keys(allData),
				R = {}

			for (const k of keys) {
				allData[k].values[Math.floor(endTimeslots[dbScale].ts / 1000)].splice(endTimeslots[scale].idx + 1) // MUST DO first
				allData[k].values[Math.floor(startTimeslots[dbScale].ts / 1000)].splice(0, startTimeslots[scale].idx)

				R[k] = R[k] || { dValues: allData[k].dValues, values: [], value: 0 }
				Object.keys(allData[k].values).sort().forEach(TS => R[k].values = R[k].values.concat(allData[k].values[TS]))
				R[k].value = R[k].values.reduce((a, b) => a + b, 0)
			}

			return R
		}
	}

	Model.initMetricValues = function(startTs, endTs, scale, granularity, timezone) {
		timezone = timezone || TIMEZONE
		const dbScale = Model.getDbScale(scale, granularity) || scale,
			_startTS = Number(dayjs.tz(startTs, timezone).startOf(dbScale).unix()),
			_endTS = Number(dayjs.tz(endTs, timezone).startOf(dbScale).unix()),
			data = {}

		data[_startTS] = _initSeriesData(_startTS * 1000, timezone)

		let i = 1, _lastTS
		while (true) {
			const _nextTS = Number(dayjs.tz(startTs, timezone).add(i++, dbScale).startOf(dbScale).unix())
			if (_nextTS < _endTS) data[_nextTS] = _initSeriesData(_nextTS * 1000, timezone)
			else break

			if (_lastTS && (_nextTS < _lastTS)) break // prevent dead loop, _nextTS must be greater than _lastTS
			else {
				_lastTS = _nextTS
			}
		}
		data[_endTS] = _initSeriesData(_endTS * 1000, timezone)
		return data

		function _initSeriesData(timestamp, timezone) {
			return (dbScale === scale) ? [ 0 ] : _.values(TimeSeries.initSeriesData(dbScale, granularity, timestamp, timezone)[scale])
		}
	}

	// -----  Instance Methods  -----

	Model.prototype.getValues = function(scale) {
		const self = this
		if (TimeSeries.SCALE[self.scale] === scale) {
			return [ self.timeseries[scale] ]
		}
		return _.values(flatten(self.timeseries[scale] || {}))
	}

	// -----  Remote Methods  -----
}

/**
 * End of script
 */
