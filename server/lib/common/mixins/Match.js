/**
 *  @module Mixin:Match
 */

const objectPath = require('object-path'),
	{ isEmptyObj, similarity, cloneDeep, bm } = require('@perkd/utils')

module.exports = function(Model) {

	/**
	 * Match an host Object
	 * @param  {Object} profile
	 * @param  {Object} options
	 * @param  {Object} options.fields - fetch fields of instance
	 * @param  {Number} options.limit - fetch limit
	 * @param  {Array}  options.mandatory - mandatory fields in filter
	 * @param  {Number} options.matchThreshold
	 * @return {Array}	an array of match model instances
	 */
	Model.match = async function(profile = {}, options = {}) {
		const { limit } = options,
			conditions = genConditions(cloneDeep(profile), options),
			finds = conditions.map(condition => findInstances(condition, options)),
			result = await Promise.all(finds),
			instances = uniqueInstances(result),
			scores = calcScores(instances, profile, options),
			sortedInsIds = Object.keys(scores)
				.sort((k1, k2) => (scores[k2] - scores[k1])),
			ranked = []

		if (!sortedInsIds.length) return []

		let anIns, find = 0

		for (let j = 0; j < sortedInsIds.length; j++) {
			anIns = instances[sortedInsIds[j]]
			if (anIns) {
				anIns._score = scores[sortedInsIds[j]]
				ranked.push(anIns)
				find++
			}
			if (limit && find >= limit) break
		}

		return ranked
	}

	function calcScores(models, profile, options = {}) {
		const { matchThreshold = null } = options,
			scoreList = {}

		for (const model of models) {
			const score = model.matchScore(profile, matchThreshold)
			if (score > 0) {
				scoreList[model.id] = score
			}
		}

		return scoreList
	}

	/**
	 * @param {Object} profile
	 * @param {Object} options
	 * @return {Object} conditions - [{"and":[{"familyName":{"like":"doe.*","options":"i"}},{"givenName":{"like":"john.*","options":"i"}}],"phoneList.0.fullNumber":"6598765432"}]
	 */
	function genConditions(profile, options = {}) {
		const conditions = [],
			mandProps = options.mandatory || [],
			mandatoryConds = []

		for (const key in profile) {
			const map = propertyMap(key), aCondition = {}

			if (map.condition) {
				const part = {},
					bindPart = {}

				aCondition.and = []
				part[map.property] = map.fuzzy
					? { like: profile[key] + '.*', options: 'i' }
					: profile[key]

				// aCondition.and.push(part);
				if (map.bindProperty) {
					// means that property & bindProperty need to be bound together
					if (profile[map.bindProperty]) {
						bindPart[map.bindProperty] = map.fuzzy
							? { like: profile[map.bindProperty] + '.*', options: 'i' }
							: profile[map.bindProperty]

						delete profile[map.bindProperty]
						aCondition.and.push(part)
						aCondition.and.push(bindPart)
					}
				}
				else {
					aCondition.and.push(part)
				}
				aCondition.and.push(map.condition)
			}
			else if (!map.bindProperty) {
				aCondition[map.property] = map.fuzzy
					? { like: profile[key] + '.*', options: 'i' }
					: profile[key]
			}
			else {
				const part1 = {},
					bindPart1 = {}

				aCondition.and = []

				if (profile[map.bindProperty]) {
					part1[map.property] = map.fuzzy
						? { like: profile[key] + '.*', options: 'i' }
						: profile[key]

					bindPart1[map.bindProperty] = map.fuzzy
						? { like: profile[map.bindProperty] + '.*', options: 'i' }
						: profile[map.bindProperty]

					delete profile[map.bindProperty]
					aCondition.and.push(part1)
					aCondition.and.push(bindPart1)
				}
			}
			if (mandProps.indexOf(key) >= 0) mandatoryConds.push(aCondition)
			else conditions.push(aCondition)
		}
		if (mandatoryConds.length > 0) {
			for (let i = 0; i < conditions.length; i++) {
				const newCond = { and: [] }
				let aCond = conditions[i]
				if (!aCond.and) {
					aCond = { and: [ aCond ] }
				}
				newCond.and = aCond.and.concat(mandatoryConds)
				conditions[i] = newCond
			}

			// concat "and" operations
			const mUnion = {}
			mandatoryConds.forEach(mCond => {

				for (const i in mCond) {
					if (Array.isArray(mUnion[i])) Array.prototype.push.apply(mUnion[i], mCond[i])
					else mUnion[i] = mCond[i]
				}
			})
			conditions.push(mUnion)
		}
		return conditions
	}

	function propertyMap(field) {
		const { app } = Model,
			{ propertyMap: propMap } = app.service.settings.match[Model.name]

		return propMap[field] || { property: field }
	}

	function mandatoryFields() {
		return Model.app.service.settings.match[Model.name].mandatoryFields || {}
	}

	function limitControl() {
		return Model.app.service.settings.match[Model.name].limit || null
	}

	/**
	 * Compute match score for an Instance
	 * @param  {Object} profile        	contain fields and values to match
	 * @param  {Number} matchThreshold 	(optional) cut-off number of matches, ie. treat as NO match if below threshold
	 * @return {Number}                	match score
	 */
	Model.prototype.matchScore = function(profile, matchThreshold) {
		const model = this.toJSON(),
			{ app } = Model,
			matchConfig = app.service.settings.match[Model.name],
			{ similarityThreshold, propertyWeight } = matchConfig,
			fields = Object.keys(profile)

		let matched = 0,
			score = 0,
			similar = 0

		model.id = model.id.toString()

		for (const field of fields) {
			const { property: name } = propertyMap(field),
				// extract values of 'embedsMany', 'embedsOne' / nested object properties
				rootProp = name.split('.', 1)[0],
				directPath = name.split('.').find(key => !isNaN(key)), 	// directPath: phoneList.0.fullNumber
				properties = []

			if (Array.isArray(model[rootProp]) && !directPath) {		// embedsMany (array) property (limitation: only first-level)
				const path = name.slice(rootProp.length + 1)

				model[rootProp].forEach(prop => {
					properties.push(objectPath.get(prop, path))
				})
			}
			else properties.push(objectPath.get(model, name))			// plain/nested object property

			// compute score
			for (const property of properties) {
				if (similarityThreshold[name] !== undefined) {			// similarity scoring
					similar = similarity(profile[field], property)

					if (similar >= similarityThreshold[name]) {
						matched += propertyWeight[name] || 1
						score += similar * (propertyWeight[name] || 1)
					}
				}
				else if (profile[field] === property) {					// exact match scoring
					matched++
					score += 1
				}
			}
		}

		matchThreshold = matchThreshold || matchConfig.matchThreshold

		console.log('>> matchScore (%s): %s  (matched: %s)', model.id, score, matched)

		return matched >= matchThreshold ? score : 0
	}

	// --- Private functions

	async function findInstances(cond, options = {}) {
		const _start = bm.mark(),
			{ fields } = options,
			filter = {
				where: {
					and: [
						cond,
						{
							or: [
								{ deletedAt: null },
								{ deletedAt: { exists: false } }
							]
						}
					]
				}
			},
			limit = limitControl()

		if (typeof fields === 'object' && !isEmptyObj(fields)) {
			filter.fields = Object.assign(fields, mandatoryFields())
		}
		if (limit && limit > 0) {
			filter.limit = limit
		}

		const instances = await Model.find(filter).catch(() => [])

		console.log('>> Total %s records, lapsed %s ms, cond: %j', instances.length, bm.diff(_start), cond)
		return instances
	}

	function uniqueInstances(result = []) {		// result is an array of arrays of Instances
		const unique = {}

		for (const instances of result) {
			for (const instance of instances) {
				if (!unique[instance.id]) {
					unique[instance.id] = instance
				}
			}
		}

		return unique
	}

	// --- remote methods ---

	Model.remoteMethod('match', {
		description: 'Match existing instances by given profile & options',
		http: { path: '/match', verb: 'get' },
		accepts: [
			{ arg: 'profile', type: 'object', required: true },
			{ arg: 'options', type: 'object' },
		],
		returns: { type: 'object', root: true },
	})
}
