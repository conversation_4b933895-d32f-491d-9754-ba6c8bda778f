/**
 * @module Mixin:Identity
 */

const { Persons } = require('@crm/types'),
	{ EmbedLib } = appRequire('lib/common/embedLibrary')

const { USER } = Persons.Identities

class IdentityLib extends EmbedLib {
	// original handler function

	static _identityHandler(ident) {
		switch (ident.provider) {
		case 'google':
			return IdentityLib._google(ident)
		case 'facebook':
			return IdentityLib._facebook(ident)
		default:
			return ident
		}
	}

	static _google(ident) {
		return IdentityLib._loopback(ident)
	}

	static _facebook(ident) {
		let result = ''
		switch (ident.type) {
		// case 'link':
		// 	result = IdentityLib._loopback(ident);
		// 	break;
		case 'login':
			if (ident.data) { // from mobile
				const _ident = {
					provider: 'facebook',
					type: ident.type,
					externalId: ident.data.userID,
					authScheme: 'oAuth 2.0',
					data: ident.data,
					credentials: { accessToken: ident.accessToken },
				}
				result = _ident
			}
			else {
				result = IdentityLib._loopback(ident)
			}
			break
		default:
			result = IdentityLib._loopback(ident)
		}
		return result
	}

	static _loopback(ident = {}) {
		const { provider, type, externalId, authScheme, credentials, data, visible } = ident
		return { provider, type, externalId, authScheme, credentials, data, visible }
	}
}

module.exports = function(Model, options) {

	const identLib = new IdentityLib(Model, 'identities', options)
	identLib.setup()

	Model.findByIdentity = function(provider = '', type, externalId = '') { // TODO: change to findOneByIdentity - ZJ
		const filter = {
			where: { and: [ { 'identityList.provider': provider }, { 'identityList.externalId': externalId } ] },
			order: [ 'createdAt DESC' ],
		}

		if (type) {
			filter.where.and.push({ 'identityList.type': type })
		}
		return Model.find(filter)
	}

	Model.findOneByIdentity = function(provider = '', type, externalId = '') {
		const filter = {
			where: { 'identityList.identity': type ? `${provider}.${type}.${externalId}` : `${provider}.${externalId}` },
			// where: { and: [ { 'identityList.provider': provider }, { 'identityList.externalId': externalId } ] },
			order: [ 'createdAt DESC' ],
		}
		return Model.findOne(filter)
	}

	Model.findIdentity = async function(id, provider, type = USER, domain) {
		const instance = await Model.findById(id)
		return instance?.findIdentity(provider, type, domain)
	}

	Model.upsertIdentity = async function(id, provider, type = USER, externalId) {
		const instance = await Model.findById(id)
		await instance?.upsertIdentity(provider, type, externalId)
	}

	// -----  Instance Methods  -----

	Model.prototype._identity = function({ provider, type, externalId }) {
		const identity = keyFor(provider, type, externalId)
		return this.identities.value().find(ident => ident.identity === identity)
	}

	Model.prototype.findIdentity = function(provider, type = USER, domain) {
		const identity = this.identities.value().find(i => i.provider === provider && i.type === type && (!domain || i.domain === domain))
		return identity ? identity.externalId : ''
	}

	Model.prototype.upsertIdentity = function(provider, type, externalId) {
		const key = keyFor(provider, type),
			identity = keyFor(provider, type, externalId),
			NOW = new Date(),
			identityList = this.identities.value(),
			existing = identityList.find(i => i.identity.startsWith(key))

		if (existing) {
			existing.externalId = externalId
			existing.identity = identity
			existing.modifiedAt = NOW
		}
		else {
			identityList.push({ provider, type, externalId, identity, createdAt: NOW })
		}

		return this.updateAttributes({ identityList })
	}

	function keyFor(provider, type, externalId) {
		let key = provider
		if (type) key = `${key}.${type}`
		if (externalId) key = `${key}.${externalId}`
		return key
	}
}
