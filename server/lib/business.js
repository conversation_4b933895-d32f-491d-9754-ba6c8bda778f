/**
 * @module Service:Business
 */

const { Places } = require('@crm/types'),
	{ isFromMachine, machineIdFromSpot } = require('@perkd/machines'),
	Service = appRequire('lib/common/Service')

const { VENDING_MACHINE } = Places.Type,
	// Mapped events  (see mapping in eventbus.json)
	EVENT = {
		PAYMENT: {
			TRANSACTION: 'payment.transaction'
		},
		SALES: {
			FULILLMENT: 'sales.fulfillment'
		},
		APPLET: {
			SERVICE: 'applet.service'
		}
	}

class BusinessService extends Service {

	async _ready() {
		const { app } = this,
			{ Event, models } = app,
			{ Business, Staff, Assignment, Vending, Kiosk } = models

		// Handle Payment Event
		app.on(EVENT.PAYMENT.TRANSACTION, ({ data }) => Business.handlePaymentEvent(data))

		// Handle Staff Notify Events
		app.on(EVENT.SALES.FULILLMENT, event => Staff.handleFulfillmentNotify(event))
		app.on(EVENT.APPLET.SERVICE, event => Staff.handleServceNotify(event))
		app.on(Event.sales.order.manual.created, event => Staff.handleManualOrderNotify(event))

		// monitor shopify event receive (testing purpose)
		app.on(Event.shopify.shop.redact, data => {
			console.log('EVENT [shopify.shop.redact]: %j', { data })
		})

		// Emit Vending Machine payment events
		app.on(Event.pay.paid, data => {
			emitMachineEvent(Event.pay.machine.paid, data)
		})

		app.on(Event.pay.failed, data => {
			emitMachineEvent(Event.pay.machine.failed, data)
		})
		app.on(Event.pay.authorized, data => {
			emitMachineEvent(Event.pay.machine.authorized, data)
		})

		// FIXME:  below still required?

		// app.on(Event.place.place.closed, ({ data: place }) => {
		// 	const NOW = new Date(),
		// 		closeDate = place.dateList.find(date => date.name === 'contractEnd'),
		// 		contractEnd = closeDate && closeDate.date || NOW,
		// 		filter = {
		// 			where: { placeId: place.id, end: { gt: NOW } },
		// 		}

		// 	Assignment.find(filter).then(assignments => {
		// 		Promise.map(assignments, assign => assign.updateAttributes({ end: contractEnd }))
		// 	})
		// })

		async function emitMachineEvent(event, data) {
			if (!isFromMachine(data)) return

			try {
				const { acquired = {} } = data,
					{ location } = acquired,
					{ type } = location ?? {},
					machineId = machineIdFromSpot(location),
					Machine = (type === VENDING_MACHINE) ? Vending : Kiosk,		// model
					filter = {
						where: { machineId }
					},
					machine = await Machine.findOne(filter),
					{ provider } = machine ?? {}

				acquired.context ||= {}
				acquired.context.provider = provider
				data.acquired = acquired

				appNotify('[Machine]payment event', data)

				appEmit(event, data)
			}
			catch (err) {
				console.error(`[Machine]payment event - ${event}`, { err, data })
				appNotify(`[Machine]payment event - ${event}`, err, 'error')
			}
		}
	}
}

module.exports = BusinessService
