{"name": "Order", "plural": "Orders", "description": "CRM", "base": "PersistedModel", "idInjection": true, "strict": false, "mixins": {"MultitenantRemote": true}, "properties": {"id": {"type": "String", "id": true, "generated": true}, "when": {"type": {"received": {"type": "date", "description": "Time order received from Customer", "default": null}, "paid": {"type": "date", "description": "Time of payment", "default": null}, "allocated": {"type": "date", "description": "Time order allocated to driver", "default": null}, "packed": {"type": "date", "default": null, "description": "Time order packed & ready for collection/delivery"}, "collected": {"type": "date", "description": "Time order collected at store", "default": null}, "dispatched": {"type": "date", "default": null, "description": "Time order dispatched for delivery/shipped"}, "delivered": {"type": "date", "default": null, "description": "Time customer reeceived delivery"}, "accepted": {"type": "date", "description": "Time order accepted by store", "default": null}, "declined": {"type": "date", "description": "Time order declined by store", "default": null}, "fulfilled": {"type": "date", "description": "Time order was fulfilled by provider", "default": null}, "returned": {"type": "date", "default": null}, "cancelled": {"type": "date", "description": "Time order cancelled by customer", "default": null}}, "default": {}}}, "validations": [], "relations": {}, "acls": [], "indexes": {}, "scopes": {}, "methods": {"provisionProvider": {"description": "Provision Provider for business", "http": {"path": "/provision/provider", "verb": "post"}, "accepts": [{"arg": "businessId", "type": "string", "required": true}, {"arg": "config", "type": "object", "required": true, "description": "{ provider, currency, credentials, options }"}], "returns": {"type": "object", "root": true}}, "deprovisionProvider": {"description": "de-provision Provider for business", "http": {"path": "/provision/provider", "verb": "delete"}, "accepts": [{"arg": "businessId", "type": "string", "required": true}, {"arg": "provider", "type": "string", "required": true, "description": "key of provider"}], "returns": {"type": ["string"], "root": true}}, "doFind": {"description": "Find all instances of the model matched by filter from the data source.", "http": {"path": "/find", "verb": "post"}, "accepts": {"arg": "filter", "type": "object"}, "returns": {"type": "array", "root": true}}, "doFindOne": {"description": "Find first instance of the model matched by filter from the data source.", "http": {"path": "/findOne", "verb": "post"}, "accepts": {"arg": "filter", "type": "object"}, "returns": {"type": "object", "root": true}}, "doCount": {"description": "Count instances of the model matched by where from the data source.", "http": {"path": "/count", "verb": "post"}, "accepts": {"arg": "where", "type": "object"}, "returns": {"type": "number", "root": true}}, "toAccept": {"description": "Should order be accepted (considering fulfillment)", "http": {"path": "/accept", "verb": "get"}, "accepts": [{"arg": "order", "type": "object", "required": true}, {"arg": "fulfillments", "type": ["object"], "required": true}], "returns": {"type": "boolean", "root": true}}, "createOrder": {"description": "Create an Order", "http": {"path": "/create", "verb": "post"}, "accepts": [{"arg": "data", "type": "object", "required": true}, {"arg": "options", "type": "object"}], "returns": {"type": "Order", "root": true}}, "markPaid": {"description": "Mark an Order as Pa<PERSON>", "http": {"path": "/paid", "verb": "post"}, "accepts": [{"arg": "data", "type": "object", "required": true}], "returns": {"type": "Order", "root": true}}, "findByTransaction": {"description": "Get an Order for payment Transaction id (with status injected)", "http": {"path": "/byTransaction", "verb": "get"}, "accepts": [{"arg": "transactionId", "type": "string", "required": true}, {"arg": "sourceType", "type": "string", "description": "Business | Program | Variant"}], "returns": {"type": "Order", "root": true}}, "cancel": {"description": "Cancel an Order", "http": {"path": "/cancel", "verb": "post"}, "accepts": [{"arg": "id", "type": "any", "required": true}, {"arg": "options", "type": "object"}], "returns": {"type": "Order", "root": true}}, "cancelByTransaction": {"description": "Cancel Order associated with transaction id", "http": {"path": "/cancel/byTransaction", "verb": "post"}, "accepts": [{"arg": "transactionId", "type": "any", "required": true}, {"arg": "options", "type": "object"}], "returns": {"type": "Order", "root": true}}, "cancelByProviderOrderId": {"description": "Cancel Order by (external) order id of provider", "http": {"path": "/cancel/provider/orderId", "verb": "post"}, "accepts": [{"arg": "provider", "type": "string", "required": true}, {"arg": "orderId", "type": "string", "required": true, "description": "external order id of provider"}, {"arg": "options", "type": "object"}], "returns": {"type": "Order", "root": true}}, "relocateTableFulfillments": {"description": "Relocate (partial) fulfillments of DINEIN orders from source tables to destination tables", "http": {"path": "/tables/relocate", "verb": "post"}, "accepts": [{"arg": "from", "type": [{"type": "object", "description": "{ type, name, placeId, resourceId, position[] }"}], "required": true, "description": "List of source tables (Spot)"}, {"arg": "to", "type": [{"type": "object", "description": "{ type, name, placeId, resourceId, position[] }"}], "required": true, "description": "List of destination tables (Spot)"}], "returns": {"type": "object", "root": true}}, "storedValuesMetricsTransactions": {"description": "Stored Value metrics - transactions", "http": {"path": "/metrics/storedvalues/transactions", "verb": "get"}, "accepts": [{"arg": "from", "type": "date", "required": true}, {"arg": "to", "type": "date", "required": true}], "returns": {"type": "object", "root": true}}, "storedValuesMetricsHistory": {"description": "Stored Value metrics - history", "http": {"path": "/metrics/storedvalues/history", "verb": "get"}, "accepts": [{"arg": "from", "type": "date", "required": true}, {"arg": "to", "type": "date", "required": true}], "returns": {"type": ["object"], "root": true, "description": "[{ transactionId, type, transactionDate, balance, discount, expiryDate, currency, customerId, orderId }]"}}, "prototype.markPaid": {"description": "Mark order as Paid", "http": {"path": "/paid", "verb": "post"}, "accepts": [{"arg": "data", "type": "object"}, {"arg": "forced", "type": "boolean"}], "returns": {"type": "Order", "root": true}}, "prototype.requestFulfillment": {"description": "Request fulfillment of order", "http": {"path": "/fulfillments/request", "verb": "post"}, "accepts": [], "returns": {"type": ["Fulfillment"], "root": true}}, "prototype.cancel": {"description": "Cancel order", "http": {"path": "/cancel", "verb": "post"}, "accepts": [{"arg": "options", "type": "object"}], "returns": {"type": "Order", "root": true}}, "prototype.fulfilled": {"description": "Set status of ALL fulfillments of Order to fulfilled", "http": {"path": "/fulfillments/fulfilled", "verb": "post"}, "accepts": [{"arg": "fulfillment", "type": "object", "description": "immediate fulfill, mainly used for digital fulfillments"}], "returns": {"type": "Array", "root": true}}}}