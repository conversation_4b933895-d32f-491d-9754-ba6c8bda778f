{"name": "Provision", "plural": "Provisions", "base": "Model", "strict": true, "mixins": {"MultitenantRemote": true}, "properties": {}, "acls": [], "scopes": {}, "methods": {"action": {"description": "Preset Action", "http": {"path": "/action", "verb": "post"}, "accepts": [{"arg": "key", "type": "string", "required": true, "description": "ActionKey"}, {"arg": "data", "type": "object", "description": "Action data"}], "returns": {"type": "object", "root": true}}}}