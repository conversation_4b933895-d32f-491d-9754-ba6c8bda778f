{"name": "ProductImage", "plural": "ProductImages", "base": "Image", "idInjection": true, "strict": false, "options": {}, "mixins": {}, "properties": {"id": {"type": "String", "id": true, "generated": true}}, "hidden": ["container", "aws", "aws.ImageAcl", "aws.ImageExpires", "aws.ImageMaxAge"], "validations": [], "relations": {}, "acls": [], "indexes": {}, "scopes": {}, "methods": {"findByIds": {"http": {"path": "/findByIds", "verb": "get"}, "accepts": [{"arg": "ids", "type": "array", "required": true}, {"arg": "query", "type": "object"}, {"arg": "options", "type": "object"}], "returns": {"type": "array", "root": true}}, "getUrlsWithIds": {"description": "Get Original urls associated with id", "http": {"path": "/urls", "verb": "post"}, "accepts": [{"description": "Image ids", "arg": "ids", "type": ["string"], "required": true}], "returns": {"type": "object", "description": "{ [id]: url }", "root": true}}}}