{"name": "Booking", "plural": "Bookings", "base": "PersistedModel", "idInjection": true, "strict": true, "options": {"validateUpsert": true}, "mixins": {"MultitenantRemote": true}, "properties": {"id": {"type": "String", "id": true, "generated": true}, "kind": {"type": "string", "required": true, "description": "Inherit from Resource kind"}, "startTime": {"type": "date", "description": "Start time of booking"}, "endTime": {"type": "date", "description": "End time of booking"}, "quantity": {"type": "number"}, "capacity": {"type": "number", "description": "Number of person"}, "partySize": {"type": "number", "description": "Number of person of reservation"}, "digitalCard": {"type": {"id": {"type": "string", "description": "Card instance id"}}, "description": "Digital card information"}, "preferences": {"type": {"allowCombined": {"type": "boolean", "description": "Accept combined tables"}, "adjacentOnly": {"type": "boolean", "description": "Adjacent tables only"}}, "description": "Preferences for table booking"}, "note": {"type": "string", "max": 180, "description": "Customer instructions/comments"}, "status": {"type": "string", "enum": ["pending", "open", "success", "reserved", "ended", "cancelled", "noshow", "error"], "default": "pending"}, "deposit": {"type": "boolean", "description": "If deposit is required"}, "source": {"type": "string", "enum": ["queuing", "website"], "description": "Source of booking, omitted: in-app"}, "calendar": {"type": {"id": {"type": "string", "description": "Google calendarId"}, "eventId": {"type": "string", "description": "Google calendar eventId"}}, "description": "For Google Calendar"}, "reservationId": {"type": "string", "description": "Unique id shared across bookings under same reservation"}, "arrivedAt": {"type": "date", "description": "Time when customer arrived (check-in)"}, "departedAt": {"type": "date", "description": "Time when customer departed (check-out)"}, "expiresAt": {"type": "date", "description": "Time when pending booking will be cancelled automatically"}, "createdAt": {"type": "Date"}, "modifiedAt": {"type": "Date"}}, "validations": [], "relations": {"order": {"type": "belongsTo", "model": "Order", "foreignKey": "orderId"}, "product": {"type": "belongsTo", "model": "Product", "foreignKey": "productId", "description": "Product booked"}, "resource": {"type": "belongsTo", "model": "Resource", "foreignKey": "resourceId", "description": "Resource booked"}, "place": {"type": "belongsTo", "model": "Place", "foreignKey": "placeId", "description": "Place of resource booked"}, "person": {"type": "belongsTo", "model": "Person", "foreignKey": "personId"}, "membership": {"type": "belongsTo", "model": "Membership", "foreignKey": "membershipId"}, "tickets": {"type": "hasMany", "model": "Offer", "foreignKey": "bookingId"}}, "acls": [], "indexes": {}, "scopes": {}, "methods": {"request": {"description": "Secure use of a Resource (pending) for time period", "http": {"path": "/request", "verb": "post"}, "accepts": [{"arg": "products", "type": "array", "required": true, "description": "[ { id, resourceId } ]"}, {"arg": "from", "type": "Date", "required": true}, {"arg": "to", "type": "Date", "required": true}, {"arg": "quantity", "type": "Number", "required": true}, {"arg": "price", "type": "Number", "description": "Must match price"}, {"arg": "partySize", "type": "Number"}, {"arg": "preferences", "type": "object"}, {"arg": "customer", "type": "object", "description": "{ personId, membershipId, cardId }"}, {"arg": "options", "type": "object", "description": "note, ttl, deposit, source"}], "returns": {"type": "array", "root": true}}, "confirm": {"description": "Confirm (pending) booking", "http": {"path": "/confirm", "verb": "post"}, "accepts": [{"arg": "id", "type": "string", "required": true, "description": "Booking id"}, {"arg": "orderId", "type": "string"}, {"arg": "customer", "type": "object", "description": "{ personId, membershipId, cardId }"}], "returns": {"type": "Booking", "root": true}}, "requestAndConfirm": {"description": "Request and confirm bookings", "http": {"path": "/request/confirm", "verb": "post"}, "accepts": [{"arg": "products", "type": "array", "required": true, "description": "[ { id, resourceId } ]"}, {"arg": "from", "type": "Date", "required": true}, {"arg": "to", "type": "Date", "required": true}, {"arg": "quantity", "type": "Number", "required": true}, {"arg": "price", "type": "Number", "description": "Must match price"}, {"arg": "partySize", "type": "Number"}, {"arg": "preferences", "type": "object"}, {"arg": "customer", "type": "object", "description": "{ personId, membershipId, cardId }"}, {"arg": "options", "type": "object", "description": "note, ttl, deposit, source, orderId"}], "returns": {"type": ["Booking"], "root": true}}, "bookingsAndOrders": {"description": "Get active orders, current and future bookings for a resource", "http": {"path": "/request/confirm", "verb": "get"}, "accepts": [{"arg": "resourceId", "type": "string", "http": {"source": "query"}, "required": true}, {"arg": "timeZone", "type": "string", "http": {"source": "query"}, "required": true, "description": "Timezone of resource"}, {"arg": "endTime", "type": "date", "http": {"source": "query"}, "description": "Optional end time to limit future bookings"}], "returns": {"type": "object", "root": true}}, "cancel": {"description": "<PERSON><PERSON> (confirmed) booking", "http": {"path": "/cancel", "verb": "post"}, "accepts": [{"arg": "id", "type": "string", "required": true, "description": "Booking id"}], "returns": {"type": "object", "root": true}}, "occupied": {"description": "Get Occupied (busy) time slots within period for given resources", "http": {"path": "/occupied", "verb": "post"}, "accepts": [{"arg": "resourceIds", "type": "array", "required": true, "description": "List of resource ids"}, {"arg": "from", "type": "date", "required": true}, {"arg": "to", "type": "date", "required": true}, {"arg": "options", "type": "object", "description": "Additional options like includeOverstay"}], "returns": {"type": "array", "root": true}}, "forResources": {"description": "Get (confirmed) Booking for resources at a given time (now)", "http": {"path": "/resource", "verb": "get"}, "accepts": [{"arg": "resourceIds", "type": ["string"], "http": {"source": "query"}, "required": true}, {"arg": "at", "type": "date", "http": {"source": "query"}}, {"arg": "personId", "type": "string", "http": {"source": "query"}, "description": "Only for specific person"}], "returns": {"type": ["Booking"], "root": true}}, "pendingForMembership": {"description": "Get pending bookings for a membership", "http": {"path": "/pending/membership", "verb": "get"}, "accepts": [{"arg": "membershipId", "type": "string", "http": {"source": "query"}, "required": true}], "returns": {"type": ["Booking"], "root": true}}, "activeReservationOfResource": {"description": "Get active reservation (with bookings) for resource", "http": {"path": "/reservation/active", "verb": "get"}, "accepts": [{"arg": "resourceId", "type": "string", "http": {"source": "query"}, "required": true}], "returns": {"type": "object", "root": true, "description": "{ id, partySize, startTime, endTime, note, deposit, bookings[] }"}}}}