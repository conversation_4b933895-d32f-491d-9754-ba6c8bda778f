{"name": "Pending", "plural": "Pending", "description": "CRM", "base": "PersistedModel", "idInjection": true, "strict": true, "options": {}, "mixins": {"Multitenant": true, "Timestamp": true}, "properties": {"mode": {"type": "String", "required": true, "enum": ["clone", "invite", "transfer"]}, "modelName": {"type": "string", "required": true, "enum": ["offer", "reward", "message"]}, "masterId": {"type": "string", "required": true}, "cardId": {"type": "string", "required": true}, "sharerId": {"type": "string", "description": "Id of (Perkd) user who share originated"}, "sharerName": {"type": "string", "description": "Name of (Perkd) user who share originated"}, "sharerImageUrl": {"type": "string", "description": "Profile image url of (Perkd) user who share originated"}, "originId": {"type": "string", "description": "Id of (Perkd) instance from which share originated"}, "sharingId": {"type": "string", "description": "Id of (Perkd) sharing instance which share originated"}, "through": {"type": "TouchPoint", "description": "Source of request"}, "purgeTime": {"type": "date", "required": true, "description": "Time when instance will be auto-deleted"}, "createdAt": {"type": "date"}, "modifiedAt": {"type": "date"}}, "relations": {}, "acls": [], "indexes": {"masterId_cardId": {"keys": {"masterId": 1, "cardId": 1}}}, "scopes": {}, "methods": {}}