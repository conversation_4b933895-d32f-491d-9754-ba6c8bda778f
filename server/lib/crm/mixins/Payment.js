/**
 *  @module Mixin:Payment  (for Model, must be used together with Buy mixin)
 *
 *  Implement For Buy Mixin
 *
 * 		Payment handlers - 	paymentRequest()
 * 							paymentAuthorize()
 * 							paymentCapture()
 * 							paymentCancel()
 * 							paymentRefund()
 */
const { Payments } = require('@crm/types')

const { PAID } = Payments.Status

module.exports = function(Model) {

	Model.handlePaymentEvent = function(transaction) {
		this.orderCommit(transaction)
			.catch(err => {
				appNotify('[handlePaymentEvent]', { err, transaction })
			})
	}

	/**
	 * Request a Payment
	 * @param	{mOrder} mOrder
	 * @param	{Object} payment (mutated)
	 * @return	{Object} intent transaction
	 */
	Model.paymentRequest = async function(mOrder, payment) {
		const { Business } = Model.app.models,
			{ method, request, details, options } = mOrder.buildPaymentRequest(payment)

		try {
			const transaction = await Business.paymentRequest(method, request, details, options),
				{ details: det } = transaction,
				{ receipt } = det ?? {}

			if (receipt) {		// inject/override receipt (TW)
				Object.assign(mOrder.receipt, receipt)
			}

			return transaction
		}
		catch (err) {
			appNotify('paymentRequest', { err, mOrder, payment, method, request, options })
			throw err
		}
	}

	/**
	 * Authorize a Payment (ie. reserve funds)
	 * @param	{mOrder} mOrder (mini) order
	 * @param	{Object} payment (mutated)
	 * @return	{Object} intent from provider
	 */
	Model.paymentAuthorize = async function(mOrder, payment) {
		const { Business } = Model.app.models,
			{ method, request, details, options } = mOrder.buildPaymentRequest(payment)

		return Business.paymentAuthorize(method, request, details, options)
			.catch(err => {
				appNotify('paymentAuthorize', { err, mOrder, payment, method, request, options })
				throw err
			})
	}

	/**
 	 * Capture (authorized) payment (ie. deduct funds)
	 * @param	{Transaction} transaction
	 * 			{String} type
	 * 			{String} method - eg. card, storedvalue
	 *			{String} provider
	 *			{Number} amount
	 *			{String} currency
	 * 			{String} status
	 * 			{String} referenceId
	 *			{String} paymentId
	 *			{String} orderId
	 * 			{Object} details
	 * @return	{Transaction} transaction
	 */
	Model.paymentCapture = async function(transaction = {}) {
		const { Business } = Model.app.models,
			{ status } = transaction

		if (status === PAID) return transaction

		return Business.paymentCapture(transaction)
			.catch(err => {
				appNotify('paymentCapture', { err, transaction })
				throw err
			})
	}

	/**
     * Cancel payment intent from Perkd app
	 * @param	{Transaction} transaction
	 * 			{String} method
	 * 			{String} referenceId
	 * @param	{String} [reason]
	 * @return	{Intent} intent
	 */
	Model.paymentCancel = async function(transaction, reason) {
		const { Business } = Model.app.models

		return Business.paymentCancel(transaction, reason)
			.catch(err => {
				appNotify('paymentCancel', { err, transaction, reason })
				throw err
			})
	}

	/**
	 * Refund/release payment
	 * @param	{Transaction} transaction
	 * @param	{String} reason
	 * @return	{Intent}
	 */
	Model.paymentRefund = async function(transaction, reason) {
		const { Business } = Model.app.models

		return Business.paymentRefund(transaction, { reason })
			.catch(err => {
				appNotify('paymentRefund', { err, transaction, reason })
				throw err
			})
	}
}
