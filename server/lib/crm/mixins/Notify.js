/**
 *  @module Mixin:Notify - send push notifications to Perkd app
 */
const { Settings } = require('@crm/types'),
	i18n = appRequire('lib/common/i18nLib')

const { LOCALE } = Settings.Name,
	ENGLISH = 'en'

module.exports = function(Model) {

	/**
	 * Send Push Notification to membership card
	 * @param {Membership} membership
	 * @param {Object} template - { name, widget? }
	 * @param {Object} [personalize] data
	 * @param {Object} [options] - { image, payload, fetch: true | false }
	 */
	Model.appNotify = async function (membership, template, personalize = {}, options = {}) {
		const { app } = Model,
			{ Perkd } = appModule('perkd'),
			{ digitalCard } = membership ?? {},
			{ name, widget } = template ?? {},
			{ id } = digitalCard ?? {},
			{ image, fetch, ...rest } = options,
			{ payload: customizedPayload = {} } = rest

		if (!id || !name) return

		const cardId = String(id),
			{ languages = [] } = app.getSettings(LOCALE),
			[ language = ENGLISH ] = languages,
			content = i18n.translate(
				{ body: name },
				{ title: 'title' },
				{ globalize: { t: {}, default: language } },
				{ ...personalize }
			),
			payload = { ...customizedPayload, nav: { route: [ { card: { id } } ] }, options: { banner: true } },
			opt = { ...rest, payload, fetch: (fetch && widget) ? { widget } : fetch }

		if (image) {
			for (const l of Object.keys(content)) {
				content[l].image = image
			}
		}
		if (widget) {
			opt.payload.nav.route.push({ [widget]: {} })
		}

		try {
			await Perkd.notify.send(cardId, content, opt)
		}
		catch (err) {
			appNotify(`[${Model.name}]appNotify`, { cardId, err }, 'error')
		}
	}

	Model.sendData = async function (membership, options = {}) {
		const { Perkd } = appModule('perkd'),
			{ digitalCard } = membership ?? {},
			{ id } = digitalCard ?? {},
			cardId = String(id),
			opt = {
				...options,
				payload: {
					action: {
						object: 'sync',
						action: 'cache',
						data: {}
					},
				}
			}

		try {
			await Perkd.notify.send(cardId, null, opt)
		}
		catch (err) {
			appNotify(`[${Model.name}]sendData`, { cardId, err }, 'error')
		}
	}

	/**
	 * Send Push Notification to message
	 * @param {String} messageId
	 * @param {String} template
	 * @param {Object} [personalize] data
	 * @param {Object} [options] - { image, payload }
	 */
	Model.messageNotify = async function (messageId, template, personalize, options) {
		const { Perkd } = appModule('perkd'),
			{ app } = Model,
			{ languages = [] } = app.getSettings(LOCALE),
			[ language = ENGLISH ] = languages,
			content = i18n.translate(
				{ body: template },
				{ title: 'title' },
				{ globalize: { t: {}, default: language } },
				{ ...personalize }
			),
			notification = { content }

		try {
			await Perkd.messages.notify(messageId, notification, options)
		}
		catch (err) {
			appNotify(`[${Model.name}]messageNotify`, { messageId, template, personalize, err }, 'error')
		}
	}
}
