/**
 *  @module Mixin:Qualify
 */

const { default: sift } = require('sift'),
	{ parse } = require('mongo-parse'),
	{ Memberships, Rewards } = require('@crm/types'),
	{ isEmptyObj, satisfyCondition, diffDays, operations, removeEmptyValues, merge } = require('@perkd/utils')

const { ACTIVE } = Memberships.State,
	{ WITHDRAWN, CANCELLED } = Rewards.State,
	QUALIFY_PROPERTIES = {
		PERSON: [ 'gender', 'ethnicity', 'nationality', 'tags', 'dates', 'identities', 'addresses', 'phones', 'behaviors', 'acquired' ],
		MEMBERSHIP: [ 'id', 'tierLevel', 'qualifier', 'startTime', 'endTime', 'state', 'qualifyMethod', 'cardNumber', 'digitalCard', 'toQualify', 'behaviors', 'acquired', 'upgradedAt', 'programId', 'memberId', 'personId', 'previousId', 'nextId', 'storedValue' ],
		REWARD: [ 'id', 'startTime', 'endTime', 'levels', 'transactions', 'digital', 'state', 'when', 'reasons', 'sharer', 'createdAt', 'personId', 'memberId', 'membershipId' ],
		REWARDMASTER: [ 'key', 'activePeriod', 'validity', 'issued' ],
	},
	PREFIX = 'qualifying'

module.exports = function(Model) {

	const { POLICIES, QUALIFIERS } = Model

	// -----  Static Methods  -----

	/**
	 * Qualify member for qualifiers and apply action when qualified
	 * @param	{String} memberId member to qualify
	 * @param	{Array} qualifiers list of qualifiers (default to ALL qualifiers)
	 * @param	{Behaviors} behavior injected behavior (usually from behavior event)
	 * @param	{Object} options
	 *			{Object} custom - custom properties to be injected into qualify profile
	 *			{Array} programs (Membership only)
	 *			{Array} levels (Membership only)
	 *			{Object} [through]
	 * @return	{Object} qualifier applied { qualifier: ... }, (OR) NULL when not qualified or no action taken
	 */
	Model.qualifyAndApply = function(memberId, qualifiers = QUALIFIERS, behavior, options) {
		return Model.queue(PREFIX + memberId, async () => {
			const { profile, qualified } = await Model.qualify(memberId, qualifiers, behavior, options)

			return isEmptyObj(qualified)
				? null
				: Model.applyQualified(memberId, qualified, profile, behavior, options)
		})
	}

	/**
	 * Qualify member for qualifiers
	 * @param	{String} memberId member to qualify
	 * @param	{Array} qualifiers restrict to these qualifier names (string), eg. ["issueStamp"]
	 * @param	{Behaviors} behavior injected behavior (usually from behavior event)
	 * @param	{Object} options
	 *			{Object} custom - custom properties to be injected into qualify profile
	 *			{Array} programs (Membership only)
	 *			{Array} levels (Membership only)
	 *			{Object} [through]
	 * @return	{Object} { profile, qualified }, qualified is: { qualifier1: [{ master }, { master }], qualifier2: [] }
	 */
	Model.qualify = async function(memberId, qualifiers, behavior, options = {}) {
		try {
			const { custom } = options,
				masters = await Model.getActives(options),
				profiles = masters.map(m => removeEmptyValues(m?.policies?.profile?.toJSON ? m?.policies?.profile?.toJSON() : (m?.policies?.profile || {}))), // removeEmptyValues to avoid the properties which value is undefined
				include = merge.all(profiles),
				profile = await Model.qualifyProfile(memberId, include, behavior, { custom }),
				qualifyList = []

			for (const master of masters) {
				qualifyList.push(
					await master.qualify(profile, qualifiers, options)
				)
			}

			// cluster by qualifier
			const qualified = qualifyList.reduce((res, m) => {
				for (const q in m) {
					q in res ? (res[q] = res[q].concat(m[q])) : (res[q] = m[q])
				}
				return res
			}, {})

			return { profile, qualified }
		}
		catch (err) {
			appNotify(`${Model.name}.qualify`, { memberId, qualifiers, behavior, options, err }, 'error')
			return {}
		}
	}

	/**
	 * Apply FIRST (one) of the qualified based on order of POLICIES, if multiple qualified per qualifier, first will be used
	 * @param	{String} memberId
	 * @param	{Object} qualified
	 * @param	{Object} profile
	 * @param	{Behaviors} behavior
	 * @param	{Object} options
	 *			{String} programId
	 *			{String} tierLevel
	 *			{Object} [through]
	 * @return	{Object|null} qualifier applied { qualifier: ... }, (OR) must return NULL when no action taken
	 */
	Model.applyQualified = async function(memberId, qualified, profile, behavior, options) {
		const policies = Object.values(POLICIES),
			qualifier = Object.keys(qualified).find(q => policies.find(p => q.startsWith(p)))

		if (!qualifier) return null

		const { instance } = qualified[qualifier][0]	// all instances are the same
		return instance.applyQualifier(memberId, qualifier, qualified[qualifier], profile, behavior, options)
	}

	/**
	 * Construct Member profile object to be used for qualifying
	 * @param	{String} memberId
	 * @param	{Object} include models to include in profile, eg. { person: true }
	 * @param	{Behaviors} [behavior]
	 * @param	{Object} [options]
	 *			{Object} custom - { behaviors }
	 * @return	{Object} { person, member, memberships, rewards, behavior }, example:
	 *			{Object} rewards: { current: [], past: [], future: [] }
	 *			{Behaviors} behavior
	 */
	Model.qualifyProfile = async function(memberId, include = {}, behavior, options = {}) {
		const { app } = Model,
			{ Person, Member, Membership, Reward } = app.models,
			{ custom } = options

		if (!memberId) {
			return { behavior, ...custom }
		}

		const personFilter = {
				where: { memberId },
				fields: QUALIFY_PROPERTIES.PERSON,
			},
			membershipFilter = {
				where: { memberId, state: { neq: CANCELLED } }, // include terminated memberships to support Gift Card
				fields: QUALIFY_PROPERTIES.MEMBERSHIP.concat([ 'programId' ]),
				include: {
					relation: 'program',
					scope: { fields: [ 'key' ] }
				},
				order: 'startTime DESC'
			},
			rewardFilter = {
				where: { memberId, state: { nin: [ WITHDRAWN, CANCELLED ] } },
				fields: QUALIFY_PROPERTIES.REWARD.concat([ 'masterId' ]),
				include: {
					relation: 'rewardMaster',
					scope: { fields: QUALIFY_PROPERTIES.REWARDMASTER }
				},
			}

		const [ person, member, memberships, rewards ] = await Promise.all([
				include.person ? Person.findOne(personFilter) : undefined,
				// note: find entire member instance because subsequent logic may use member instance methods: e.g. replaceActiveMembershp/ProgramIds.
				include.member ? Member.findById(memberId) : undefined,
				include.membership ? Membership.find(membershipFilter) : undefined,
				include.reward ? Reward.find(rewardFilter) : undefined,
			]),
			profile = { person, member, memberships, rewards, behavior, ...custom }

		if (memberships && rewards) {
			profile.rewards = clusterRewards(memberships, rewards, behavior)
		}
		if (memberships) {
			profile.memberships = clusterMemberships(memberships, behavior)
		}

		return profile
	}

	/**
	 * Reverse actions taken with a past Behavior
	 * @param	{String} memberId member associated with behavior
	 * @param	{Behaviors} behavior - with sourceId of original behavior to be reversed
	 * @param	{Object} options
	 * @return  {Promise}
	 */
	Model.disqualify = function(memberId, behavior, options) {
		return Model.queue(PREFIX + memberId, async () => {
			const Log = Model.app.models[Model.name + 'Log'],
				{ sourceId } = behavior,
				filter = {
					where: {
						memberId: String(memberId),
						sourceId: String(sourceId),
						revertedAt: null
					},
					order: 'createdAt DESC',
				},
				actions = await Log.find(filter),
				revertedAt = new Date()

			for (const action of actions) {
				await Model.undoQualified(action, options)
				await action.updateAttributes({ revertedAt })
			}
		})
	}

	// -----  Instance Methods  -----

	/**
	 * Determine the qualifiers that object satisfy
	 * @param	{Object} profile profile of candidate { person, member, memberships, rewards, behaviors, custom }
	 * @param	{Array} [qualifiers] restricted list of qualifiers (names), defaults to all qualifiers
	 * @param	{Object} options
	 * 			{Boolean} manual - coerced qualify (all qualifiers) manually
	 * @return	{Object} - collection of qualifiers satisfied:  { qualifier: [ { instance: <instance>, param: {} } ] }
	 */
	if (!Model.prototype.qualify) {
		Model.prototype.qualify = async function(profile, qualifiers, options = {}) {
			const { manual } = options,
				Qualifiers = qualifiers || Object.keys(this.qualifiers),
				qualified = {}

			for (const name of Qualifiers) {
				const satisfied = satisfyCondition(profile, this.qualifiers[name], { operations })

				if (name in this.qualifiers && (manual || satisfied)) {
					const instance = { instance: this, param: {} }

					qualified[name] ? qualified[name].push(instance) : (qualified[name] = [ instance ])
				}
			}

			return qualified
		}
	}

	/**
	 * Apply list of qualifications to member
	 * @param	{String} memberId
	 * @param	{String} qualifier
	 * @param	{Array} aQualified list fo qualifier parameters { instance, param }, from prototype.qualify()
	 * @param	{Object} profile
	 * @param	{Behaviors} behavior
	 * @param	{Object} options
	 *			{String} programId
	 *			{String} tierLevel
	 * 			{Boolean} manual - coerced qualify (all qualifiers) manually
	 * 			{Object} [through]
	 * @return	{Object} qualifier applied { qualifier: ... }, (OR) must return NULL when no action taken
	 */
	Model.prototype.applyQualifier = function(memberId, qualifier, aQualified, profile, behavior, options = {}) {
		const { id, policies } = this,
			programId = options.programId || (Model.name === 'Program' && id),
			tierLevel = options.tierLevel,
			policy = policies.qualifiers[qualifier],
			{ inheritCard } = policy?.options ?? {},
			{ current = [], past = [] } = profile.memberships,
			memberships = inheritCard ? [ ...current, ...past ] : current,
			membership = programId
				? memberships.find(m => String(m.programId) === String(programId) && (!tierLevel || m.tierLevel === tierLevel))
				: memberships[0],
			{ handler, selectors } = policy,
			targets = this.selectFromProfile(selectors, profile),
			[ q, qualifyMethod ] = qualifier.split('/'),		// eg. 'upgrade/complimentary' (AstaLift)
			opt = {
				...policy.options,
				...options,
				qualifyMethod
			}

		return this.callHandler(handler, [ profile.member, membership, targets, behavior, aQualified, opt ])
		// note: handler MUST return NULL when no action taken
	}

	Model.prototype.callHandler = function(fnName, params) {
		if (!fnName) throw new Error('Handler not configured')
		// ---  built-in handler
		if (typeof this[fnName] === 'function') {
			return this[fnName](...params)
		}
		// ---  custom handler in external script file
		else if (fnName && typeof fnName === 'string') {
			try {
				const { handler } = appRequire('./scripts/' + fnName)
				return handler.apply(this, params)
			}
			catch (err) {
				appNotify('callHandler', { err }, 'error')
				throw err
			}
		}
	}

	// -----  Private methods  -----

	Model.getProfile = function(props) {
		const PROPS = qualifyProperties(props),
			MAP = {
				person: 'person',
				member: 'member',
				memberships: 'membership',
				rewards: 'reward',
			},
			SUPPORTED = Object.keys(MAP),
			include = { member: true }

		for (const p of PROPS) {
			const [ profileName ] = p.split('.')

			if (SUPPORTED.includes(profileName)) {
				include[MAP[profileName]] = true
			}
		}
		return include
	}

	/**
	 * Shortlist profile instances based on Selectors (under Policies of qualifier)
	 * @param	{Object} selectors { 'rewards.current': 'stringified query' }
	 * !NOTE: We treat the selectors conditions as {$or:[xxx]}
	 * @param	{Object} profile { person, member, memberships, rewards }
	 * @return	{Object[]} list of target instances
	 */
	Model.prototype.selectFromProfile = function(selectors = {}, profile) {
		const res = []

		if (typeof selectors === 'string') {
			selectors = JSON.parse(selectors)
		}

		Object.keys(selectors).forEach(key => {
			const filter = selectors[key],
				values = _.get(profile, key) || [],
				result = values.filter(sift(filter))

			res.push(...result)
		})
		return res
	}

	// -----  Private function  -----

	function clusterMemberships(memberships, behavior = {}) {
		const { createdAt: at = new Date() } = behavior,	// cluster memberships based on createdAt
			res = { current: [], past: [], future: [] }

		for (let i = 0; i < memberships.length; i++) {
			const m = memberships[i],
				{ key } = m.toJSON().program

			// inject master property
			m.key = key

			// cluster and inject calculated values
			if (m.startTime >= at) {
				m.startsIn = diffDays(at, m.startTime)
				res.future.push(m)
			}
			else if (m.startTime < at && m.upgradedAt ? m.upgradedAt >= at : m.state === ACTIVE) {
				const days = diffDays(m.endTime, at)
				// business logic: expiredAgo 1 1/2 days consider 2 days
				m.endTime ? (m.endTime < at ? (m.expiredAgo = days + 1) : (m.expiresIn = days)) : (m.expiresIn = Infinity)
				res.current.push(m)
			}
			else {
				m.expiredAgo = diffDays(m.endTime, at) + 1
				res.past.push(m)
			}
		}
		return res
	}

	function clusterRewards(memberships, rewards, behavior = {}) {
		const { createdAt: at = new Date() } = behavior,	// cluster rewards based on createdAt
			res = { current: [], past: [], future: [], count: { total: 0, current: 0, past: 0, future: 0 } },
			masters = {}

		for (let i = 0; i < rewards.length; i++) {
			const r = rewards[i]

			if (!masters[r.masterId]) {
				const { key } = r.toJSON().rewardMaster
				res.count[key] = { total: 0, current: 0, past: 0, future: 0 }
				masters[r.masterId] = key
			}
			// inject master property
			r.key = masters[r.masterId]

			// inject common calculated values
			r.stampsLeft = r.remainingStamps()
			res.count.total++
			res.count[masters[r.masterId]].total++

			// cluster and inject calculated values
			const m = memberships.find(membership => String(membership.id) === String(r.membershipId))

			if (!m) continue

			if (m.startTime >= at) {
				r.startsIn = diffDays(at, r.startTime)
				res.count.future++
				res.count[masters[r.masterId]].future++
				res.future.push(r)
			}
			else if (m.startTime < at && m.upgradedAt ? m.upgradedAt >= at : m.state === ACTIVE) {
				r.expiresIn = diffDays(at, r.endTime)
				res.count.current++
				res.count[masters[r.masterId]].current++
				res.current.push(r)
			}
			else {
				r.expiredAgo = diffDays(r.endTime, at) + 1
				res.count.past++
				res.count[masters[r.masterId]].past++
				res.past.push(r)
			}
		}
		return res
	}

	function qualifyProperties(qualifiers) {
		const Qualifiers = Object.keys(qualifiers),
			properties = {},
			getFields = (parts, parent = '') => {
				for (const part of parts) {
					const { field, parts, operator } = part

					if (operator === '$elemMatch') {
						getFields(parts, field)
					}
					else {
						if (field) properties[parent ? parent + '.' + field : field] = true
						if (parts) getFields(parts)
					}
				}
			}

		for (const qualifier of Qualifiers) {
			const { parts } = parse(JSON.parse(qualifiers[qualifier]))		// FIXME @zhangli
			getFields(parts)
		}
		return Object.keys(properties)
	}
}
