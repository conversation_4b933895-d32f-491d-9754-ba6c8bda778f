{"restApiRoot": "/api", "host": "0.0.0.0", "port": 3120, "remoting": {"cors": false, "rest": {"normalizeHttpPath": false, "handleErrors": false, "xml": false}, "json": {"strict": false, "limit": "10000kb"}, "urlencoded": {"extended": true, "limit": "10000kb"}}, "service": {"name": "Business", "domain": "business", "version": "1.0.0", "description": "", "appPath": "lib/", "settings": ["settings"], "dependencies": {}, "autoStart": true, "canTerminate": true, "state": {"now": 0, "text": "", "since": ""}, "multitenancy": true, "tenantCode": "", "tenants": {}}, "modules": {"checkin": {"enabled": true}, "metrics": {"enabled": true}, "eventbus": {"enabled": true}, "activity": {"enabled": true}, "perkd": {"enabled": true}, "watchdog": {"enabled": true}, "provider": {"enabled": true}, "i18n": {"enabled": true, "namespaces": ["staff"], "filepath": "../../lib/crm/i18n/"}}, "apiRequest": {"version": "1.0.0", "apiVersion": 0, "timeout": 15000, "https": false, "host": "", "port": "", "apiRoot": "/api", "multitenancy": true}}