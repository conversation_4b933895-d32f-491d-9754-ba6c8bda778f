{"_meta": {"sources": ["loopback/common/models", "loopback/server/models", "../common/models", "./lib/common/models", "./lib/crm/models", "./models"], "mixins": ["loopback/common/mixins", "loopback/server/mixins", "../common/mixins", "./lib/common/mixins", "./lib/crm/mixins", "./mixins"]}, "User": {"dataSource": "db", "public": false}, "AccessToken": {"dataSource": "db", "public": false}, "ACL": {"dataSource": "db", "public": false}, "RoleMapping": {"dataSource": "db", "public": false}, "Service": {"dataSource": "db", "public": true}, "Setting": {"dataSource": "transient", "public": false}, "Identity": {"dataSource": "transient", "public": false}, "Date": {"dataSource": "transient", "public": false}, "Email": {"dataSource": "transient", "public": false}, "Phone": {"dataSource": "transient", "public": false}, "Address": {"dataSource": "transient", "public": false}, "OpeningHour": {"dataSource": "transient", "public": false}, "Geometry": {"dataSource": "transient", "public": false}, "Locale": {"dataSource": "transient", "public": false}, "Style": {"dataSource": "transient", "public": false}, "Tag": {"dataSource": "transient", "public": false}, "Note": {"dataSource": "transient", "public": false}, "Callback": {"dataSource": "transient", "public": false}, "Url": {"dataSource": "transient", "public": false}, "Globalize": {"dataSource": "transient", "public": false}, "Spot": {"dataSource": "transient", "public": false}, "TouchPoint": {"dataSource": "transient", "public": false}, "Tier": {"dataSource": "transient", "public": false}, "Share": {"dataSource": "transient", "public": false}, "Flow": {"dataSource": "transient", "public": false}, "WidgetConfig": {"dataSource": "transient", "public": false}, "Pickup": {"dataSource": "transient", "public": false}, "Deliver": {"dataSource": "transient", "public": false}, "Store": {"dataSource": "transient", "public": false}, "FulfillPrice": {"dataSource": "transient", "public": false}, "Business": {"dataSource": "trap", "public": true}, "Dashboard": {"dataSource": "trap", "public": false}, "Staff": {"dataSource": "trap", "public": true}, "StaffActivity": {"dataSource": "trap", "public": true}, "Assignment": {"dataSource": "trap", "public": true}, "Provider": {"dataSource": "trap", "public": true}, "Provision": {"dataSource": "transient", "public": true}, "SharedProvider": {"dataSource": "shared", "public": true}, "role": {"dataSource": "authRemote", "public": false}, "AuthUser": {"dataSource": "authRemote", "public": false}, "Account": {"dataSource": "authRemote", "public": false}, "LogoImage": {"dataSource": "imageRemote", "public": false}, "Image": {"dataSource": "imageRemote", "public": false}, "Place": {"dataSource": "placeRemote", "public": false}, "Printer": {"dataSource": "placeRemote", "public": false}, "Vending": {"dataSource": "placeRemote", "public": false}, "Kiosk": {"dataSource": "placeRemote", "public": false}, "Numbering": {"dataSource": "placeRemote", "public": false}, "Person": {"dataSource": "person<PERSON><PERSON><PERSON>", "public": false}, "Member": {"dataSource": "membershipRemote", "public": false}, "Membership": {"dataSource": "membershipRemote", "public": false}, "Program": {"dataSource": "membershipRemote", "public": false}, "Payment": {"dataSource": "paymentRemote", "public": false}, "Transaction": {"dataSource": "paymentRemote", "public": false}, "Gateway": {"dataSource": "paymentRemote", "public": false}, "Order": {"dataSource": "salesRemote", "public": false}, "Product": {"dataSource": "productRemote", "public": false}, "Resource": {"dataSource": "productRemote", "public": false}, "Campaign": {"dataSource": "campaignRemote", "public": false}, "OfferMaster": {"dataSource": "offerRemote", "public": false}, "Action": {"dataSource": "actionRemote", "public": false}, "shopify": {"dataSource": "transient", "public": false}, "grab": {"dataSource": "productRemote", "public": false}}