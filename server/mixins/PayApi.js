/**
 *  @module Mixin:PayApi	(callbacks for Card<PERSON>)
 */
const { Payments } = require('@crm/types')

const { PAID } = Payments.Status,
	{ INTENT, SOURCE } = Payments.TransactionType

module.exports = function(Business) {

	/**
	 *	Make payment to business
	 * @param	{String} userId
	 * @param	{Object[]} payments
	 * @param	{Object} order
	 * @param	{Object} pricings
	 * @param	{Object} options
	 * 			{Object} through
	 * 			{String} description
	 * 			{Object} reservation - { id: '', queueNumber: 123, referenceId: '' }
	 * 			{String} masterId - where applicable
	 * 			{String} cardId - where applicable
	 * @return	{Object} { payment: {} }
	 */
	Business.pay = async function(userId, payments, order, pricings, options) {
		const business = await Business.merchantOf()
		return business?.pay(userId, payments, order, pricings, options)
	}

	/**
	 * Commit (pending) payment (by app) - eg. Alipay, LinePay
	 * @param	{String} userId - not used
	 * @param	{Object} payment
	 *			{String} method
	 *			{String} provider
	 *			{Object} intent|source: { id }
	 * @return	{Object} { payment: {}, payments: [], fulfilled: [] }
	 */
	Business.payCommit = async function(userId, payment) {
		const business = await Business.merchantOf(),
			{ method, provider, intent, source } = payment,
			type = intent ? INTENT : SOURCE,
			status = PAID,
			details = intent || source || {},
			{ id: referenceId } = details,
			transaction = { type, method, referenceId, status, provider, details }

		return business.payCommit(transaction)
	}

	/**
	 * Cancel a Payment
	 * @param	{String} userId - not used
	 * @param	{Object} payment
	 * 			{String} method
	 * 			{String} provider
	 * 			{Object} intent|source: { id }
	 * @param	{Object} options - { reason, cancelledAt, through }
	 * @return	{Order} cancelled order
	 */
	Business.payCancel = async function(userId, payment, options) {
		const business = await Business.merchantOf(),
			{ method, provider, intent, source } = payment,
			type = intent ? INTENT : SOURCE,
			details = intent || source || {},
			{ id: referenceId } = details,
			transaction = { type, method, referenceId, provider, details }

		return business.payCancel(transaction, options)
	}

	/**
	 * Save PaymentMethod at Payment provider for future payment
	 * @param	{String} userId
	 * @param	{String} type - eg. 'card'
	 * @param	{String} provider name, eg. 'stripe'
	 * @param	{Object} metadata - { installationId }
	 * @return	{Object} { intent: { customerId, clientSecret } }
	 */
	Business.paymentMethodAdd = async function(userId, type, provider, metadata) {
		const business = await Business.merchantOf()
		return business.paymentMethodAdd(userId, type, provider, metadata)
	}

	/**
	 * Remove PaymentMethod at Payment provider
	 * @param	{String} userId - not in use
	 * @param	{String} type - eg. 'card'
	 * @param	{String} paymentMethodId
	 * @return	{Object} { count: 1 }
	 */
	Business.paymentMethodRemove = async function(userId, type, paymentMethodId) {
		const business = await Business.merchantOf()
		return business.paymentMethodRemove(userId, type, paymentMethodId)
	}

	// -----  Remote Methods  -----

	Business.remoteMethod('pay', {
		description: 'Make payment to business (callback API)',
		http: { path: '/perkd/pay', verb: 'post' },
		accepts: [
			{ arg: 'userId', type: 'string', required: true },
			{ arg: 'payments', type: 'array', required: true },
			{ arg: 'order', type: 'object', required: true },
			{ arg: 'pricings', type: 'array' },
			{ arg: 'options', type: 'object' },
		],
		returns: { type: 'object', root: true },
	})

	Business.remoteMethod('payCommit', {
		description: 'Commit (pending) Payment (callback API)',
		http: { path: '/perkd/pay/commit', verb: 'post' },
		accepts: [
			{ arg: 'userId', type: 'string', required: true },
			{ arg: 'payment', type: 'object', required: true },
		],
		returns: { type: 'object', root: true },
	})

	Business.remoteMethod('payCancel', {
		description: 'Cancel (pending) Payment (callback API)',
		http: { path: '/perkd/pay/cancel', verb: 'post' },
		accepts: [
			{ arg: 'userId', type: 'string', required: true },
			{ arg: 'payment', type: 'object', required: true, description: '{ method, provider, intent: { id } }' },
			{ arg: 'options', type: 'object', description: '{ reason, cancelledAt, through }' },
		],
		returns: { type: 'object', root: true },
	})

	// ----  Payment Method

	Business.remoteMethod('paymentMethodAdd', {
		description: 'Store PaymentMethod with Provider for future use (callback API)',
		http: { path: '/perkd/pay/method', verb: 'post' },
		accepts: [
			{ arg: 'userId', type: 'string', required: true },
			{ arg: 'type', type: 'string', required: true },
			{ arg: 'provider', type: 'string', required: true },
			{ arg: 'metadata', type: 'object', required: true, description: '{ installationId }' },
		],
		returns: { type: 'Object', root: true, description: '{ intent: { clientSecret } }' },
	})

	Business.remoteMethod('paymentMethodRemove', {
		description: 'Remove PaymentMethod with Provider (callback API)',
		http: { path: '/perkd/pay/method', verb: 'delete' },
		accepts: [
			{ arg: 'userId', type: 'string', required: true },
			{ arg: 'type', type: 'string', required: true },
			{ arg: 'paymentMethodId', type: 'string', required: true },
		],
		returns: { type: 'Object', root: true, description: '{ count }' },
	})
}
