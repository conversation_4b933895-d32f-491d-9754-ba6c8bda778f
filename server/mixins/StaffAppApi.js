/**
 *  @module Mixin:Staff<PERSON>pp<PERSON>pi	(used by Shopify Staff app)
 */
const { toDataURL } = require('qrcode'),
	{ Programs, Memberships, Settings, Images, Persons, Providers } = require('@crm/types'),
	{ Actions, ACTION_INTENT } = require('@perkd/actions'),
	{ getAction } = require('@perkd/provisions'),
	{ startOf, endOf } = require('@perkd/utils'),
	{ Service, HttpErrors } = require('@perkd/errors')

const {
	ACTION_SECRET = '',
} = process.env

const { SHOPIFY_STAFF_APP } = Programs.PRESET,
	{ ACTIVE, CANCELLED, TERMINATED } = Memberships.State,
	{ STAFF, LOCALE } = Settings.Name,
	{ DEFAULT, ActionKey } = Settings,
	{ TIMEZONE } = DEFAULT,
	{ STAFF_CHECKIN } = ActionKey,
	{ PNG } = Images.Format,
	{ GIVEN_FAMILY } = Persons.NameOrder,
	{ GRABFOOD, GRABMART, UBEREATS } = Providers.Order,
	{ STAFF: ERROR } = Service,
	{ INVALID_PLACE } = ERROR,
	LIMIT = 2000,
	MODEL_NAME = { [GRABFOOD]: 'grab', [GRABMART]: 'grab', [UBEREATS]: UBEREATS }

module.exports = function(Staff) {

	/**
	 * Retrieve Staff's checkin activities, optionally by storeId
	 * @param	{Date} from
	 * @param	{Date} to
	 * @param	{String} [storeId]
	 * @return	{Object[]} { array: [{staffId, userName, placeId, placeName, action, occurredAt}] }
	 */
	Staff.checkinActivities = async function(from, to, storeId) {
		const { app } = Staff,
			{ StaffActivity } = app.models,
			{ timeZone = TIMEZONE } = app.getSettings(LOCALE),
			start = startOf('day', from, timeZone),
			end = endOf('day', to, timeZone),
			filter = {
				where: {
					name: {
						in: [ 'staff-checkin', 'staff-checkout' ]
					},
					occurredAt: { between: [ start, end ] }, // do not use occurredAt:{gte: from,lte: to}, lb3 does not support, will turn to occurredAt:{gte: from}
					'result.checkInAt.location.placeId': storeId
				},
				order: [ 'actor.id ASC', 'occurredAt ASC' ],
				limit: LIMIT
			},
			activities = await StaffActivity.find(filter),
			history = []

		for (const { actor, result, occurredAt, name: action } of activities) {
			const { checkInAt = {}, context } = result ?? {},
				{ id: staffId } = actor,
				{ location } = checkInAt,
				{ placeId, name: placeName } = location ?? {},
				{ familyName, givenName, fullName, name: profileName } = context ?? {},
				{ order } = profileName ?? {},
				userName = fullName || (order === GIVEN_FAMILY ? `${givenName} ${familyName}` : `${familyName} ${givenName}`)

			history.push({ staffId, userName, placeId, placeName, action, occurredAt })
		}

		return history
	}

	/**
	 * Retrieve Staff's checkin history by storeId
	 * @param	{Date} from
	 * @param	{Date} to
	 * @param	{String} [storeId]
	 * @return	{Object[]} { array: [{staffId, userName, placeId, placeName, checkInAt, checkOutAt}] }
	 */
	Staff.checkinHistory = async function(from, to, storeId) {
		const { app } = Staff,
			{ StaffActivity } = app.models,
			{ timeZone = TIMEZONE } = app.getSettings(LOCALE),
			start = startOf('day', from, timeZone),
			end = endOf('day', to, timeZone),
			filter = {
				where: {
					name: 'staff-checkout',
					occurredAt: { between: [ start, end ] }, // do not use occurredAt:{gte: from,lte: to}, lb3 does not support, will turn to occurredAt:{gte: from}
					'result.checkInAt.location.placeId': storeId
				},
				order: [ 'actor.id ASC', 'occurredAt ASC' ],
				limit: LIMIT
			},
			activities = await StaffActivity.find(filter),
			history = []

		for (const { actor, result, occurredAt: checkOutAt } of activities) {
			const { id: staffId } = actor,
				{ checkInFrom, checkInAt = {}, context } = result ?? {},
				{ location } = checkInAt,
				{ placeId, name: placeName } = location ?? {},
				{ familyName, givenName, fullName, name: profileName } = context ?? {},
				{ order } = profileName ?? {},
				userName = fullName || (order === GIVEN_FAMILY ? `${givenName} ${familyName}` : `${familyName} ${givenName}`)

			history.push({ staffId, userName, placeId, placeName, checkInAt: checkInFrom, checkOutAt })
		}

		return history
	}

	/**
	 * Get check in action QR Code for Location
	 * @param	{String} locationId - Shopify Location
	 * @param	{Object} options for qrcode {}
	 * @param	{Response} res
	 * @return	{DataURL} image in DataURL
	 */
	Staff.checkinQrCode = async function(locationId, options = {}, res) {
		const { app } = Staff,
			{ Place } = app.models,
			{ scale = 4, width = 640, margin, version, color = 'white', errorCorrection = 'L' } = options,
			filter = {
				where: {
					'external.shopify.storeId': locationId,
				},
			},
			place = await Place.findOne(filter),
			{ id, name } = place ?? {}

		if (!place) throw new Error({ statusCode: 404 })

		const action = new Actions(ACTION_SECRET),
			{ CHECKIN = {} } = DEFAULT.STAFF,
			{ checkin = {} } = app.getSettings(STAFF),
			{ ttl = CHECKIN.ttl } = checkin,
			placeId = String(id),
			url = `${ACTION_INTENT}/checkin`,
			data = { url, placeId, name, ttl },
			payload = getAction(STAFF_CHECKIN, data),
			uri = action.getURI(STAFF_CHECKIN, payload),
			opts = {
				type: PNG,
				scale, width, margin, version,
				errorCorrectionLevel: errorCorrection,
				color: color || {
					dark: '#000',	// Black dots
					light: '#0000', // Transparent background
				},
			},
			dataURL = toDataURL(uri, opts)

		return dataURL
	}

	/**
	 * Get list of Staff cards
	 * @param	{String} locationId - Shopify Location
	 * @return	{Object[]} { array: [{staffId, userName, mobile, cardId, cardNumber, startTime, endTime, state}] }
	 */
	Staff.checkinCards = async function(locationId) {
		const { Place, Assignment, Membership } = Staff.app.models,
			NOW = new Date(),
			byStore = {}

		// if locationId is provided, get staffs assigned to this store
		if (locationId) {
			const placefilter = {
					where: {
						'external.shopify.storeId': locationId,
					},
				},
				place = await Place.findOne(placefilter),
				{ id: placeId, name } = place ?? {}

			if (!place) throw new Error({ statusCode: 404 })

			const assignmentFilter = {
					where: {
						placeId,
						start: { lte: NOW },
						or: [
							{ end: { gte: NOW } },
							{ end: { exists: false } },
							{ end: null }
						]
					},
					fields: [ 'staffId' ]
				},
				lists = await Assignment.find(assignmentFilter)

			for (const a of lists) {
				byStore[String(a.staffId)] = a.staffId
			}
		}

		// get staff card list
		const staffCardFilter = {
				where: {
					staffId: { exists: true },
					state: {
						in: [ ACTIVE, CANCELLED, TERMINATED ]
					}
				},
				fields: [ 'personId', 'id', 'staffId', 'cardNumber', 'digitalCard', 'startTime', 'endTime', 'state' ],
				include: {
					relation: 'person',
					scope: {
						fields: [ 'fullName', 'phoneList' ]
					}
				},
				limit: LIMIT
			},
			membershipList = await Membership.find(staffCardFilter),
			filtered = [],
			cardList = []

		// filter by assigned store
		if (Object.keys(byStore).length > 0) {
			for (const data of membershipList) {
				if (byStore[String(data.staffId)]) filtered.push(data)
			}
		}
		else {
			filtered.push(...membershipList)
		}

		// format data
		for (const { staffId, cardNumber, digitalCard = {}, startTime, endTime, state, person = {} } of filtered) {
			const { fullName: userName = '', phoneList = [] } = person,
				[ first = {} ] = phoneList,
				{ fullNumber: mobile = '' } = first,
				{ id: cardId } = digitalCard

			cardList.push({ staffId, userName, mobile, cardId, cardNumber, startTime, endTime, state })
		}

		return cardList
	}

	/**
	 * Issue Staff Card for check in
	 * @param	{String} familyName
	 * @param	{String} givenName
	 * @param	{String} mobile
	 * @return	{Object} { personId, membershipId, cardNumber }
	 */
	Staff.checkinIssueCard = async function(familyName, givenName, mobile) {
		const { Program, Member } = Staff.app.models,
			profile = { familyName, givenName, mobile },
			[ program ] = await Program.getActives({ programs: [ SHOPIFY_STAFF_APP ] })

		if (!program) {
			appNotify(`[Staff]checkinIssueCard - Staff App program (${SHOPIFY_STAFF_APP}) not found`)
			throw new Error({ statusCode: 404 })
		}

		const programId = String(program.id),
			tierLevel = 1,
			through = {}, // TODO @zhangli
			{ personId, membershipId, cardNumber } = await Member.staffCardIssue(programId, tierLevel, undefined, profile, through)

		return { personId, membershipId, cardNumber }
	}

	/**
	 * Terminate Staff Card
	 * @param	{String} cardId
	 * @param	{String} reason
	 * @return	{Object} { personId, membershipId, cardNumber }
	 */
	Staff.checkinTerminateCard = async function(cardId, reason) {
		const { Membership, Member } = Staff.app.models,
			membership = await Membership.findActiveByCardId(cardId),
			through = {}

		if (!membership) throw new Error({ statusCode: 404 })

		const { personId, membershipId, cardNumber } = await Member.staffCardTerminate(membership.id, reason, through)
		return { personId, membershipId, cardNumber }
	}

	/**
	 * Request provider (grab/ubereats) to sync menu
	 * @param	{String} provider
	 * @param	{String} storeId - shopify locationId (gid://shopify/Location/67058100000)
	 */
	Staff.refreshMenu = async function(provider, storeId) {
		const { models } = Staff.app,
			{ Place } = models,
			providerModel = models[MODEL_NAME[provider]]

		if (!providerModel) throw HttpErrors.BadRequest('Not implemented')

		if (!storeId) return providerModel.menuRefresh(provider)

		const filter = {
				where: { 'external.shopify.storeId': storeId },
				fields: [ 'external' ]
			},
			place = await Place.findOne(filter),
			{ external } = place ?? {},
			{ storeId: shop } = external[provider] ?? {}

		if (!shop) throw HttpErrors.BadRequest(INVALID_PLACE)
		return providerModel.menuRefresh(provider, shop)
	}

	// -----  Remote Methods  -----

	Staff.remoteMethod('checkinActivities', {
		description: 'Staff Check In / Out activities (Shopify Staff app)',
		http: { path: '/shopify/staff/checkin/activities', verb: 'get' },
		accepts: [
			{ arg: 'from', type: 'date', http: { source: 'query' }, required: true },
			{ arg: 'to', type: 'date', http: { source: 'query' }, required: true },
			{ arg: 'storeId', type: 'string', http: { source: 'query' } },
		],
		returns: { type: 'array', root: true },
	})

	Staff.remoteMethod('checkinHistory', {
		description: 'Staff Check In history (Shopify Staff app)',
		http: { path: '/shopify/staff/checkin/history', verb: 'get' },
		accepts: [
			{ arg: 'from', type: 'date', http: { source: 'query' }, required: true },
			{ arg: 'to', type: 'date', http: { source: 'query' }, required: true },
			{ arg: 'storeId', type: 'string', http: { source: 'query' } },
		],
		returns: { type: 'array', root: true },
	})

	Staff.remoteMethod('checkinQrCode', {
		description: 'Staff Check In QR Code (Shopify Staff app)',
		http: { path: '/shopify/staff/checkin/qrcode', verb: 'get' },
		accepts: [
			{ arg: 'locationId', type: 'string', http: { source: 'query' }, required: true, description: 'Shopify Location Id' },
			{ arg: 'options', type: 'object', description: '{ scale, width, margin, version, color, errorCorrection }' },
			{ arg: 'res', type: 'object', http: { source: 'res' } },
		],
		returns: { type: 'file', root: true, description: 'dataurl' }
	})

	Staff.remoteMethod('checkinCards', {
		description: 'Get list of Staff cards (Shopify Staff app)',
		http: { path: '/shopify/staff/cards', verb: 'get' },
		accepts: [
			{ arg: 'locationId', type: 'string', http: { source: 'query' }, description: 'Shopify Location Id' },
		],
		returns: { type: 'object', root: true, description: '[{staffId, userName, mobile, cardId, cardNumber, startTime, endTime, state}]' },
	})

	Staff.remoteMethod('checkinIssueCard', {
		description: 'Issue Staff Card for check in (Shopify Staff app)',
		http: { path: '/shopify/staff/cards/issue', verb: 'post' },
		accepts: [
			{ arg: 'familyName', type: 'string', required: true },
			{ arg: 'givenName', type: 'string', required: true },
			{ arg: 'mobile', type: 'string', required: true },
		],
		returns: { type: 'object', root: true, description: '{ personId, membershipId, cardNumber }' },
	})

	Staff.remoteMethod('checkinTerminateCard', {
		description: 'Terminate Staff Card (Shopify Staff app)',
		http: { path: '/shopify/staff/cards/:cardId', verb: 'delete' },
		accepts: [
			{ arg: 'cardId', type: 'string', http: { source: 'path' }, required: true },
		],
		returns: { type: 'object', root: true },
	})

	Staff.remoteMethod('refreshMenu', {
		description: 'Request Provider (grab/ubereats) to sync menu (Shopify Staff app)',
		http: { path: '/shopify/staff/menu/refresh', verb: 'post' },
		accepts: [
			{ arg: 'provider', type: 'string', required: true },
			{ arg: 'storeId', type: 'string', description: 'gid://shopify/Location/67058100000' },
		]
	})
}
