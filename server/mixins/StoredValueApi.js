/**
 *  @module Mixin:StoredValueAPI	(callbacks for Card<PERSON>)
 */

const { Payments } = require('@crm/types'),
	{ updatePaymentWithTransaction, buildResponseToApp } = require('@perkd/commerce')

const { STOREDVALUE } = Payments.Method

module.exports = function(Business) {

	/**
	 * Transfer amount from card to card
	 * @param	{String} userId
	 * @param	{String} fromCardId
	 * @param	{String} toCardId
	 * @param	{Number} amount
	 * @return	{Object} { payments: [] }
	 */
	Business.storedvalueSend = async function(userId, fromCardId, toCardId, amount) {
		const [ from, to ] = await Promise.all([
				this.getWalletByCardId(fromCardId, STOREDVALUE),
				this.getWalletByCardId(toCardId, STOREDVALUE),
			]),
			transaction = await this.storedvalueTransfer(from, to, amount),
			payment = updatePaymentWithTransaction({}, transaction)

		return buildResponseToApp({ payments: [ payment ] }, undefined, transaction)
	}

	Business.storedvalueBalance = async function(userId, cardId) {
		return Business.paymentBalance(cardId, STOREDVALUE)
	}

	// -----  Remote Methods  -----

	Business.remoteMethod('storedvalueSend', {
		description: 'Send amount from a Card to another (callback API)',
		http: { path: '/perkd/pay/send', verb: 'post' },
		accepts: [
			{ arg: 'userId', type: 'string', required: true },
			{ arg: 'fromCardId', type: 'string', required: true },
			{ arg: 'toCardId', type: 'string', required: true },
			{ arg: 'amount', type: 'number', required: true },
		],
		returns: { type: 'object', root: true },
	})

	Business.remoteMethod('storedvalueBalance', {
		description: 'Retrieve balance (callback API)',
		http: { path: '/perkd/pay/balance', verb: 'get' },
		accepts: [
			{ arg: 'userId', type: 'string', required: true },
			{ arg: 'cardId', type: 'string', required: true },
		],
		returns: { type: 'object', root: true, description: '{ currency, balance }' },
	})
}
