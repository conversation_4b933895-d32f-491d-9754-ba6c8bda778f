/**
 *  @module Mixin:Deprovision - level 2 & 3
 */
const { Settings, Payments, Products, Programs, Fulfillments } = require('@crm/types'),
	{ StoredValues } = require('@perkd/provisions'),
	{ HttpErrors } = require('@perkd/errors')

const { FULFILLMENT } = Settings.Name,
	{ STOREDVALUE } = Payments.Method,
	{ TOPUP } = StoredValues.Type,
	{ HIDE } = Products.Availability,
	{ VENDING } = Fulfillments.Type,
	{ STAFF } = Programs.Type

module.exports = function(Provision) {

	/**
	 * De-provision fulfillment type
	 * @param {String} type
	 * @param {String} [businessId]
	 */
	Provision.deprovisionFulfillment = async function (type, businessId) {
		const { app } = Provision,
			{ Business, OfferMaster } = app.models,
			settings = app.getSettings(FULFILLMENT),
			fulfillment = settings[type],
			{ pickupOfferMasterId } = fulfillment ?? {}

		if (!fulfillment) throw HttpErrors.BadRequest('Not provisioned')
		if (type === VENDING && !pickupOfferMasterId) throw HttpErrors.BadRequest('Not provisioned')

		if (pickupOfferMasterId) {
			await OfferMaster.deleteById(pickupOfferMasterId)
		}

		await Business.updateSetting(FULFILLMENT, { [type]: undefined }, true)
	}

	/**
	 * De-provision Stored Value for a Tier of Program:
	 * 	1. delete products & variants related to tier
	 * 	2. update program
	 * 	3. deprovision payment
	 * @param {String} programId
	 * @param {Number} level of tier
	 */
	Provision.deprovisionStoredValue = async function (programId, level) {
		const { Program, Product, Gateway, Payment } = Provision.app.models,
			program = await Program.findById(programId)

		if (!program) throw HttpErrors.NotFound('Program not found')

		const { tierList, businessId } = program,
			tier = tierList.find(t => t.level === level),
			{ products = [] } = tier ?? {},
			preloadIds = Array.from(new Set(products.map(p => p.productId))),	// uniques
			title = TOPUP.charAt(0).toUpperCase() + TOPUP.slice(1),
			filter = {
				where: {
					title,
					availability: HIDE,
					'tags.category': STOREDVALUE
				}
			},
			topup = await Product.find(filter),
			topupIds = topup.map(p => p.id)

		// delete products & variants
		for (const id of [ ...preloadIds, ...topupIds ]) {
			await Product.deleteById(id)
		}

		// update program
		tier.products = []
		await program.updateAttributes({ tierList, storedValue: {} })

		// deprovision payment (if not in use by other programs)
		const where = {
				'storedValue.enabled': true
			},
			[ provider, count ] = await Promise.all([
				Gateway.providerFor(businessId, STOREDVALUE).catch(() => null),
				Program.count(where)
			])

		if (provider && count === 0) {
			await Payment.deprovisionProvider(businessId, provider, [ STOREDVALUE ])
		}
	}

	/**
	 * De-provision Roles for a Tier of Staff Program:
	 * 	1. check if any associated memberships
	 * 	2. delete corresponding cardMaster		// TODO
	 * 	3. clear widgets (config) of tier
	 * @param {String} programId
	 * @param {Number} tierLevel
	 */
	Provision.deprovisionStaffRoles = async function (programId, tierLevel) {
		const { Program, Membership } = Provision.app.models,
			where = { programId, tierLevel },
			[ program, count ] = await Promise.all([
				Program.findById(programId),
				Membership.count(where)
			]),
			{ type, tierList } = program ?? {},
			tier = tierList.find(t => t.level === tierLevel),
			{ digitalCard = {} } = tier ?? {}

		// validation
		if (!program) throw HttpErrors.NotFound('Program not found')
		if (!tier) throw HttpErrors.NotFound('Tier not found')

		if (type !== STAFF) throw HttpErrors.BadRequest('Not staff program')
		if (count > 0) throw HttpErrors.BadRequest(`Active memberships: ${count}`)

		digitalCard.widgets = []
		await program.__updateById__tiers(tierLevel, { digitalCard })
	}
}
