/**
 *  @module Mixin:PaymentApi	(External Api, used by vending)
 */
const { Payments } = require('@crm/types'),
	{ PAYMENT: PAYMENT_ERR } = require('@perkd/errors/dist/service')

const { AUTHORIZED } = Payments.Status,
	{ INTENT } = Payments.TransactionType,
	{ PAYMENT_NOT_AUTHORIZED } = PAYMENT_ERR

module.exports = function(Business) {

	/**
	 * Capture an authorized Payment			used by vending
	 * @param	{String} intentId
	 * @param	{Object} order { amount, currency, items }
	 * @param	{String} [referenceId]
	 * @return	{Object}  { amount, referenceId }
	 */
	Business.paymentApiCommit = async function(intentId, order, referenceId) {
		const { Transaction } = Business.app.models,
			[ transaction, business ] = await Promise.all([
				Transaction.findByReferenceId(INTENT, intentId),
				Business.merchantOf()
			]),
			{ amount } = order ?? {},
			{ method, status, provider, currency, amount: amt } = transaction ?? {}

		if (!transaction || status !== AUTHORIZED) throw PAYMENT_NOT_AUTHORIZED

		try {
			const trans = {
					type: INTENT,
					method,
					status,
					provider,
					currency,
					amount: amount || amt,
					details: { id: intentId },
					referenceId: intentId
				},
				{ payment } = await business.payCommit(trans)

			return { amount: payment?.amount, referenceId }
		}
		catch (err) {
			const { error = err } = err		// payCommit() throws transaction with error prop
			appNotify('[paymentCapture]', { error, intentId }, 'error')
			throw error
		}
	}

	/**
	 * Cancel an authorized Payment			used by vending
	 * @param	{String} intentId
	 * @param	{String} [referenceId]
	 * @return	{Object} { amount, referenceId }
	 */
	Business.paymentApiCancel = async function(intentId, referenceId) {
		const { Transaction } = Business.app.models,
			[ transaction, business ] = await Promise.all([
				Transaction.findByReferenceId(INTENT, intentId),
				Business.merchantOf()
			]),
			{ status } = transaction ?? {},
			trans = { referenceId: intentId }

		if (!transaction || status !== AUTHORIZED) throw PAYMENT_NOT_AUTHORIZED

		const { amount } = await business.payCancel(trans)

		return { amount, referenceId }
	}

	// -----  Remote Methods  -----

	Business.remoteMethod('paymentApiCommit', {
		description: 'Capture (authorized) Payment (Perkd Pay API)',
		http: { path: '/pay/commit', verb: 'post' },
		accepts: [
			{ arg: 'intentId', type: 'string', required: true },
			{ arg: 'order', type: 'object', required: true, description: '{ amount, currency, items }' },
			{ arg: 'referenceId', type: 'string' },
		],
		returns: { type: 'object', root: true, description: '{ amount, referenceId }' },
	})

	Business.remoteMethod('paymentApiCancel', {
		description: 'Cancel (authorized) Payment (Perkd Pay API)',
		http: { path: '/pay/cancel', verb: 'post' },
		accepts: [
			{ arg: 'intentId', type: 'string', required: true },
			{ arg: 'referenceId', type: 'string' },
		],
		returns: { type: 'object', root: true, description: '{ amount, referenceId }' },
	})
}
