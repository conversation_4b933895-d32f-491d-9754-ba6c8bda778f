/**
 *  @module Mixin:PayPrimitives - Level-0 payment primitives, used by Pay (local) mixin & Payment (remote) mixin
 */
const { Payments, Persons, Settings } = require('@crm/types'),
	{ PAYMENT: PAYMENT_ERR } = require('@perkd/errors/dist/service')

const { CARD, STOREDVALUE, REDEEMEDCASH, CREDITCREDITS, REDEEMEDCREDITS } = Payments.Method,
	{ PAID, FAILED, AUTHORIZED } = Payments.Status,
	{ PERKD, USER, CUSTOMER } = Persons.Identities,
	{ PAYMENT } = Settings.Name,
	{ PROVIDER_NOT_FOUND, PAYMENT_TYPE_NOT_FOUND, PAYMENT_NOT_AUTHORIZED, AMOUNT_INVALID } = PAYMENT_ERR,
	{ PAYMENT_ACCOUNT_NOT_FOUND, PAYMENT_DESTINATION_NOT_FOUND, AMOUNT_TOO_SMALL } = PAYMENT_ERR

module.exports = function(Business) {

	// -----  Static Methods  -----

	// FIXME createCustomer
	Business.createCustomer = async function(id, type, profile = {}) {
		// Person.findOrCreateUser()
		// Person.findOneByIdentity(provider, CUSTOMER, userId),
	}

	Business.merchantOf = async function(storeId) {
		const { app } = Business,
			{ Place } = app.models,
			{ multiMerchant } = app.getSettings(PAYMENT)

		if (multiMerchant === true && storeId) {
			const store = await Place.findById(storeId),
				{ ownerId } = store ?? {}

			if (!ownerId) return this.getMain()

			const business = await Business.findById(ownerId),
				{ payments } = business ?? {}

			return payments ? business : this.getMain()		// fallback to main if no payments configured
		}

		return this.getMain()
	}

	/**
	 * Get payment identities of Perkd User, including customer id for provider (if stored)
	 * @param	{String} userId - perkd
	 * @param	{String} provider eg. stripe
	 * @return {Object} { personId, customerId, email }
	 */
	Business.getPaymentCustomer = async function(userId, provider) {
		const { Person } = Business.app.models,
			person = await Person.findOneByIdentity(PERKD, USER, userId),
			{ id, emailList = [], identityList = [] } = person ?? {},
			{ address: email } = emailList[0] ?? {},
			customer = identityList.find(i => i.provider === provider && i.type === CUSTOMER),
			{ externalId: customerId } = customer ?? {}

		return { personId: String(id), customerId, email }
	}

	/**
	 * Get Wallet of payment type by cardId  (only single wallet per membership)
	 * @param	{String} cardId
	 * @param	{String} type - of payment
	 * @return {Promise<Payment>} wallet
	 */
	Business.getWalletByCardId = async function(cardId, type) {
		const { Membership } = Business.app.models,
			membership = await Membership.findOneWithPaymentByCardId(cardId, type),
			{ payments } = membership ?? {},
			[ wallet ] = payments ? payments() : []

		if (!wallet) {
			// FIXME critical error, add metric
			appNotify('[getWalletByCardId] not found', { cardId, type }, 'error')
		}

		return wallet || Business.rejectErr(PAYMENT_TYPE_NOT_FOUND, { cardId, type })
	}

	/**
	 * Create a Wallet
	 * 	- caller must ensure no other (same type) wallet exists, not checking here for speed
	 * @param	{String} id - businessId
	 * @param	{String} type - type of wallet, one of METHOD
	 * @param	{String} membershipId - of associated membership (owner)
	 * @param	{Object} [profile] - maybe required by payment providers for KYC
	 *			{String} mobile - eg. **********
	 *			{String} countryCode - eg. 65
	 *			{String} number - eg. 98765432
	 *			{String} [familyName]
	 *			{String} [givenName]
	 *			{String} [name]
	 *			{String} [email]
	 *			{Object} [storedValue]
	 *				{String} cardNumber
	 *				{String} [startTime]
	 *				{String} [endTime]
	 *				{Boolean} [taxIncluded] - if tax paid at top up
	 *				{Object} [external] - provider-specific parameters, eg. giftCardId for FlexM
	 * @return	{Payment} wallet
	 */
	Business.createWallet = async function(id, type, membershipId, profile) {
		const { Membership } = Business.app.models,
			{ name: sourceType } = Membership,
			merchantWalletType = (type === STOREDVALUE) ? REDEEMEDCASH : type,		// TODO: other types?
			merchantWallet = await Business.getWallet(id, merchantWalletType),
			wallet = await merchantWallet.createWallet(profile, type)

		// link wallet (payment) to membership
		return wallet.updateAttributes({ sourceId: membershipId, sourceType })
	}

	/**
	 * Get (merchant) Wallet
	 * @param	{String} id - businessId
	 * @param	{String} type - type of wallet, one of METHOD
	 * @return	{Payment}
	 */
	Business.getWallet = async function(id, type) {
		const business = await this.findById(id)

		return business.getWallet(type)
	}

	Business.paymentRequest = async function(method, request, details, options) {
		const { storeId } = details ?? {},
			business = await this.merchantOf(storeId)

		return business.paymentRequest(method, request, details, options)
	}

	Business.paymentAuthorize = async function(method, request, details, options) {
		const { storeId } = details ?? {},
			business = await this.merchantOf(storeId)

		return business.paymentAuthorize(method, request, details, options)
	}

	Business.paymentCapture = async function(request) {
		const business = await this.merchantOf()

		return business.paymentCapture(request)
	}

	Business.paymentCancel = async function(request, reason) {
		const business = await this.merchantOf()

		return business.paymentCancel(request, reason)
	}

	Business.paymentRefund = async function(transaction, reason) {
		const business = await this.merchantOf()

		return business.paymentRefund(transaction, reason)
	}

	Business.paymentBalance = async function(cardId, method) {
		const business = await this.merchantOf()

		return business.paymentBalance(cardId, method)
	}

	Business.storedvalueInit = async function(walletId, amount, discount, expiresAt) {
		const business = await this.merchantOf()

		return business.storedvalueInit(walletId, amount, discount, expiresAt)
	}

	Business.storedvalueTopup = async function(cardId, amount, discount, expiresAt) {
		const business = await this.merchantOf()

		return business.storedvalueTopup(cardId, amount, discount, expiresAt)
	}

	Business.storedvalueDeduct = async function(cardId, amount) {
		const business = await this.merchantOf()

		return business.storedvalueDeduct(cardId, amount)
	}

	/**
	 * Transfer amount from wallet to wallet (c2c)
	 * @param	{Payment|String} from - wallet or id
	 * @param	{Payment|String} to - wallet or id
	 * @param	{Number} [amount] to transfer, omit to transfer total balance!!
	 * @return	{Transaction}
	 */
	Business.storedvalueTransfer = async function(from, to, amount) {
		const { Event } = Business.app,
			[ source, destination ] = await Promise.all([
				(typeof from === 'string') ? getWalletById(from) : from,
				(typeof to === 'string') ? getWalletById(to) : to,
			]),
			{ code: currency } = source.currency,
			{ accountId: sourceId } = source.credentials,
			{ accountId: destinationId, userId } = destination.credentials,		// userId: flexmPay only
			intent = { sourceId, destinationId, userId },
			request = { amount, currency },
			details = { intent },
			options = {}

		if (!source || !sourceId) throw PAYMENT_ACCOUNT_NOT_FOUND
		if (!destination || !destinationId) throw PAYMENT_DESTINATION_NOT_FOUND

		if (amount && (typeof amount !== 'number' || amount <= 0)) {
			throw AMOUNT_TOO_SMALL
		}

		const transaction = await source.createIntent(STOREDVALUE, request, details, options)
		{
			const { id, sourceId, sourceType } = source
			appEmit(Event.payment.balance.changed, { id, sourceId, sourceType })
		}
		{
			const { id, sourceId, sourceType } = destination
			appEmit(Event.payment.balance.changed, { id, sourceId, sourceType })
		}
		return transaction
	}

	// -----  Instance Methods  -----

	/**
	 * Request payment to business
	 * @param	{String} method
	 * @param	{Object} request
	 * 			{Number} amount
	 * 			{String} currency
	 * 			{Object[]} items
	 * 			{Object[]} fulfillments
	 * @param	{Object} details
	 * 			{String} userId - Perkd userId
	 * 			{String} wallet - applepay / googlepay
	 * 			{String} provider - eg. stripe
	 * 			{Object} intent - provided by app: {
	 * 						paymentMethodId: pm_1Ic06yLFbSgjvNHiKbbWK2oj	stripe, storedvalue (paymentId)
	 * 						customerId								stripe, platform or connected account customer id
	 * 						cardId: 62a9e3dfb6cdfb58709af528		storedvalue, used to inject walletId as paymentMethodId
	 * 						[destinationId]							storedvalue, walletId of payee in c2c payment
	 * 						offerId (CRM)							VOUCHER, used as referenceId
	 * 					 }
	 * 			{Object} metadata - { orderId, personId, cardId, userId }
	 * 			{String} statement - appears on credit card statement (max. 22 chars)
	 * 			{String} storeId - only needed when store-level payment, eg. sub-merchants/特約商店編號 of MyPay
	 * 			{String} taxId - TW (MyPay): individual: 載具號 or business: 統編
	 * @param	{Object} [options]
	 * 			{Boolean} capture - default TRUE
	 * 			{String} description
	 * 			{String} idempotency
	 * @return	{Transaction}
	 */
	Business.prototype.paymentRequest = async function(method, request = {}, details = {}, options = {}) {
		const { name: businessName, locale, payments = {} } = this,
			{ Event } = Business.app,
			{ payee, fees } = payments,
			{ currency = locale.currency, amount, totalDiscounts, items, fulfillments } = request,
			{ userId, wallet, provider, intent, storeId, metadata, taxId } = details,
			{ capture = true, description, idempotency } = options,
			{ cardId } = intent,
			payerWallet = cardId ? await Business.getWalletByCardId(cardId, method) : undefined,
			{ id, sourceId, sourceType } = payerWallet || {}

		// validations
		if (!amount || amount <= 0 || typeof amount !== 'number') {
			return this.rejectErr(AMOUNT_INVALID, { request }, true)
		}

		metadata.businessId = this.id.toString()		// inject, used by payment events handler

		// inject accountId as intent.sourceId (stored value)
		if (method === STOREDVALUE && payerWallet) {
			const { credentials = {} } = payerWallet,
				{ accountId } = credentials

			intent.sourceId = accountId
		}

		const [ payeeWallet, person, store = {} ] = await Promise.all([
				this.getWallet(method, provider),
				userId ? Business.getPaymentCustomer(userId, provider) : {},
				Business.getStorePaymentSetting(storeId),
			]),
			{ merchantId } = store,
			customer = { ...person, taxId },
			statement = `${payee || businessName}${description ? ` - ${description}` : ''}`.slice(0, 22),	// max 22 chars
			req = { amount, currency, totalDiscounts, items, fulfillments },
			det = { wallet, intent, customer, metadata, statement, storeId },
			opt = { capture, merchantId, fees: getRate(request, fees), description, idempotency }

		try {
			const transaction = await payeeWallet.createIntent(method, req, det, opt),
				{ status, balance } = transaction

			if (payerWallet && status === PAID) {
				appEmit(Event.payment.balance.changed, { id, sourceId, sourceType, amount: -transaction.amount, balance })
			}

			return transaction
		}
		catch (err) {
			if (payerWallet) {	// force balance refresh
				appEmit(Event.payment.balance.changed, { id, sourceId, sourceType })
			}
			throw err
		}
	}

	Business.prototype.paymentAuthorize = function(method, request, details, options = {}) {
		options.capture = false
		return this.paymentRequest(method, request, details, options)
	}

	/**
	 * Capture an authorized Payment
	 * @param	{Transaction} transaction (mutated)
	 * 			{String} type
	 * 			{String} method - eg. card, storedvalue
	 *			{String} provider
	 * 			{Number} amount - support overriding of authorized
	 *			{String} currency
	 * 			{String} status
	 * 			{String} referenceId
	 *			{String} paymentId
	 *			{String} orderId
	 * 			{Object} details - { sourceId }
	 * @return	{Transaction}
	 */
	Business.prototype.paymentCapture = async function(transaction = {}) {
		const { Event } = Business.app,
			{ method, status, currency, details = {} } = transaction

		if (status === PAID) return transaction
		if (status !== AUTHORIZED) throw PAYMENT_NOT_AUTHORIZED

		try {
			const merchantWallet = await this.getWallet(method),
				capturedTransaction = await merchantWallet.capture(transaction)

			if (method === STOREDVALUE) {
				const { amount, balance } = capturedTransaction,
					{ sourceId: accountId } = details

				getWalletByAccountId(accountId).then(payerWallet => {
					const { id, sourceId, sourceType } = payerWallet
					appEmit(Event.payment.balance.changed, { id, sourceId, sourceType, amount: -amount, balance, currency })
				})
			}

			return capturedTransaction
		}
		catch (error) {
			transaction.status = FAILED
			transaction.error = error

			appNotify('[paymentCapture]', { error, transaction }, 'error')
			throw transaction
		}
	}

	/**
	 * Cancel a (pending) Payment
	 * @param	{Transaction} transaction
	 *			{String} type
	 *			{String} method
	 *			{String} referenceId
	 * @param	{String} [reason]
	 * @return	{Transaction} transaction
	 */
	Business.prototype.paymentCancel = async function(transaction = {}, reason) {
		const { method, referenceId } = transaction,
			wallet = await this.getWallet(method)

		return wallet.cancelIntent(referenceId, { reason }, method)
	}

	/**
	 * Refund a (paid) Payment
	 * @param	{Transaction} transaction
	 * 			{String} id
	 * 			{String} type - intent|charge
	 * 			{String} method
	 * 			{String} provider
	 * 			{String} status
	 * 			{Number} amount
	 * 			{String} currency
	 * 			{String} personId
	 *			{Object} details
	 * 				{String} id
	 * 				{String} method
	 * 				{String} status
	 * 				{String} [sourceId] - storedvalue (mandatory)
	 * 				{String} [destinationId] - storedvalue (mandatory)
	 * 				{String} [key] - MyPay only
	 * 				{String} [merchantId] - id of sub-merchant (MyPay only)
	 * @param	{String} [reason]
	 * @return	{Transaction}
	 */
	Business.prototype.paymentRefund = async function(transaction = {}, reason) {
		const { Event } = Business.app,
			{ id: orderId, type, method, amount, currency, personId, details = {} } = transaction,
			{ sourceId: accountId } = details,
			metadata = { orderId, personId, reason },
			det = { ...details, type, method, metadata },
			[ merchantWallet, memberWallet = {} ] = await Promise.all([
				this.getWallet(method),
				getWalletByAccountId(accountId)
			]),
			refund = await merchantWallet.createRefund(amount, currency, det),
			{ balance, amount: refunded } = refund,
			{ id, sourceId, sourceType } = memberWallet

		appEmit(Event.payment.balance.changed, { id, sourceId, sourceType, amount: refunded, balance })

		return refund
	}

	/**
	 * Retrieve balance on consumer wallet (cardId)
	 * @param	{String} cardId
	 * @param	{String} [method] - default storedvalue
	 * @return	{Object}
	 *			{String} currency
	 *			{Number} balance
	 */
	Business.prototype.paymentBalance = async function(cardId, method = STOREDVALUE) {
		const wallet = await Business.getWalletByCardId(cardId, method)
		return wallet.balance()
	}

	// ----  Stored Value

	/**
	 * Initialize consumer wallet, paying out from Merchant's 'creditCredits' wallet
	 * @param	{String} walletId - paymentId
	 * @param	{Number} amount
	 * @param	{Number} [discount] % given on amount (if applicable)
	 * @param	{Date} [expiresAt] of amount (if applicable)
	 * @return	{Object}
	 */
	Business.prototype.storedvalueInit = async function(walletId, amount, discount, expiresAt) {
		const { Payment } = Business.app.models,
			[ merchantWallet, memberWallet ] = await Promise.all([
				this.getWallet(CREDITCREDITS),
				Payment.findById(walletId)
			]),
			{ balance, currency } = await this.storedvaluePayout2Wallet(merchantWallet, memberWallet, amount, discount, expiresAt)

		return { balance, currency }
	}

	/**
	 * Top up amount to consumer wallet (cardId), paying out from Merchant's 'creditCredits' wallet
	 * @param	{String} cardId
	 * @param	{Number} amount
	 * @param	{Number} [discount] % given on amount
	 * @param	{Date} [expiresAt] of amount
	 * @return	{Object} { balance, currency }
	 */
	Business.prototype.storedvalueTopup = async function(cardId, amount, discount, expiresAt) {
		const { Event } = Business.app,
			[ merchantWallet, memberWallet ] = await Promise.all([
				this.getWallet(CREDITCREDITS),
				Business.getWalletByCardId(cardId, STOREDVALUE),
			]),
			{ balance, currency } = await this.storedvaluePayout2Wallet(merchantWallet, memberWallet, amount, discount, expiresAt),
			{ id, sourceId, sourceType } = memberWallet

		appEmit(Event.payment.balance.changed, { id, sourceId, sourceType, amount, balance })

		return { balance, currency }
	}

	/**
	 * Payout to (credit) wallet with amount  (private method)
	 * @param	{Payment} merchantWallet - source
	 * @param	{Payment} wallet - destination
	 * @param	{Number} amount
	 * @param	{Number} [discount] % given on amount
	 * @param	{Date} [expiresAt] of amount
	 */
	Business.prototype.storedvaluePayout2Wallet = async function(merchantWallet, wallet, amount, discount, expiresAt) {
		const { code } = merchantWallet.currency,
			{ credentials = {} } = wallet,
			{ accountId } = credentials,
			details = { discount, expiresAt },
			transaction = await merchantWallet.createPayout(amount, code, accountId, details),
			{ balance, currency } = transaction

		return { balance, currency }
	}

	/**
	 * Deduct amount from consumer wallet (cardId), paying out to Merchant's 'redeemedCredits' wallet
	 * @param	{String} cardId
	 * @param	{Number} amount
	 * @return	{Object} { balance, currency }
	 */
	Business.prototype.storedvalueDeduct = async function(cardId, amount) {
		const { Event } = Business.app,
			[ merchantWallet, payerWallet ] = await Promise.all([
				this.getWallet(REDEEMEDCREDITS),
				Business.getWalletByCardId(cardId, STOREDVALUE),
			]),
			{ credentials = {} } = payerWallet,
			{ accountId } = credentials,
			intent = { sourceId: accountId },
			{ code } = merchantWallet.currency,
			request = { amount, currency: code },
			details = { intent },
			options = {},
			transaction = await merchantWallet.createIntent(STOREDVALUE, request, details, options),
			{ balance, currency } = transaction,
			{ id, sourceId, sourceType } = payerWallet

		appEmit(Event.payment.balance.changed, { id, sourceId, sourceType, amount: -amount, balance })

		return { balance, currency }
	}

	// ----  Payment Method

	/**
	 * Save PaymentMethod at Payment provider for future payment
	 * @param	{String} userId
	 * @param	{String} type - eg. 'card'
	 * @param	{String} provider name, eg. 'stripe'
	 * @param	{Object} metadata - { installationId }
	 * @return	{Object} { intent: { customerId, clientSecret } }
	 */
	Business.prototype.paymentMethodAdd = async function(userId, type, provider, metadata = {}) {
		const { Person } = Business.app.models,
			[ person, merchantWallet ] = await Promise.all([
				Person.findOrCreateUser(userId, PERKD),		// find/create person for Perkd user
				this.getWallet(type),
			]),
			{ id: personId, identityList } = person

		metadata.personId = personId
		metadata.userId = userId

		let identity = identityList.find(i => i.provider === provider && i.type === CUSTOMER)

		if (!identity) {
			const profile = {},		// not passing any PII for now
				metadata = { userId, personId },
				customer = await merchantWallet.createCustomer(profile, metadata),
				through = { type: PERKD, touchedAt: new Date() }

			identity = { provider, type: CUSTOMER, externalId: customer.id, visible: false }
			const changes = { id: personId, identityList: [ identity ] }

			await Person.doUpsert(changes, null, { through })
		}

		const { externalId: customerId } = identity,
			opt = { metadata }

		if (type === CARD) {
			opt.description = 'Card Authorization Fee'
		}

		const intent = await merchantWallet.addPaymentMethod(type, customerId, opt),
			{ clientSecret } = intent || {}

		return { intent: { customerId, clientSecret } }
	}

	/**
	 * Remove PaymentMethod at Payment provider
	 * @param	{String} userId - not in use
	 * @param	{String} type - eg. 'card'
	 * @param	{String} paymentMethodId
	 * @return	{Object} { count: 1 }
	 */
	Business.prototype.paymentMethodRemove = async function(userId, type, paymentMethodId) {
		const merchantWallet = await this.getWallet(type),
			removedMethod = await merchantWallet.removePaymentMethod(type, paymentMethodId)

		return { count: 1 }
	}

	// -----  Private Methods  -----

	/**
	 * Get Wallet of business for payment method
	 * @param	{String} method of payment
	 * @param	{String} provider
	 * @return {Promise<Payment>} wallet
	 */
	Business.prototype.getWallet = async function(method, provider) {
		const { payments } = this,
			{ Gateway } = Business.app.models,
			{ id } = payments ? this : await Business.getMain(),
			wallet = await Gateway.getPayment(String(id), method, provider)

		return wallet || this.rejectErr(PROVIDER_NOT_FOUND, { method })
	}

	// -----  Private Functions  -----

	/**
	 * Get Wallet by id
	 * @param	{String} id
	 * @return {Promise<Payment>} wallet
	 */
	async function getWalletById(id = '') {
		const { Payment } = Business.app.models

		return Payment.findById(id)
			.catch(err => {
				appNotify('[getWalletById] not found', { id }, 'error')		// FIXME critical error, add metric
				throw err
			})
	}

	/**
	 * Get Wallet by (provider) accountId
	 * @param	{String} id
	 * @return {Promise<Payment>} wallet
	 */
	async function getWalletByAccountId(id) {
		if (!id) return undefined

		const { Payment } = Business.app.models,
			filter = {
				where: { 'credentials.accountId': id }
			},
			wallet = await Payment.findOne(filter)

		if (!wallet) {
			appNotify('[getWalletByAccountId] not found', { id }, 'error')		// FIXME critical error, add metric
		}

		return wallet
	}

	function getRate(order, fees) {
		if (!fees) return undefined

		const { rate: baseRate, options = {} } = fees,
			{ excludeFulfillment } = options

		if (!baseRate) return baseRate

		const { amount: totalPrice, fulfillments = [] } = order,
			price = excludeFulfillment
				? totalPrice - fulfillments.reduce((total, f) => {
					total += f.price || 0
					return total
				}, 0)
				: totalPrice

		return baseRate / (totalPrice / price)
	}

	// -----  Remote Methods  -----

	Business.remoteMethod('paymentRequest', {
		description: 'Request a Payment',
		http: { path: '/payment/request', verb: 'post' },
		accepts: [
			{ arg: 'method', type: 'string', required: true },
			{ arg: 'request', type: 'object', required: true },
			{ arg: 'details', type: 'object', required: true },
			{ arg: 'options', type: 'object' },
		],
		returns: { type: 'object', root: true },
	})
	Business.remoteMethod('prototype.paymentRequest', {
		description: 'Request a Payment',
		http: { path: '/payment/request', verb: 'post' },
		accepts: [
			{ arg: 'method', type: 'string', required: true },
			{ arg: 'request', type: 'object', required: true },
			{ arg: 'details', type: 'object', required: true },
			{ arg: 'options', type: 'object' },
		],
		returns: { type: 'object', root: true },
	})

	Business.remoteMethod('paymentAuthorize', {
		description: 'Authorize a Payment',
		http: { path: '/payment/authorize', verb: 'post' },
		accepts: [
			{ arg: 'method', type: 'string', required: true },
			{ arg: 'request', type: 'object', required: true },
			{ arg: 'details', type: 'object', required: true },
			{ arg: 'options', type: 'object' },
		],
		returns: { type: 'object', root: true },
	})
	Business.remoteMethod('prototype.paymentAuthorize', {
		description: 'Authorize a Payment',
		http: { path: '/payment/authorize', verb: 'post' },
		accepts: [
			{ arg: 'method', type: 'string', required: true },
			{ arg: 'request', type: 'object', required: true },
			{ arg: 'details', type: 'object', required: true },
			{ arg: 'options', type: 'object' },
		],
		returns: { type: 'object', root: true },
	})

	Business.remoteMethod('paymentCapture', {
		description: 'Capture an authorized Payment',
		http: { path: '/payment/capture', verb: 'post' },
		accepts: [
			{ arg: 'request', type: 'object', required: true, description: '{ type, method, referenceId, status, details }' },
		],
		returns: { type: 'object', root: true },
	})
	Business.remoteMethod('prototype.paymentCapture', {
		description: 'Capture an authorized Payment',
		http: { path: '/payment/capture', verb: 'post' },
		accepts: [
			{ arg: 'request', type: 'object', required: true, description: '{ type, method, referenceId, status, details }' },
		],
		returns: { type: 'object', root: true },
	})

	Business.remoteMethod('paymentCancel', {
		description: 'Cancel a (pending) Payment',
		http: { path: '/payment/cancel', verb: 'delete' },
		accepts: [
			{ arg: 'request', type: 'object', required: true, description: '{ method, referenceId }' },
			{ arg: 'reason', type: 'string', description: 'eg. customer, expired, error' },
		],
		returns: { type: 'object', root: true },
	})
	Business.remoteMethod('prototype.paymentCancel', {
		description: 'Cancel a (pending) Payment',
		http: { path: '/payment/cancel', verb: 'delete' },
		accepts: [
			{ arg: 'request', type: 'object', required: true, description: '{ method, referenceId }' },
			{ arg: 'reason', type: 'string', description: 'eg. customer, expired, error' },
		],
		returns: { type: 'object', root: true },
	})

	Business.remoteMethod('paymentRefund', {
		description: 'Refund a (succeeded) Payment',
		http: { path: '/payment/refund', verb: 'post' },
		accepts: [
			{ arg: 'transaction', type: 'object', required: true },
			{ arg: 'reason', type: 'string', description: 'eg. customer, expired, error' },
		],
		returns: { type: 'object', root: true },
	})
	Business.remoteMethod('prototype.paymentRefund', {
		description: 'Refund a (succeeded) Payment',
		http: { path: '/payment/refund', verb: 'post' },
		accepts: [
			{ arg: 'transaction', type: 'object', required: true },
			{ arg: 'reason', type: 'string', description: 'eg. customer, expired, error' },
		],
		returns: { type: 'object', root: true },
	})

	Business.remoteMethod('paymentBalance', {
		description: 'Retrieve balance',
		http: { path: '/payment/balance', verb: 'get' },
		accepts: [
			{ arg: 'cardId', type: 'string', required: true },
			{ arg: 'method', type: 'string' },
		],
		returns: { type: 'object', root: true },
	})
	Business.remoteMethod('prototype.paymentBalance', {
		description: 'Retrieve balance',
		http: { path: '/payment/balance', verb: 'get' },
		accepts: [
			{ arg: 'cardId', type: 'string', required: true },
			{ arg: 'method', type: 'string' },
		],
		returns: { type: 'object', root: true },
	})

	// ----  Stored Value

	Business.remoteMethod('storedvalueInit', {
		description: 'Initialize amount (storedValue)',
		http: { path: '/payment/storedvalue/init', verb: 'post' },
		accepts: [
			{ arg: 'walletId', type: 'string', required: true },
			{ arg: 'amount', type: 'number', required: true },
			{ arg: 'discount', type: 'number' },
			{ arg: 'expiresAt', type: 'date' },
		],
		returns: { type: 'object', root: true },
	})

	Business.remoteMethod('storedvalueTopup', {
		description: 'Top up amount (storedValue)',
		http: { path: '/payment/storedvalue/topup', verb: 'post' },
		accepts: [
			{ arg: 'cardId', type: 'string', required: true },
			{ arg: 'amount', type: 'number', required: true },
			{ arg: 'discount', type: 'number' },
			{ arg: 'expiresAt', type: 'date' },
		],
		returns: { type: 'object', root: true },
	})

	Business.remoteMethod('storedvalueDeduct', {
		description: 'Deduct amount (storedValue)',
		http: { path: '/payment/storedvalue/deduct', verb: 'post' },
		accepts: [
			{ arg: 'cardId', type: 'string', required: true },
			{ arg: 'amount', type: 'number', required: true },
		],
		returns: { type: 'object', root: true },
	})

	Business.remoteMethod('storedvalueTransfer', {
		description: 'Transfer amount from wallet to wallet, C2C (storedValue)',
		http: { path: '/payment/storedvalue/transfer', verb: 'post' },
		accepts: [
			{ arg: 'from', type: 'string', required: true, description: 'source walletId' },
			{ arg: 'to', type: 'string', required: true, description: 'destination walletId' },
			{ arg: 'amount', type: 'number', description: 'omit to transfer entire balance' },
		],
		returns: { type: 'object', root: true, description: 'intent' },
	})
}
