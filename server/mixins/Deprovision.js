/**
 *  @module Mixin:Deprovision - level 0 & 1
 */
const { Context } = require('@perkd/multitenant-context'),
	{ Settings, Programs } = require('@crm/types'),
	{ Provider: ProviderRedis } = require('@perkd/providers'),
	{ HttpErrors } = require('@perkd/errors')

const { PROVIDER } = Settings.Name,
	{ STAFF } = Programs.Type

module.exports = function(Provision) {

	// ----  Level-0 (executed from G account)  ----

	Provision.decommission = async function (accountId) {
		const { app } = Provision,
			{ Account, Business, Service } = app.models,
			{ TENANTS } = Service,
			account = await Account.findById(accountId),
			{ businessId, tenantCode, adminUserId, ownerId, state } = account

		await Context.runAsTenant(tenantCode, async () => {
			const business = await Business.findById(businessId),
				{ settingList = [] } = business?.toJSON() ?? {}

			for (const { name } of settingList) {
				await Business.removeSetting(name)
			}

			// TODO await this.deletePerkdBusiness(main)+++

			// await business.delete()
			await this.dropDatabase()
		}, app.connectionManager)

		await Account.deleteById(accountId)
		await TENANTS.remove(tenantCode)
	}

	// ----  Level-1  (executed in Tenant account)  ----

	/**
	 * De-provision Payment Provider for business
	 * @param {String} businessId
	 * @param {String} provider
	 * @return {String[]} - payment types deprovisioned
	 */
	Provision.deprovisionPaymentProvider = async function (businessId, provider) {
		const { Business, Payment } = Provision.app.models,
			business = await Business.findById(businessId)

		if (!business) throw HttpErrors.NotFound('Business not found')

		const deprovisioned = await Payment.deprovisionProvider(businessId, provider),
			providers = new ProviderRedis(provider)

		await Promise.allSettled([
			providers.tenants.remove(Context.tenant),
			Business.updateSetting(PROVIDER, { [provider]: false }, true)
		])
		await providers.end()
		return deprovisioned
	}

	/**
	 * Deprovision Order Provider for tenant
	 * @param {String} name - provider key
	 * @param {String} [shop] - multi-merchant only
	 * @return {Number} - count deprovisioned (should always be 1)
	 */
	Provision.deprovisionOrderProvider = async function (name, shop) {
		const { Provider, Business } = Provision.app.models,
			{ count } = await Provider.deleteAll({ name, shop })

		if (!count) throw HttpErrors.NotFound('Provider not found')

		const providers = new ProviderRedis(name)

		await Promise.allSettled([
			providers.tenants.remove(Context.tenant),
			Business.updateSetting(PROVIDER, { [name]: false }, true)
		])
		await providers.end()
		return count
	}

	/**
	 * De-provision Membership Program
	 * @param {String} programId
	 */
	Provision.deprovisionProgram = async function (programId) {
		const { Program, Membership } = Provision.app.models,
			where = { programId },
			[ program, count ] = await Promise.all([
				Program.findById(programId),
				Membership.count(where)
			]),
			{ type, storedValue = {}, tierList } = program ?? {}

		if (!program) throw HttpErrors.NotFound('Program not found')
		if (count !== 0) throw HttpErrors.BadRequest(`There are ${count} memberships`)

		for (const { level } of tierList) {
			if (type === STAFF) {
				await this.deprovisionStaffRoles(programId, level)
			}
			if (storedValue.enabled) {
				await this.deprovisionStoredValue(programId, level)
			}
		}

		await program.deprovision(true)
	}

	// ----  Private Methods  ----

	Provision.dropDatabase = async function() {
		const { app } = Provision,
			{ Business } = app.models,
			ds = Business.getDataSource(),
			connected = await ds.connect(),
			{ db } = ds.connector

		if (!db) return

		await db.dropDatabase()
		await ds.disconnect()
	}
}
