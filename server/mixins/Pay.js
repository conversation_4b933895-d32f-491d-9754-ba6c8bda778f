/**
 *  @module Mixin:Pay - depends on PayPrimitives mixin
 * 		1. event handling
 * 		2. endpoints used by Payment mixin (remotely)
 * 		3. methods for Cardmaster callback APIs (PayApi mixin)
 */
const { Payments, Persons, Orders, Touchpoints } = require('@crm/types'),
	{ MOrder, buildResponseToApp, updateBillingWithTransaction, updatePaymentWithTransaction, billingsToPayments } = require('@perkd/commerce'),
	{ statusOf } = require('@perkd/orders'),
	{ ORDER: ORDER_ERR, PAYMENT: PAYMENT_ERR } = require('@perkd/errors/dist/service')

const { PAID, AUTHORIZED, CANCELLED, FAILED } = Payments.Status,
	{ CRM } = Touchpoints.Type,
	{ SYSTEM } = Touchpoints.Attributed,
	{ PERKD, USER } = Persons.Identities,
	{ ORDER_INVALID } = ORDER_ERR,
	{ PAYMENT_CANCELED } = PAYMENT_ERR,
	{ PENDING } = Orders.Status,
	PREFIX = 'order'

module.exports = function(Business) {

	// -----  Static methods  -----

	Business.handlePaymentEvent = async function(transaction = {}) {
		try {
			const business = await Business.merchantOf()
			await business.payCommit(transaction)
		}
		catch (error) {
			appNotify('[handlePaymentEvent]', { error, transaction })
		}
	}

	/**
	 * Create Order							(used by Buy mixin as well)
	 * @param	{MOrder} mOrder (mutated)
	 * @param	{Object[]} payments - list of payments
	 * @return	{Order}
	 */
	Business.orderCreate = async function(mOrder, payments) {
		const { Order, Membership, Person } = Business.app.models,
			{ userId, options } = mOrder,
			{ cardId } = options,
			membership = cardId
				? await Membership.findLastJoinByCardId(cardId)
				: undefined,
			person = membership
				? { id: membership.personId }
				: await Person.findOneByIdentity(PERKD, USER, userId),
			{ id: personId } = person || {},
			data = mOrder.buildOrder(payments, membership, personId)

		return Order.createOrder(data)
	}

	// -----  Instance methods  -----

	/**
	 * Make payment to business (intent, source or charge)
	 * @param	{String} userId - perkd personId
	 * @param	{Object[]} payments
	 * 			{String} method - card, applepay, googlepay, alipay, linepay, grabpay, storedvalue etc.
	 * 			{String} provider - stripe, mypay etc.
	 * 			{Object} [intent] - { paymentMethodId: pm_1Ic06yLFbSgjvNHiKbbWK2oj, customerId, sourceId, destinationId }
	 * 			{Object} [source] - { id: tok_1IdF57AmEFR5egjBafcs6vXL }
	 * @param	{MOrder} order (mini) order
	 * 			{String} id - if paying for an existing order OR use id to create new order
	 * @param	{Object[]} pricings  (@perkd/commerce)
	 * @param	{Object} options
	 * 			{Object} through
	 * 			{String} description
	 * 			{Boolean} capture - used by manual payment
	 * 							true => staff already authorized payment via QR-Code
	 * 							false => need staff authorization/capture later (with widget)
	 * 			{Object} reservation - { id: '', queueNumber: 123, referenceId: '' }
	 * 			{String} masterId - where applicable
	 * 			{String} cardId - where applicable
	 * @return	{Object} { payment: { intent, source, pendingId, expiresAt }, fulfilled: [] } - fulfilled omitted if payment_pending
	 */
	Business.prototype.pay = async function(userId, payments, order, pricings, options) {
		const { PENDING } = Payments.Status

		order.sourceType = Business.name
		options.skipItems = true

		return Business.queue(PREFIX + userId, async () => {
			const { Event } = Business.app,
				{ Order } = Business.app.models

			let orderInstance

			if (order.id) {
				orderInstance = await Order.findById(order.id)

				if (orderInstance) {
					const { itemList: items, currency, amount } = orderInstance.toJSON()
					if (statusOf(orderInstance) !== PENDING) {
						return this.rejectErr(ORDER_INVALID, { err: 'Not pending order' }, true)
					}
					Object.assign(order, { items, currency, amount })
				}
			}

			const mOrder = new MOrder(order, pricings, payments, userId, options),
				valid = mOrder.validate()

			if (valid !== true) {
				return this.rejectErr(ORDER_INVALID, { err: valid }, true)
			}

			const parts = [ ...mOrder.payments ],
				last = parts.pop(),
				authorized = []		// list of [ payment, transaction ]

			try {
				// 1. authorize inital parts
				for (const payment of parts) {
					const { method, request, details: det, options: opt } = mOrder.buildPaymentRequest(payment),
						transaction = await Business.paymentAuthorize(method, request, det, opt)

					updatePaymentWithTransaction(payment, transaction)
					authorized.push([ payment, transaction ])
				}

				// 2. auto-capture last part
				const { method, request, details: det, options: opt } = mOrder.buildPaymentRequest(last),
					lastTransaction = await Business.paymentRequest(method, request, det, opt),
					{ status } = lastTransaction || {}

				updatePaymentWithTransaction(last, lastTransaction)

				if (status === PAID) {
					// 3. capture intial parts (non-pending)
					for (const [ payment, transaction ] of authorized) {
						const captured = await Business.paymentCapture(transaction)
						updatePaymentWithTransaction(payment, captured)
					}
				}

				// 4. update/create order, automatically caters for pending/async payment methods
				const updatedPayments = [ ...parts, last ]
				if (orderInstance) {
					const billingList = mOrder.buildBilling(updatedPayments)
					if (status === PAID) {
						await orderInstance.markPaid({ billingList })
					}
					else {
						await orderInstance.updateAttributes({ billingList })
					}
				}
				else {
					orderInstance = await Business.orderCreate(mOrder, updatedPayments)
				}

				// 5. emit appropriate events based on payment status
				if (status === PAID) {
					appEmit(Event.business.pay.paid, orderInstance)
				}
				else if (status === AUTHORIZED) {
					appEmit(Event.business.pay.authorized, orderInstance)
				}

				return buildResponseToApp(mOrder, last, lastTransaction)
			}
			catch (err) {
				const { message, statusCode } = err || {}

				// For server errors with authorized payments, still create the order
				// This ensures we don't lose orders for asynchronous payments
				if ((message?.startsWith('Unhandled error') || statusCode === 500) && parts.length && authorized.length) {
					appNotify('[pay]server-error', { err, parts, last, authorized })

					const updatedPayments = [ ...parts, last ].filter(Boolean)
					await Business.orderCreate(mOrder, updatedPayments)
					throw err
				}

				appNotify('[pay]rollback', { err, parts, last, authorized })

				for (const [ _, transaction ] of authorized) {
					await this.paymentCancel(transaction).catch(() => null)	// suppress error
				}

				appEmit(Event.business.pay.failed, mOrder.buildOrder())
				throw err
			}
		})
	}

	/**
	 * Commit (pending) Payment created earlier with pay()
	 * 		Note: triggered via events (handlePaymentEvent) & app API (Business.payCommit)
	 * @param   {Object} transaction
	 *		  	{String} type
	 *		  	{String} method
	 *		  	{String} referenceId
	 *		  	{String} status
	 *		  	{String} provider
	 *		  	{Number} amount - for overriding authorized amount
	 *		  	{Object} details { id }
	 * @return	{Order|void} updated order
	 */
	Business.prototype.payCommit = async function(transaction) {
		const { app, name } = Business,
			{ models, Event, service } = app,
			{ Order } = models,
			serviceName = service.name.toLowerCase(),
			{ status, referenceId, amount } = transaction

		if (!referenceId) throw new Error('[payCommit] referenceId missing')

		return Business.queue(PREFIX + referenceId, async () => {
			const order = await Order.findByTransaction(referenceId, name),
				{ id, billingList } = order || {},
				NOW = new Date()

			if (!order) return undefined		// not ours
			if (order.status !== PENDING) {
				// already committed due to race condition between app & provider event
				const payments = billingsToPayments(billingList)
				return buildResponseToApp({ id, payments }, {}, transaction)
			}

			switch (status) {
			case PAID:
			case AUTHORIZED: {
				if (status === PAID) {
					updateBillingWithTransaction(billingList, transaction)
				}

				// capture any pending payments
				const captures = []

				for (const billing of billingList) {
					const { paymentMethod } = billing,
						{ status, transactions = [] } = paymentMethod,
						[ latest ] = transactions

					if (latest && status === AUTHORIZED) {
						if (amount) {
							latest.amount = amount		// override initial authorized
						}

						captures.push(
							this.paymentCapture(latest).then(captured => {
								updateBillingWithTransaction(billingList, captured)
							})
						)
					}
				}
				await Promise.all(captures)

				const { itemList } = order,
					payments = billingsToPayments(billingList)

				await order.markPaid({ itemList, billingList })

				appEmit(Event.business.pay.paid, order)
				return buildResponseToApp({ id, payments }, {}, transaction)
			}

			case FAILED: {
				const failedTransaction = await this.paymentCancel(transaction)

				failedTransaction.status = FAILED
				updateBillingWithTransaction(billingList, failedTransaction)
				await order.updateAttributes({ billingList })

				appEmit(Event.business.pay.failed, order)
				return order
			}

			case CANCELLED: {
				const through = {
						type: CRM,
						attributedTo: { type: SYSTEM, name: serviceName },
						touchedAt: NOW
					},
					cancelOptions = {
						reason: PAYMENT_CANCELED,
						cancelledAt: NOW,
						touchedAt: NOW,
						through
					}

				updateBillingWithTransaction(billingList, transaction)
				await order.updateAttributes({ billingList })
				await order.cancel(cancelOptions)

				appEmit(Event.business.pay.failed, order)
				return order
			}

			default:
				throw new Error(`invalid status: '${status}'`)
			}
		})
			.catch(err => {
				// note: paymentCapture() throws transaction object with error property
				appNotify('[payCommit] error:', { err, transaction }, 'error')
				throw err
			})
	}

	/**
	 * Cancel (pending) Order, created earlier		(used by Payment mixin)
	 * @param	{Object} transaction
	 * 			{String} method
	 * 			{String} referenceId
	 * @param	{Object} options
	 *			{String} reason
	 * @return	{Order} cancelled order
	 */
	Business.prototype.payCancel = async function(transaction, options) {
		const { Order } = Business.app.models,
			{ referenceId } = transaction

		return Order.cancelByTransaction(referenceId, options)
	}
}
