/**
 *  @module Mixin:Provision - level 2
 */
const { Context } = require('@perkd/multitenant-context'),
	{ Providers, Settings, Programs, Memberships, Payments, Wallet, OfferMasters, Places, Fulfillments } = require('@crm/types'),
	{ getSetting, getFulfillment, StoredValues } = require('@perkd/provisions'),
	{ Currencies } = require('@perkd/utils'),
	{ validRoles, widgetsFor } = require('@perkd/accesscontrol'),
	{ getWidgets } = require('@perkd/wallet-widgets'),
	{ HttpErrors } = require('@perkd/errors')

const { PERKDPAY, FLEXM } = Providers.PROVIDER,
	{ LOCALE, FULFILLMENT } = Settings.Name,
	{ STOREDVALUE } = Payments.Method,
	{ GIFT_CARD, STAFF } = Programs.Type,
	{ JOIN } = Memberships.Qualifier,
	{ StoredValueKind } = Wallet.CardMasters,
	{ STYLE } = StoredValues,
	{ TOPUP, PRELOAD } = StoredValues.Type,
	{ PICKUP } = OfferMasters.Kind,
	{ SCAN } = OfferMasters.Authorize,
	{ VENDING_MACHINE } = Places.Type

module.exports = function(Provision) {

	/**
	 * Create Printer associated with place		(dependencies: business, place & print provider)
	 * @param {String} placeId
	 * @param {String} [name]
	 * @param {String} [purpose]
	 * @param {String} credentials - qr-code on printer: <serialNo>:<key>
	 */
	Provision.printer = async function (placeId, name, purpose, credentials) {
		const { Place } = Provision.app.models

		return Place.provisionPrinter(placeId, name, purpose, credentials)
	}

	/**
	 * Pickup (fulfillment)						(dependencies: business)
	 * @param {String} [ticketTemplate]
	 * @param {String} [type] - store, vending, convenience_store
	 * @param {Object} [custom]
	 *			{String[]} countries
	 *			{Object} pricing - { lookup, zones }
	 *			{Object} hours - { specific, periods }
	 *			{Boolean} prescheduled
	 *			{Number} leadTime
	 *			{Number} timeslot
	 *			{Object[]} [include] - { id, name, locationCode, hours, timeslot }
	 *			{String[]} [excludePlaces] - placeIds
	 *			{Object} [availability]
	 *				{Boolean} check
	 *			{String} [lookup]
	 *			{String[]} [images] - icon/logo of service
	 *			{Object} [form] - definition of form for data collection
	 */
	Provision.pickup = async function (ticketTemplate, type, custom) {
		const { app } = Provision,
			{ Business } = app.models,
			{ PICKUP } = Fulfillments.Type,
			{ country } = app.getSettings(LOCALE),
			fulfillment = app.getSettings(FULFILLMENT),
			{ pickup = getSetting(country, PICKUP) } = fulfillment,
			presets = getFulfillment(country, PICKUP)

		if (fulfillment.pickup?.[type]) throw HttpErrors.BadRequest(`Already provisioned '${type}'`)

		if (ticketTemplate) pickup.ticketTemplate = ticketTemplate
		if (type) {
			pickup[type] ||= []
			pickup[type].push({ ...presets, ...custom, type })
		}

		await Business.updateSetting(FULFILLMENT, { pickup }, true)
	}

	/**
	 * Deliver (fulfillment)					(dependencies: business)
	 * @param {String} [ticketTemplate]
	 * @param {Object} [custom]
	 */
	Provision.deliver = async function(ticketTemplate, custom) {
		const { app } = Provision,
			{ Business } = app.models,
			{ DELIVER } = Fulfillments.Type,
			{ country } = app.getSettings(LOCALE),
			fulfillment = app.getSettings(FULFILLMENT),
			{ deliver = getSetting(country, DELIVER) } = fulfillment,
			presets = getFulfillment(country, DELIVER)

		if (fulfillment.deliver) throw HttpErrors.BadRequest('Already provisioned')

		if (ticketTemplate) deliver.ticketTemplate = ticketTemplate
		Object.assign(deliver, presets, custom)

		await Business.updateSetting(FULFILLMENT, { deliver }, true)
	}

	/**
	 * Store (fulfillment)						(dependencies: business)
	 * @param {String} [ticketTemplate]
	 * @param {Object} [custom]
	 */
	Provision.store = async function (ticketTemplate, custom) {
		const { app } = Provision,
			{ Business } = app.models,
			{ STORE } = Fulfillments.Type,
			{ country } = app.getSettings(LOCALE),
			fulfillment = app.getSettings(FULFILLMENT),
			{ store = getSetting(country, STORE) } = fulfillment,
			presets = getFulfillment(country, STORE)

		if (fulfillment.store) throw HttpErrors.BadRequest('Already provisioned')

		if (ticketTemplate) store.ticketTemplate = ticketTemplate
		Object.assign(store, presets, custom)

		await Business.updateSetting(FULFILLMENT, { store }, true)
	}

	/**
	 * Enable Kitchen (fulfillment)				(dependencies: business)
	 * @param {String} [ticketTemplate]
	 * @param {Number} [maxScheduleWindow]
	 * @param {Number} [prepareLookahead]
	 * @param {Number} [minTimer]
	 * @param {Boolean} [autoPrepare]
	 * @param {Boolean} [autoPack]
	 */
	Provision.kitchen = async function (ticketTemplate, maxScheduleWindow, prepareLookahead, minTimer, autoPrepare, autoPack) {
		const { app } = Provision,
			{ Business } = app.models,
			{ KITCHEN } = Fulfillments.Type,
			{ country } = app.getSettings(LOCALE),
			kitchen = getSetting(country, KITCHEN),
			settings = app.getSettings(FULFILLMENT) || {}

		if (settings.kitchen) throw HttpErrors.BadRequest('Already provisioned')

		if (ticketTemplate) kitchen.ticketTemplate = ticketTemplate
		if (maxScheduleWindow !== undefined) kitchen.maxScheduleWindow = maxScheduleWindow
		if (prepareLookahead !== undefined) kitchen.prepareLookahead = prepareLookahead
		if (minTimer !== undefined) kitchen.minTimer = minTimer
		if (autoPrepare !== undefined) kitchen.autoPrepare = autoPrepare
		if (autoPack !== undefined) kitchen.autoPack = autoPack

		await Business.updateSetting(FULFILLMENT, { kitchen }, true)
	}

	/**
	 * Provision Vending (fulfillment)			(dependencies: business)
	 * @param {String} [businessId]
	 */
	Provision.vending = async function (businessId) {
		const { app } = this,
			{ Business, OfferMaster } = app.models,
			{ VENDING } = Fulfillments.Type,
			{ country } = app.getSettings(LOCALE),
			settings = app.getSettings(FULFILLMENT),
			vending = getSetting(country, VENDING),
			{ pickupOfferMasterId } = settings.vending ?? {}

		if (pickupOfferMasterId) throw HttpErrors.BadRequest('Already provisioned')

		const data = {
				kind: PICKUP,
				name: 'For pickup at vending machine',
				shortName: 'Vending',
				items: [],
				redemption: { authorize: SCAN },
				options: {
					skipCode: true,
					redeemOnline: false,
					appOnly: false,
				},
				visible: false,
				tags: { system: [ VENDING_MACHINE ] }
			},
			{ id } = await OfferMaster.create(data)

		vending.pickupOfferMasterId = String(id)
		await Business.updateSetting(FULFILLMENT, { vending }, true)
	}

	/**
	 * Order provider for place 			(dependencies: business & place)
	 */
	Provision.orderProviderForPlace = async function (placeId, shop, name, credentials, liveMode, enabled, services, modules, options) {
		const { Place } = Provision.app.models,
			place = await Place.findById(placeId),
			updates = {
				external: { [name]: { shop } }
			}

		if (!place) throw HttpErrors.NotFound('Place not found')

		await this.createProvider(name, credentials, liveMode, enabled, services, modules, options, shop)
		await place.updateAttributes(updates)
	}

	/**
	 * Provision Stored Value for a Tier of Program		(dependencies: program)
	 * 		Gift card - must have preload
	 * 		Stored value - either topup, preload or both
	 * 	1. provision StoredValue payment (if not already)
	 * 	2. create Product & Variants
	 * 	3. update storedValue (config) of program
	 * @param {String} programId
	 * @param {Number} level of tier
	 * @param {Object} provider
	 *			{String} name
	 *			{Object} credentials - provider specific
	 *			{Object} options - provider specific
	 *			{Object} external - provider specific, eg. FlexM giftCardId
	 * @param {Object} topup
	 * 			{Number[]} denominations - eg. [ 10, 25, 50 ]
	 * 			{Object} variable - { min, max, increment }
	 * @param {Object} preload
	 * 			{Number[]} denominations - eg. [ 10, 25, 50 ]
	 * @param {Object} [currency] { code, symbol, precision, exchangeRate }
	 * @param {Object} [style] { color, format, fontSize }
	 * @param {Boolean} [singleUse] - giftcard only
	 * @param {Boolean} [sharedOnly] - giftcard only
	 */
	Provision.storedValue = async function (programId, level, provider, topup, preload, currency, style = {}, singleUse, sharedOnly) {
		const { Business, Program, Gateway, Product } = Provision.app.models,
			{ name = PERKDPAY, credentials, options, external } = provider,
			program = await Program.findById(programId),
			{ type, businessId } = program ?? {}

		// validations
		if (!program) throw HttpErrors.NotFound('Program not found')
		if (type === GIFT_CARD && !preload) throw HttpErrors.BadRequest("Must provide 'preload' for Gift Card")

		const [ exists, business ] = await Promise.all([
			Gateway.getPayment(businessId, STOREDVALUE).catch(() => null),
			Business.findById(businessId)
		])

		if (!exists) {		// provision payment
			const provisioned = await this.paymentProvider(businessId, name, credentials, options, [ STOREDVALUE ]),
				[ payment ] = provisioned

			if (payment !== STOREDVALUE) throw HttpErrors.InternalServerError('Payment not provisioned')
		}

		const { locale = {} } = business,
			{ currency: cod } = locale,
			{ precision: pre, symbol: sym } = Currencies.currency(cod),
			{ code = cod, precision = pre, symbol = sym, exchangeRate } = currency ?? {},
			currencyParams = { code, precision, symbol, exchangeRate },
			storedValue = {
				name: `${Context.tenant}-svf`,
				enabled: true,
				currency: { code, precision },
				style: { ...STYLE, ...style }
			}

		// create product & variants
		if (topup) {
			const { denominations = [], variable } = topup
			await Product.provisionStoredValue(TOPUP, currencyParams, denominations, variable)
		}
		if (preload) {
			const { denominations } = preload,
				list = await Product.provisionStoredValue(PRELOAD, currencyParams, denominations),
				products = list.map(p => ({ ...p, qualifier: JOIN }))

			await program.__updateById__tiers(level, { products })
		}

		// update storedValue (config) of program
		if (!program.storedValue) {
			if (type === GIFT_CARD) {
				storedValue.kind = StoredValueKind.GIFT_CARD
				storedValue.external = external
				storedValue.singleUse = singleUse
				storedValue.sharedOnly = sharedOnly
			}
			else {
				storedValue.kind = StoredValueKind.STORE_CREDITS
			}

			await program.updateAttributes({ storedValue })
		}

		return { program }
	}

	/**
	 * Provision Roles for a Tier of Staff Program			(dependencies: program)
	 * 	1. update digitalCard (config) of tier
	 * @param {String} programId
	 * @param {Number} level
	 * @param {String[]} roles - staff with ANY of the roles qualifies for this tier
	 */
	Provision.staffRoles = async function (programId, level, roles) {
		const { Program } = Provision.app.models,
			keys = widgetsFor(roles),
			widgets = getWidgets(keys),
			program = await Program.findById(programId),
			{ type, tierList = [], businessId } = program ?? {},
			tier = tierList.find(t => t.level === level),
			{ digitalCard = {} } = tier ?? {}

		// validation
		if (!program) throw HttpErrors.NotFound('Program not found')
		if (!tier) throw HttpErrors.NotFound('Tier not found')

		if (type !== STAFF) throw HttpErrors.BadRequest('Not staff program')
		if (digitalCard.widgets?.length) throw HttpErrors.BadRequest('Already provisioned')

		digitalCard.widgets = widgets
		await program.__updateById__tiers(level, { roles, digitalCard })
	}

	// ----  Level-3  (after Level-2, executed in Tenant account)  ----

	// -----  Validations  -----

	Provision.beforeRemote('storedValue', async ({ args = {} }) => {
		const { topup, preload, provider = {} } = args,
			{ name, credentials } = provider,
			{ denominations: tDenom, variable } = topup ?? {},
			{ denominations: pDenom } = preload ?? {}

		if (!topup && !preload) throw HttpErrors.BadRequest("Either 'topup' or 'preload' must be provided")
		if (topup && !tDenom && !variable) throw HttpErrors.BadRequest("Either 'denomination' or 'variable' must be in 'topup'")
		if (preload && !pDenom) throw HttpErrors.BadRequest("Either 'denomination' must be in 'preload'")

		if (!name) throw HttpErrors.BadRequest('Provider name missing')
		if (!credentials) throw HttpErrors.BadRequest('Provider credentials missing')

		if (![ PERKDPAY, FLEXM ].includes(name)) throw HttpErrors.BadRequest('Provider not supported')
	})

	Provision.beforeRemote('staffRoles', async ({ args = {} }) => {
		const { roles = [] } = args

		if (!roles.length) throw HttpErrors.BadRequest('At least one role')
		if (!validRoles(roles)) throw HttpErrors.BadRequest('Invalid roles')
	})
}
