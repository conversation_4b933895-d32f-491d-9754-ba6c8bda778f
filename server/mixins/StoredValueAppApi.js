/**
 *  @module Mixin:StoredValueAppApi		(used by Shopify StoredValue app)
 */

module.exports = function(Staff) {

	/**
	 * Summary metrics as at now
	 * @return	{Object} { totalBalance, customers, averageBalance, averageDiscount, currency }
	 */
	Staff.storedValuesSummary = async function() {
		const { Payment } = Staff.app.models
		return Payment.storedValuesMetricsSummary()
	}

	/**
	 * Transaction metrics for time period
	 * @param	{Date} from
	 * @param	{Date} to
	 * @return	{Object} { revenue, activeCustomers, averageDiscount, currency,
	 * 						reloads, reloaded, avgReloaded, redeem, redeemed, avgRedeemed }
	 */
	Staff.storedValuesSummaryTransactions = async function(from, to) {
		const { Payment } = Staff.app.models
		return Payment.storedValuesMetricsTransactions(from, to)
	}

	/**
	 * Transactions for time period
	 * @param	{Date} from
	 * @param	{Date} to
	 * @return	{Object}
	 */
	Staff.storedValuesTransactions = async function(from, to) {
		const { Payment } = Staff.app.models
		return Payment.storedValuesTransactions(from, to)
	}

	// -----  Remote Methods  -----

	Staff.remoteMethod('storedValuesSummary', {
		description: 'Stored Value Summary (Shopify StoredValue app)',
		http: { path: '/shopify/storedvalues/summary', verb: 'get' },
		accepts: [
		],
		returns: { type: 'object', root: true },
	})

	Staff.remoteMethod('storedValuesSummaryTransactions', {
		description: 'Stored Value Transactions Summary (Shopify StoredValue app)',
		http: { path: '/shopify/storedvalues/summary/transactions', verb: 'get' },
		accepts: [
			{ arg: 'from', type: 'date', http: { source: 'query' }, required: true },
			{ arg: 'to', type: 'date', http: { source: 'query' }, required: true },
		],
		returns: { type: 'object', root: true },
	})

	Staff.remoteMethod('storedValuesTransactions', {
		description: 'Stored Value Transactions (Shopify StoredValue app)',
		http: { path: '/shopify/storedvalues/transactions', verb: 'get' },
		accepts: [
			{ arg: 'from', type: 'date', http: { source: 'query' }, required: true },
			{ arg: 'to', type: 'date', http: { source: 'query' }, required: true },
		],
		returns: { type: 'object', root: true },
	})
}
