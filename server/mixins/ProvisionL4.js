/**
 *  @module Mixin:Provision - level 4
 */
const { Settings } = require('@crm/types'),
	{ getAction } = require('@perkd/provisions'),
	{ HttpErrors } = require('@perkd/errors')

const { ACTION } = Settings.Name,
	{ ActionKey } = Settings

module.exports = function(Provision) {

	/**
	 * Provision preset Action
	 * @param {String} key - ActionKey
	 * @param {Object} data
	 * @returns {Object} action id
	 */
	Provision.action = async function (key, data) {
		const { app } = Provision,
			{ Action, Business } = app.models,
			settings = app.getSettings(ACTION),
			preset = getAction(key, data)

		if (settings[key]) throw HttpErrors.BadRequest('Already provisioned')
		if (!Object.values(ActionKey).includes(key)) throw HttpErrors.BadRequest(`Invalid key ('${key}')`)
		if (!preset) throw HttpErrors.NotFound(`Action key ('${key}') not found`)

		const { id } = await Action.create(preset),
			update = { [key]: id }

		await Business.updateSetting(ACTION, update, true)
		return { id }
	}
}
