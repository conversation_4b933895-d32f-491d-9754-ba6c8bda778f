/**
 *  @module Mixin:Access
 */

const { Context } = require('@perkd/multitenant-context'),
	{ Users } = require('@crm/types')

const { SYSTEM } = Users.Type,
	DENY = 'DENY'

module.exports = function(Model, options) {
	// reference: https://loopback.io/doc/en/lb3/Remote-hooks.html

	// -----  Private functions  -----

	function hasACL(method) {
		const { acls = [] } = Model.definition.settings

		acls.forEach(acl => {
			if (!Array.isArray(acl.property)) acl.property = [ acl.property ]
			if (acl.property.indexOf(method) > -1) {
				if (acl.permission === DENY) return true
			}
		})
		return false
	}

	// -----  Remote Hooks  -----
	// execute before or after calling a remote method, either a custom remote method  or
	// a standard create, retrieve, update, and delete method inherited from PersistedModel methodName: *.save

	Model.afterRemote('findById', async (ctx, modelInstance) => {
		const { user } = Context,
			{ username } = user ?? {}

		if (hasACL('findById') && (username !== SYSTEM) && !modelInstance.visible) {
			const error = new Error('Authorization Required')
			error.code = 'AUTHORIZATION_REQUIRED'
			return Promise.reject(error)
		}
	})

	Model.afterRemote('find', async (ctx, modelInstance) => {
		const { result } = ctx,
			{ user } = Context,
			{ username } = user ?? {}

		if (hasACL('find') && (username !== SYSTEM) && result) {
			ctx.result = result.filter(({ visible }) => visible)
		}
	})
}
