/**
 *  @module Mixin:MarketPlace - Provision Merchant
 */
const { <PERSON>ting<PERSON>, Staffs, Contacts, Touchpoints, Products } = require('@crm/types'),
	{ Context } = require('@perkd/multitenant-context'),
	{ formData } = require('@perkd/utils'),
	{ HttpErrors } = require('@perkd/errors')

const { OWNER } = Staffs.Title,
	{ MOBILE, WORK } = Contacts.Type,
	{ CONTRACT_START, CONTRACT_END } = Contacts.Dates,
	{ STORE } = Touchpoints.Type,
	{ STAFF_CARD } = Touchpoints.Format,
	{ STAFF } = Touchpoints.Attributed,
	{ MENU } = Products.Channel,
	{ ORDER, FULFILLMENT, MARKETPLACE } = Settings.Name,
	{ DEFAULT } = Settings,
	UNNAMED = 'unnamed'

module.exports = function(Provision) {

	/**
	 * Create (placeholder) Place, Settings & Numbering to be associated with Business later
	 */
	Provision.provisionPlace = async function () {
		const { app } = Provision,
			{ Event, models } = app,
			{ Place } = models,
			{ numbering = DEFAULT.ORDER.numbering } = app.getSettings(ORDER),
			{ kitchen = {} } = app.getSettings(FULFILLMENT),
			{ autoPrepare, autoPack } = kitchen,
			settings = {
				name: FULFILLMENT,
				value: { kitchen: { autoPrepare, autoPack } }
			},
			place = await Place.create({ name: UNNAMED, visible: false }),
			{ id } = place

		await Promise.all([
			(autoPrepare || autoPack) && place.__create__settings(settings),
			place.__create__queueNumbers(numbering)
		])

		appEmit(Event.place.provision.started, place)
		return id
	}

	/**
	 * Create Business associated with place (dual mode: form-data & json)
	 * @param {String} placeId
	 * @param {IncomingMessage} req - form-data (fields & files)
	 * @param {Object} [data] - json - file upload not supported
	 *			{String} name - registered name of business
	 *			{String} registration - number
	 *			{String} address
	 *			{Object} owner
	 *				{String} name
	 *				{Object} mobile - { countryCode, number }
	 *			{Object} contract - { startTime, endTime }
	 *			{Object} profile - { brand, email }
	 */
	Provision.provisionMerchant = async function (placeId, req, data) {
		const { app } = Provision,
			{ Event, models } = app,
			{ Business, Place, Member, shopify } = models,
			{ body, files } = await formData(req) ?? {},
			{ name, registration, address, owner, contract, profile = {} } = body || data || {},
			{ webMenu } = app.getSettings(ORDER),
			{ staff = {}, card = {} } = Context.appContext,
			{ id: staffId, userId, userName } = staff,
			{ mobile = {}, name: ownerName } = owner ?? {},
			{ countryCode, number } = mobile,
			{ startTime, endTime } = contract ?? {},
			start = new Date(startTime),
			end = new Date(endTime),
			tags = webMenu ? { system: [ MENU ], user: [] } : undefined,
			[ place, main ] = await Promise.all([
				Place.findById(placeId),
				Business.getMain()
			]),
			{ name: placeName, ownerId } = place ?? {},
			{ locale, urls } = main.toJSON(),
			person = {
				familyName: '',
				givenName: ownerName,
				mobile: `${countryCode}${number}`
			},
			through = {
				type: STORE,
				format: STAFF_CARD,
				location: {
					type: STORE,
					id: placeId,
					name: placeName
				},
				attributedTo: {
					type: STAFF,
					id: staffId,
					userId,
					userName
				},
				instrument: {
					type: STAFF_CARD,
					id: card.id
				},
				touchedAt: new Date(),
			}

		if (!place) throw HttpErrors.NotFound(`Place not found (id: ${placeId})`)
		if (ownerId) throw HttpErrors.BadRequest(`Already provisioned (id: ${ownerId})`)

		if (!startTime || !endTime) throw HttpErrors.BadRequest('Contract start or end time absent')
		if (end <= new Date()) throw HttpErrors.BadRequest('Contract end time passed')
		if (end <= start) throw HttpErrors.BadRequest('Contract period not valid')

		if (!mobile || !countryCode || !number) throw HttpErrors.BadRequest('Owner\'s mobile number mandatory')

		const member = await Member.createWithPerson(person, through),
			{ personId } = member

		owner.id = personId
		profile.locale = locale
		profile.urls = urls

		const business = await Provision.createBusiness({ name, registration, address, owner, contract, profile, tags }, req, files),
			{ id: businessId, brand = {} } = business ?? {},
			{ short } = brand

		// MUST be sequential, staffCardIssue() depends on place
		await this.provisionUpdatePlace(place, businessId, short, address, owner, tags)
		await this.provisionIssueOwnerCard(place, businessId, owner, person, startTime, endTime, through)

		shopify.upsertSmartCollection(business)
			.catch(err => {
				appNotify('[upsertSmartCollection]', { err, businessId: business.id })
			})

		appEmit(Event.business.provision.started, place)
	}

	/**
	 * Activate Place after final check on status
	 * 	- considered activated when startTime and endTime are set
	 */
	Provision.provisionActivate = async function (placeId) {
		const { app } = Provision,
			{ Event, models } = app,
			{ Place } = models,
			{ business, contract, staffs, printers } = await Provision.getMerchantByPlaceId(placeId),
			{ activated } = business,
			{ startTime, endTime } = contract

		if (activated) throw HttpErrors.BadRequest('Already activated')
		if (!startTime || !endTime) throw HttpErrors.BadRequest('Contract period invalid')
		if (!staffs.length) throw HttpErrors.BadRequest('Staff not found')
		if (!printers.length) throw HttpErrors.BadRequest('Printer not found')

		const place = await Place.updateOrCreate({ id: placeId, startTime, endTime })

		appEmit(Event.place.provision.activated, { place, business })
	}

	/**
	 * Get provision status of Place
	 */
	Provision.getMerchantByPlaceId = async function (placeId) {
		const { Business, Place, Staff } = Provision.app.models,
			place = await Place.findById(placeId),
			{ ownerId: businessId = null, startTime, endTime } = place ?? {},
			filter = {
				where: { businessId }
			}

		if (!place) throw HttpErrors.NotFound(`Place not found (id: ${placeId})`)
		if (!businessId) throw HttpErrors.NotFound('Not provisioned')

		const [ business, staffList, printerList ] = await Promise.all([
				Business.findById(businessId),
				Staff.find(filter),
				place.printers()
			]),
			[ persons, status ] = await Promise.all([
				Promise.all(
					staffList.map(s => s.person.get())
				),
				Promise.all(
					printerList.map(p => p.isOnline())
				)
			]),
			dates = business.dateList.toJSON(),
			contract = {
				startTime: dates.find(d => d.name === CONTRACT_START)?.date,
				endTime: dates.find(d => d.name === CONTRACT_END)?.date
			},
			staffs = staffList.map((s, ndx) => {
				const { givenName, fullName, phoneList = [] } = persons[ndx] ?? {}

				return {
					id: s.personId,
					title: s.title,
					fullName: fullName || givenName,
					phoneList
				}
			}),
			printers = printerList.map((p, ndx) => ({
				id: p.id,
				name: p.name,
				online: status[ndx]
			}))

		// activated when place.startTime = contract.startTime
		business.activated = !!startTime && !!contract.startTime
			&& (new Date(startTime).getTime() === contract.startTime.getTime())

		return { business, contract, staffs, printers }
	}

	// -----  Private Methods  -----

	Provision.provisionUpdatePlace = async function (place, businessId, name, address, owner, tags) {
		const { Place } = Provision.app.models,
			{ mobile = {} } = owner,
			{ countryCode, number } = mobile,
			work = {
				type: WORK,
				formatted: address,
				optIn: true,
			},
			phone = {
				type: MOBILE,
				countryCode,
				number,
				optIn: true
			},
			updates = {
				id: String(place.id),
				name,
				tags,
				unlisted: false,
				visible: true,
				phoneList: [ phone ],
				addressList: [ work ],
				ownerId: String(businessId),
			}

		return Place.doUpsert(updates)
	}

	Provision.provisionIssueOwnerCard = async function (place, businessId, owner, person, startTime, endTime, through) {
		const { app } = Provision,
			{ Member } = app.models,
			{ ownerProgram = {} } = app.getSettings(MARKETPLACE),
			{ id: programId, tierLevel } = ownerProgram,
			{ id: placeId } = place,
			{ id: personId } = owner,
			profile = { ...person, title: OWNER },
			options = {
				endTime: new Date(endTime),
				businessId,
				placeId,
				disableRevoke: true
			}

		return Member.staffCardIssue(programId, tierLevel, personId, profile, through, options)
	}

	// -----  Remote Methods  -----

	Provision.remoteMethod('provisionPlace', {
		description: 'Provision a new Place (Multi-merchant API)',
		http: { path: '/multimerchant/place', verb: 'post' },
		accepts: [
		],
		returns: { type: 'string', root: true },
	})

	Provision.remoteMethod('provisionMerchant', {
		description: 'Provision Merchant for Place Id [form data] (Multi-merchant API)',
		http: { path: '/multimerchant/place/:id/merchant', verb: 'post' },
		accepts: [
			{ arg: 'id', type: 'string', http: { source: 'path' }, required: true },
			{ arg: 'req', type: 'object', http: { source: 'req' } },
			{ arg: 'data', type: 'object', http: { source: 'body' }, description: 'for json, no file upload' },
		],
		returns: { type: 'object', root: true },
	})

	Provision.remoteMethod('provisionActivate', {
		description: 'Activate a new Place (Multi-merchant API)',
		http: { path: '/multimerchant/place/:id/activate', verb: 'post' },
		accepts: [
			{ arg: 'id', type: 'string', http: { source: 'path' }, required: true },
		],
		returns: { type: 'object', root: true },
	})

	Provision.remoteMethod('getMerchantByPlaceId', {
		description: 'Get Merchant by Place Id (Multi-merchant API)',
		http: { path: '/multimerchant/place/:id/merchant', verb: 'get' },
		accepts: [
			{ arg: 'id', type: 'string', http: { source: 'path' }, required: true },
		],
		returns: { type: 'object', root: true },
	})
}
