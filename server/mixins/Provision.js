/**
 *  @module Mixin:Provision - Level 0 & 1
 */
const { Context } = require('@perkd/multitenant-context'),
	{ Providers, Users, Apps, Settings } = require('@crm/types'),
	{ getLocale, getSetting, getCampaignTemplates, getProgram, getCommerce, getRoles, EMAILS } = require('@perkd/provisions'),
	{ generateTenantCode, Identities } = require('@perkd/utils'),
	{ Provider: ProviderRedis } = require('@perkd/providers'),
	{ HttpErrors } = require('@perkd/errors'),
	{ isBusinessRegNumber } = Identities

const { SUPER_ADMIN } = Users.Roles,
	{ SYSTEM } = Users.Type,
	{ CRMAdminApp } = Apps.Type,
	{ PAYMENT_PROVIDERS, ORDER_PROVIDERS, PRINT_PROVIDERS, FULFILLMENT_PROVIDERS } = Providers,
	{ LOCALE, SMS, EMAIL, PROVIDER, FULFILLMENT, PRINT } = Settings.Name,
	{ SYSTEM_USER } = EMAILS

module.exports = function(Provision) {

	/**
	 * Status of Provision
	 * 	1. Program: membership, staff, stored value
	 * 	2. Providers: payment, fulfillment, print, vending
	 * 	3. Commerce: shop, kitchen, booking/ticketing
	 * @param {String} [businessId]
	 */
	Provision.status = async function (businessId) {
		const { app } = this,
			{ Business, Program, Payment, Place } = app.models,
			business = businessId ? await Business.findById(businessId) : await Business.getMain()

		// validation
		if (!business) throw HttpErrors.NotFound('Business not found')

		const id = business.id.toString(),
			fulfillment = app.getSettings(FULFILLMENT),
			{ kitchen } = fulfillment,
			[ programs, payments, places ] = await Promise.all([
				Program.provisionStatus(id),
				Payment.provisionStatus(id),
				Place.provisionStatus(id)
			]),
			status = { programs, payments, places, kitchen: !!kitchen }

		return status
	}

	// ----  Level-0 (executed from G account)  ----

	/**
	 * Provision CRM Account:
	 * 	1. Account
	 * 	2. Admin User (business owner)
	 * 	3. Business (main) with logo image and core settings (locale, sms, email)
	 * 	4. Campaign templates (core)
	 * 	5. Payment Gateway (skeleton)
	 * 	6. Roles
	 * @param	{Object} profile
	 *				{String} name - registered name of business
	 *				{String} registration - number
	 *				{String} country
	 *				{String} address
	 *				{Boolean} [taxIncluded] - tax included in prices
	 *				{Object[]} [taxes] - { title, rate }
	 *				{String[]} [categories] - to be used for card discovery
	 * @param	{Object} owner
	 *				{String} name - of owner
	 *				{String} email
	 *				{Object} phone { countryCode, number }
	 * @param	{Object} [options]
	 *				{String} app - name of app to provision for, will generate & return app access token
	 *				{String} tenant - use this code instead of generating one
	 * @returns	{Object} account - { id, businessId, tenantCode, accessToken }
	 */
	Provision.account = async function (profile, owner, options = {}) {
		const { app } = Provision,
			{ Business, Campaign, Payment, role } = app.models,
			{ name, country, address }  = profile,
			{ tenant, app: appName = CRMAdminApp } = options,
			locale = getLocale(country, address),
			sms = getSetting(country, SMS),
			email = getSetting(country, EMAIL),
			print = getSetting(country, PRINT),
			templates = getCampaignTemplates(country),
			commerce = getCommerce(country),
			roles = getRoles(country),
			data = { ...commerce, ...profile },
			account = await this.createAccount(data, owner, locale, appName, tenant),
			{ id, tenantCode, businessId, appTokens } = account,
			accessToken = appTokens[appName],
			result = { id, tenantCode, businessId, accessToken }

		await Context.runAsTenant(tenantCode, async() => {
			await Business.addSetting({ name: LOCALE, value: locale })
			await Business.addSetting({ name: SMS, value: sms })
			await Business.addSetting({ name: EMAIL, value: email })
			await Business.addSetting({ name: PRINT, value: print })
			await Business.addSetting({ name: FULFILLMENT, value: {} })

			const provisions = [
				Business.findById(businessId),
				Campaign.provisionTemplates(templates),
				Payment.provision(name, businessId),
				Provision.createUploadLogoImage(businessId)
			]

			for (const name of roles) {
				provisions.push(role.create({ name }))
			}

			const [ main, { count }, gateway, logo ] = await Promise.all(provisions)
				.catch(err => {
					console.log('debug [Provision] - all', err)
				})

			result.campaignTemplates = count
			result.paymentGateway = !!gateway
			result.logo = !!logo
			await this.createPerkdBusiness(main, tenantCode)
				.catch(err => {
					console.log('debug [Provision] - perkd', err)
				})
		}, app.connectionManager)

		return result
	}

	/**
	 * Create CRM Account (using 'super_admin' privilege)
	 * @param	{Object} profile - { name, registration, country, address, categories, taxIncluded, taxes }
	 * @param	{Object} owner - { name, email, phone }
	 * @param	{Object} locale - { country, address }
	 * @param	{String} appName name of app to provision for
	 * @param	{String} [tenant] code
	 * @return	{Object} account { id, businessId, tenantCode, appTokens }
	 */
	Provision.createAccount = async function (profile, owner, locale, appName, tenant) {
		const { app } = Provision,
			{ Account } = app.models,
			{ name: businessName, address, registration, categories, taxIncluded, taxes } = profile,
			{ name, email, phone } = owner,
			{ countryCode, number } = phone,
			{ country } = locale,
			commerce = { taxIncluded, taxes },
			tags = { category: categories, system: [], user: [] },
			tenantCode = tenant || generateTenantCode(businessName, country),
			{ staffId } = Context.user ?? {},
			user = {
				email: SYSTEM_USER,
				username: SYSTEM,
				emailVerified: false,
				staffId,
			},
			options = {
				tenantCode,
				apps: [ appName ]
			},
			provision = async () => Account.provision(businessName, registration, address, email, locale, commerce, tags, options)

		return Context.runWithPrivilege(app, user, SUPER_ADMIN, provision, tenantCode)
	}

	// ----  Level-1  (executed in Tenant account)  ----

	/**
	 * Provision Payment Provider for business		(dependencies: business)
	 * @param {String} businessId
	 * @param {String} provider
	 * @param {Object} credentials - provider specific
	 * @param {Object} options - provider specific
	 * @param {String[]} [restrict] - limit to these payment types
	 * @return {String[]} - payment types provisioned
	 */
	Provision.paymentProvider = async function (businessId, provider, credentials, options, restrict) {
		const { Business, Payment } = Provision.app.models,
			{ profile, currency } = await this.getBusinessProfile(businessId),
			config = { provider, currency, credentials, options, profile },
			{ accountId, provisioned } = await Payment.provisionProvider(businessId, config, restrict),
			providers = new ProviderRedis(provider)

		await Promise.allSettled([
			providers.tenants.add(accountId, Context.tenant),
			Business.updateSetting(PROVIDER, { [provider]: true }, true)
		])
		await providers.end()
		return provisioned
	}

	/**
	 * Provision Providers					(dependencies: business)
	 * @param {String} name - provider key
	 * @param {Object} credentials
	 * @param {Boolean} liveMode
	 * @param {Boolean} enabled
	 * @param {String[]} [services]
	 * @param {Object[]} [modules]
	 * @param {Object} [options]
	 */
	Provision.orderProvider = async function (name, credentials, liveMode, enabled, services, modules, options) {
		return this.createProvider(name, credentials, liveMode, enabled, services, modules, options)
	}

	Provision.fulfillmentProvider = async function (name, credentials, liveMode, enabled, services, modules, options) {
		return this.createProvider(name, credentials, liveMode, enabled, services, modules, options)
	}

	Provision.printProvider = async function (name, credentials, liveMode, enabled, services, modules, options) {
		return this.createProvider(name, credentials, liveMode, enabled, services, modules, options)
	}

	Provision.invoiceProvider = async function (name, credentials, liveMode, enabled, services, modules, options) {
		return this.createProvider(name, credentials, liveMode, enabled, services, modules, options)
	}

	/**
	 * Provision Membership Program				(dependencies: business)
	 * 	- tags.category of business will be injected to program
	 * @param {String} businessId
	 * @param {String} type of program
	 * @param {String} name
	 * @param {String} shortName
	 * @param {Object[]} levels
	 *			{Number} level
	 *			{String} name
	 *			{String} description
	 *			{String} [type] - only for 'mixed'
	 *			{Object} [numbering]
	 *			{Object} [tenure]
	 *			{Object} [qualifiers]
	 *			{Object} [options]
	 * @param {Object} [data] - { key, policies, options, tags }
	 * @return {Program}
	 */
	Provision.program = async function (businessId, type, name, shortName, levels = [], data) {
		const { Program, Business } = Provision.app.models,
			business = await Business.findById(businessId),
			{ tags = {} } = business ?? {},
			{ category = [] } = tags,
			{ properties } = getProgram(type),
			program = { tags: { category }, ...properties, ...data, type, name, shortName, businessId },
			tierLevels = []

		if (!business) throw HttpErrors.BadRequest('Business does not exist')

		for (const { type: tierType = type, level, ...tier } of levels) {
			const { tiers = [] } = getProgram(tierType),
				defaults = tiers.find(t => t.level = level)

			tierLevels.push({ ...defaults, ...tier })
		}

		return Program.provision(type, program, tierLevels)
	}

	// -----  Validations  -----

	Provision.beforeRemote('account', async ({ args = {} }) => {
		const { profile, owner } = args,
			{ name: businessName, registration, country, address, categories = [] } = profile,
			{ name, email, phone } = owner,
			{ countryCode, number } = phone ?? {}

		if (!businessName || !registration) throw HttpErrors.BadRequest('Incomplete profile: name or registration missing')
		if (!isBusinessRegNumber(registration)) throw HttpErrors.BadRequest('Invalid registration number')

		if (!country || !address) throw HttpErrors.BadRequest('Incomplete profile: address or country missing')
		if (!name || !email || !phone) throw HttpErrors.BadRequest('Incomplete owner info')
		if (!countryCode || !number) throw HttpErrors.BadRequest('Invalid owner phone, countryCode or number missing')
		if (!countryCode || !number) throw HttpErrors.BadRequest('Invalid owner phone, countryCode or number missing')
		if (!categories.length) throw HttpErrors.BadRequest('Categories missing')
	})

	Provision.beforeRemote('paymentProvider', async ({ args = {} }) => {
		const { provider } = args
		if (!PAYMENT_PROVIDERS.includes(provider)) throw HttpErrors.BadRequest('Provider not supported')
	})

	Provision.beforeRemote('orderProvider', async ({ args = {} }) => {
		const { name } = args
		if (!ORDER_PROVIDERS.includes(name)) throw HttpErrors.BadRequest('Provider not supported')
	})

	Provision.beforeRemote('orderProviderForPlace', async ({ args = {} }) => {
		const { name } = args
		if (!ORDER_PROVIDERS.includes(name)) throw HttpErrors.BadRequest('Provider not supported')
	})

	Provision.beforeRemote('printProvider', async ({ args = {} }) => {
		const { name } = args
		if (!PRINT_PROVIDERS.includes(name)) throw HttpErrors.BadRequest('Provider not supported')
	})

	Provision.beforeRemote('fulfillmentProvider', async ({ args = {} }) => {
		const { name } = args
		if (!FULFILLMENT_PROVIDERS.includes(name)) throw HttpErrors.BadRequest('Provider not supported')
	})

	Provision.beforeRemote('program', async ({ args = {} }) => {
		const { data, levels } = args,
			PROPERTIES = [ 'key', 'policies', 'options', 'tags' ]

		if (data && Object.keys(data).some(p => !PROPERTIES.includes(p))) throw 'Invalid property in data'
		if (!levels.length) throw 'Must have a least one level'

		for (const { level, name, description } of levels) {
			if (!level) throw "Missing 'level'"
			if (!name || !description) throw `'name' & 'description' missing in level ${level}`
		}
	})
}
