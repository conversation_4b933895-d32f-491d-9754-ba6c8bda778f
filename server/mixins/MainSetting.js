/**
 *  @module Mixin:MainSetting - for Main Business
 */
const { Providers } = require('@crm/types'),
	{ Context } = require('@perkd/multitenant-context'),
	{ ScopeTypes, Roles } = require('@provider/google')

const { OWNER } = Roles,
	{ USER } = ScopeTypes

module.exports = function(Business) {

	Business.redisSyncSettings = async function() {
		const { Service } = Business.app.models,
			business = await Business.getMain(),
			{ tenant } = Context

		for (const setting of business.settings()) {
			await Service.SETTINGS.add(tenant, setting)
		}
	}

	Business.addSetting = async function(setting) {
		const { Service } = Business.app.models,
			business = await Business.getMain(),
			{ tenant } = Context,
			result = await business.settings.create(setting)

		await Service.SETTINGS.add(tenant, result.toJSON())
		return result
	}

	Business.getSettingsByName = async function(name) {
		const business = await Business.getMain()

		return business.getSettingsByName(name)
	}

	Business.removeSetting = async function(name) {
		const { Service } = Business.app.models,
			NAME = name.toLowerCase(),
			{ tenant } = Context,
			business = await Business.getMain()

		await business.removeSetting(NAME)
		await Service.SETTINGS.remove(tenant, NAME)
	}

	/**
	 * Override existing Setting (unless partial is true)
	 * @param {String} name
	 * @param {Object} value
	 * @param {Boolean} [partial] true => incremental update
	 */
	Business.updateSetting = async function(name, value, partial) {
		try {
			const { app } = Business,
				NAME = name.toLowerCase(),
				handler = Business[`settings${name.toUpperCase()}updated`],
				hasHandler = typeof handler === 'function',
				ctx = {}

			if (hasHandler || partial) {
				ctx.before = app.getSettings(NAME)
			}

			await Business.removeSetting(NAME)

			const updates = partial ? { ...ctx.before, ...value } : value,
				{ value: updated } = await Business.addSetting({ name, value: updates })

			if (hasHandler) {
				ctx.after = updated
				handler(ctx)
			}
			return updated
		}
		catch (err) {
			appNotify('[updateSetting]', { err, name })
		}
	}

	// -----  Post-update handlers  -----

	Business.settingsBOOKINGupdated = async function({ before, after }) {
		const { user: oldEmail } = before.calendar ?? {},
			{ user: newEmail } = after.calendar ?? {}

		if (newEmail === oldEmail) return	// no change in owner

		const { Resource } = Business.app.models,
			{ GOOGLE } = Providers.PROVIDER,
			[ google, resources ] = await Promise.all([
				Business.getProvider(GOOGLE),
				Resource.find()
			]),
			role = OWNER,
			scope = { type: USER, value: newEmail }

		for (const { calendarId } of resources) {
			if (!calendarId) continue
			await google.calendars.addAcl(calendarId, role, scope)
		}
	}

	// -----  Remote Methods  -----

	Business.remoteMethod('redisSyncSettings', {
		description: 'Add the whole settings list of the main business to redis',
		http: { path: '/settings/redis/sync', verb: 'post' },
		returns: { type: 'object', root: true },
	})

	Business.remoteMethod('addSetting', {
		description: 'Add a setting to the main business (sync to redis)',
		http: { path: '/settings', verb: 'post' },
		accepts: {
			arg: 'setting',
			type: { name: String, value: Object },
			http: { source: 'body' },
		},
		returns: { type: 'object', root: true },
	})

	Business.remoteMethod('getSettingsByName', {
		description: 'Get settings of Main business by name',
		http: { path: '/settings/:name', verb: 'get' },
		accepts: [
			{ arg: 'name', type: 'string', http: { source: 'path' }, required: true }
		],
		returns: { type: 'object', root: true },
	})

	Business.remoteMethod('removeSetting', {
		description: 'Remove a setting of Main business (sync to redis) by name',
		http: { path: '/settings/:name', verb: 'delete' },
		accepts: [
			{ arg: 'name', type: 'string', http: { source: 'path' }, required: true }
		],
		returns: { type: 'object', root: true },
	})

	Business.remoteMethod('updateSetting', {
		description: 'Update a setting of Main business (sync to redis) by name',
		http: { path: '/settings/:name', verb: 'post' },
		accepts: [
			{ arg: 'name', type: 'string', http: { source: 'path' }, required: true },
			{ arg: 'setting', type: 'object', required: true },
			{ arg: 'partial', type: 'boolean' }
		],
		returns: { type: 'object', root: true },
	})
}
