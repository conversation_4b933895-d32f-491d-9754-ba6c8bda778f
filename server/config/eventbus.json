{"definitions": "@perkd/event-registry-crm", "tenant": {"subscribe": ["place.place.closed", "payment.transaction.paid", "payment.transaction.authorized", "payment.transaction.chargeable", "payment.transaction.cancelled", "payment.transaction.failed", "payment.transaction.event", "sales.fulfillment.requested.kitchen", "sales.fulfillment.packed.kitchen", "sales.fulfillment.allocated.deliver", "sales.fulfillment.arrived.deliver", "sales.order.created.manual", "shopify.shop.redact", "applet.service.request"], "publish": ["business.*", "watchdog.*", "payment.balance.changed", "place.provision.started", "place.provision.activated", "applet.service.accepted"]}, "mapping": [{"from": "payment.transaction.paid", "to": "payment.transaction"}, {"from": "payment.transaction.authorized", "to": "payment.transaction"}, {"from": "payment.transaction.chargeable", "to": "payment.transaction"}, {"from": "payment.transaction.cancelled", "to": "payment.transaction"}, {"from": "payment.transaction.failed", "to": "payment.transaction"}, {"from": "sales.fulfillment.requested.kitchen", "to": "sales.fulfillment"}, {"from": "sales.fulfillment.packed.kitchen", "to": "sales.fulfillment"}, {"from": "sales.fulfillment.arrived.deliver", "to": "sales.fulfillment"}, {"from": "applet.service.request", "to": "applet.service"}, {"from": "applet.service.accepted", "to": "applet.service"}], "MAXLEN": 1000, "LIMIT": 100}