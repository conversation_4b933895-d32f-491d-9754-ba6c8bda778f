# This file is generated by running "yarn install" inside your project.
# Manual changes might be lost - proceed with caution!

__metadata:
  version: 8
  cacheKey: 10

"@ampproject/remapping@npm:^2.2.0":
  version: 2.3.0
  resolution: "@ampproject/remapping@npm:2.3.0"
  dependencies:
    "@jridgewell/gen-mapping": "npm:^0.3.5"
    "@jridgewell/trace-mapping": "npm:^0.3.24"
  checksum: 10/f3451525379c68a73eb0a1e65247fbf28c0cccd126d93af21c75fceff77773d43c0d4a2d51978fb131aff25b5f2cb41a9fe48cc296e61ae65e679c4f6918b0ab
  languageName: node
  linkType: hard

"@aws-crypto/crc32@npm:5.2.0":
  version: 5.2.0
  resolution: "@aws-crypto/crc32@npm:5.2.0"
  dependencies:
    "@aws-crypto/util": "npm:^5.2.0"
    "@aws-sdk/types": "npm:^3.222.0"
    tslib: "npm:^2.6.2"
  checksum: 10/1b0a56ad4cb44c9512d8b1668dcf9306ab541d3a73829f435ca97abaec8d56f3db953db03ad0d0698754fea16fcd803d11fa42e0889bc7b803c6a030b04c63de
  languageName: node
  linkType: hard

"@aws-crypto/crc32c@npm:5.2.0":
  version: 5.2.0
  resolution: "@aws-crypto/crc32c@npm:5.2.0"
  dependencies:
    "@aws-crypto/util": "npm:^5.2.0"
    "@aws-sdk/types": "npm:^3.222.0"
    tslib: "npm:^2.6.2"
  checksum: 10/08bd1db17d7c772fa6e34b38a360ce77ad041164743113eefa8343c2af917a419697daf090c5854129ef19f3a9673ed1fd8446e03eb32c8ed52d2cc409b0dee7
  languageName: node
  linkType: hard

"@aws-crypto/sha1-browser@npm:5.2.0":
  version: 5.2.0
  resolution: "@aws-crypto/sha1-browser@npm:5.2.0"
  dependencies:
    "@aws-crypto/supports-web-crypto": "npm:^5.2.0"
    "@aws-crypto/util": "npm:^5.2.0"
    "@aws-sdk/types": "npm:^3.222.0"
    "@aws-sdk/util-locate-window": "npm:^3.0.0"
    "@smithy/util-utf8": "npm:^2.0.0"
    tslib: "npm:^2.6.2"
  checksum: 10/239f4c59cce9abd33c01117b10553fbef868a063e74faf17edb798c250d759a2578841efa2837e5e51854f52ef57dbc40780b073cae20f89ebed6a8cc7fa06f1
  languageName: node
  linkType: hard

"@aws-crypto/sha256-browser@npm:5.2.0":
  version: 5.2.0
  resolution: "@aws-crypto/sha256-browser@npm:5.2.0"
  dependencies:
    "@aws-crypto/sha256-js": "npm:^5.2.0"
    "@aws-crypto/supports-web-crypto": "npm:^5.2.0"
    "@aws-crypto/util": "npm:^5.2.0"
    "@aws-sdk/types": "npm:^3.222.0"
    "@aws-sdk/util-locate-window": "npm:^3.0.0"
    "@smithy/util-utf8": "npm:^2.0.0"
    tslib: "npm:^2.6.2"
  checksum: 10/2b1b701ca6caa876333b4eb2b96e5187d71ebb51ebf8e2d632690dbcdedeff038202d23adcc97e023437ed42bb1963b7b463e343687edf0635fd4b98b2edad1a
  languageName: node
  linkType: hard

"@aws-crypto/sha256-js@npm:5.2.0, @aws-crypto/sha256-js@npm:^5.2.0":
  version: 5.2.0
  resolution: "@aws-crypto/sha256-js@npm:5.2.0"
  dependencies:
    "@aws-crypto/util": "npm:^5.2.0"
    "@aws-sdk/types": "npm:^3.222.0"
    tslib: "npm:^2.6.2"
  checksum: 10/f46aace7b873c615be4e787ab0efd0148ef7de48f9f12c7d043e05c52e52b75bb0bf6dbcb9b2852d940d7724fab7b6d5ff1469160a3dd024efe7a68b5f70df8c
  languageName: node
  linkType: hard

"@aws-crypto/supports-web-crypto@npm:^5.2.0":
  version: 5.2.0
  resolution: "@aws-crypto/supports-web-crypto@npm:5.2.0"
  dependencies:
    tslib: "npm:^2.6.2"
  checksum: 10/6ed0c7e17f4f6663d057630805c45edb35d5693380c24ab52d4c453ece303c6c8a6ade9ee93c97dda77d9f6cae376ffbb44467057161c513dffa3422250edaf5
  languageName: node
  linkType: hard

"@aws-crypto/util@npm:5.2.0, @aws-crypto/util@npm:^5.2.0":
  version: 5.2.0
  resolution: "@aws-crypto/util@npm:5.2.0"
  dependencies:
    "@aws-sdk/types": "npm:^3.222.0"
    "@smithy/util-utf8": "npm:^2.0.0"
    tslib: "npm:^2.6.2"
  checksum: 10/f80a174c404e1ad4364741c942f440e75f834c08278fa754349fe23a6edc679d480ea9ced5820774aee58091ed270067022d8059ecf1a7ef452d58134ac7e9e1
  languageName: node
  linkType: hard

"@aws-sdk/client-quicksight@npm:^3.775.0":
  version: 3.817.0
  resolution: "@aws-sdk/client-quicksight@npm:3.817.0"
  dependencies:
    "@aws-crypto/sha256-browser": "npm:5.2.0"
    "@aws-crypto/sha256-js": "npm:5.2.0"
    "@aws-sdk/core": "npm:3.816.0"
    "@aws-sdk/credential-provider-node": "npm:3.817.0"
    "@aws-sdk/middleware-host-header": "npm:3.804.0"
    "@aws-sdk/middleware-logger": "npm:3.804.0"
    "@aws-sdk/middleware-recursion-detection": "npm:3.804.0"
    "@aws-sdk/middleware-user-agent": "npm:3.816.0"
    "@aws-sdk/region-config-resolver": "npm:3.808.0"
    "@aws-sdk/types": "npm:3.804.0"
    "@aws-sdk/util-endpoints": "npm:3.808.0"
    "@aws-sdk/util-user-agent-browser": "npm:3.804.0"
    "@aws-sdk/util-user-agent-node": "npm:3.816.0"
    "@smithy/config-resolver": "npm:^4.1.2"
    "@smithy/core": "npm:^3.3.3"
    "@smithy/fetch-http-handler": "npm:^5.0.2"
    "@smithy/hash-node": "npm:^4.0.2"
    "@smithy/invalid-dependency": "npm:^4.0.2"
    "@smithy/middleware-content-length": "npm:^4.0.2"
    "@smithy/middleware-endpoint": "npm:^4.1.6"
    "@smithy/middleware-retry": "npm:^4.1.7"
    "@smithy/middleware-serde": "npm:^4.0.5"
    "@smithy/middleware-stack": "npm:^4.0.2"
    "@smithy/node-config-provider": "npm:^4.1.1"
    "@smithy/node-http-handler": "npm:^4.0.4"
    "@smithy/protocol-http": "npm:^5.1.0"
    "@smithy/smithy-client": "npm:^4.2.6"
    "@smithy/types": "npm:^4.2.0"
    "@smithy/url-parser": "npm:^4.0.2"
    "@smithy/util-base64": "npm:^4.0.0"
    "@smithy/util-body-length-browser": "npm:^4.0.0"
    "@smithy/util-body-length-node": "npm:^4.0.0"
    "@smithy/util-defaults-mode-browser": "npm:^4.0.14"
    "@smithy/util-defaults-mode-node": "npm:^4.0.14"
    "@smithy/util-endpoints": "npm:^3.0.4"
    "@smithy/util-middleware": "npm:^4.0.2"
    "@smithy/util-retry": "npm:^4.0.3"
    "@smithy/util-utf8": "npm:^4.0.0"
    tslib: "npm:^2.6.2"
  checksum: 10/4018429ab3e2efa6280b6f42aa0e92f6b5dcf33f3647aa71a62dbd89b928ad0701383e31a670a857801328355f7c24484fd94a8c5d9e708fa3b1556733aad2fb
  languageName: node
  linkType: hard

"@aws-sdk/client-s3@npm:^3.775.0":
  version: 3.817.0
  resolution: "@aws-sdk/client-s3@npm:3.817.0"
  dependencies:
    "@aws-crypto/sha1-browser": "npm:5.2.0"
    "@aws-crypto/sha256-browser": "npm:5.2.0"
    "@aws-crypto/sha256-js": "npm:5.2.0"
    "@aws-sdk/core": "npm:3.816.0"
    "@aws-sdk/credential-provider-node": "npm:3.817.0"
    "@aws-sdk/middleware-bucket-endpoint": "npm:3.808.0"
    "@aws-sdk/middleware-expect-continue": "npm:3.804.0"
    "@aws-sdk/middleware-flexible-checksums": "npm:3.816.0"
    "@aws-sdk/middleware-host-header": "npm:3.804.0"
    "@aws-sdk/middleware-location-constraint": "npm:3.804.0"
    "@aws-sdk/middleware-logger": "npm:3.804.0"
    "@aws-sdk/middleware-recursion-detection": "npm:3.804.0"
    "@aws-sdk/middleware-sdk-s3": "npm:3.816.0"
    "@aws-sdk/middleware-ssec": "npm:3.804.0"
    "@aws-sdk/middleware-user-agent": "npm:3.816.0"
    "@aws-sdk/region-config-resolver": "npm:3.808.0"
    "@aws-sdk/signature-v4-multi-region": "npm:3.816.0"
    "@aws-sdk/types": "npm:3.804.0"
    "@aws-sdk/util-endpoints": "npm:3.808.0"
    "@aws-sdk/util-user-agent-browser": "npm:3.804.0"
    "@aws-sdk/util-user-agent-node": "npm:3.816.0"
    "@aws-sdk/xml-builder": "npm:3.804.0"
    "@smithy/config-resolver": "npm:^4.1.2"
    "@smithy/core": "npm:^3.3.3"
    "@smithy/eventstream-serde-browser": "npm:^4.0.2"
    "@smithy/eventstream-serde-config-resolver": "npm:^4.1.0"
    "@smithy/eventstream-serde-node": "npm:^4.0.2"
    "@smithy/fetch-http-handler": "npm:^5.0.2"
    "@smithy/hash-blob-browser": "npm:^4.0.2"
    "@smithy/hash-node": "npm:^4.0.2"
    "@smithy/hash-stream-node": "npm:^4.0.2"
    "@smithy/invalid-dependency": "npm:^4.0.2"
    "@smithy/md5-js": "npm:^4.0.2"
    "@smithy/middleware-content-length": "npm:^4.0.2"
    "@smithy/middleware-endpoint": "npm:^4.1.6"
    "@smithy/middleware-retry": "npm:^4.1.7"
    "@smithy/middleware-serde": "npm:^4.0.5"
    "@smithy/middleware-stack": "npm:^4.0.2"
    "@smithy/node-config-provider": "npm:^4.1.1"
    "@smithy/node-http-handler": "npm:^4.0.4"
    "@smithy/protocol-http": "npm:^5.1.0"
    "@smithy/smithy-client": "npm:^4.2.6"
    "@smithy/types": "npm:^4.2.0"
    "@smithy/url-parser": "npm:^4.0.2"
    "@smithy/util-base64": "npm:^4.0.0"
    "@smithy/util-body-length-browser": "npm:^4.0.0"
    "@smithy/util-body-length-node": "npm:^4.0.0"
    "@smithy/util-defaults-mode-browser": "npm:^4.0.14"
    "@smithy/util-defaults-mode-node": "npm:^4.0.14"
    "@smithy/util-endpoints": "npm:^3.0.4"
    "@smithy/util-middleware": "npm:^4.0.2"
    "@smithy/util-retry": "npm:^4.0.3"
    "@smithy/util-stream": "npm:^4.2.0"
    "@smithy/util-utf8": "npm:^4.0.0"
    "@smithy/util-waiter": "npm:^4.0.3"
    tslib: "npm:^2.6.2"
  checksum: 10/acb63257dc1f673896823c27382e978f8c92e6a5b7ec54eb50788b6a939fbd3c4acab1df29dfa67acc57914517b0f98d0e1ab93e8642f81ffddb07ca293a9ab4
  languageName: node
  linkType: hard

"@aws-sdk/client-secrets-manager@npm:^3.799.0":
  version: 3.817.0
  resolution: "@aws-sdk/client-secrets-manager@npm:3.817.0"
  dependencies:
    "@aws-crypto/sha256-browser": "npm:5.2.0"
    "@aws-crypto/sha256-js": "npm:5.2.0"
    "@aws-sdk/core": "npm:3.816.0"
    "@aws-sdk/credential-provider-node": "npm:3.817.0"
    "@aws-sdk/middleware-host-header": "npm:3.804.0"
    "@aws-sdk/middleware-logger": "npm:3.804.0"
    "@aws-sdk/middleware-recursion-detection": "npm:3.804.0"
    "@aws-sdk/middleware-user-agent": "npm:3.816.0"
    "@aws-sdk/region-config-resolver": "npm:3.808.0"
    "@aws-sdk/types": "npm:3.804.0"
    "@aws-sdk/util-endpoints": "npm:3.808.0"
    "@aws-sdk/util-user-agent-browser": "npm:3.804.0"
    "@aws-sdk/util-user-agent-node": "npm:3.816.0"
    "@smithy/config-resolver": "npm:^4.1.2"
    "@smithy/core": "npm:^3.3.3"
    "@smithy/fetch-http-handler": "npm:^5.0.2"
    "@smithy/hash-node": "npm:^4.0.2"
    "@smithy/invalid-dependency": "npm:^4.0.2"
    "@smithy/middleware-content-length": "npm:^4.0.2"
    "@smithy/middleware-endpoint": "npm:^4.1.6"
    "@smithy/middleware-retry": "npm:^4.1.7"
    "@smithy/middleware-serde": "npm:^4.0.5"
    "@smithy/middleware-stack": "npm:^4.0.2"
    "@smithy/node-config-provider": "npm:^4.1.1"
    "@smithy/node-http-handler": "npm:^4.0.4"
    "@smithy/protocol-http": "npm:^5.1.0"
    "@smithy/smithy-client": "npm:^4.2.6"
    "@smithy/types": "npm:^4.2.0"
    "@smithy/url-parser": "npm:^4.0.2"
    "@smithy/util-base64": "npm:^4.0.0"
    "@smithy/util-body-length-browser": "npm:^4.0.0"
    "@smithy/util-body-length-node": "npm:^4.0.0"
    "@smithy/util-defaults-mode-browser": "npm:^4.0.14"
    "@smithy/util-defaults-mode-node": "npm:^4.0.14"
    "@smithy/util-endpoints": "npm:^3.0.4"
    "@smithy/util-middleware": "npm:^4.0.2"
    "@smithy/util-retry": "npm:^4.0.3"
    "@smithy/util-utf8": "npm:^4.0.0"
    "@types/uuid": "npm:^9.0.1"
    tslib: "npm:^2.6.2"
    uuid: "npm:^9.0.1"
  checksum: 10/ce494123433eb996e8601ac03cba2b5944fd5aa34bdf6dd09cd26ae2d3234ed4a7d47bc8900d91191af7d0357b2d7bb83dac1b4e25a9cd168d7d6911498f3b31
  languageName: node
  linkType: hard

"@aws-sdk/client-sso@npm:3.817.0":
  version: 3.817.0
  resolution: "@aws-sdk/client-sso@npm:3.817.0"
  dependencies:
    "@aws-crypto/sha256-browser": "npm:5.2.0"
    "@aws-crypto/sha256-js": "npm:5.2.0"
    "@aws-sdk/core": "npm:3.816.0"
    "@aws-sdk/middleware-host-header": "npm:3.804.0"
    "@aws-sdk/middleware-logger": "npm:3.804.0"
    "@aws-sdk/middleware-recursion-detection": "npm:3.804.0"
    "@aws-sdk/middleware-user-agent": "npm:3.816.0"
    "@aws-sdk/region-config-resolver": "npm:3.808.0"
    "@aws-sdk/types": "npm:3.804.0"
    "@aws-sdk/util-endpoints": "npm:3.808.0"
    "@aws-sdk/util-user-agent-browser": "npm:3.804.0"
    "@aws-sdk/util-user-agent-node": "npm:3.816.0"
    "@smithy/config-resolver": "npm:^4.1.2"
    "@smithy/core": "npm:^3.3.3"
    "@smithy/fetch-http-handler": "npm:^5.0.2"
    "@smithy/hash-node": "npm:^4.0.2"
    "@smithy/invalid-dependency": "npm:^4.0.2"
    "@smithy/middleware-content-length": "npm:^4.0.2"
    "@smithy/middleware-endpoint": "npm:^4.1.6"
    "@smithy/middleware-retry": "npm:^4.1.7"
    "@smithy/middleware-serde": "npm:^4.0.5"
    "@smithy/middleware-stack": "npm:^4.0.2"
    "@smithy/node-config-provider": "npm:^4.1.1"
    "@smithy/node-http-handler": "npm:^4.0.4"
    "@smithy/protocol-http": "npm:^5.1.0"
    "@smithy/smithy-client": "npm:^4.2.6"
    "@smithy/types": "npm:^4.2.0"
    "@smithy/url-parser": "npm:^4.0.2"
    "@smithy/util-base64": "npm:^4.0.0"
    "@smithy/util-body-length-browser": "npm:^4.0.0"
    "@smithy/util-body-length-node": "npm:^4.0.0"
    "@smithy/util-defaults-mode-browser": "npm:^4.0.14"
    "@smithy/util-defaults-mode-node": "npm:^4.0.14"
    "@smithy/util-endpoints": "npm:^3.0.4"
    "@smithy/util-middleware": "npm:^4.0.2"
    "@smithy/util-retry": "npm:^4.0.3"
    "@smithy/util-utf8": "npm:^4.0.0"
    tslib: "npm:^2.6.2"
  checksum: 10/72e4e1767d444784933d0414a7c6c197d216c8ff6aa760efea97533ec2bd10a9f841d9ef36d19510d644b42d455f0aec63bb97c13b8b4f3bab645c4741ceead2
  languageName: node
  linkType: hard

"@aws-sdk/core@npm:3.816.0":
  version: 3.816.0
  resolution: "@aws-sdk/core@npm:3.816.0"
  dependencies:
    "@aws-sdk/types": "npm:3.804.0"
    "@smithy/core": "npm:^3.3.3"
    "@smithy/node-config-provider": "npm:^4.1.1"
    "@smithy/property-provider": "npm:^4.0.2"
    "@smithy/protocol-http": "npm:^5.1.0"
    "@smithy/signature-v4": "npm:^5.1.0"
    "@smithy/smithy-client": "npm:^4.2.6"
    "@smithy/types": "npm:^4.2.0"
    "@smithy/util-middleware": "npm:^4.0.2"
    fast-xml-parser: "npm:4.4.1"
    tslib: "npm:^2.6.2"
  checksum: 10/2b6c4188969e00af0f4d77ed88b8b23b4654c0e66b3356ae062a0d40ab6831c78db1473efd20514c182f8d4033ce2728b0425a292c57246ceb22a177c17d970f
  languageName: node
  linkType: hard

"@aws-sdk/credential-provider-env@npm:3.816.0":
  version: 3.816.0
  resolution: "@aws-sdk/credential-provider-env@npm:3.816.0"
  dependencies:
    "@aws-sdk/core": "npm:3.816.0"
    "@aws-sdk/types": "npm:3.804.0"
    "@smithy/property-provider": "npm:^4.0.2"
    "@smithy/types": "npm:^4.2.0"
    tslib: "npm:^2.6.2"
  checksum: 10/0fcc1f287bfd4daa650b5d72a83c87c031f7b99e1877bd2ff78efc4d756b30e1f69770f2034294da1da05466b961cd4596a848b8e3295d8abf6502abdc48b35f
  languageName: node
  linkType: hard

"@aws-sdk/credential-provider-http@npm:3.816.0":
  version: 3.816.0
  resolution: "@aws-sdk/credential-provider-http@npm:3.816.0"
  dependencies:
    "@aws-sdk/core": "npm:3.816.0"
    "@aws-sdk/types": "npm:3.804.0"
    "@smithy/fetch-http-handler": "npm:^5.0.2"
    "@smithy/node-http-handler": "npm:^4.0.4"
    "@smithy/property-provider": "npm:^4.0.2"
    "@smithy/protocol-http": "npm:^5.1.0"
    "@smithy/smithy-client": "npm:^4.2.6"
    "@smithy/types": "npm:^4.2.0"
    "@smithy/util-stream": "npm:^4.2.0"
    tslib: "npm:^2.6.2"
  checksum: 10/ddc59373a984a1df1cb4efa50027d01dba0d7031b3d0d720edd8bf03d35e4c6ff2333fd8310603c7cc034308124f4f0412e3ca52d7c3d12dad6b0528de11fea0
  languageName: node
  linkType: hard

"@aws-sdk/credential-provider-ini@npm:3.817.0":
  version: 3.817.0
  resolution: "@aws-sdk/credential-provider-ini@npm:3.817.0"
  dependencies:
    "@aws-sdk/core": "npm:3.816.0"
    "@aws-sdk/credential-provider-env": "npm:3.816.0"
    "@aws-sdk/credential-provider-http": "npm:3.816.0"
    "@aws-sdk/credential-provider-process": "npm:3.816.0"
    "@aws-sdk/credential-provider-sso": "npm:3.817.0"
    "@aws-sdk/credential-provider-web-identity": "npm:3.817.0"
    "@aws-sdk/nested-clients": "npm:3.817.0"
    "@aws-sdk/types": "npm:3.804.0"
    "@smithy/credential-provider-imds": "npm:^4.0.4"
    "@smithy/property-provider": "npm:^4.0.2"
    "@smithy/shared-ini-file-loader": "npm:^4.0.2"
    "@smithy/types": "npm:^4.2.0"
    tslib: "npm:^2.6.2"
  checksum: 10/d7c6f44e1dbf2eedb20f45581990136e531975870866573ca2256c325f9ea96264c6e7a45e691622668f034f8d04937c65da5a5ddc18f169f02652ea37c1b7bb
  languageName: node
  linkType: hard

"@aws-sdk/credential-provider-node@npm:3.817.0":
  version: 3.817.0
  resolution: "@aws-sdk/credential-provider-node@npm:3.817.0"
  dependencies:
    "@aws-sdk/credential-provider-env": "npm:3.816.0"
    "@aws-sdk/credential-provider-http": "npm:3.816.0"
    "@aws-sdk/credential-provider-ini": "npm:3.817.0"
    "@aws-sdk/credential-provider-process": "npm:3.816.0"
    "@aws-sdk/credential-provider-sso": "npm:3.817.0"
    "@aws-sdk/credential-provider-web-identity": "npm:3.817.0"
    "@aws-sdk/types": "npm:3.804.0"
    "@smithy/credential-provider-imds": "npm:^4.0.4"
    "@smithy/property-provider": "npm:^4.0.2"
    "@smithy/shared-ini-file-loader": "npm:^4.0.2"
    "@smithy/types": "npm:^4.2.0"
    tslib: "npm:^2.6.2"
  checksum: 10/19aee97abcbbd3e84004d2be109c0e05e50947a78f3493ca98cadc1210a597d68eed6ab9a42b188dae8545349ea5e92fc43ae32209799e5e4ab4541df4e85f70
  languageName: node
  linkType: hard

"@aws-sdk/credential-provider-process@npm:3.816.0":
  version: 3.816.0
  resolution: "@aws-sdk/credential-provider-process@npm:3.816.0"
  dependencies:
    "@aws-sdk/core": "npm:3.816.0"
    "@aws-sdk/types": "npm:3.804.0"
    "@smithy/property-provider": "npm:^4.0.2"
    "@smithy/shared-ini-file-loader": "npm:^4.0.2"
    "@smithy/types": "npm:^4.2.0"
    tslib: "npm:^2.6.2"
  checksum: 10/f52781ce364a37b7187a10f1f4beb5977e58fc8a2eedb1c67afd6f5cd1f07495be5f928a5aa1731a3b5874d86bb198754d6d81ef5a3b3a4e58bf4c35f16bc479
  languageName: node
  linkType: hard

"@aws-sdk/credential-provider-sso@npm:3.817.0":
  version: 3.817.0
  resolution: "@aws-sdk/credential-provider-sso@npm:3.817.0"
  dependencies:
    "@aws-sdk/client-sso": "npm:3.817.0"
    "@aws-sdk/core": "npm:3.816.0"
    "@aws-sdk/token-providers": "npm:3.817.0"
    "@aws-sdk/types": "npm:3.804.0"
    "@smithy/property-provider": "npm:^4.0.2"
    "@smithy/shared-ini-file-loader": "npm:^4.0.2"
    "@smithy/types": "npm:^4.2.0"
    tslib: "npm:^2.6.2"
  checksum: 10/b6fc63d0c7e6582591b30f53250429a9eb6f8e116ec3fce1cff4cb892264ff87d5c69b68a3eea23f1630d63d8a80b544142726a0687f2c76a6533bd58bd6ddec
  languageName: node
  linkType: hard

"@aws-sdk/credential-provider-web-identity@npm:3.817.0":
  version: 3.817.0
  resolution: "@aws-sdk/credential-provider-web-identity@npm:3.817.0"
  dependencies:
    "@aws-sdk/core": "npm:3.816.0"
    "@aws-sdk/nested-clients": "npm:3.817.0"
    "@aws-sdk/types": "npm:3.804.0"
    "@smithy/property-provider": "npm:^4.0.2"
    "@smithy/types": "npm:^4.2.0"
    tslib: "npm:^2.6.2"
  checksum: 10/8b6b3fe7c20e633a576af4a3ae085bc5d858883a78630372082085294f2f37ef951a1f86574981b52dd6946ff49015f61eec4b3e457ef73491563f47b4873389
  languageName: node
  linkType: hard

"@aws-sdk/middleware-bucket-endpoint@npm:3.808.0":
  version: 3.808.0
  resolution: "@aws-sdk/middleware-bucket-endpoint@npm:3.808.0"
  dependencies:
    "@aws-sdk/types": "npm:3.804.0"
    "@aws-sdk/util-arn-parser": "npm:3.804.0"
    "@smithy/node-config-provider": "npm:^4.1.1"
    "@smithy/protocol-http": "npm:^5.1.0"
    "@smithy/types": "npm:^4.2.0"
    "@smithy/util-config-provider": "npm:^4.0.0"
    tslib: "npm:^2.6.2"
  checksum: 10/bf0d3b9e70f58575b45fdd8483a874bd7400783bf0f3ab456a999de6b3e8ca100b6bb3e8462055e0ee8db628d367dba3e87fe3c201f89d342a875a419e49d275
  languageName: node
  linkType: hard

"@aws-sdk/middleware-expect-continue@npm:3.804.0":
  version: 3.804.0
  resolution: "@aws-sdk/middleware-expect-continue@npm:3.804.0"
  dependencies:
    "@aws-sdk/types": "npm:3.804.0"
    "@smithy/protocol-http": "npm:^5.1.0"
    "@smithy/types": "npm:^4.2.0"
    tslib: "npm:^2.6.2"
  checksum: 10/75298c41c39e5f54ea0e15419a06005c15ecec6bcc139a92ed374fdda291cb8a9a67f6d761c30f36ed25c5224159ae08cbe9cadc8a2599d365252d83c9bf350d
  languageName: node
  linkType: hard

"@aws-sdk/middleware-flexible-checksums@npm:3.816.0":
  version: 3.816.0
  resolution: "@aws-sdk/middleware-flexible-checksums@npm:3.816.0"
  dependencies:
    "@aws-crypto/crc32": "npm:5.2.0"
    "@aws-crypto/crc32c": "npm:5.2.0"
    "@aws-crypto/util": "npm:5.2.0"
    "@aws-sdk/core": "npm:3.816.0"
    "@aws-sdk/types": "npm:3.804.0"
    "@smithy/is-array-buffer": "npm:^4.0.0"
    "@smithy/node-config-provider": "npm:^4.1.1"
    "@smithy/protocol-http": "npm:^5.1.0"
    "@smithy/types": "npm:^4.2.0"
    "@smithy/util-middleware": "npm:^4.0.2"
    "@smithy/util-stream": "npm:^4.2.0"
    "@smithy/util-utf8": "npm:^4.0.0"
    tslib: "npm:^2.6.2"
  checksum: 10/84868f8a4d6ec900e3b047b11ab9c142baed235d80f2539742ff148b88f112d75a23e1cef8fc85772e86e6fc7572e01b97091a317fc10ad0e5b4c26ad672e12c
  languageName: node
  linkType: hard

"@aws-sdk/middleware-host-header@npm:3.804.0":
  version: 3.804.0
  resolution: "@aws-sdk/middleware-host-header@npm:3.804.0"
  dependencies:
    "@aws-sdk/types": "npm:3.804.0"
    "@smithy/protocol-http": "npm:^5.1.0"
    "@smithy/types": "npm:^4.2.0"
    tslib: "npm:^2.6.2"
  checksum: 10/e511fc88eabf44a88458a24ad134933a01cc44704db5f9baaa179e87a541da7fee2de55ea5d1e16daedb5cad40d270d4f2fab41dd0b16db7ffa812461409194d
  languageName: node
  linkType: hard

"@aws-sdk/middleware-location-constraint@npm:3.804.0":
  version: 3.804.0
  resolution: "@aws-sdk/middleware-location-constraint@npm:3.804.0"
  dependencies:
    "@aws-sdk/types": "npm:3.804.0"
    "@smithy/types": "npm:^4.2.0"
    tslib: "npm:^2.6.2"
  checksum: 10/cd24059bb0fdfe23da619ce09dc154a862be574ae0912408963d3bfe648921136c8f9f795f69cbe2a62e9c855a2561098f7b66fa9695bbe3a36cf14bbd5acd37
  languageName: node
  linkType: hard

"@aws-sdk/middleware-logger@npm:3.804.0":
  version: 3.804.0
  resolution: "@aws-sdk/middleware-logger@npm:3.804.0"
  dependencies:
    "@aws-sdk/types": "npm:3.804.0"
    "@smithy/types": "npm:^4.2.0"
    tslib: "npm:^2.6.2"
  checksum: 10/18097aae48558cf5d40bda906829b500693c9a22378babd9f177886cdb88eaf3da28cf38c09952502d9f951f50821a4f9082270c77329a12e61dc4d74905ec32
  languageName: node
  linkType: hard

"@aws-sdk/middleware-recursion-detection@npm:3.804.0":
  version: 3.804.0
  resolution: "@aws-sdk/middleware-recursion-detection@npm:3.804.0"
  dependencies:
    "@aws-sdk/types": "npm:3.804.0"
    "@smithy/protocol-http": "npm:^5.1.0"
    "@smithy/types": "npm:^4.2.0"
    tslib: "npm:^2.6.2"
  checksum: 10/781b4fa873a10b2b058f66eb5e5ddb758695edc62a1e802f2cba6e845e1e5e578d59e6adad4e0978020271eec5d1974b044bd529798cec1105b93ecc077ffa7d
  languageName: node
  linkType: hard

"@aws-sdk/middleware-sdk-s3@npm:3.816.0":
  version: 3.816.0
  resolution: "@aws-sdk/middleware-sdk-s3@npm:3.816.0"
  dependencies:
    "@aws-sdk/core": "npm:3.816.0"
    "@aws-sdk/types": "npm:3.804.0"
    "@aws-sdk/util-arn-parser": "npm:3.804.0"
    "@smithy/core": "npm:^3.3.3"
    "@smithy/node-config-provider": "npm:^4.1.1"
    "@smithy/protocol-http": "npm:^5.1.0"
    "@smithy/signature-v4": "npm:^5.1.0"
    "@smithy/smithy-client": "npm:^4.2.6"
    "@smithy/types": "npm:^4.2.0"
    "@smithy/util-config-provider": "npm:^4.0.0"
    "@smithy/util-middleware": "npm:^4.0.2"
    "@smithy/util-stream": "npm:^4.2.0"
    "@smithy/util-utf8": "npm:^4.0.0"
    tslib: "npm:^2.6.2"
  checksum: 10/9b8c6096ab730f91555f62f734daa67d8f8bd536b0cfa04a3108ad59c94d7e09cc27f70d48f8a592b47e77c7c569ba2a7447a281ca3509592fe02ec4f42e7a06
  languageName: node
  linkType: hard

"@aws-sdk/middleware-ssec@npm:3.804.0":
  version: 3.804.0
  resolution: "@aws-sdk/middleware-ssec@npm:3.804.0"
  dependencies:
    "@aws-sdk/types": "npm:3.804.0"
    "@smithy/types": "npm:^4.2.0"
    tslib: "npm:^2.6.2"
  checksum: 10/05b0d392e7485113010ef61f2d47d5e6bc715bc2d2fa1d3dba26e059908a177e2119eb93e135bc5f471822c6aab411fa0122410142c3b4a92dfc9980d35e783f
  languageName: node
  linkType: hard

"@aws-sdk/middleware-user-agent@npm:3.816.0":
  version: 3.816.0
  resolution: "@aws-sdk/middleware-user-agent@npm:3.816.0"
  dependencies:
    "@aws-sdk/core": "npm:3.816.0"
    "@aws-sdk/types": "npm:3.804.0"
    "@aws-sdk/util-endpoints": "npm:3.808.0"
    "@smithy/core": "npm:^3.3.3"
    "@smithy/protocol-http": "npm:^5.1.0"
    "@smithy/types": "npm:^4.2.0"
    tslib: "npm:^2.6.2"
  checksum: 10/1b5fafbd21b1cf96309c5063472d3a91c8e202e1762bf965310da3723bc191eccc891e1d89b3602a312b051ce8221ec029d13215920dfb42502c897478da3ef9
  languageName: node
  linkType: hard

"@aws-sdk/nested-clients@npm:3.817.0":
  version: 3.817.0
  resolution: "@aws-sdk/nested-clients@npm:3.817.0"
  dependencies:
    "@aws-crypto/sha256-browser": "npm:5.2.0"
    "@aws-crypto/sha256-js": "npm:5.2.0"
    "@aws-sdk/core": "npm:3.816.0"
    "@aws-sdk/middleware-host-header": "npm:3.804.0"
    "@aws-sdk/middleware-logger": "npm:3.804.0"
    "@aws-sdk/middleware-recursion-detection": "npm:3.804.0"
    "@aws-sdk/middleware-user-agent": "npm:3.816.0"
    "@aws-sdk/region-config-resolver": "npm:3.808.0"
    "@aws-sdk/types": "npm:3.804.0"
    "@aws-sdk/util-endpoints": "npm:3.808.0"
    "@aws-sdk/util-user-agent-browser": "npm:3.804.0"
    "@aws-sdk/util-user-agent-node": "npm:3.816.0"
    "@smithy/config-resolver": "npm:^4.1.2"
    "@smithy/core": "npm:^3.3.3"
    "@smithy/fetch-http-handler": "npm:^5.0.2"
    "@smithy/hash-node": "npm:^4.0.2"
    "@smithy/invalid-dependency": "npm:^4.0.2"
    "@smithy/middleware-content-length": "npm:^4.0.2"
    "@smithy/middleware-endpoint": "npm:^4.1.6"
    "@smithy/middleware-retry": "npm:^4.1.7"
    "@smithy/middleware-serde": "npm:^4.0.5"
    "@smithy/middleware-stack": "npm:^4.0.2"
    "@smithy/node-config-provider": "npm:^4.1.1"
    "@smithy/node-http-handler": "npm:^4.0.4"
    "@smithy/protocol-http": "npm:^5.1.0"
    "@smithy/smithy-client": "npm:^4.2.6"
    "@smithy/types": "npm:^4.2.0"
    "@smithy/url-parser": "npm:^4.0.2"
    "@smithy/util-base64": "npm:^4.0.0"
    "@smithy/util-body-length-browser": "npm:^4.0.0"
    "@smithy/util-body-length-node": "npm:^4.0.0"
    "@smithy/util-defaults-mode-browser": "npm:^4.0.14"
    "@smithy/util-defaults-mode-node": "npm:^4.0.14"
    "@smithy/util-endpoints": "npm:^3.0.4"
    "@smithy/util-middleware": "npm:^4.0.2"
    "@smithy/util-retry": "npm:^4.0.3"
    "@smithy/util-utf8": "npm:^4.0.0"
    tslib: "npm:^2.6.2"
  checksum: 10/ee798825612fd68566af155e79b200f02809f3e367c05105baec617bb06a46b958766f096b9221732f7bec53c921cef1df63c3a10868b5ea170fec7d6d53f9bf
  languageName: node
  linkType: hard

"@aws-sdk/region-config-resolver@npm:3.808.0":
  version: 3.808.0
  resolution: "@aws-sdk/region-config-resolver@npm:3.808.0"
  dependencies:
    "@aws-sdk/types": "npm:3.804.0"
    "@smithy/node-config-provider": "npm:^4.1.1"
    "@smithy/types": "npm:^4.2.0"
    "@smithy/util-config-provider": "npm:^4.0.0"
    "@smithy/util-middleware": "npm:^4.0.2"
    tslib: "npm:^2.6.2"
  checksum: 10/b3710a568ba23af4169a87c497ffaebfa764f3439f3e515ec6ab4b6cd445b16901445b907fb8496a6175f86dccefbef396df23211cad5c75b899a473a8e3fda3
  languageName: node
  linkType: hard

"@aws-sdk/signature-v4-multi-region@npm:3.816.0":
  version: 3.816.0
  resolution: "@aws-sdk/signature-v4-multi-region@npm:3.816.0"
  dependencies:
    "@aws-sdk/middleware-sdk-s3": "npm:3.816.0"
    "@aws-sdk/types": "npm:3.804.0"
    "@smithy/protocol-http": "npm:^5.1.0"
    "@smithy/signature-v4": "npm:^5.1.0"
    "@smithy/types": "npm:^4.2.0"
    tslib: "npm:^2.6.2"
  checksum: 10/a433c59ce1609813dec8ebb46bafbf0d1a8a9a55c2f47bf2766244c23ead7a8850bf731fc3a1e1f3e0270d89d41b48c5bc5c3041e6b3fa6ebd9611160e5efd89
  languageName: node
  linkType: hard

"@aws-sdk/token-providers@npm:3.817.0":
  version: 3.817.0
  resolution: "@aws-sdk/token-providers@npm:3.817.0"
  dependencies:
    "@aws-sdk/core": "npm:3.816.0"
    "@aws-sdk/nested-clients": "npm:3.817.0"
    "@aws-sdk/types": "npm:3.804.0"
    "@smithy/property-provider": "npm:^4.0.2"
    "@smithy/shared-ini-file-loader": "npm:^4.0.2"
    "@smithy/types": "npm:^4.2.0"
    tslib: "npm:^2.6.2"
  checksum: 10/f28319417ac3b94a8340d35672b6e75d421c7dc837d5dfb45a1e06ad94f5d6b2cf4b0b7639b7f2869dcf9a94c7a5d7ecfe23f7cd02efa81ef6fa99a8e30845c2
  languageName: node
  linkType: hard

"@aws-sdk/types@npm:3.804.0, @aws-sdk/types@npm:^3.222.0":
  version: 3.804.0
  resolution: "@aws-sdk/types@npm:3.804.0"
  dependencies:
    "@smithy/types": "npm:^4.2.0"
    tslib: "npm:^2.6.2"
  checksum: 10/832be8774a2322f2114c93a632876308edd94b4def2005facade754e0ada38a96dab0919444387440ae94f5b562c6b8195b93a70c401c3fa6541f1980224739d
  languageName: node
  linkType: hard

"@aws-sdk/util-arn-parser@npm:3.804.0":
  version: 3.804.0
  resolution: "@aws-sdk/util-arn-parser@npm:3.804.0"
  dependencies:
    tslib: "npm:^2.6.2"
  checksum: 10/3a66cee522fd1de7693eaf9dd8c4bad9efdf0d42f1af86797c138af3f84d38e9e6e38f0dffd963a372a95c5e0de07c570f613c49bbf85f7e03cea6d985fdbe01
  languageName: node
  linkType: hard

"@aws-sdk/util-endpoints@npm:3.808.0":
  version: 3.808.0
  resolution: "@aws-sdk/util-endpoints@npm:3.808.0"
  dependencies:
    "@aws-sdk/types": "npm:3.804.0"
    "@smithy/types": "npm:^4.2.0"
    "@smithy/util-endpoints": "npm:^3.0.4"
    tslib: "npm:^2.6.2"
  checksum: 10/bc735ac0d7c58d12e5ef820050879bba7300534ff9e05a3f0c3cb32ac5874e208b7e000e5cb4d04f407789cef0e48926e1564a5edc89f64a141b3494970f6ba4
  languageName: node
  linkType: hard

"@aws-sdk/util-locate-window@npm:^3.0.0":
  version: 3.804.0
  resolution: "@aws-sdk/util-locate-window@npm:3.804.0"
  dependencies:
    tslib: "npm:^2.6.2"
  checksum: 10/4f1ad094cf9f23a6f09dbad8823d3c58d628412352a045e2fb3bea66281aa59a944545ffd8ed09b096b60b44671fb3fd3f3bfb5b924f3326ee82fb68c2c7b785
  languageName: node
  linkType: hard

"@aws-sdk/util-user-agent-browser@npm:3.804.0":
  version: 3.804.0
  resolution: "@aws-sdk/util-user-agent-browser@npm:3.804.0"
  dependencies:
    "@aws-sdk/types": "npm:3.804.0"
    "@smithy/types": "npm:^4.2.0"
    bowser: "npm:^2.11.0"
    tslib: "npm:^2.6.2"
  checksum: 10/6fe62a4625acc86cc018e6646b6737eec1aface0f0ae8215431eef740635cf3d2339e859f77d4030db696ac36a69a309fe2d2534b45e790feeb596f39c83e135
  languageName: node
  linkType: hard

"@aws-sdk/util-user-agent-node@npm:3.816.0":
  version: 3.816.0
  resolution: "@aws-sdk/util-user-agent-node@npm:3.816.0"
  dependencies:
    "@aws-sdk/middleware-user-agent": "npm:3.816.0"
    "@aws-sdk/types": "npm:3.804.0"
    "@smithy/node-config-provider": "npm:^4.1.1"
    "@smithy/types": "npm:^4.2.0"
    tslib: "npm:^2.6.2"
  peerDependencies:
    aws-crt: ">=1.0.0"
  peerDependenciesMeta:
    aws-crt:
      optional: true
  checksum: 10/96ac27c29054912103420c6d2b5997d04f60ec135ee3b4c26ce09a241cbba1de74a39217278186be91e44ab936f0a2b302644d2365265a8b53669b3599dc289e
  languageName: node
  linkType: hard

"@aws-sdk/xml-builder@npm:3.804.0":
  version: 3.804.0
  resolution: "@aws-sdk/xml-builder@npm:3.804.0"
  dependencies:
    "@smithy/types": "npm:^4.2.0"
    tslib: "npm:^2.6.2"
  checksum: 10/b319e2e55591dfbdc17c2697308a3a0fcbd53f24dbbbd022101b066bb2101d51afbee76699d0f71998a2008cadb545f36652fcf8f41fe14f87680e98fd5be7c3
  languageName: node
  linkType: hard

"@babel/code-frame@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/code-frame@npm:7.27.1"
  dependencies:
    "@babel/helper-validator-identifier": "npm:^7.27.1"
    js-tokens: "npm:^4.0.0"
    picocolors: "npm:^1.1.1"
  checksum: 10/721b8a6e360a1fa0f1c9fe7351ae6c874828e119183688b533c477aa378f1010f37cc9afbfc4722c686d1f5cdd00da02eab4ba7278a0c504fa0d7a321dcd4fdf
  languageName: node
  linkType: hard

"@babel/compat-data@npm:^7.27.2":
  version: 7.27.3
  resolution: "@babel/compat-data@npm:7.27.3"
  checksum: 10/3bc4f53f2c076468c1df405e3fb3aac60a8118f46ff4ea8d093e00dcf919e915adc68d9c0a46fffe9cdc5b0d41fefe3b44370d43da09bbd7c9e5474d2cd4c656
  languageName: node
  linkType: hard

"@babel/core@npm:^7.27.3":
  version: 7.27.3
  resolution: "@babel/core@npm:7.27.3"
  dependencies:
    "@ampproject/remapping": "npm:^2.2.0"
    "@babel/code-frame": "npm:^7.27.1"
    "@babel/generator": "npm:^7.27.3"
    "@babel/helper-compilation-targets": "npm:^7.27.2"
    "@babel/helper-module-transforms": "npm:^7.27.3"
    "@babel/helpers": "npm:^7.27.3"
    "@babel/parser": "npm:^7.27.3"
    "@babel/template": "npm:^7.27.2"
    "@babel/traverse": "npm:^7.27.3"
    "@babel/types": "npm:^7.27.3"
    convert-source-map: "npm:^2.0.0"
    debug: "npm:^4.1.0"
    gensync: "npm:^1.0.0-beta.2"
    json5: "npm:^2.2.3"
    semver: "npm:^6.3.1"
  checksum: 10/c0cf72f01c9913b10e974e548e46359563cae849e914211df951bbfc90ceac75c609156f20faa2db784308dc413ba64e2c0b94f9173e7e1e92a75053aab1e1bb
  languageName: node
  linkType: hard

"@babel/eslint-parser@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/eslint-parser@npm:7.27.1"
  dependencies:
    "@nicolo-ribaudo/eslint-scope-5-internals": "npm:5.1.1-v1"
    eslint-visitor-keys: "npm:^2.1.0"
    semver: "npm:^6.3.1"
  peerDependencies:
    "@babel/core": ^7.11.0
    eslint: ^7.5.0 || ^8.0.0 || ^9.0.0
  checksum: 10/d8b7a12355cb702c366ae7f3af7f890a593dab1b10a61758c6ecc2019a8de9fb582d03683d00bbc3d5f0396464c97a70b933848cb8daed5ea73179b1226074a3
  languageName: node
  linkType: hard

"@babel/generator@npm:^7.27.3":
  version: 7.27.3
  resolution: "@babel/generator@npm:7.27.3"
  dependencies:
    "@babel/parser": "npm:^7.27.3"
    "@babel/types": "npm:^7.27.3"
    "@jridgewell/gen-mapping": "npm:^0.3.5"
    "@jridgewell/trace-mapping": "npm:^0.3.25"
    jsesc: "npm:^3.0.2"
  checksum: 10/3b8477ae0c305639f86aeb553115535b103626008945462d32171fa4ebd77f2a0345600dc5baee7ced98d54cc7da9c806808a04b555c75136f42e0e9d7794bdf
  languageName: node
  linkType: hard

"@babel/helper-compilation-targets@npm:^7.27.2":
  version: 7.27.2
  resolution: "@babel/helper-compilation-targets@npm:7.27.2"
  dependencies:
    "@babel/compat-data": "npm:^7.27.2"
    "@babel/helper-validator-option": "npm:^7.27.1"
    browserslist: "npm:^4.24.0"
    lru-cache: "npm:^5.1.1"
    semver: "npm:^6.3.1"
  checksum: 10/bd53c30a7477049db04b655d11f4c3500aea3bcbc2497cf02161de2ecf994fec7c098aabbcebe210ffabc2ecbdb1e3ffad23fb4d3f18723b814f423ea1749fe8
  languageName: node
  linkType: hard

"@babel/helper-module-imports@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/helper-module-imports@npm:7.27.1"
  dependencies:
    "@babel/traverse": "npm:^7.27.1"
    "@babel/types": "npm:^7.27.1"
  checksum: 10/58e792ea5d4ae71676e0d03d9fef33e886a09602addc3bd01388a98d87df9fcfd192968feb40ac4aedb7e287ec3d0c17b33e3ecefe002592041a91d8a1998a8d
  languageName: node
  linkType: hard

"@babel/helper-module-transforms@npm:^7.27.3":
  version: 7.27.3
  resolution: "@babel/helper-module-transforms@npm:7.27.3"
  dependencies:
    "@babel/helper-module-imports": "npm:^7.27.1"
    "@babel/helper-validator-identifier": "npm:^7.27.1"
    "@babel/traverse": "npm:^7.27.3"
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 10/47abc90ceb181b4bdea9bf1717adf536d1b5e5acb6f6d8a7a4524080318b5ca8a99e6d58677268c596bad71077d1d98834d2c3815f2443e6d3f287962300f15d
  languageName: node
  linkType: hard

"@babel/helper-string-parser@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/helper-string-parser@npm:7.27.1"
  checksum: 10/0ae29cc2005084abdae2966afdb86ed14d41c9c37db02c3693d5022fba9f5d59b011d039380b8e537c34daf117c549f52b452398f576e908fb9db3c7abbb3a00
  languageName: node
  linkType: hard

"@babel/helper-validator-identifier@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/helper-validator-identifier@npm:7.27.1"
  checksum: 10/75041904d21bdc0cd3b07a8ac90b11d64cd3c881e89cb936fa80edd734bf23c35e6bd1312611e8574c4eab1f3af0f63e8a5894f4699e9cfdf70c06fcf4252320
  languageName: node
  linkType: hard

"@babel/helper-validator-option@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/helper-validator-option@npm:7.27.1"
  checksum: 10/db73e6a308092531c629ee5de7f0d04390835b21a263be2644276cb27da2384b64676cab9f22cd8d8dbd854c92b1d7d56fc8517cf0070c35d1c14a8c828b0903
  languageName: node
  linkType: hard

"@babel/helpers@npm:^7.27.3":
  version: 7.27.3
  resolution: "@babel/helpers@npm:7.27.3"
  dependencies:
    "@babel/template": "npm:^7.27.2"
    "@babel/types": "npm:^7.27.3"
  checksum: 10/534e6bd68d96a7e548aeab7a4cf8003d7cffc0202448e47242095692e243d08fcf89b6f4b9ea369b4fc29a4247b443da9fa29f43de4b71639b3687c1c8f534d2
  languageName: node
  linkType: hard

"@babel/parser@npm:^7.27.2, @babel/parser@npm:^7.27.3":
  version: 7.27.3
  resolution: "@babel/parser@npm:7.27.3"
  dependencies:
    "@babel/types": "npm:^7.27.3"
  bin:
    parser: ./bin/babel-parser.js
  checksum: 10/ea5a0cd55e18f905d4c732b009ca0f66b0e5580f0d2af82643c26ef0909a16704778f59b7a2959096e9cf881b6291da747bfd29e400422e04d9074eb1f80983e
  languageName: node
  linkType: hard

"@babel/runtime@npm:^7.27.1":
  version: 7.27.3
  resolution: "@babel/runtime@npm:7.27.3"
  checksum: 10/c1975b37bfc3f24a32c0beb4999d4302a33a70d0599fb3d42a9baf836fcd615de7ae36eff2fbbc7451c892d3dcc9314a90c3f1f3b337f5617f6c5b826d2dbd54
  languageName: node
  linkType: hard

"@babel/template@npm:^7.27.2":
  version: 7.27.2
  resolution: "@babel/template@npm:7.27.2"
  dependencies:
    "@babel/code-frame": "npm:^7.27.1"
    "@babel/parser": "npm:^7.27.2"
    "@babel/types": "npm:^7.27.1"
  checksum: 10/fed15a84beb0b9340e5f81566600dbee5eccd92e4b9cc42a944359b1aa1082373391d9d5fc3656981dff27233ec935d0bc96453cf507f60a4b079463999244d8
  languageName: node
  linkType: hard

"@babel/traverse@npm:^7.27.1, @babel/traverse@npm:^7.27.3":
  version: 7.27.3
  resolution: "@babel/traverse@npm:7.27.3"
  dependencies:
    "@babel/code-frame": "npm:^7.27.1"
    "@babel/generator": "npm:^7.27.3"
    "@babel/parser": "npm:^7.27.3"
    "@babel/template": "npm:^7.27.2"
    "@babel/types": "npm:^7.27.3"
    debug: "npm:^4.3.1"
    globals: "npm:^11.1.0"
  checksum: 10/caccdb8335705847d34123ee76d70c3fb575f8c8b3de83ff1560c5d3229269fd4721510b7b63564a122995eb844f651313cad57b85d45023a0f50f848bf23c55
  languageName: node
  linkType: hard

"@babel/types@npm:^7.27.1, @babel/types@npm:^7.27.3":
  version: 7.27.3
  resolution: "@babel/types@npm:7.27.3"
  dependencies:
    "@babel/helper-string-parser": "npm:^7.27.1"
    "@babel/helper-validator-identifier": "npm:^7.27.1"
  checksum: 10/a24e6accd85c4747b974b3d68a3210d0aa1180c1a77b287ffcb7401cd2edad7bdecadaeb40fe5191be3990c3a5252943f7de7c09da13ed269adbb054b97056ee
  languageName: node
  linkType: hard

"@crm/loopback@github:perkd/crm-loopback#semver:^0.8.1, @crm/loopback@github:perkd/crm-loopback#semver:^0.8.2":
  version: 0.8.2
  resolution: "@crm/loopback@https://github.com/perkd/crm-loopback.git#commit=719f4180ea4f7887bc212c121a63438b1d414c67"
  dependencies:
    "@crm/types": "github:perkd/crm-types#semver:^1.10.16"
    "@perkd/errors": "github:perkd/errors#semver:^0.5.1"
    "@perkd/utils": "github:perkd/utils#semver:^1.9.10"
    debug: "npm:^4.4.0"
  checksum: 10/ad02c8c7d469773cfff7c891a2accb3beb64a77c3995de0967febb150436b159a90055c42ccd8f6004aaa25a1ed5e9105936df27dd21b727747b0fd89bd06c53
  languageName: node
  linkType: hard

"@crm/types@github:perkd/crm-types#semver:^1.10.0, @crm/types@github:perkd/crm-types#semver:^1.10.16, @crm/types@github:perkd/crm-types#semver:^1.10.19, @crm/types@github:perkd/crm-types#semver:^1.10.23, @crm/types@github:perkd/crm-types#semver:^1.10.24, @crm/types@github:perkd/crm-types#semver:^1.10.3, @crm/types@github:perkd/crm-types#semver:^1.10.6, @crm/types@github:perkd/crm-types#semver:^1.10.9, @crm/types@github:perkd/crm-types#semver:^1.11.16, @crm/types@github:perkd/crm-types#semver:^1.11.17, @crm/types@github:perkd/crm-types#semver:^1.11.19, @crm/types@github:perkd/crm-types#semver:^1.8.43":
  version: 1.11.19
  resolution: "@crm/types@https://github.com/perkd/crm-types.git#commit=ef5291faf6574d114fb96c17a2911fa4e3e116da"
  dependencies:
    "@perkd/wallet": "github:perkd/wallet#semver:^0.5.0"
    tslib: "npm:^2.8.1"
  checksum: 10/b1fb5c53591843b8d56964e1240df99c80b05c0a6dbe4a7288d0bfd842b287ba9323c4b46f7610f21360b8f48f4345061807d3f8bda3cd8f5f5ca2a9e54fd788
  languageName: node
  linkType: hard

"@eslint-community/eslint-utils@npm:^4.1.2, @eslint-community/eslint-utils@npm:^4.2.0, @eslint-community/eslint-utils@npm:^4.5.0, @eslint-community/eslint-utils@npm:^4.5.1":
  version: 4.7.0
  resolution: "@eslint-community/eslint-utils@npm:4.7.0"
  dependencies:
    eslint-visitor-keys: "npm:^3.4.3"
  peerDependencies:
    eslint: ^6.0.0 || ^7.0.0 || >=8.0.0
  checksum: 10/43ed5d391526d9f5bbe452aef336389a473026fca92057cf97c576db11401ce9bcf8ef0bf72625bbaf6207ed8ba6bf0dcf4d7e809c24f08faa68a28533c491a7
  languageName: node
  linkType: hard

"@eslint-community/regexpp@npm:^4.11.0, @eslint-community/regexpp@npm:^4.12.1":
  version: 4.12.1
  resolution: "@eslint-community/regexpp@npm:4.12.1"
  checksum: 10/c08f1dd7dd18fbb60bdd0d85820656d1374dd898af9be7f82cb00451313402a22d5e30569c150315b4385907cdbca78c22389b2a72ab78883b3173be317620cc
  languageName: node
  linkType: hard

"@eslint/config-array@npm:^0.20.0":
  version: 0.20.0
  resolution: "@eslint/config-array@npm:0.20.0"
  dependencies:
    "@eslint/object-schema": "npm:^2.1.6"
    debug: "npm:^4.3.1"
    minimatch: "npm:^3.1.2"
  checksum: 10/9db7f6cbb5363f2f98ee4805ce09d1a95c4349e86f3f456f2c23a0849b7a6aa8d2be4c25e376ee182af062762e15a101844881c89b566eea0856c481ffcb2090
  languageName: node
  linkType: hard

"@eslint/config-helpers@npm:^0.2.1":
  version: 0.2.2
  resolution: "@eslint/config-helpers@npm:0.2.2"
  checksum: 10/55dbb0b8d63c4cb28fa2a5fd5f16c785f6bd87eb0f50d2f42ec3f7d06b5c6201e2e170846a4360ca00105578b034fba132ed54e4ee3215be240c4a43e7839189
  languageName: node
  linkType: hard

"@eslint/core@npm:^0.14.0":
  version: 0.14.0
  resolution: "@eslint/core@npm:0.14.0"
  dependencies:
    "@types/json-schema": "npm:^7.0.15"
  checksum: 10/d9b060cf97468150675ddf4fb3db55edaa32467e0adf9f80919a5bfd15d0835ad7765456f4397ec2d16b0a1bb702af63f6d4712f94194d34fea118231ae1e2db
  languageName: node
  linkType: hard

"@eslint/eslintrc@npm:^3.3.1":
  version: 3.3.1
  resolution: "@eslint/eslintrc@npm:3.3.1"
  dependencies:
    ajv: "npm:^6.12.4"
    debug: "npm:^4.3.2"
    espree: "npm:^10.0.1"
    globals: "npm:^14.0.0"
    ignore: "npm:^5.2.0"
    import-fresh: "npm:^3.2.1"
    js-yaml: "npm:^4.1.0"
    minimatch: "npm:^3.1.2"
    strip-json-comments: "npm:^3.1.1"
  checksum: 10/cc240addbab3c5fceaa65b2c8d5d4fd77ddbbf472c2f74f0270b9d33263dc9116840b6099c46b64c9680301146250439b044ed79278a1bcc557da412a4e3c1bb
  languageName: node
  linkType: hard

"@eslint/js@npm:9.27.0":
  version: 9.27.0
  resolution: "@eslint/js@npm:9.27.0"
  checksum: 10/cdbe380fd31bb325b9ec65ae310fb92a57efb796d3cc5d8bc9dafef447d634c64e497bedade6a49e0cf44a5b14560ab6ac9ed9da5a38e2415b31ae01ae5b758e
  languageName: node
  linkType: hard

"@eslint/object-schema@npm:^2.1.6":
  version: 2.1.6
  resolution: "@eslint/object-schema@npm:2.1.6"
  checksum: 10/266085c8d3fa6cd99457fb6350dffb8ee39db9c6baf28dc2b86576657373c92a568aec4bae7d142978e798b74c271696672e103202d47a0c148da39154351ed6
  languageName: node
  linkType: hard

"@eslint/plugin-kit@npm:^0.3.1":
  version: 0.3.1
  resolution: "@eslint/plugin-kit@npm:0.3.1"
  dependencies:
    "@eslint/core": "npm:^0.14.0"
    levn: "npm:^0.4.1"
  checksum: 10/ab0c4cecadc6c38c7ae5f71b9831d3521d08237444d8f327751d1133a4369ccd42093a1c06b26fd6c311015807a27d95a0184a761d1cdd264b090896dcf0addb
  languageName: node
  linkType: hard

"@googleapis/calendar@npm:^9.8.0":
  version: 9.8.0
  resolution: "@googleapis/calendar@npm:9.8.0"
  dependencies:
    googleapis-common: "npm:^7.0.0"
  checksum: 10/a34be5b728a21697ab8d546d6200df7919e2336be76bde3fb12af27efd5f7fe2c44ec7d383fd7d59cab67d0251f6369cc0c7493f365107f9d8590feae573420b
  languageName: node
  linkType: hard

"@googlemaps/google-maps-services-js@npm:^3.4.1":
  version: 3.4.1
  resolution: "@googlemaps/google-maps-services-js@npm:3.4.1"
  dependencies:
    "@googlemaps/url-signature": "npm:^1.0.4"
    agentkeepalive: "npm:^4.1.0"
    axios: "npm:^1.5.1"
    query-string: "npm:<8.x"
    retry-axios: "npm:<3.x"
  checksum: 10/704b12206cc3047670e8e760ccbf050097b518db6cbee967bfc4634cbb8a8e577b15f5515505b4a5619d6c1f77d9fe4cec25e4220e08e259967897e244e443d8
  languageName: node
  linkType: hard

"@googlemaps/url-signature@npm:^1.0.4":
  version: 1.0.40
  resolution: "@googlemaps/url-signature@npm:1.0.40"
  dependencies:
    crypto-js: "npm:^4.2.0"
  checksum: 10/8ffd53774815fafcb350ec9c8a5741806bcd80bca63156f9f7c54dd073e496edfe07df914316a218a980cea80b0ef409b556f91af62e7b60b8d907fa1855e39a
  languageName: node
  linkType: hard

"@humanfs/core@npm:^0.19.1":
  version: 0.19.1
  resolution: "@humanfs/core@npm:0.19.1"
  checksum: 10/270d936be483ab5921702623bc74ce394bf12abbf57d9145a69e8a0d1c87eb1c768bd2d93af16c5705041e257e6d9cc7529311f63a1349f3678abc776fc28523
  languageName: node
  linkType: hard

"@humanfs/node@npm:^0.16.6":
  version: 0.16.6
  resolution: "@humanfs/node@npm:0.16.6"
  dependencies:
    "@humanfs/core": "npm:^0.19.1"
    "@humanwhocodes/retry": "npm:^0.3.0"
  checksum: 10/6d43c6727463772d05610aa05c83dab2bfbe78291022ee7a92cb50999910b8c720c76cc312822e2dea2b497aa1b3fef5fe9f68803fc45c9d4ed105874a65e339
  languageName: node
  linkType: hard

"@humanwhocodes/module-importer@npm:^1.0.1":
  version: 1.0.1
  resolution: "@humanwhocodes/module-importer@npm:1.0.1"
  checksum: 10/e993950e346331e5a32eefb27948ecdee2a2c4ab3f072b8f566cd213ef485dd50a3ca497050608db91006f5479e43f91a439aef68d2a313bd3ded06909c7c5b3
  languageName: node
  linkType: hard

"@humanwhocodes/retry@npm:^0.3.0":
  version: 0.3.1
  resolution: "@humanwhocodes/retry@npm:0.3.1"
  checksum: 10/eb457f699529de7f07649679ec9e0353055eebe443c2efe71c6dd950258892475a038e13c6a8c5e13ed1fb538cdd0a8794faa96b24b6ffc4c87fb1fc9f70ad7f
  languageName: node
  linkType: hard

"@humanwhocodes/retry@npm:^0.4.2":
  version: 0.4.3
  resolution: "@humanwhocodes/retry@npm:0.4.3"
  checksum: 10/0b32cfd362bea7a30fbf80bb38dcaf77fee9c2cae477ee80b460871d03590110ac9c77d654f04ec5beaf71b6f6a89851bdf6c1e34ccdf2f686bd86fcd97d9e61
  languageName: node
  linkType: hard

"@ioredis/commands@npm:^1.1.1":
  version: 1.2.0
  resolution: "@ioredis/commands@npm:1.2.0"
  checksum: 10/a8253c9539b7e5463d4a98e6aa5b1b863fb4a4978191ba9dc42ec2c0fb5179d8d1fe4a29096d5954f91ba9600d1bdc6c1d18b044eab36f645f267fd37d7c0906
  languageName: node
  linkType: hard

"@isaacs/cliui@npm:^8.0.2":
  version: 8.0.2
  resolution: "@isaacs/cliui@npm:8.0.2"
  dependencies:
    string-width: "npm:^5.1.2"
    string-width-cjs: "npm:string-width@^4.2.0"
    strip-ansi: "npm:^7.0.1"
    strip-ansi-cjs: "npm:strip-ansi@^6.0.1"
    wrap-ansi: "npm:^8.1.0"
    wrap-ansi-cjs: "npm:wrap-ansi@^7.0.0"
  checksum: 10/e9ed5fd27c3aec1095e3a16e0c0cf148d1fee55a38665c35f7b3f86a9b5d00d042ddaabc98e8a1cb7463b9378c15f22a94eb35e99469c201453eb8375191f243
  languageName: node
  linkType: hard

"@isaacs/fs-minipass@npm:^4.0.0":
  version: 4.0.1
  resolution: "@isaacs/fs-minipass@npm:4.0.1"
  dependencies:
    minipass: "npm:^7.0.4"
  checksum: 10/4412e9e6713c89c1e66d80bb0bb5a2a93192f10477623a27d08f228ba0316bb880affabc5bfe7f838f58a34d26c2c190da726e576cdfc18c49a72e89adabdcf5
  languageName: node
  linkType: hard

"@jridgewell/gen-mapping@npm:^0.3.5":
  version: 0.3.8
  resolution: "@jridgewell/gen-mapping@npm:0.3.8"
  dependencies:
    "@jridgewell/set-array": "npm:^1.2.1"
    "@jridgewell/sourcemap-codec": "npm:^1.4.10"
    "@jridgewell/trace-mapping": "npm:^0.3.24"
  checksum: 10/9d3a56ab3612ab9b85d38b2a93b87f3324f11c5130859957f6500e4ac8ce35f299d5ccc3ecd1ae87597601ecf83cee29e9afd04c18777c24011073992ff946df
  languageName: node
  linkType: hard

"@jridgewell/resolve-uri@npm:^3.1.0":
  version: 3.1.2
  resolution: "@jridgewell/resolve-uri@npm:3.1.2"
  checksum: 10/97106439d750a409c22c8bff822d648f6a71f3aa9bc8e5129efdc36343cd3096ddc4eeb1c62d2fe48e9bdd4db37b05d4646a17114ecebd3bbcacfa2de51c3c1d
  languageName: node
  linkType: hard

"@jridgewell/set-array@npm:^1.2.1":
  version: 1.2.1
  resolution: "@jridgewell/set-array@npm:1.2.1"
  checksum: 10/832e513a85a588f8ed4f27d1279420d8547743cc37fcad5a5a76fc74bb895b013dfe614d0eed9cb860048e6546b798f8f2652020b4b2ba0561b05caa8c654b10
  languageName: node
  linkType: hard

"@jridgewell/sourcemap-codec@npm:^1.4.10, @jridgewell/sourcemap-codec@npm:^1.4.14":
  version: 1.5.0
  resolution: "@jridgewell/sourcemap-codec@npm:1.5.0"
  checksum: 10/4ed6123217569a1484419ac53f6ea0d9f3b57e5b57ab30d7c267bdb27792a27eb0e4b08e84a2680aa55cc2f2b411ffd6ec3db01c44fdc6dc43aca4b55f8374fd
  languageName: node
  linkType: hard

"@jridgewell/trace-mapping@npm:^0.3.24, @jridgewell/trace-mapping@npm:^0.3.25":
  version: 0.3.25
  resolution: "@jridgewell/trace-mapping@npm:0.3.25"
  dependencies:
    "@jridgewell/resolve-uri": "npm:^3.1.0"
    "@jridgewell/sourcemap-codec": "npm:^1.4.14"
  checksum: 10/dced32160a44b49d531b80a4a2159dceab6b3ddf0c8e95a0deae4b0e894b172defa63d5ac52a19c2068e1fe7d31ea4ba931fbeec103233ecb4208953967120fc
  languageName: node
  linkType: hard

"@jsep-plugin/assignment@npm:^1.3.0":
  version: 1.3.0
  resolution: "@jsep-plugin/assignment@npm:1.3.0"
  peerDependencies:
    jsep: ^0.4.0||^1.0.0
  checksum: 10/0c93b703d84af95b4be9fb6c23fbdbe7c7b6985b41c98fd10386cd54686ed1eb751cb39f5d54abcb621e4da2a0900a3b2a852e5bf7f2d322b756db3b22e42a45
  languageName: node
  linkType: hard

"@jsep-plugin/regex@npm:^1.0.4":
  version: 1.0.4
  resolution: "@jsep-plugin/regex@npm:1.0.4"
  peerDependencies:
    jsep: ^0.4.0||^1.0.0
  checksum: 10/0ea6ba81f03955972b762fd9fbc8e3fd7e1c1c12e52ce3d4366e23c0a63c8bff8528687b8b3d8f641cf9f626f8bf5a7841efcd31a2489fe967e1900e5738ee3a
  languageName: node
  linkType: hard

"@nicolo-ribaudo/eslint-scope-5-internals@npm:5.1.1-v1":
  version: 5.1.1-v1
  resolution: "@nicolo-ribaudo/eslint-scope-5-internals@npm:5.1.1-v1"
  dependencies:
    eslint-scope: "npm:5.1.1"
  checksum: 10/f2e3b2d6a6e2d9f163ca22105910c9f850dc4897af0aea3ef0a5886b63d8e1ba6505b71c99cb78a3bba24a09557d601eb21c8dede3f3213753fcfef364eb0e57
  languageName: node
  linkType: hard

"@npmcli/agent@npm:^3.0.0":
  version: 3.0.0
  resolution: "@npmcli/agent@npm:3.0.0"
  dependencies:
    agent-base: "npm:^7.1.0"
    http-proxy-agent: "npm:^7.0.0"
    https-proxy-agent: "npm:^7.0.1"
    lru-cache: "npm:^10.0.1"
    socks-proxy-agent: "npm:^8.0.3"
  checksum: 10/775c9a7eb1f88c195dfb3bce70c31d0fe2a12b28b754e25c08a3edb4bc4816bfedb7ac64ef1e730579d078ca19dacf11630e99f8f3c3e0fd7b23caa5fd6d30a6
  languageName: node
  linkType: hard

"@npmcli/fs@npm:^4.0.0":
  version: 4.0.0
  resolution: "@npmcli/fs@npm:4.0.0"
  dependencies:
    semver: "npm:^7.3.5"
  checksum: 10/405c4490e1ff11cf299775449a3c254a366a4b1ffc79d87159b0ee7d5558ac9f6a2f8c0735fd6ff3873cef014cb1a44a5f9127cb6a1b2dbc408718cca9365b5a
  languageName: node
  linkType: hard

"@perkd/accesscontrol@github:perkd/accesscontrol#semver:^0.5.0":
  version: 0.5.0
  resolution: "@perkd/accesscontrol@https://github.com/perkd/accesscontrol.git#commit=b7423e7229455259b98d498eb41eab704c1cec95"
  dependencies:
    tslib: "npm:^2.8.1"
  checksum: 10/bacc8014abb46dac0b46c6fbccf40629abc707b939d2bc57ebf600f02c84b679a0f17482ef44bae950d585af50e005d951764ed582eb3b028e2c08ea9c601705
  languageName: node
  linkType: hard

"@perkd/actions@github:perkd/actions#semver:^0.5.0":
  version: 0.5.0
  resolution: "@perkd/actions@https://github.com/perkd/actions.git#commit=78dc83126b47eaa3ccf7904b4b5cc1c6b5506507"
  dependencies:
    "@perkd/hosts": "github:perkd/hosts#semver:^0.2.1"
    bl: "npm:^6.1.0"
    tslib: "npm:^2.8.1"
  checksum: 10/9d21052edd8824aeeeb189de0f9276e5224620711ca4a080ce2a135f7664a96ef5901bd74df063fcb7ddc086e343af9559f2157cb91729ec2756599fa3872093
  languageName: node
  linkType: hard

"@perkd/activity-registry-crm@github:perkd/activity-registry-crm#semver:^1.1.5":
  version: 1.1.5
  resolution: "@perkd/activity-registry-crm@https://github.com/perkd/activity-registry-crm.git#commit=e7d816a792fa3f8f155d7f566aed8e2f108856ec"
  checksum: 10/d215595140a626779309d00764619af84972c85f2799f6c17962657ba12a628eb29fca821bd8a3a34b15269d8d6b6bac4f9556641bcf7c68af47bec247b87e7c
  languageName: node
  linkType: hard

"@perkd/api-request@github:perkd/api-request#semver:^1.17.1, @perkd/api-request@github:perkd/api-request#semver:^1.17.2":
  version: 1.17.3
  resolution: "@perkd/api-request@https://github.com/perkd/api-request.git#commit=45800e12bdbbcc3041454aca38cf27bea97a2da5"
  dependencies:
    axios: "npm:^1.9.0"
    axios-retry: "npm:^4.5.0"
    limiter: "npm:^3.0.0"
    tslib: "npm:^2.8.1"
  checksum: 10/fecd0ca25b7b013d90c726eb1859abf9905cd60b84cf71f826323bbb1a84e4263abf20743746e26e573c4914100e78e865416b0f1d82c1a1b12bf7e300cc7313
  languageName: node
  linkType: hard

"@perkd/api-request@github:perkd/api-request#semver:^2.0.1":
  version: 2.0.2
  resolution: "@perkd/api-request@https://github.com/perkd/api-request.git#commit=0d271d568a0d9d649d0cd0a0e41ba6f76190c1b0"
  dependencies:
    axios: "npm:^1.9.0"
    axios-retry: "npm:^4.5.0"
    limiter: "npm:^3.0.0"
    tslib: "npm:^2.8.1"
  checksum: 10/ca9f3c3c2a7edf112df27e766b2285787b47ccdad97a8296adfcdbbd77f52b6b062908386b4e9f2c435c2aa5729a88f250f91f9ddbb08e8f46a720c07ee21a97
  languageName: node
  linkType: hard

"@perkd/aws-secrets@github:perkd/aws-secrets#semver:^2.0.0":
  version: 2.0.0
  resolution: "@perkd/aws-secrets@https://github.com/perkd/aws-secrets.git#commit=4d7d68609ca0b74365b62f6e10975adb5494bc0e"
  dependencies:
    "@aws-sdk/client-secrets-manager": "npm:^3.799.0"
  checksum: 10/3eaec57b0e7e0cd3a7b4f6af83ec2f9bf7efe7d62e28c94bb73ac09cab3dde48ee71587b73a664fdb0170b2720f009568a41fa914f87e539d5e2c190d2f0f813
  languageName: node
  linkType: hard

"@perkd/cached-config@github:perkd/cached-config#semver:^1.7.0":
  version: 1.7.1
  resolution: "@perkd/cached-config@https://github.com/perkd/cached-config.git#commit=ddd9368070fbe141e6464b93a9d28bcb61656725"
  dependencies:
    "@perkd/pubsub": "github:perkd/pubsub#semver:^4.7.1"
    tslib: "npm:^2.8.1"
  checksum: 10/32f0750315b46d4062eea48913f2ead548901107729f042ba2ae4115f3fcdfbe4b74b5baefd476cb70235ca452d9c5be5b58821ee8201c7be0d0f61e39d176e8
  languageName: node
  linkType: hard

"@perkd/commerce@github:perkd/commerce#semver:^1.7.3":
  version: 1.7.3
  resolution: "@perkd/commerce@https://github.com/perkd/commerce.git#commit=61e210ba40abf3631ec5d8bf6c858a467256878b"
  dependencies:
    "@crm/types": "github:perkd/crm-types#semver:^1.11.16"
    "@perkd/utils": "github:perkd/utils#semver:^2.0.1"
    nanoid: "npm:3.3.8"
    rfdc: "npm:^1.4.1"
    tslib: "npm:^2.8.1"
  checksum: 10/c3e974bb72a553314d0f075175e3019b366b52365e63551e4e864e2c4cc672985512e66ae337cceef3a3aaa8455638ab73fcfd0696713299ee93da045e66f82f
  languageName: node
  linkType: hard

"@perkd/data-prometheus@github:perkd/data-prometheus#semver:^0.4.14":
  version: 0.4.14
  resolution: "@perkd/data-prometheus@https://github.com/perkd/data-prometheus.git#commit=032a22875ee5a9ee9caa22883639881d491b4656"
  dependencies:
    "@perkd/metrics": "github:perkd/metrics#semver:^1.8.0"
    "@provider/providers": "github:perkd/provider-providers#semver:^1.7.0"
    prometheus-query: "npm:^3.4.1"
    tslib: "npm:^2.8.1"
  checksum: 10/e2c2eb9a7b020fa9157bad1144530a860a81f4ed6693bba630fd82b3facf2e94ad74b105a51eed0aa2b99c10e590c8f00a7b8ce4a620678312ca7032b00df681
  languageName: node
  linkType: hard

"@perkd/errors@github:perkd/errors#semver:^0.5.1":
  version: 0.5.1
  resolution: "@perkd/errors@https://github.com/perkd/errors.git#commit=926c82412553e3a23e1a9014378e5ce645aa3fdb"
  dependencies:
    http-errors: "npm:^2.0.0"
    tslib: "npm:^2.8.1"
  checksum: 10/f1a287b09681dd950beca087bc70851db4b9f7ee7292a8474cd6597b53c30f4156a22f96b7316966f3800cf1c17eba21983f0198cdc06fc86f47a71824f4e634
  languageName: node
  linkType: hard

"@perkd/eslint-config@github:Perkd-X/eslint-config#semver:^3.1.3":
  version: 3.1.3
  resolution: "@perkd/eslint-config@https://github.com/Perkd-X/eslint-config.git#commit=864553f9b5517707787a1e0e5813ee1dc76060f0"
  checksum: 10/0ce64ec413bb493ec9482012369c7dc9e9509cb1360aba08d21fb2cca2c2ea1b86f56882bf5c0f21e18a74563a4001e5f2ef50a31226ac629f867a0928f7d7d5
  languageName: node
  linkType: hard

"@perkd/event-registry-crm@github:perkd/event-registry-crm#semver:^1.5.4":
  version: 1.5.4
  resolution: "@perkd/event-registry-crm@https://github.com/perkd/event-registry-crm.git#commit=f098353b409bf291b16e575504aa639f1801dd6f"
  checksum: 10/2914682ffebdeb1669092e6a11356d5743193929086d33164e0d07bf21be00669e09e004b410effea64596faa5c37813c7d549bd366b7082fcdfb0ccdf082861
  languageName: node
  linkType: hard

"@perkd/eventbus@github:perkd/eventbus#semver:^4.9.5":
  version: 4.9.5
  resolution: "@perkd/eventbus@https://github.com/perkd/eventbus.git#commit=e0d09c9c0891a0d4e6459ae0e91507efbb274a9c"
  dependencies:
    "@perkd/redis": "github:perkd/redis#semver:^4.8.1"
    "@perkd/utils": "github:perkd/utils#semver:^2.0.1"
    limiter: "npm:^3.0.0"
    tslib: "npm:^2.8.1"
  checksum: 10/5eb03ddd9634b1c9a211bd8bc276f31a46fea4fa31dc4c151c6ac9d4159cd5ab23e3d5a3aa8ee67f5bdb6675d1d90b045fb058e55c85910c3556605ac1725021
  languageName: node
  linkType: hard

"@perkd/format-datetime@github:perkd/format-datetime#semver:^1.3.1, @perkd/format-datetime@github:perkd/format-datetime#semver:^1.3.3":
  version: 1.3.3
  resolution: "@perkd/format-datetime@https://github.com/perkd/format-datetime.git#commit=09fc7564a4ed19f213d7e9cc92c4b78ae40699fd"
  dependencies:
    dayjs: "npm:^1.11.13"
  checksum: 10/d74bf673d52436b9d5c8145f5089e5356dbb1b7ac8f1d862672376aa27d1f3399d1f5080005971f1e655ccb2180748c38c89cba7d40a52471e643bf464574b97
  languageName: node
  linkType: hard

"@perkd/fulfillments@github:perkd/fulfillments#semver:^0.5.2":
  version: 0.5.2
  resolution: "@perkd/fulfillments@https://github.com/perkd/fulfillments.git#commit=645214702de1c5a507fd244e278f45fd01fa61bd"
  dependencies:
    "@crm/types": "github:perkd/crm-types#semver:^1.10.19"
    tslib: "npm:^2.8.1"
  checksum: 10/1d61b394e6684c7aa4cb861025a3c35f477d88bb2833f54e2bd00e58af14466782f139671c1635f0cf2306d7ade25f7de3bf972b32bc2d3e915ff7066cb561f8
  languageName: node
  linkType: hard

"@perkd/grab-sdk@github:perkd/grab-sdk#semver:^1.0.17":
  version: 1.1.0
  resolution: "@perkd/grab-sdk@https://github.com/perkd/grab-sdk.git#commit=54b08eb6612ca345e6792bbcc13eadcf579f4a3b"
  dependencies:
    "@crm/types": "github:perkd/crm-types#semver:^1.10.23"
    "@perkd/api-request": "github:perkd/api-request#semver:^2.0.1"
    "@perkd/metrics": "github:perkd/metrics#semver:^1.8.0"
    "@perkd/utils": "github:perkd/utils#semver:^2.0.0"
    "@provider/providers": "github:perkd/provider-providers#semver:^1.7.0"
  checksum: 10/be410c3cd3f2c8798b4141b6dbc5979a316d283b9b8fa775e63c4b3045c2b5a94ba5d3813d40538ce585a45975cfe90e03904eee9668c5d25ff042e3088ab8a6
  languageName: node
  linkType: hard

"@perkd/hosts@github:perkd/hosts#semver:^0.2.1":
  version: 0.2.1
  resolution: "@perkd/hosts@https://github.com/perkd/hosts.git#commit=d50f69cca8697236b3f7ebdcd842855416e1c80b"
  checksum: 10/12fc063e6152dd324a11a02bcd7d36fd55efbb0b26ed9134c166271c15d1ce54243f4e4a3f56c82fc4fc3aae7c506299e72ea76c2c82c1d177fc71fd10aa6731
  languageName: node
  linkType: hard

"@perkd/machines@github:perkd/machines#semver:^0.4.0":
  version: 0.4.0
  resolution: "@perkd/machines@https://github.com/perkd/machines.git#commit=59a9b9b8ab905c5d5560cb6995fe43c20747febe"
  dependencies:
    "@crm/types": "github:perkd/crm-types#semver:^1.10.0"
    tslib: "npm:^2.8.1"
  checksum: 10/9ef2398c39e0a9c971dde5132c79f86d3c0f921328dc27930c2108dbe8cb52c14398284f9b164ce029ecc6bc75affb7535f0e90a65165017e16f12e7ed173021
  languageName: node
  linkType: hard

"@perkd/metrics-push@github:perkd/metrics-push#semver:^1.5.0":
  version: 1.5.0
  resolution: "@perkd/metrics-push@https://github.com/perkd/metrics-push.git#commit=b98d365d770455012c0d2c8748376f6cb1272495"
  dependencies:
    "@perkd/data-prometheus": "github:perkd/data-prometheus#semver:^0.4.14"
    "@perkd/metrics": "github:perkd/metrics#semver:^1.8.0"
    "@perkd/websockets": "github:perkd/websockets#semver:^0.5.7"
    jws: "npm:^4.0.0"
    object.omit: "npm:^3.0.0"
    sift: "npm:^17.1.3"
    tslib: "npm:^2.8.1"
    ws: "npm:^8.18.1"
    xxhashjs: "npm:^0.2.2"
  checksum: 10/b034bbec655f7dc73b879dd7e8786f3f042352c25566ad1f03975035e02ddadbc85830accdb56cad55f4212c6af4f009c1effb2482e1294f2afd54e4270e8ed3
  languageName: node
  linkType: hard

"@perkd/metrics@github:perkd/metrics#semver:^1.7.2, @perkd/metrics@github:perkd/metrics#semver:^1.8.0":
  version: 1.8.0
  resolution: "@perkd/metrics@https://github.com/perkd/metrics.git#commit=008883c1b1800f60c8e49c364ab0da89c9d7759a"
  dependencies:
    hot-shots: "npm:^10.2.1"
    tslib: "npm:^2.8.1"
  checksum: 10/1c3bd0b951b8e6e819c55acedff40433dde9ee4038137f11667a8292faf43e585869da1e429348133ab49ac1da13ac38e6a83f42b7a63faa0ca0ba1a3ae6db3c
  languageName: node
  linkType: hard

"@perkd/multitenant-context@github:perkd/multitenant-context#semver:^0.5.1":
  version: 0.5.3
  resolution: "@perkd/multitenant-context@https://github.com/perkd/multitenant-context.git#commit=2688c75ef61141facd7cdd30935b8bfce0b19a88"
  dependencies:
    "@crm/loopback": "github:perkd/crm-loopback#semver:^0.8.1"
    "@perkd/errors": "github:perkd/errors#semver:^0.5.1"
    "@perkd/utils": "github:perkd/utils#semver:^1.9.10"
    "@perkd/wallet": "github:perkd/wallet#semver:^0.5.0"
    tslib: "npm:^2.8.1"
  checksum: 10/dbeeb0805a77109784b1029562ca8607225eccdecc9d3b37d5a7003e686870614bf2cf723d38a115590cce3e901b6a5d339665cc99e98893a6f67b8a672b7590
  languageName: node
  linkType: hard

"@perkd/multitenant-context@github:perkd/multitenant-context#semver:^0.6.1":
  version: 0.6.1
  resolution: "@perkd/multitenant-context@https://github.com/perkd/multitenant-context.git#commit=23bd819f6223418711972826cbe70492527503f8"
  dependencies:
    "@crm/loopback": "github:perkd/crm-loopback#semver:^0.8.2"
    "@perkd/errors": "github:perkd/errors#semver:^0.5.1"
    "@perkd/utils": "github:perkd/utils#semver:^2.0.4"
    "@perkd/wallet": "github:perkd/wallet#semver:^0.5.0"
    tslib: "npm:^2.8.1"
  checksum: 10/f55f442c06bd2cac2f227629defe1bb2d90db8054d742b6ee19c6cce19ab6fabc81d502616adc8e65bdaab48b445e54b1c2c0981f041063c81b8fff0486332c7
  languageName: node
  linkType: hard

"@perkd/orders@github:perkd/orders#semver:^0.5.7":
  version: 0.5.7
  resolution: "@perkd/orders@https://github.com/perkd/orders.git#commit=c0b7fe5e7fa5f29a40e15d3aad4aa886415d3654"
  dependencies:
    "@crm/types": "github:perkd/crm-types#semver:^1.10.9"
    "@perkd/format-datetime": "github:perkd/format-datetime#semver:^1.3.1"
    tslib: "npm:^2.8.1"
  checksum: 10/a32a3d39566ac8e328a0b7790e923ad65de4ec59e0487705ee8d20f918621b2cebbfbd131913e69370212369f80d36189fffa539658b9dbdec1e4aa5553c3639
  languageName: node
  linkType: hard

"@perkd/providers@github:perkd/providers#semver:^5.0.0":
  version: 5.0.0
  resolution: "@perkd/providers@https://github.com/perkd/providers.git#commit=cc0845098d22e47bf2604a4db8fcde7fb919c711"
  dependencies:
    "@perkd/aws-secrets": "github:perkd/aws-secrets#semver:^2.0.0"
    "@perkd/redis": "github:perkd/redis#semver:^4.8.1"
    "@types/node": "npm:^22.15.3"
    tslib: "npm:^2.8.1"
  checksum: 10/c9770fa314ccb9a09e1576da45f3c519097af248333b4545e1688a52778f98748b707f771e2eba16a66cdf72e3291391ed7c75f4596f124aae5b32feada3d4e7
  languageName: node
  linkType: hard

"@perkd/provisions@github:perkd/provisions#semver:^0.5.0":
  version: 0.5.0
  resolution: "@perkd/provisions@https://github.com/perkd/provisions.git#commit=59963801b98ccb0cb8ba9739b9ad63f6278c7c32"
  dependencies:
    "@perkd/accesscontrol": "github:perkd/accesscontrol#semver:^0.5.0"
    tslib: "npm:^2.8.1"
  checksum: 10/7e06e37ce3769970442bf5a87d1e93e35cb7249b5b04904a0096c7f5864bf0c7edf9f15ac81495ce2d6d92d0764039790579673c3f3367fc0185bbe7bef5e358
  languageName: node
  linkType: hard

"@perkd/pubsub@github:perkd/pubsub#semver:^4.7.1":
  version: 4.7.1
  resolution: "@perkd/pubsub@https://github.com/perkd/pubsub.git#commit=418fb0be2f81baaf49692cf177891351d249ab8c"
  dependencies:
    "@perkd/redis": "github:perkd/redis#semver:^4.8.1"
    tslib: "npm:^2.8.1"
  checksum: 10/66ebbb505a3f00091db5140f5d98dec1e52d765832d29b67ebafd14abb5976f7e5fc31ff142207187e774c639b2b507f12b67992a36ba902ddc00ddd1e544116
  languageName: node
  linkType: hard

"@perkd/redis@github:perkd/redis#semver:^4.8.1":
  version: 4.8.1
  resolution: "@perkd/redis@https://github.com/perkd/redis.git#commit=40321a8aab57bbfd4df0fa778b738849fb551819"
  dependencies:
    deepmerge: "npm:^4.3.1"
    ioredis: "npm:^5.6.1"
    nanoid: "npm:3.3.8"
    tslib: "npm:^2.8.1"
  checksum: 10/7b243c309540b7ee304c824bdb6a726debe9c1586467b765a0b96ebfe48a6b2bb4524597121dfbb1465b71ec0691488ee67fca5f858a26e7e0c6fbf9b2b7d33b
  languageName: node
  linkType: hard

"@perkd/sdk@github:perkd/wallet-perkd#semver:^1.6.0":
  version: 1.6.0
  resolution: "@perkd/sdk@https://github.com/perkd/wallet-perkd.git#commit=5f09c0e3e0d4f959015319848ec775d45c00a8a0"
  dependencies:
    "@crm/types": "github:perkd/crm-types#semver:^1.10.0"
    "@perkd/api-request": "github:perkd/api-request#semver:^1.17.1"
    "@perkd/metrics": "github:perkd/metrics#semver:^1.8.0"
    "@perkd/utils": "github:perkd/utils#semver:^1.9.6"
    "@provider/providers": "github:perkd/provider-providers#semver:^1.7.0"
  checksum: 10/2e3291cfdcf5f98a6d870ef0316310e52ddab2fa029ec054ad1b304836c0868d3d074cabe8b391101f0d655809d17c247a4559fcdc187378d292cec5132583af
  languageName: node
  linkType: hard

"@perkd/settings@github:perkd/settings#semver:^1.6.0":
  version: 1.6.0
  resolution: "@perkd/settings@https://github.com/perkd/settings.git#commit=6e8031ab9a175095a864208faa711bd07f873537"
  dependencies:
    "@perkd/cached-config": "github:perkd/cached-config#semver:^1.7.0"
  checksum: 10/18b5af0b92ab4b055ee147bb9bbf491c8d671b8e95763a05206f2f9cd8d9581f3f1d70f2665b2450a20643afa63662a83b4f55c02484145f15dcf58b7a5af059
  languageName: node
  linkType: hard

"@perkd/sync@github:perkd/sync#semver:^1.1.5":
  version: 1.1.5
  resolution: "@perkd/sync@https://github.com/perkd/sync.git#commit=41e0853cf3150fdd1f5731fb0bdc76086450d554"
  dependencies:
    "@perkd/pubsub": "github:perkd/pubsub#semver:^4.7.1"
    "@perkd/redis": "github:perkd/redis#semver:^4.8.1"
    fast-deep-equal: "npm:^3.1.3"
    fast-json-patch: "npm:^3.1.1"
    object.omit: "npm:^3.0.0"
    object.pick: "npm:^1.3.0"
    sift: "npm:^17.1.3"
    tslib: "npm:^2.8.1"
  checksum: 10/8a4a70c3c98d56271d9883871fa615ee08f520f095ea73a590bd8589e3459faa3f1c7c41274ee5609ea4d761afaa4d4bfabfe385284962f3fd1bd278e71517a6
  languageName: node
  linkType: hard

"@perkd/tenants@github:perkd/tenants#semver:^4.9.0":
  version: 4.9.0
  resolution: "@perkd/tenants@https://github.com/perkd/tenants.git#commit=037a61b9554f9f2dc9e62fab8032a493a03c9d3c"
  dependencies:
    "@perkd/cached-config": "github:perkd/cached-config#semver:^1.7.0"
    tslib: "npm:^2.8.1"
  checksum: 10/dbdd2e769aeb2bcf3e46becba5319d5f937a63a1fe04b4c5dfe034ebd48053b445965d94c0f6cf4f0f6be67ddbd49c1f4890251e3c20fab6418b980cd64a8128
  languageName: node
  linkType: hard

"@perkd/touchpoints@github:perkd/touchpoints#semver:^0.2.4":
  version: 0.2.4
  resolution: "@perkd/touchpoints@https://github.com/perkd/touchpoints.git#commit=d0272a47ffa9a1cdb2a0cc9d1b7bdb034aaea91a"
  dependencies:
    "@crm/types": "github:perkd/crm-types#semver:^1.10.6"
    "@perkd/multitenant-context": "github:perkd/multitenant-context#semver:^0.5.1"
    tslib: "npm:^2.8.1"
  checksum: 10/a2fb74ba19fd6957c400d1f3cd0c606ee80726b3cd0d9e301755a91f021e06877d837cca8803d5da5aab2110c4cf56d7e0a1008c28d420c7fa5d1af0fe2e9f84
  languageName: node
  linkType: hard

"@perkd/uber-sdk@github:perkd/uber-sdk#semver:^0.8.1":
  version: 0.8.1
  resolution: "@perkd/uber-sdk@https://github.com/perkd/uber-sdk.git#commit=10c464cf82cd442ef84e734eddfeb3228573cce5"
  dependencies:
    "@crm/types": "github:perkd/crm-types#semver:^1.10.24"
    "@perkd/api-request": "github:perkd/api-request#semver:^2.0.1"
    "@perkd/metrics": "github:perkd/metrics#semver:^1.8.0"
    "@perkd/utils": "github:perkd/utils#semver:^2.0.0"
    "@provider/providers": "github:perkd/provider-providers#semver:^1.7.0"
    striptags: "npm:^3.2.0"
  checksum: 10/47baba80ac593b703eb93ee6f772b404af21062fe7e9bd251f10f2093cfb797c4765ba0716ebb2e69a0d73754e53e7e3d18f7fa17d1b5fdb425f033f5f9b66d2
  languageName: node
  linkType: hard

"@perkd/utils@github:perkd/utils#semver:^1.9.10, @perkd/utils@github:perkd/utils#semver:^1.9.6, @perkd/utils@github:perkd/utils#semver:^1.9.7":
  version: 1.10.0
  resolution: "@perkd/utils@https://github.com/perkd/utils.git#commit=ca6543e27a884b4bfa9fe84c31ee83a9e484a687"
  dependencies:
    "@perkd/format-datetime": "github:perkd/format-datetime#semver:^1.3.1"
    bson-objectid: "npm:^2.0.4"
    camelcase: "npm:6.3.0"
    country-data: "npm:^0.0.31"
    currency-symbol-map: "npm:^5.1.0"
    deepmerge: "npm:^4.3.1"
    email-addresses: "npm:^5.0.0"
    flat: "npm:5.0.2"
    franc: "npm:5.0.0"
    get-value: "npm:^4.0.1"
    htmlparser2: "npm:^10.0.0"
    jws: "npm:^4.0.0"
    libphonenumber-js: "npm:^1.12.7"
    limiter: "npm:^3.0.0"
    locale: "npm:^0.1.0"
    nanoid: "npm:3.3.8"
    pinyin-pro: "npm:^3.26.0"
    referer-parser: "npm:^0.0.3"
    rfdc: "npm:^1.4.1"
    set-value: "npm:^4.1.0"
    sift: "npm:^17.1.3"
    similarity: "npm:^1.2.1"
    traverse: "npm:^0.6.11"
    tslib: "npm:^2.8.1"
    validator: "npm:^13.15.0"
  checksum: 10/07c35f85206fe4bad2a8a930ddc7969f2c31178ac422f695a234852275e716df44b253ea7ccae126cdb204bbd54ff8ee099abb93af9191e35ce28741b54dd1d9
  languageName: node
  linkType: hard

"@perkd/utils@github:perkd/utils#semver:^2.0.0, @perkd/utils@github:perkd/utils#semver:^2.0.1, @perkd/utils@github:perkd/utils#semver:^2.0.4, @perkd/utils@github:perkd/utils#semver:^2.0.5":
  version: 2.0.5
  resolution: "@perkd/utils@https://github.com/perkd/utils.git#commit=d27d4e654693d6f0ba7a5da056b49353433b1940"
  dependencies:
    "@perkd/format-datetime": "github:perkd/format-datetime#semver:^1.3.3"
    bson-objectid: "npm:^2.0.4"
    camelcase: "npm:6.3.0"
    country-data: "npm:^0.0.31"
    currency-symbol-map: "npm:^5.1.0"
    deepmerge: "npm:^4.3.1"
    email-addresses: "npm:^5.0.0"
    flat: "npm:5.0.2"
    franc: "npm:5.0.0"
    get-value: "npm:^4.0.1"
    htmlparser2: "npm:^10.0.0"
    jws: "npm:^4.0.0"
    libphonenumber-js: "npm:^1.12.8"
    limiter: "npm:^3.0.0"
    locale: "npm:^0.1.0"
    nanoid: "npm:3.3.8"
    pinyin-pro: "npm:^3.26.0"
    referer-parser: "npm:^0.0.3"
    rfdc: "npm:^1.4.1"
    set-value: "npm:^4.1.0"
    sift: "npm:^17.1.3"
    similarity: "npm:^1.2.1"
    traverse: "npm:^0.6.11"
    tslib: "npm:^2.8.1"
    validator: "npm:^13.15.0"
  checksum: 10/4c20ac1e0cc3032aacdf85eb6af83cff3c38f7fbeecba30a865655e3d8652b0ed683afb53ad5a9baa765bb25fa9d3923fb6c2af5d6d1c341831b0b6c5f3a4533
  languageName: node
  linkType: hard

"@perkd/wallet-widgets@github:perkd/wallet-widgets#semver:^0.6.0":
  version: 0.6.0
  resolution: "@perkd/wallet-widgets@https://github.com/perkd/wallet-widgets.git#commit=e9c27fba7250ff4eeec1c778820b85c08be68a70"
  dependencies:
    rfdc: "npm:^1.4.1"
    tslib: "npm:^2.8.1"
  checksum: 10/62452c7c8e3cd68da1afa9119602a8c2f97bbf4c299407c83abeec1ebf9bf2be5b64759d528b2b3e70d90afc7d83231bbfb5945b46f73eee2e3f4d752bb8473b
  languageName: node
  linkType: hard

"@perkd/wallet@github:perkd/wallet#semver:^0.5.0":
  version: 0.5.0
  resolution: "@perkd/wallet@https://github.com/perkd/wallet.git#commit=0c6ab4de193863361f4f183e1398ba75dd1dce56"
  dependencies:
    jws: "npm:^4.0.0"
    tslib: "npm:^2.8.1"
  checksum: 10/4f52a67dd97a93b40bd5b4cf056f1b13e4634ce1413763b1064c1e0a780457c8a728561ce2dd2562feafb0f6d47250073d98affdd2ae82c5a287ad086d63cc56
  languageName: node
  linkType: hard

"@perkd/websockets@github:perkd/websockets#semver:^0.5.7":
  version: 0.5.7
  resolution: "@perkd/websockets@https://github.com/perkd/websockets.git#commit=c981b243522bd524a2917f86cbaf5fdff9bb1025"
  dependencies:
    nanoid: "npm:3.3.8"
    tslib: "npm:^2.8.1"
    ws: "npm:^8.18.1"
  checksum: 10/227e0ea34472bca3a81f9fbece1898f3595691436628b7e0af26128bc5f3c201f88ddff88cccbe36b797a85c0d9e05d55daa9da55e776fcf61ce2fc549a23c22
  languageName: node
  linkType: hard

"@pkgjs/parseargs@npm:^0.11.0":
  version: 0.11.0
  resolution: "@pkgjs/parseargs@npm:0.11.0"
  checksum: 10/115e8ceeec6bc69dff2048b35c0ab4f8bbee12d8bb6c1f4af758604586d802b6e669dcb02dda61d078de42c2b4ddce41b3d9e726d7daa6b4b850f4adbf7333ff
  languageName: node
  linkType: hard

"@pkgr/core@npm:^0.2.4":
  version: 0.2.4
  resolution: "@pkgr/core@npm:0.2.4"
  checksum: 10/8544f0346c3f7035b9e2fdf60179c68b12d3c76b3fba9533844099af67cf5c0ce5257538f5faa05953d48cc1536d046f003231f321b2f75b3fb449db8410a2b7
  languageName: node
  linkType: hard

"@provider/aws@github:perkd/aws#semver:^0.8.0":
  version: 0.8.0
  resolution: "@provider/aws@https://github.com/perkd/aws.git#commit=b9997325fcc72e3e40810b1f90fbb4047f154bc2"
  dependencies:
    "@aws-sdk/client-quicksight": "npm:^3.775.0"
    "@aws-sdk/client-s3": "npm:^3.775.0"
    "@perkd/metrics": "github:perkd/metrics#semver:^1.8.0"
    "@provider/providers": "github:perkd/provider-providers#semver:^1.6.19"
  checksum: 10/18be3ece61624afa726b3be7e7e1934f95662d8f513d6c8f5e27661239aae8f3661a0fe195524f1c1a169e4d6eb873f5ceb65897787f06584bab7dc9c258dd51
  languageName: node
  linkType: hard

"@provider/ezreceipt@github:perkd/invoice-ezreceipt#semver:^0.5.6":
  version: 0.5.6
  resolution: "@provider/ezreceipt@https://github.com/perkd/invoice-ezreceipt.git#commit=e84b89cce9c74bf855d0afe2b207352a71eb67b1"
  dependencies:
    "@crm/types": "github:perkd/crm-types#semver:^1.10.3"
    "@perkd/api-request": "github:perkd/api-request#semver:^1.17.2"
    "@perkd/metrics": "github:perkd/metrics#semver:^1.8.0"
    "@perkd/utils": "github:perkd/utils#semver:^1.9.7"
    "@provider/providers": "github:perkd/provider-providers#semver:^1.7.0"
  checksum: 10/f961ac734c476f4561f6e3fff261520ff55338226ee6bb0d3cfa2b7bdb10d67377fdcea18517f9fc9f35b159365ad01e608227f56845e18f429bbf32333c89dd
  languageName: node
  linkType: hard

"@provider/feie@github:perkd/print-feie#semver:^1.4.0":
  version: 1.4.0
  resolution: "@provider/feie@https://github.com/perkd/print-feie.git#commit=0944303d02676eb50d39f65e6724ac8421f1cf03"
  dependencies:
    "@perkd/api-request": "github:perkd/api-request#semver:^1.17.2"
    "@perkd/metrics": "github:perkd/metrics#semver:^1.8.0"
    "@provider/providers": "github:perkd/provider-providers#semver:^1.7.0"
    get-value: "npm:^4.0.1"
    tslib: "npm:^2.8.1"
  checksum: 10/dc07b85e48e4863c9da8fffd7894a76063a0f85d130752982af77b020794a76bbb8638e6d730dd77d017064a68d33d0d50125cebfaf26d83c317579b56531061
  languageName: node
  linkType: hard

"@provider/google@github:perkd/google#semver:^1.3.0":
  version: 1.3.0
  resolution: "@provider/google@https://github.com/perkd/google.git#commit=f055d789f412d98e3e8c04b8ed9f5a6f71c994a1"
  dependencies:
    "@googleapis/calendar": "npm:^9.8.0"
    "@googlemaps/google-maps-services-js": "npm:^3.4.1"
    "@perkd/metrics": "github:perkd/metrics#semver:^1.8.0"
    "@provider/providers": "github:perkd/provider-providers#semver:^1.7.3"
  checksum: 10/f5c7f2b8096a5876c06768de5515ab52a2891ae0d3b67e8326b8f54839021f263fbfd342a59ed6207defc37b0f86691828ac1da579b60310021234ff200678f3
  languageName: node
  linkType: hard

"@provider/grabfood@github:perkd/sales-grabfood#semver:^1.0.17":
  version: 1.0.17
  resolution: "@provider/grabfood@https://github.com/perkd/sales-grabfood.git#commit=099107c318b521745e5763d64658b280a51e3ad1"
  dependencies:
    "@crm/types": "github:perkd/crm-types#semver:^1.8.43"
    "@perkd/grab-sdk": "github:perkd/grab-sdk#semver:^1.0.17"
    "@perkd/metrics": "github:perkd/metrics#semver:^1.7.2"
    "@provider/providers": "github:perkd/provider-providers#semver:^1.6.17"
  checksum: 10/c7aa97dbb985ca14b30609722418274d09a1a84afa91092f05a96a1a55e2afb6565f2ed7efe456a468314d4ccbb163c0f5374a02dd5f49ba5e13e238f3f2d36b
  languageName: node
  linkType: hard

"@provider/grabmart@github:perkd/sales-grabmart#semver:^1.0.17":
  version: 1.0.17
  resolution: "@provider/grabmart@https://github.com/perkd/sales-grabmart.git#commit=43358ab19f8051e6b8502933993afcc4682606f0"
  dependencies:
    "@crm/types": "github:perkd/crm-types#semver:^1.8.43"
    "@perkd/grab-sdk": "github:perkd/grab-sdk#semver:^1.0.17"
    "@perkd/metrics": "github:perkd/metrics#semver:^1.7.2"
    "@provider/providers": "github:perkd/provider-providers#semver:^1.6.17"
  checksum: 10/59616fe5c63ad2fc5ee755fccd761b311a4899c621806d4a5fd96147746fc1fadbadb1b9be46445a90fef0608c06d5913c32a82dbaed5de7b5970ff5bfabaffd
  languageName: node
  linkType: hard

"@provider/providers@github:perkd/provider-providers#semver:^1.6.17, @provider/providers@github:perkd/provider-providers#semver:^1.6.19, @provider/providers@github:perkd/provider-providers#semver:^1.7.0, @provider/providers@github:perkd/provider-providers#semver:^1.7.3, @provider/providers@github:perkd/provider-providers#semver:^1.7.4":
  version: 1.7.4
  resolution: "@provider/providers@https://github.com/perkd/provider-providers.git#commit=a94dd27d86c8e46932bc5311611cdf5c133ce1a7"
  dependencies:
    "@crm/types": "github:perkd/crm-types#semver:^1.11.17"
    "@perkd/hosts": "github:perkd/hosts#semver:^0.2.1"
    "@perkd/metrics": "github:perkd/metrics#semver:^1.8.0"
    "@perkd/utils": "github:perkd/utils#semver:^2.0.4"
    p-limit: "npm:3.1.0"
    rfdc: "npm:^1.4.1"
    tslib: "npm:^2.8.1"
  checksum: 10/21fd3a73cad026ff55442646dcb483208464b66a0a5843055b971caf66af10137c178289e974350afa5b9070a2dcac936e196feba5b336d751b6894f718b4082
  languageName: node
  linkType: hard

"@provider/shopify@github:perkd/sales-shopify#semver:^1.5.3":
  version: 1.5.3
  resolution: "@provider/shopify@https://github.com/perkd/sales-shopify.git#commit=01d34804fd773ed37e6a8f80b1c3647d6723daf8"
  dependencies:
    "@crm/types": "github:perkd/crm-types#semver:^1.11.19"
    "@provider/providers": "github:perkd/provider-providers#semver:^1.7.4"
    nanoid: "npm:3.3.8"
    shopify-api-node: "npm:^3.15.0"
  checksum: 10/04f92564a2d9b8219615033f1c006cffc4681f352955cb7e81f9f38289d13ad0daf5f7fbd5d38180793e980e16a8200df667fdfbf077f126e9b65df9c81546a5
  languageName: node
  linkType: hard

"@provider/ubereats@github:perkd/sales-ubereats#semver:^0.8.0":
  version: 0.8.0
  resolution: "@provider/ubereats@https://github.com/perkd/sales-ubereats.git#commit=e9422ff0018511de8ba014b19c4661b74dd12373"
  dependencies:
    "@crm/types": "github:perkd/crm-types#semver:^1.10.24"
    "@perkd/metrics": "github:perkd/metrics#semver:^1.8.0"
    "@perkd/uber-sdk": "github:perkd/uber-sdk#semver:^0.8.1"
    "@provider/providers": "github:perkd/provider-providers#semver:^1.7.0"
  checksum: 10/4489e29968e58a2443ce9cb1ff73f7a39302bbf1338cc068a931ee7ee8f0b148f8e91f3f4c8a8a4acb46ca66808cc4155cd6a2753b7a59f753a7034751843d87
  languageName: node
  linkType: hard

"@sindresorhus/is@npm:^4.0.0":
  version: 4.6.0
  resolution: "@sindresorhus/is@npm:4.6.0"
  checksum: 10/e7f36ed72abfcd5e0355f7423a72918b9748bb1ef370a59f3e5ad8d40b728b85d63b272f65f63eec1faf417cda89dcb0aeebe94015647b6054659c1442fe5ce0
  languageName: node
  linkType: hard

"@slack/logger@npm:^4.0.0":
  version: 4.0.0
  resolution: "@slack/logger@npm:4.0.0"
  dependencies:
    "@types/node": "npm:>=18.0.0"
  checksum: 10/dc79e9d2032c4bf9ce01d96cc72882f003dd376d036f172d4169662cfc2c9b384a80d5546b06021578dd473e7059f064303f0ba851eeb153387f2081a1e3062e
  languageName: node
  linkType: hard

"@slack/types@npm:^2.9.0":
  version: 2.14.0
  resolution: "@slack/types@npm:2.14.0"
  checksum: 10/fa24a113b88e087f899078504c2ba50ab9795f7c2dd1a2d95b28217a3af20e554494f9cc3b8c8ce173120990d98e19400c95369f9067cecfcc46c08b59d2a46f
  languageName: node
  linkType: hard

"@slack/web-api@npm:^7.9.2":
  version: 7.9.2
  resolution: "@slack/web-api@npm:7.9.2"
  dependencies:
    "@slack/logger": "npm:^4.0.0"
    "@slack/types": "npm:^2.9.0"
    "@types/node": "npm:>=18.0.0"
    "@types/retry": "npm:0.12.0"
    axios: "npm:^1.8.3"
    eventemitter3: "npm:^5.0.1"
    form-data: "npm:^4.0.0"
    is-electron: "npm:2.2.2"
    is-stream: "npm:^2"
    p-queue: "npm:^6"
    p-retry: "npm:^4"
    retry: "npm:^0.13.1"
  checksum: 10/a24c172168a6e464d3eb478282a0f0ad4dc9fcdefa964d17bdfe5494b73ecb471d9b4015e8a110370a9fa13a66c898a378515d856cd194359e97986ca297ef5b
  languageName: node
  linkType: hard

"@smithy/abort-controller@npm:^4.0.3":
  version: 4.0.3
  resolution: "@smithy/abort-controller@npm:4.0.3"
  dependencies:
    "@smithy/types": "npm:^4.3.0"
    tslib: "npm:^2.6.2"
  checksum: 10/254721823c9f7f65a5db53542c3559eac3038e2aa3f3579791f673fa92d7c87110e79ac162461fc12b2dca3b2d4e03067a4515b0700ee6db4c1b14f5502a493a
  languageName: node
  linkType: hard

"@smithy/chunked-blob-reader-native@npm:^4.0.0":
  version: 4.0.0
  resolution: "@smithy/chunked-blob-reader-native@npm:4.0.0"
  dependencies:
    "@smithy/util-base64": "npm:^4.0.0"
    tslib: "npm:^2.6.2"
  checksum: 10/c58c4af5344cb9e2feddc15e020474930dc1a53a71b6dd2b3bd01d5555a5eb30ba964226b0fdac0c7e1f31d0354967a2e0c3c64860d6f0fe36652a7a003a8a19
  languageName: node
  linkType: hard

"@smithy/chunked-blob-reader@npm:^5.0.0":
  version: 5.0.0
  resolution: "@smithy/chunked-blob-reader@npm:5.0.0"
  dependencies:
    tslib: "npm:^2.6.2"
  checksum: 10/d27333cfe68f7d8af6b7b9b3f6edf32c8dea9cac9e4933f2a062b0836b126af4abcec6b908f9607a2f137f86e59f2eee37a57f87dbaea046da95c1f01e44d5ef
  languageName: node
  linkType: hard

"@smithy/config-resolver@npm:^4.1.2, @smithy/config-resolver@npm:^4.1.3":
  version: 4.1.3
  resolution: "@smithy/config-resolver@npm:4.1.3"
  dependencies:
    "@smithy/node-config-provider": "npm:^4.1.2"
    "@smithy/types": "npm:^4.3.0"
    "@smithy/util-config-provider": "npm:^4.0.0"
    "@smithy/util-middleware": "npm:^4.0.3"
    tslib: "npm:^2.6.2"
  checksum: 10/b32d9d719f69a75a2dd0933f6e714ed6cd0d80c86e88706e8fe835d66e04ece50e5fe2daa27043178163836b2611952cc40829a0d28c3f82b1b30fbc3350f4b4
  languageName: node
  linkType: hard

"@smithy/core@npm:^3.3.3, @smithy/core@npm:^3.4.0":
  version: 3.4.0
  resolution: "@smithy/core@npm:3.4.0"
  dependencies:
    "@smithy/middleware-serde": "npm:^4.0.6"
    "@smithy/protocol-http": "npm:^5.1.1"
    "@smithy/types": "npm:^4.3.0"
    "@smithy/util-body-length-browser": "npm:^4.0.0"
    "@smithy/util-middleware": "npm:^4.0.3"
    "@smithy/util-stream": "npm:^4.2.1"
    "@smithy/util-utf8": "npm:^4.0.0"
    tslib: "npm:^2.6.2"
  checksum: 10/f08310266485cdb854a78d00f875bd60c68db62159de3516b096a3a57ee49947fcd8292b1611eca5e51ad90906987b7d494ba9dc9f8acc910912483c62faafd6
  languageName: node
  linkType: hard

"@smithy/credential-provider-imds@npm:^4.0.4, @smithy/credential-provider-imds@npm:^4.0.5":
  version: 4.0.5
  resolution: "@smithy/credential-provider-imds@npm:4.0.5"
  dependencies:
    "@smithy/node-config-provider": "npm:^4.1.2"
    "@smithy/property-provider": "npm:^4.0.3"
    "@smithy/types": "npm:^4.3.0"
    "@smithy/url-parser": "npm:^4.0.3"
    tslib: "npm:^2.6.2"
  checksum: 10/204e3c5f5899a76164e27bf3cc96b66d8423954e48537962dfa0060a8f922518c2394f27ba940b795ca1347384f2fae72b0ab6043375c0e16217d334e42bf703
  languageName: node
  linkType: hard

"@smithy/eventstream-codec@npm:^4.0.3":
  version: 4.0.3
  resolution: "@smithy/eventstream-codec@npm:4.0.3"
  dependencies:
    "@aws-crypto/crc32": "npm:5.2.0"
    "@smithy/types": "npm:^4.3.0"
    "@smithy/util-hex-encoding": "npm:^4.0.0"
    tslib: "npm:^2.6.2"
  checksum: 10/d5ca5c9a8f1d1f59112a539117414ed68c515680f8ff6413363efae5305396c51df53c9febc2985ec2b46d985ca642c8f25a8bc8c9882e142d40564523b98164
  languageName: node
  linkType: hard

"@smithy/eventstream-serde-browser@npm:^4.0.2":
  version: 4.0.3
  resolution: "@smithy/eventstream-serde-browser@npm:4.0.3"
  dependencies:
    "@smithy/eventstream-serde-universal": "npm:^4.0.3"
    "@smithy/types": "npm:^4.3.0"
    tslib: "npm:^2.6.2"
  checksum: 10/a0e37ef21abfc128f70ffc9bab6a237877b1e527c6c53b410e54d851279e5d335b1dee1504760097340c24b76a3f1d97ad3a692214516eb396b8061881a2ff9f
  languageName: node
  linkType: hard

"@smithy/eventstream-serde-config-resolver@npm:^4.1.0":
  version: 4.1.1
  resolution: "@smithy/eventstream-serde-config-resolver@npm:4.1.1"
  dependencies:
    "@smithy/types": "npm:^4.3.0"
    tslib: "npm:^2.6.2"
  checksum: 10/b82c2e3fd858b2ae4a1aa1f4569a3e27cb1364503ded547de320b05db74b9ceaa90570a0066cda6523b23c63e06edb9eba9f719ea7efa2efbeba055ac22c4cb2
  languageName: node
  linkType: hard

"@smithy/eventstream-serde-node@npm:^4.0.2":
  version: 4.0.3
  resolution: "@smithy/eventstream-serde-node@npm:4.0.3"
  dependencies:
    "@smithy/eventstream-serde-universal": "npm:^4.0.3"
    "@smithy/types": "npm:^4.3.0"
    tslib: "npm:^2.6.2"
  checksum: 10/80f17f15cb98ef5fb1a67a1bcbaffb82aa6b405c6faceafb0d60facb8e6730961ed672e0265644cec8765f33333c9a59ab07d37e63eeefbdd890a493e5c11bc9
  languageName: node
  linkType: hard

"@smithy/eventstream-serde-universal@npm:^4.0.3":
  version: 4.0.3
  resolution: "@smithy/eventstream-serde-universal@npm:4.0.3"
  dependencies:
    "@smithy/eventstream-codec": "npm:^4.0.3"
    "@smithy/types": "npm:^4.3.0"
    tslib: "npm:^2.6.2"
  checksum: 10/11f5beaf3ccd8cb3fed50817f1f4c9a08d536b862d1164c0cc3cd851c83170ea498a6bad81f220971eb9d1ebb5a5600475b7ab762fb30db57495dbc20a9e7db1
  languageName: node
  linkType: hard

"@smithy/fetch-http-handler@npm:^5.0.2, @smithy/fetch-http-handler@npm:^5.0.3":
  version: 5.0.3
  resolution: "@smithy/fetch-http-handler@npm:5.0.3"
  dependencies:
    "@smithy/protocol-http": "npm:^5.1.1"
    "@smithy/querystring-builder": "npm:^4.0.3"
    "@smithy/types": "npm:^4.3.0"
    "@smithy/util-base64": "npm:^4.0.0"
    tslib: "npm:^2.6.2"
  checksum: 10/bc0ac0882f20cbfaf2668eb3cd8935496658cf4e6d72a436ac7b789d8ce3b3b0621236d83ca271d7a1b7265565cc03647157c0b0a777198e06b8e5f3f997540b
  languageName: node
  linkType: hard

"@smithy/hash-blob-browser@npm:^4.0.2":
  version: 4.0.3
  resolution: "@smithy/hash-blob-browser@npm:4.0.3"
  dependencies:
    "@smithy/chunked-blob-reader": "npm:^5.0.0"
    "@smithy/chunked-blob-reader-native": "npm:^4.0.0"
    "@smithy/types": "npm:^4.3.0"
    tslib: "npm:^2.6.2"
  checksum: 10/39142baed14117629f54df6fedc89ad6dcad7c81315f4a730f6eb57c5ed0ed7b43ab2c7568d29736116f1138a2df9ff29199bbc861322a5e2817dabd25cdb5d1
  languageName: node
  linkType: hard

"@smithy/hash-node@npm:^4.0.2":
  version: 4.0.3
  resolution: "@smithy/hash-node@npm:4.0.3"
  dependencies:
    "@smithy/types": "npm:^4.3.0"
    "@smithy/util-buffer-from": "npm:^4.0.0"
    "@smithy/util-utf8": "npm:^4.0.0"
    tslib: "npm:^2.6.2"
  checksum: 10/9f9aa9fb25cc91416e0548018b594eaa968a1d294d4ae632b55108aa6388123f7ded52a7b20144e1b735dc3f8a34755dba2754e2d4691413007ed5d29aff77f0
  languageName: node
  linkType: hard

"@smithy/hash-stream-node@npm:^4.0.2":
  version: 4.0.3
  resolution: "@smithy/hash-stream-node@npm:4.0.3"
  dependencies:
    "@smithy/types": "npm:^4.3.0"
    "@smithy/util-utf8": "npm:^4.0.0"
    tslib: "npm:^2.6.2"
  checksum: 10/f6f684279278986fd8f961841f9e0f3c89a3de06accf904c0c1501a8c5723b9e12ee6878ac6923ea280bbdfeaeffad34a6b8505e097ecf2ce90d68900c3e1be8
  languageName: node
  linkType: hard

"@smithy/invalid-dependency@npm:^4.0.2":
  version: 4.0.3
  resolution: "@smithy/invalid-dependency@npm:4.0.3"
  dependencies:
    "@smithy/types": "npm:^4.3.0"
    tslib: "npm:^2.6.2"
  checksum: 10/155d1097c9faf61bc728ba179965283e7010583b5c4873a240becc6e8ec2a88fe9b13efb4b720ceb2b67a04d46b11bd82236640cbe87d04c303cab922c1b3c11
  languageName: node
  linkType: hard

"@smithy/is-array-buffer@npm:^2.2.0":
  version: 2.2.0
  resolution: "@smithy/is-array-buffer@npm:2.2.0"
  dependencies:
    tslib: "npm:^2.6.2"
  checksum: 10/d366743ecc7a9fc3bad21dbb3950d213c12bdd4aeb62b1265bf6cbe38309df547664ef3e51ab732e704485194f15e89d361943b0bfbe3fe1a4b3178b942913cc
  languageName: node
  linkType: hard

"@smithy/is-array-buffer@npm:^4.0.0":
  version: 4.0.0
  resolution: "@smithy/is-array-buffer@npm:4.0.0"
  dependencies:
    tslib: "npm:^2.6.2"
  checksum: 10/3985046ac490968fe86e2d5e87d023d67f29aa4778abebacecb0f7962d07e32507a5612701c7aa7b1fb63b5a6e68086c915cae5229e5f1abfb39419dc07e00c8
  languageName: node
  linkType: hard

"@smithy/md5-js@npm:^4.0.2":
  version: 4.0.3
  resolution: "@smithy/md5-js@npm:4.0.3"
  dependencies:
    "@smithy/types": "npm:^4.3.0"
    "@smithy/util-utf8": "npm:^4.0.0"
    tslib: "npm:^2.6.2"
  checksum: 10/5d08258feb591579b2c5aff0b9ee4e25a40f2dddb5593fb50e6d14a7c7e6c5c8b3d190e050b4b80510793f79a41ad4a7ca947725004023c3bce3f35acfb13eed
  languageName: node
  linkType: hard

"@smithy/middleware-content-length@npm:^4.0.2":
  version: 4.0.3
  resolution: "@smithy/middleware-content-length@npm:4.0.3"
  dependencies:
    "@smithy/protocol-http": "npm:^5.1.1"
    "@smithy/types": "npm:^4.3.0"
    tslib: "npm:^2.6.2"
  checksum: 10/65f13e41243417ed078d11c5b380831fc13b911f30dc7b9110f3d425455e19bd270e62b63ea5bc1cffc2cd17f73eda2669d3802560ffeac9508a683bdf8d7aab
  languageName: node
  linkType: hard

"@smithy/middleware-endpoint@npm:^4.1.6, @smithy/middleware-endpoint@npm:^4.1.7":
  version: 4.1.7
  resolution: "@smithy/middleware-endpoint@npm:4.1.7"
  dependencies:
    "@smithy/core": "npm:^3.4.0"
    "@smithy/middleware-serde": "npm:^4.0.6"
    "@smithy/node-config-provider": "npm:^4.1.2"
    "@smithy/shared-ini-file-loader": "npm:^4.0.3"
    "@smithy/types": "npm:^4.3.0"
    "@smithy/url-parser": "npm:^4.0.3"
    "@smithy/util-middleware": "npm:^4.0.3"
    tslib: "npm:^2.6.2"
  checksum: 10/11301a1957e8d6bc369a47a4c627b1708448d7fd3252c9bb9b7af8ccc1c8e7165e79502c76d69736f86ac1ab84c488539de3477b4be9f28e47c9fa36adf642ae
  languageName: node
  linkType: hard

"@smithy/middleware-retry@npm:^4.1.7":
  version: 4.1.8
  resolution: "@smithy/middleware-retry@npm:4.1.8"
  dependencies:
    "@smithy/node-config-provider": "npm:^4.1.2"
    "@smithy/protocol-http": "npm:^5.1.1"
    "@smithy/service-error-classification": "npm:^4.0.4"
    "@smithy/smithy-client": "npm:^4.3.0"
    "@smithy/types": "npm:^4.3.0"
    "@smithy/util-middleware": "npm:^4.0.3"
    "@smithy/util-retry": "npm:^4.0.4"
    tslib: "npm:^2.6.2"
    uuid: "npm:^9.0.1"
  checksum: 10/8437f443e0fd724787665a2ca35077ba20ae8cda5e0485c0fb64d5dffb465ecda76a7429296ea59b78637b68371e1ee70a1d151310507c900ab69bd77f587626
  languageName: node
  linkType: hard

"@smithy/middleware-serde@npm:^4.0.5, @smithy/middleware-serde@npm:^4.0.6":
  version: 4.0.6
  resolution: "@smithy/middleware-serde@npm:4.0.6"
  dependencies:
    "@smithy/protocol-http": "npm:^5.1.1"
    "@smithy/types": "npm:^4.3.0"
    tslib: "npm:^2.6.2"
  checksum: 10/85e3da9116888d565bc448839ec3508a270c6f51871a1d88716baf53e1dd7c6cc3ea7417f068ccb0ce5524c31751f753323d543ad663109a69e34f58b04352a0
  languageName: node
  linkType: hard

"@smithy/middleware-stack@npm:^4.0.2, @smithy/middleware-stack@npm:^4.0.3":
  version: 4.0.3
  resolution: "@smithy/middleware-stack@npm:4.0.3"
  dependencies:
    "@smithy/types": "npm:^4.3.0"
    tslib: "npm:^2.6.2"
  checksum: 10/1e7b77e8d065e04277c998ca110922594d1de7a96458784744e4478ca9bac18575d586419a0af2cb17dcc1fe8bbb054796af5f70bc1f6e37d1bf005411694e00
  languageName: node
  linkType: hard

"@smithy/node-config-provider@npm:^4.1.1, @smithy/node-config-provider@npm:^4.1.2":
  version: 4.1.2
  resolution: "@smithy/node-config-provider@npm:4.1.2"
  dependencies:
    "@smithy/property-provider": "npm:^4.0.3"
    "@smithy/shared-ini-file-loader": "npm:^4.0.3"
    "@smithy/types": "npm:^4.3.0"
    tslib: "npm:^2.6.2"
  checksum: 10/0bf7bddf1101d54039e40d2526ad736a333eafe6dcc55b18f83befa20cc02def8cb35864a0201c4150671d633a3ff9fc3f4dac8293a8584f8fd337eb8cc998b3
  languageName: node
  linkType: hard

"@smithy/node-http-handler@npm:^4.0.4, @smithy/node-http-handler@npm:^4.0.5":
  version: 4.0.5
  resolution: "@smithy/node-http-handler@npm:4.0.5"
  dependencies:
    "@smithy/abort-controller": "npm:^4.0.3"
    "@smithy/protocol-http": "npm:^5.1.1"
    "@smithy/querystring-builder": "npm:^4.0.3"
    "@smithy/types": "npm:^4.3.0"
    tslib: "npm:^2.6.2"
  checksum: 10/535e7946f801c70a4dc7f84d13db8177dd98e728ed91a5485f7c4e656f8c26ad2b43381a9260b05b073556a416b7f6d83ab0774c334465d5f72606d2a5dfa87d
  languageName: node
  linkType: hard

"@smithy/property-provider@npm:^4.0.2, @smithy/property-provider@npm:^4.0.3":
  version: 4.0.3
  resolution: "@smithy/property-provider@npm:4.0.3"
  dependencies:
    "@smithy/types": "npm:^4.3.0"
    tslib: "npm:^2.6.2"
  checksum: 10/dce9b104f20f5364b9d7036f02e15f211ec6c8fbada034c759a9c2851eac83b9e1faea4bc8a5ec29e89bbbe745de61fe9cdb9863d611c8356732496e2e5fa3bd
  languageName: node
  linkType: hard

"@smithy/protocol-http@npm:^5.1.0, @smithy/protocol-http@npm:^5.1.1":
  version: 5.1.1
  resolution: "@smithy/protocol-http@npm:5.1.1"
  dependencies:
    "@smithy/types": "npm:^4.3.0"
    tslib: "npm:^2.6.2"
  checksum: 10/56b5b6a7c2ba99491193daa8787305b65a4f90483d51777fecf477f9386e1703912772502d535de34120c6ae2b628d44834749b1e02daded138f472c5847b9eb
  languageName: node
  linkType: hard

"@smithy/querystring-builder@npm:^4.0.3":
  version: 4.0.3
  resolution: "@smithy/querystring-builder@npm:4.0.3"
  dependencies:
    "@smithy/types": "npm:^4.3.0"
    "@smithy/util-uri-escape": "npm:^4.0.0"
    tslib: "npm:^2.6.2"
  checksum: 10/a671e20dcf23d67613fbd1ee9ba5c04249d791e87bf55226351f43de8653ebc876c2117e224266ecfcb48725c90c25bfdc507dfcda0ecacdff92214a9fb7f23d
  languageName: node
  linkType: hard

"@smithy/querystring-parser@npm:^4.0.3":
  version: 4.0.3
  resolution: "@smithy/querystring-parser@npm:4.0.3"
  dependencies:
    "@smithy/types": "npm:^4.3.0"
    tslib: "npm:^2.6.2"
  checksum: 10/3d6b2f254f315989eb0ea2c187f6a7c2a96e9829f0ffda63f56f3003b8ddcc3cb8a195ad8608b2aaf6b855ee70c9fe2b0f66a3a6a2111cb1f6de50d627dad1e5
  languageName: node
  linkType: hard

"@smithy/service-error-classification@npm:^4.0.4":
  version: 4.0.4
  resolution: "@smithy/service-error-classification@npm:4.0.4"
  dependencies:
    "@smithy/types": "npm:^4.3.0"
  checksum: 10/8d821bf7fb50d141437400c451659a923fbb00325741996d68064441ed7317347f7b98890ff9bb57e4c2a0dd91580c56b23f267fef31e794402d0d44f406f9cc
  languageName: node
  linkType: hard

"@smithy/shared-ini-file-loader@npm:^4.0.2, @smithy/shared-ini-file-loader@npm:^4.0.3":
  version: 4.0.3
  resolution: "@smithy/shared-ini-file-loader@npm:4.0.3"
  dependencies:
    "@smithy/types": "npm:^4.3.0"
    tslib: "npm:^2.6.2"
  checksum: 10/a389c3dbc844eda79df506b90ca28e0d1e8c247d534e5adc043837cea7afb36e9d5dfbd0759524ee854986b1d443f2dfd5c6c0713a37e86ff7946dc3920ac2f5
  languageName: node
  linkType: hard

"@smithy/signature-v4@npm:^5.1.0":
  version: 5.1.1
  resolution: "@smithy/signature-v4@npm:5.1.1"
  dependencies:
    "@smithy/is-array-buffer": "npm:^4.0.0"
    "@smithy/protocol-http": "npm:^5.1.1"
    "@smithy/types": "npm:^4.3.0"
    "@smithy/util-hex-encoding": "npm:^4.0.0"
    "@smithy/util-middleware": "npm:^4.0.3"
    "@smithy/util-uri-escape": "npm:^4.0.0"
    "@smithy/util-utf8": "npm:^4.0.0"
    tslib: "npm:^2.6.2"
  checksum: 10/b23ab5f94d9891cfa3562d4999c058534a543e8f01109267af39657f2e559dcc2b2c6c4aa102acad3c4e0e553a144edbd038d1abc0a9338c0e49ee4523e5bd6e
  languageName: node
  linkType: hard

"@smithy/smithy-client@npm:^4.2.6, @smithy/smithy-client@npm:^4.3.0":
  version: 4.3.0
  resolution: "@smithy/smithy-client@npm:4.3.0"
  dependencies:
    "@smithy/core": "npm:^3.4.0"
    "@smithy/middleware-endpoint": "npm:^4.1.7"
    "@smithy/middleware-stack": "npm:^4.0.3"
    "@smithy/protocol-http": "npm:^5.1.1"
    "@smithy/types": "npm:^4.3.0"
    "@smithy/util-stream": "npm:^4.2.1"
    tslib: "npm:^2.6.2"
  checksum: 10/4c30258bfe1d4eeae2c6cf47ef9e2986718fe2cd35b53c65148dda266155b6afd4b10a306b6e0657b1d76b5a7c791cb4c780ad9165392cf591370926e49166e8
  languageName: node
  linkType: hard

"@smithy/types@npm:^4.2.0, @smithy/types@npm:^4.3.0":
  version: 4.3.0
  resolution: "@smithy/types@npm:4.3.0"
  dependencies:
    tslib: "npm:^2.6.2"
  checksum: 10/ee80b47e5ec14c1acb5b29225f0e71709e6238eb239e46e92864f108f8af9d2c744efd0a31265d8739f90b31e4a815240c2a633c6f0b193c3c6c45f14eb5252d
  languageName: node
  linkType: hard

"@smithy/url-parser@npm:^4.0.2, @smithy/url-parser@npm:^4.0.3":
  version: 4.0.3
  resolution: "@smithy/url-parser@npm:4.0.3"
  dependencies:
    "@smithy/querystring-parser": "npm:^4.0.3"
    "@smithy/types": "npm:^4.3.0"
    tslib: "npm:^2.6.2"
  checksum: 10/9626289aa03961ad6307fed215e45abc7f4b0bbd5828be3cc109779f7a24968b1a2fb17b400a03ff335c6b772040e4adb92f4b83ff22f844ef7f2f2008f701a5
  languageName: node
  linkType: hard

"@smithy/util-base64@npm:^4.0.0":
  version: 4.0.0
  resolution: "@smithy/util-base64@npm:4.0.0"
  dependencies:
    "@smithy/util-buffer-from": "npm:^4.0.0"
    "@smithy/util-utf8": "npm:^4.0.0"
    tslib: "npm:^2.6.2"
  checksum: 10/f495fa8f5be60a1b94f88e2de4b1236df5cfee78f32191840adffcc520f2f55cdc2f287dd7abddcac4759c51970b5326b6b371c60ad65b640992018e95e30d19
  languageName: node
  linkType: hard

"@smithy/util-body-length-browser@npm:^4.0.0":
  version: 4.0.0
  resolution: "@smithy/util-body-length-browser@npm:4.0.0"
  dependencies:
    tslib: "npm:^2.6.2"
  checksum: 10/041a5e3c98d5b0a935c992c0217dcc033886798406df803945c994fbf3302eb0d9bdea7f7f8e6abaabf3e547bdffda6f1fb00829be3e93adac6b1949d77b741f
  languageName: node
  linkType: hard

"@smithy/util-body-length-node@npm:^4.0.0":
  version: 4.0.0
  resolution: "@smithy/util-body-length-node@npm:4.0.0"
  dependencies:
    tslib: "npm:^2.6.2"
  checksum: 10/28d7b25b1465b290507b90be595bb161f9c1de755b35b4b99c3cf752725806b7d1f0c364535007f45a6aba95f2b49c2be9ebabaa4f03b5d36f9fc3287cd9d17a
  languageName: node
  linkType: hard

"@smithy/util-buffer-from@npm:^2.2.0":
  version: 2.2.0
  resolution: "@smithy/util-buffer-from@npm:2.2.0"
  dependencies:
    "@smithy/is-array-buffer": "npm:^2.2.0"
    tslib: "npm:^2.6.2"
  checksum: 10/53253e4e351df3c4b7907dca48a0a6ceae783e98a8e73526820b122b3047a53fd127c19f4d8301f68d852011d821da519da783de57e0b22eed57c4df5b90d089
  languageName: node
  linkType: hard

"@smithy/util-buffer-from@npm:^4.0.0":
  version: 4.0.0
  resolution: "@smithy/util-buffer-from@npm:4.0.0"
  dependencies:
    "@smithy/is-array-buffer": "npm:^4.0.0"
    tslib: "npm:^2.6.2"
  checksum: 10/077fd6fe88b9db69ef0d4e2dfa9946bb1e1ae3d899515d7102f8648d18fb012fcbc87244cce569c0e9e86c5001bfe309b2de874fe508e1a9a591b11540b0a2c8
  languageName: node
  linkType: hard

"@smithy/util-config-provider@npm:^4.0.0":
  version: 4.0.0
  resolution: "@smithy/util-config-provider@npm:4.0.0"
  dependencies:
    tslib: "npm:^2.6.2"
  checksum: 10/74f3cb317056f0974b0942c79d43859031cb860fcf6eb5c9244bee369fc6c4b9c823491a40ca4f03f65641f4128d7fa5c2d322860cb7ee8517c0b2e63088ac6f
  languageName: node
  linkType: hard

"@smithy/util-defaults-mode-browser@npm:^4.0.14":
  version: 4.0.15
  resolution: "@smithy/util-defaults-mode-browser@npm:4.0.15"
  dependencies:
    "@smithy/property-provider": "npm:^4.0.3"
    "@smithy/smithy-client": "npm:^4.3.0"
    "@smithy/types": "npm:^4.3.0"
    bowser: "npm:^2.11.0"
    tslib: "npm:^2.6.2"
  checksum: 10/ef05f70979443a404f3dd86a4bac992fc517ffd6db0dd16fe347573cae1f398237c48e39727eddfbdb57baaf4cd57478ee59ce2b22e11fd157db8009f38e44a1
  languageName: node
  linkType: hard

"@smithy/util-defaults-mode-node@npm:^4.0.14":
  version: 4.0.15
  resolution: "@smithy/util-defaults-mode-node@npm:4.0.15"
  dependencies:
    "@smithy/config-resolver": "npm:^4.1.3"
    "@smithy/credential-provider-imds": "npm:^4.0.5"
    "@smithy/node-config-provider": "npm:^4.1.2"
    "@smithy/property-provider": "npm:^4.0.3"
    "@smithy/smithy-client": "npm:^4.3.0"
    "@smithy/types": "npm:^4.3.0"
    tslib: "npm:^2.6.2"
  checksum: 10/8fdebf845f7de1c254ce843fbd06ce2fe4989b79686ca33fee5bb3ef0a0f9fcb64db3c2d3cb0cdb705d279e115b2a5879b637f9ad795c6c34a5d73dfe278d692
  languageName: node
  linkType: hard

"@smithy/util-endpoints@npm:^3.0.4":
  version: 3.0.5
  resolution: "@smithy/util-endpoints@npm:3.0.5"
  dependencies:
    "@smithy/node-config-provider": "npm:^4.1.2"
    "@smithy/types": "npm:^4.3.0"
    tslib: "npm:^2.6.2"
  checksum: 10/260302b09f6a770f5a852833e6b4f2861ad28aab64ca7c44f9c128fbaf4575ea2755801d555d66429940a90c7ed865828b04f0e55cb28551cdb76599db96dcfb
  languageName: node
  linkType: hard

"@smithy/util-hex-encoding@npm:^4.0.0":
  version: 4.0.0
  resolution: "@smithy/util-hex-encoding@npm:4.0.0"
  dependencies:
    tslib: "npm:^2.6.2"
  checksum: 10/447475cad8510d2727bbdf8490021a7ca8cb52b391f4bfe646c73a3aa1d5678152f1b5c4c2aaeebd9f6650272d973a1739e2d42294bd68c957429e3a30db3546
  languageName: node
  linkType: hard

"@smithy/util-middleware@npm:^4.0.2, @smithy/util-middleware@npm:^4.0.3":
  version: 4.0.3
  resolution: "@smithy/util-middleware@npm:4.0.3"
  dependencies:
    "@smithy/types": "npm:^4.3.0"
    tslib: "npm:^2.6.2"
  checksum: 10/17057f18f5b70edfd4729852491dd0259df1a94e544969e604d675e421f5b748634e41361732505caa747c06d510ac8d948f9522ae52022e79bfa28a56238de1
  languageName: node
  linkType: hard

"@smithy/util-retry@npm:^4.0.3, @smithy/util-retry@npm:^4.0.4":
  version: 4.0.4
  resolution: "@smithy/util-retry@npm:4.0.4"
  dependencies:
    "@smithy/service-error-classification": "npm:^4.0.4"
    "@smithy/types": "npm:^4.3.0"
    tslib: "npm:^2.6.2"
  checksum: 10/243e0e48092007281f2732c8267deadb8d6991ec900ed59b13005617cbfae2d715470d9cacb5aa819bbffc1dc30fc0bf1c1a27b5d8f2ec8ed3f24077bc627601
  languageName: node
  linkType: hard

"@smithy/util-stream@npm:^4.2.0, @smithy/util-stream@npm:^4.2.1":
  version: 4.2.1
  resolution: "@smithy/util-stream@npm:4.2.1"
  dependencies:
    "@smithy/fetch-http-handler": "npm:^5.0.3"
    "@smithy/node-http-handler": "npm:^4.0.5"
    "@smithy/types": "npm:^4.3.0"
    "@smithy/util-base64": "npm:^4.0.0"
    "@smithy/util-buffer-from": "npm:^4.0.0"
    "@smithy/util-hex-encoding": "npm:^4.0.0"
    "@smithy/util-utf8": "npm:^4.0.0"
    tslib: "npm:^2.6.2"
  checksum: 10/1e3639829cd93c5e1fb20550092a52f12ca31e0a23ec53e0409b9927731522fe149534e2f31fdde5eb32b15498d9516b30bb396cd017c9b0120f4f3d9ccab79d
  languageName: node
  linkType: hard

"@smithy/util-uri-escape@npm:^4.0.0":
  version: 4.0.0
  resolution: "@smithy/util-uri-escape@npm:4.0.0"
  dependencies:
    tslib: "npm:^2.6.2"
  checksum: 10/27b71d7c1bc21d9038b86fd55380449a7a1dab52959566372d24a86df027c0ad9190980879cc4903be999dc36a5619f0794acf9cdc789adba5e57e26cd6ce4a6
  languageName: node
  linkType: hard

"@smithy/util-utf8@npm:^2.0.0":
  version: 2.3.0
  resolution: "@smithy/util-utf8@npm:2.3.0"
  dependencies:
    "@smithy/util-buffer-from": "npm:^2.2.0"
    tslib: "npm:^2.6.2"
  checksum: 10/c766ead8dac6bc6169f4cac1cc47ef7bd86928d06255148f9528228002f669c8cc49f78dc2b9ba5d7e214d40315024a9e32c5c9130b33e20f0fe4532acd0dff5
  languageName: node
  linkType: hard

"@smithy/util-utf8@npm:^4.0.0":
  version: 4.0.0
  resolution: "@smithy/util-utf8@npm:4.0.0"
  dependencies:
    "@smithy/util-buffer-from": "npm:^4.0.0"
    tslib: "npm:^2.6.2"
  checksum: 10/4de06914d08753ce14ec553cf2dabe4a432cf982e415ec7dec82dfb8a6af793ddd08587fbcaeb889a0f6cc917eecca3a026880cf914082ee8e293f5bfc44e248
  languageName: node
  linkType: hard

"@smithy/util-waiter@npm:^4.0.3":
  version: 4.0.4
  resolution: "@smithy/util-waiter@npm:4.0.4"
  dependencies:
    "@smithy/abort-controller": "npm:^4.0.3"
    "@smithy/types": "npm:^4.3.0"
    tslib: "npm:^2.6.2"
  checksum: 10/9a85370918f6e5a12c5b9a4d1822edf98d5e772e5c2ffdbff394b846f6a29efe54167a14344a4a69a527ee75ccc9350fb4e9be6b54b635c0a66e5c71e8c41b36
  languageName: node
  linkType: hard

"@stylistic/eslint-plugin-js@npm:^4.4.0":
  version: 4.4.0
  resolution: "@stylistic/eslint-plugin-js@npm:4.4.0"
  dependencies:
    eslint-visitor-keys: "npm:^4.2.0"
    espree: "npm:^10.3.0"
  peerDependencies:
    eslint: ">=9.0.0"
  checksum: 10/ca3c4be073537933e21aa58987392225e4bdc23b76002078a7a8d3a6d2f03048e5e0f973c715f0251a3eca64941b531db14b0c826222698bfaafbacbb0fd6721
  languageName: node
  linkType: hard

"@szmarczak/http-timer@npm:^4.0.5":
  version: 4.0.6
  resolution: "@szmarczak/http-timer@npm:4.0.6"
  dependencies:
    defer-to-connect: "npm:^2.0.0"
  checksum: 10/c29df3bcec6fc3bdec2b17981d89d9c9fc9bd7d0c9bcfe92821dc533f4440bc890ccde79971838b4ceed1921d456973c4180d7175ee1d0023ad0562240a58d95
  languageName: node
  linkType: hard

"@types/body-parser@npm:*":
  version: 1.19.5
  resolution: "@types/body-parser@npm:1.19.5"
  dependencies:
    "@types/connect": "npm:*"
    "@types/node": "npm:*"
  checksum: 10/1e251118c4b2f61029cc43b0dc028495f2d1957fe8ee49a707fb940f86a9bd2f9754230805598278fe99958b49e9b7e66eec8ef6a50ab5c1f6b93e1ba2aaba82
  languageName: node
  linkType: hard

"@types/cacheable-request@npm:^6.0.1":
  version: 6.0.3
  resolution: "@types/cacheable-request@npm:6.0.3"
  dependencies:
    "@types/http-cache-semantics": "npm:*"
    "@types/keyv": "npm:^3.1.4"
    "@types/node": "npm:*"
    "@types/responselike": "npm:^1.0.0"
  checksum: 10/159f9fdb2a1b7175eef453ae2ced5ea04c0d2b9610cc9ccd9f9abb066d36dacb1f37acd879ace10ad7cbb649490723feb396fb7307004c9670be29636304b988
  languageName: node
  linkType: hard

"@types/connect@npm:*":
  version: 3.4.38
  resolution: "@types/connect@npm:3.4.38"
  dependencies:
    "@types/node": "npm:*"
  checksum: 10/7eb1bc5342a9604facd57598a6c62621e244822442976c443efb84ff745246b10d06e8b309b6e80130026a396f19bf6793b7cecd7380169f369dac3bfc46fb99
  languageName: node
  linkType: hard

"@types/estree@npm:^1.0.6":
  version: 1.0.7
  resolution: "@types/estree@npm:1.0.7"
  checksum: 10/419c845ece767ad4b21171e6e5b63dabb2eb46b9c0d97361edcd9cabbf6a95fcadb91d89b5fa098d1336fa0b8fceaea82fca97a2ef3971f5c86e53031e157b21
  languageName: node
  linkType: hard

"@types/express-serve-static-core@npm:^4.17.33":
  version: 4.19.6
  resolution: "@types/express-serve-static-core@npm:4.19.6"
  dependencies:
    "@types/node": "npm:*"
    "@types/qs": "npm:*"
    "@types/range-parser": "npm:*"
    "@types/send": "npm:*"
  checksum: 10/a2e00b6c5993f0dd63ada2239be81076fe0220314b9e9fde586e8946c9c09ce60f9a2dd0d74410ee2b5fd10af8c3e755a32bb3abf134533e2158142488995455
  languageName: node
  linkType: hard

"@types/express@npm:^4.16.0":
  version: 4.17.22
  resolution: "@types/express@npm:4.17.22"
  dependencies:
    "@types/body-parser": "npm:*"
    "@types/express-serve-static-core": "npm:^4.17.33"
    "@types/qs": "npm:*"
    "@types/serve-static": "npm:*"
  checksum: 10/9497634fc341ff4ac966ec0c529ded03bdacd2c3dae164f10a060ff250c66591b873aedce92d0239869cf3d05615ae9bcad584c7349fe68780242f6fef010c62
  languageName: node
  linkType: hard

"@types/http-cache-semantics@npm:*":
  version: 4.0.4
  resolution: "@types/http-cache-semantics@npm:4.0.4"
  checksum: 10/a59566cff646025a5de396d6b3f44a39ab6a74f2ed8150692e0f31cc52f3661a68b04afe3166ebe0d566bd3259cb18522f46e949576d5204781cd6452b7fe0c5
  languageName: node
  linkType: hard

"@types/http-errors@npm:*":
  version: 2.0.4
  resolution: "@types/http-errors@npm:2.0.4"
  checksum: 10/1f3d7c3b32c7524811a45690881736b3ef741bf9849ae03d32ad1ab7062608454b150a4e7f1351f83d26a418b2d65af9bdc06198f1c079d75578282884c4e8e3
  languageName: node
  linkType: hard

"@types/json-schema@npm:^7.0.15":
  version: 7.0.15
  resolution: "@types/json-schema@npm:7.0.15"
  checksum: 10/1a3c3e06236e4c4aab89499c428d585527ce50c24fe8259e8b3926d3df4cfbbbcf306cfc73ddfb66cbafc973116efd15967020b0f738f63e09e64c7d260519e7
  languageName: node
  linkType: hard

"@types/keyv@npm:^3.1.4":
  version: 3.1.4
  resolution: "@types/keyv@npm:3.1.4"
  dependencies:
    "@types/node": "npm:*"
  checksum: 10/e009a2bfb50e90ca9b7c6e8f648f8464067271fd99116f881073fa6fa76dc8d0133181dd65e6614d5fb1220d671d67b0124aef7d97dc02d7e342ab143a47779d
  languageName: node
  linkType: hard

"@types/mime@npm:^1":
  version: 1.3.5
  resolution: "@types/mime@npm:1.3.5"
  checksum: 10/e29a5f9c4776f5229d84e525b7cd7dd960b51c30a0fb9a028c0821790b82fca9f672dab56561e2acd9e8eed51d431bde52eafdfef30f643586c4162f1aecfc78
  languageName: node
  linkType: hard

"@types/node@npm:*, @types/node@npm:>=18.0.0, @types/node@npm:^22.15.3":
  version: 22.15.21
  resolution: "@types/node@npm:22.15.21"
  dependencies:
    undici-types: "npm:~6.21.0"
  checksum: 10/cb4189587cca445bfb8166c0ed39f9344d743f37f3da892f2999a99bbabda45dc773237e61ecb7d1dc83dd95718cb1b5715b0be5dd7953565b19019e36a7cf39
  languageName: node
  linkType: hard

"@types/node@npm:^10.3.5":
  version: 10.17.60
  resolution: "@types/node@npm:10.17.60"
  checksum: 10/f9161493b3284b1d41d5d594c2768625acdd9e33f992f71ccde47861916e662e2ae438d2cc5f1b285053391a31b52a7564ecedc22d485610d236bfad9c7e6a1c
  languageName: node
  linkType: hard

"@types/qs@npm:*":
  version: 6.14.0
  resolution: "@types/qs@npm:6.14.0"
  checksum: 10/1909205514d22b3cbc7c2314e2bd8056d5f05dfb21cf4377f0730ee5e338ea19957c41735d5e4806c746176563f50005bbab602d8358432e25d900bdf4970826
  languageName: node
  linkType: hard

"@types/range-parser@npm:*":
  version: 1.2.7
  resolution: "@types/range-parser@npm:1.2.7"
  checksum: 10/95640233b689dfbd85b8c6ee268812a732cf36d5affead89e806fe30da9a430767af8ef2cd661024fd97e19d61f3dec75af2df5e80ec3bea000019ab7028629a
  languageName: node
  linkType: hard

"@types/readable-stream@npm:^4.0.0":
  version: 4.0.19
  resolution: "@types/readable-stream@npm:4.0.19"
  dependencies:
    "@types/node": "npm:*"
  checksum: 10/89714955dc0d1ba9c9921e0898650c6a99ebe06cb9e69b2e2990cfda63f86535611c43daf90ebb08ff202aef5c8770e729d5fb689563e2fcb7820e9e35b89bdd
  languageName: node
  linkType: hard

"@types/responselike@npm:^1.0.0":
  version: 1.0.3
  resolution: "@types/responselike@npm:1.0.3"
  dependencies:
    "@types/node": "npm:*"
  checksum: 10/6ac4b35723429b11b117e813c7acc42c3af8b5554caaf1fc750404c1ae59f9b7376bc69b9e9e194a5a97357a597c2228b7173d317320f0360d617b6425212f58
  languageName: node
  linkType: hard

"@types/retry@npm:0.12.0":
  version: 0.12.0
  resolution: "@types/retry@npm:0.12.0"
  checksum: 10/bbd0b88f4b3eba7b7acfc55ed09c65ef6f2e1bcb4ec9b4dca82c66566934351534317d294a770a7cc6c0468d5573c5350abab6e37c65f8ef254443e1b028e44d
  languageName: node
  linkType: hard

"@types/send@npm:*":
  version: 0.17.4
  resolution: "@types/send@npm:0.17.4"
  dependencies:
    "@types/mime": "npm:^1"
    "@types/node": "npm:*"
  checksum: 10/28320a2aa1eb704f7d96a65272a07c0bf3ae7ed5509c2c96ea5e33238980f71deeed51d3631927a77d5250e4091b3e66bce53b42d770873282c6a20bb8b0280d
  languageName: node
  linkType: hard

"@types/serve-static@npm:*":
  version: 1.15.7
  resolution: "@types/serve-static@npm:1.15.7"
  dependencies:
    "@types/http-errors": "npm:*"
    "@types/node": "npm:*"
    "@types/send": "npm:*"
  checksum: 10/c5a7171d5647f9fbd096ed1a26105759f3153ccf683824d99fee4c7eb9cde2953509621c56a070dd9fb1159e799e86d300cbe4e42245ebc5b0c1767e8ca94a67
  languageName: node
  linkType: hard

"@types/uuid@npm:^9.0.1":
  version: 9.0.8
  resolution: "@types/uuid@npm:9.0.8"
  checksum: 10/b8c60b7ba8250356b5088302583d1704a4e1a13558d143c549c408bf8920535602ffc12394ede77f8a8083511b023704bc66d1345792714002bfa261b17c5275
  languageName: node
  linkType: hard

"JSONStream@npm:^1.3.1":
  version: 1.3.5
  resolution: "JSONStream@npm:1.3.5"
  dependencies:
    jsonparse: "npm:^1.2.0"
    through: "npm:>=2.2.7 <3"
  bin:
    JSONStream: ./bin.js
  checksum: 10/e30daf7b9b2da23076181d9a0e4bec33bc1d97e8c0385b949f1b16ba3366a1d241ec6f077850c01fe32379b5ebb8b96b65496984bc1545a93a5150bf4c267439
  languageName: node
  linkType: hard

"abbrev@npm:^3.0.0":
  version: 3.0.1
  resolution: "abbrev@npm:3.0.1"
  checksum: 10/ebd2c149dda6f543b66ce3779ea612151bb3aa9d0824f169773ee9876f1ca5a4e0adbcccc7eed048c04da7998e1825e2aa76fcca92d9e67dea50ac2b0a58dc2e
  languageName: node
  linkType: hard

"abort-controller@npm:^3.0.0":
  version: 3.0.0
  resolution: "abort-controller@npm:3.0.0"
  dependencies:
    event-target-shim: "npm:^5.0.0"
  checksum: 10/ed84af329f1828327798229578b4fe03a4dd2596ba304083ebd2252666bdc1d7647d66d0b18704477e1f8aa315f055944aa6e859afebd341f12d0a53c37b4b40
  languageName: node
  linkType: hard

"accept-language@npm:^3.0.18":
  version: 3.0.20
  resolution: "accept-language@npm:3.0.20"
  dependencies:
    bcp47: "npm:^1.1.2"
  checksum: 10/b0374938f968640fec607b48e023dbbb21fa31df611978b3351322214af335e62a02ea8c7680309189d1b6b2eeac5fb8b90dd6a5a9b9f74df56ca58b8dde8ff7
  languageName: node
  linkType: hard

"accepts@npm:^1.3.3, accepts@npm:~1.3.8":
  version: 1.3.8
  resolution: "accepts@npm:1.3.8"
  dependencies:
    mime-types: "npm:~2.1.34"
    negotiator: "npm:0.6.3"
  checksum: 10/67eaaa90e2917c58418e7a9b89392002d2b1ccd69bcca4799135d0c632f3b082f23f4ae4ddeedbced5aa59bcc7bdf4699c69ebed4593696c922462b7bc5744d6
  languageName: node
  linkType: hard

"accepts@npm:^2.0.0":
  version: 2.0.0
  resolution: "accepts@npm:2.0.0"
  dependencies:
    mime-types: "npm:^3.0.0"
    negotiator: "npm:^1.0.0"
  checksum: 10/ea1343992b40b2bfb3a3113fa9c3c2f918ba0f9197ae565c48d3f84d44b174f6b1d5cd9989decd7655963eb03a272abc36968cc439c2907f999bd5ef8653d5a7
  languageName: node
  linkType: hard

"acorn-jsx@npm:^5.3.2":
  version: 5.3.2
  resolution: "acorn-jsx@npm:5.3.2"
  peerDependencies:
    acorn: ^6.0.0 || ^7.0.0 || ^8.0.0
  checksum: 10/d4371eaef7995530b5b5ca4183ff6f062ca17901a6d3f673c9ac011b01ede37e7a1f7f61f8f5cfe709e88054757bb8f3277dc4061087cdf4f2a1f90ccbcdb977
  languageName: node
  linkType: hard

"acorn@npm:^8.14.0, acorn@npm:^8.5.0, acorn@npm:^8.9.0":
  version: 8.14.1
  resolution: "acorn@npm:8.14.1"
  bin:
    acorn: bin/acorn
  checksum: 10/d1379bbee224e8d44c3c3946e6ba6973e999fbdd4e22e41c3455d7f9b6f72f7ce18d3dc218002e1e48eea789539cf1cb6d1430c81838c6744799c712fb557d92
  languageName: node
  linkType: hard

"agent-base@npm:^7.1.0, agent-base@npm:^7.1.2":
  version: 7.1.3
  resolution: "agent-base@npm:7.1.3"
  checksum: 10/3db6d8d4651f2aa1a9e4af35b96ab11a7607af57a24f3bc721a387eaa3b5f674e901f0a648b0caefd48f3fd117c7761b79a3b55854e2aebaa96c3f32cf76af84
  languageName: node
  linkType: hard

"agentkeepalive@npm:^4.1.0":
  version: 4.6.0
  resolution: "agentkeepalive@npm:4.6.0"
  dependencies:
    humanize-ms: "npm:^1.2.1"
  checksum: 10/80c546bd88dd183376d6a29e5598f117f380b1d567feb1de184241d6ece721e2bdd38f179a1674276de01780ccae229a38c60a77317e2f5ad2f1818856445bd7
  languageName: node
  linkType: hard

"ajv@npm:^6.12.3, ajv@npm:^6.12.4":
  version: 6.12.6
  resolution: "ajv@npm:6.12.6"
  dependencies:
    fast-deep-equal: "npm:^3.1.1"
    fast-json-stable-stringify: "npm:^2.0.0"
    json-schema-traverse: "npm:^0.4.1"
    uri-js: "npm:^4.2.2"
  checksum: 10/48d6ad21138d12eb4d16d878d630079a2bda25a04e745c07846a4ad768319533031e28872a9b3c5790fa1ec41aabdf2abed30a56e5a03ebc2cf92184b8ee306c
  languageName: node
  linkType: hard

"ansi-regex@npm:^5.0.1":
  version: 5.0.1
  resolution: "ansi-regex@npm:5.0.1"
  checksum: 10/2aa4bb54caf2d622f1afdad09441695af2a83aa3fe8b8afa581d205e57ed4261c183c4d3877cee25794443fde5876417d859c108078ab788d6af7e4fe52eb66b
  languageName: node
  linkType: hard

"ansi-regex@npm:^6.0.1":
  version: 6.1.0
  resolution: "ansi-regex@npm:6.1.0"
  checksum: 10/495834a53b0856c02acd40446f7130cb0f8284f4a39afdab20d5dc42b2e198b1196119fe887beed8f9055c4ff2055e3b2f6d4641d0be018cdfb64fedf6fc1aac
  languageName: node
  linkType: hard

"ansi-styles@npm:^4.0.0, ansi-styles@npm:^4.1.0":
  version: 4.3.0
  resolution: "ansi-styles@npm:4.3.0"
  dependencies:
    color-convert: "npm:^2.0.1"
  checksum: 10/b4494dfbfc7e4591b4711a396bd27e540f8153914123dccb4cdbbcb514015ada63a3809f362b9d8d4f6b17a706f1d7bea3c6f974b15fa5ae76b5b502070889ff
  languageName: node
  linkType: hard

"ansi-styles@npm:^6.1.0":
  version: 6.2.1
  resolution: "ansi-styles@npm:6.2.1"
  checksum: 10/70fdf883b704d17a5dfc9cde206e698c16bcd74e7f196ab821511651aee4f9f76c9514bdfa6ca3a27b5e49138b89cb222a28caf3afe4567570139577f991df32
  languageName: node
  linkType: hard

"argparse@npm:^1.0.7":
  version: 1.0.10
  resolution: "argparse@npm:1.0.10"
  dependencies:
    sprintf-js: "npm:~1.0.2"
  checksum: 10/c6a621343a553ff3779390bb5ee9c2263d6643ebcd7843227bdde6cc7adbed796eb5540ca98db19e3fd7b4714e1faa51551f8849b268bb62df27ddb15cbcd91e
  languageName: node
  linkType: hard

"argparse@npm:^2.0.1":
  version: 2.0.1
  resolution: "argparse@npm:2.0.1"
  checksum: 10/18640244e641a417ec75a9bd38b0b2b6b95af5199aa241b131d4b2fb206f334d7ecc600bd194861610a5579084978bfcbb02baa399dbe442d56d0ae5e60dbaef
  languageName: node
  linkType: hard

"argparse@npm:~ 0.1.11":
  version: 0.1.16
  resolution: "argparse@npm:0.1.16"
  dependencies:
    underscore: "npm:~1.7.0"
    underscore.string: "npm:~2.4.0"
  checksum: 10/4984dd143a5aa465a469005c1ca62a3658067ac6cf77dc374258a1d167e1bc35cfa2d84dbc1a1e864af3c54fdada91bcb9bb4dc4e4fad1054dfb0bb98a78daa1
  languageName: node
  linkType: hard

"array-buffer-byte-length@npm:^1.0.1, array-buffer-byte-length@npm:^1.0.2":
  version: 1.0.2
  resolution: "array-buffer-byte-length@npm:1.0.2"
  dependencies:
    call-bound: "npm:^1.0.3"
    is-array-buffer: "npm:^3.0.5"
  checksum: 10/0ae3786195c3211b423e5be8dd93357870e6fb66357d81da968c2c39ef43583ef6eece1f9cb1caccdae4806739c65dea832b44b8593414313cd76a89795fca63
  languageName: node
  linkType: hard

"array-flatten@npm:1.1.1":
  version: 1.1.1
  resolution: "array-flatten@npm:1.1.1"
  checksum: 10/********************************************************************************************************************************
  languageName: node
  linkType: hard

"arraybuffer.prototype.slice@npm:^1.0.4":
  version: 1.0.4
  resolution: "arraybuffer.prototype.slice@npm:1.0.4"
  dependencies:
    array-buffer-byte-length: "npm:^1.0.1"
    call-bind: "npm:^1.0.8"
    define-properties: "npm:^1.2.1"
    es-abstract: "npm:^1.23.5"
    es-errors: "npm:^1.3.0"
    get-intrinsic: "npm:^1.2.6"
    is-array-buffer: "npm:^3.0.4"
  checksum: 10/4821ebdfe7d699f910c7f09bc9fa996f09b96b80bccb4f5dd4b59deae582f6ad6e505ecef6376f8beac1eda06df2dbc89b70e82835d104d6fcabd33c1aed1ae9
  languageName: node
  linkType: hard

"asn1@npm:~0.2.3":
  version: 0.2.6
  resolution: "asn1@npm:0.2.6"
  dependencies:
    safer-buffer: "npm:~2.1.0"
  checksum: 10/cf629291fee6c1a6f530549939433ebf32200d7849f38b810ff26ee74235e845c0c12b2ed0f1607ac17383d19b219b69cefa009b920dab57924c5c544e495078
  languageName: node
  linkType: hard

"assert-plus@npm:1.0.0, assert-plus@npm:^1.0.0":
  version: 1.0.0
  resolution: "assert-plus@npm:1.0.0"
  checksum: 10/f4f991ae2df849cc678b1afba52d512a7cbf0d09613ba111e72255409ff9158550c775162a47b12d015d1b82b3c273e8e25df0e4783d3ddb008a293486d00a07
  languageName: node
  linkType: hard

"async-function@npm:^1.0.0":
  version: 1.0.0
  resolution: "async-function@npm:1.0.0"
  checksum: 10/1a09379937d846f0ce7614e75071c12826945d4e417db634156bf0e4673c495989302f52186dfa9767a1d9181794554717badd193ca2bbab046ef1da741d8efd
  languageName: node
  linkType: hard

"async-lock@npm:^1.4.1":
  version: 1.4.1
  resolution: "async-lock@npm:1.4.1"
  checksum: 10/80d55ac95f920e880a865968b799963014f6d987dd790dd08173fae6e1af509d8cd0ab45a25daaca82e3ef8e7c939f5d128cd1facfcc5c647da8ac2409e20ef9
  languageName: node
  linkType: hard

"async@npm:^2.1.4, async@npm:^2.4.0, async@npm:^2.6.1":
  version: 2.6.4
  resolution: "async@npm:2.6.4"
  dependencies:
    lodash: "npm:^4.17.14"
  checksum: 10/df8e52817d74677ab50c438d618633b9450aff26deb274da6dfedb8014130909482acdc7753bce9b72e6171ce9a9f6a92566c4ced34c3cb3714d57421d58ad27
  languageName: node
  linkType: hard

"async@npm:^3.1.0, async@npm:^3.2.3, async@npm:^3.2.4, async@npm:^3.2.6":
  version: 3.2.6
  resolution: "async@npm:3.2.6"
  checksum: 10/cb6e0561a3c01c4b56a799cc8bab6ea5fef45f069ab32500b6e19508db270ef2dffa55e5aed5865c5526e9907b1f8be61b27530823b411ffafb5e1538c86c368
  languageName: node
  linkType: hard

"asynckit@npm:^0.4.0":
  version: 0.4.0
  resolution: "asynckit@npm:0.4.0"
  checksum: 10/3ce727cbc78f69d6a4722517a58ee926c8c21083633b1d3fdf66fd688f6c127a53a592141bd4866f9b63240a86e9d8e974b13919450bd17fa33c2d22c4558ad8
  languageName: node
  linkType: hard

"available-typed-arrays@npm:^1.0.7":
  version: 1.0.7
  resolution: "available-typed-arrays@npm:1.0.7"
  dependencies:
    possible-typed-array-names: "npm:^1.0.0"
  checksum: 10/6c9da3a66caddd83c875010a1ca8ef11eac02ba15fb592dc9418b2b5e7b77b645fa7729380a92d9835c2f05f2ca1b6251f39b993e0feb3f1517c74fa1af02cab
  languageName: node
  linkType: hard

"aws-sign2@npm:~0.7.0":
  version: 0.7.0
  resolution: "aws-sign2@npm:0.7.0"
  checksum: 10/2ac497d739f71be3264cf096a33ab256a1fea7fe80b87dc51ec29374505bd5a661279ef1c22989d68528ea61ed634021ca63b31cf1d3c2a3682ffc106f7d0e96
  languageName: node
  linkType: hard

"aws4@npm:^1.8.0":
  version: 1.13.2
  resolution: "aws4@npm:1.13.2"
  checksum: 10/290b9f84facbad013747725bfd8b4c42d0b3b04b5620d8418f0219832ef95a7dc597a4af7b1589ae7fce18bacde96f40911c3cda36199dd04d9f8e01f72fa50a
  languageName: node
  linkType: hard

"axios-retry@npm:^4.5.0":
  version: 4.5.0
  resolution: "axios-retry@npm:4.5.0"
  dependencies:
    is-retry-allowed: "npm:^2.2.0"
  peerDependencies:
    axios: 0.x || 1.x
  checksum: 10/39ed05248757387a44dde94255df8ad54088aece50574c6ce9a1cd02b9e40252f7390285cea54ded04e33a3a549e462d5bdacc8d3178221b7cd40e8aff09ba46
  languageName: node
  linkType: hard

"axios@npm:^1.5.1, axios@npm:^1.8.3, axios@npm:^1.8.4, axios@npm:^1.9.0":
  version: 1.9.0
  resolution: "axios@npm:1.9.0"
  dependencies:
    follow-redirects: "npm:^1.15.6"
    form-data: "npm:^4.0.0"
    proxy-from-env: "npm:^1.1.0"
  checksum: 10/a2f90bba56820883879f32a237e2b9ff25c250365dcafd41cec41b3406a3df334a148f90010182dfdadb4b41dc59f6f0b3e8898ff41b666d1157b5f3f4523497
  languageName: node
  linkType: hard

"balanced-match@npm:^1.0.0":
  version: 1.0.2
  resolution: "balanced-match@npm:1.0.2"
  checksum: 10/9706c088a283058a8a99e0bf91b0a2f75497f185980d9ffa8b304de1d9e58ebda7c72c07ebf01dadedaac5b2907b2c6f566f660d62bd336c3468e960403b9d65
  languageName: node
  linkType: hard

"base64-js@npm:0.0.2":
  version: 0.0.2
  resolution: "base64-js@npm:0.0.2"
  checksum: 10/77aaf0f920e3dd1c61392ec69493fc96f37fc3565cca92f9f6a4b5bad0abc02c328e9f2546cf4ef8c09d43443461faee08158763948431b238a86e16acea784f
  languageName: node
  linkType: hard

"base64-js@npm:1.0.2":
  version: 1.0.2
  resolution: "base64-js@npm:1.0.2"
  checksum: 10/68e352c13687362b15a59a347d5404be558828235ebb26f98e75b9d8645499c4856b1a3dd584289f778a2fc94f2ebd1a54a407a2f4ae370eabd78ae9a1dde743
  languageName: node
  linkType: hard

"base64-js@npm:^1.3.0, base64-js@npm:^1.3.1":
  version: 1.5.1
  resolution: "base64-js@npm:1.5.1"
  checksum: 10/669632eb3745404c2f822a18fc3a0122d2f9a7a13f7fb8b5823ee19d1d2ff9ee5b52c53367176ea4ad093c332fd5ab4bd0ebae5a8e27917a4105a4cfc86b1005
  languageName: node
  linkType: hard

"bcp47@npm:^1.1.2":
  version: 1.1.2
  resolution: "bcp47@npm:1.1.2"
  checksum: 10/4e67daddfacf846e28895d682972f7de0c22669e99b2ed4561615f13c9a8fecc100ebe76f4b8496f535945db84ce04a32f7bce6ef26329a3cf49242cf0d36a0a
  languageName: node
  linkType: hard

"bcrypt-pbkdf@npm:^1.0.0":
  version: 1.0.2
  resolution: "bcrypt-pbkdf@npm:1.0.2"
  dependencies:
    tweetnacl: "npm:^0.14.3"
  checksum: 10/13a4cde058250dbf1fa77a4f1b9a07d32ae2e3b9e28e88a0c7a1827835bc3482f3e478c4a0cfd4da6ff0c46dae07da1061123a995372b32cc563d9975f975404
  languageName: node
  linkType: hard

"bcryptjs@npm:^3.0.2":
  version: 3.0.2
  resolution: "bcryptjs@npm:3.0.2"
  bin:
    bcrypt: bin/bcrypt
  checksum: 10/02c37e7298aba74e13e0576d63617cc1c9005efc165e55c11b287dc17192bedce8cd6f6e78180203c2519a211d9eb77ff14b286f962e2f0b93c08d2ada02dc22
  languageName: node
  linkType: hard

"bignumber.js@npm:^9.0.0":
  version: 9.3.0
  resolution: "bignumber.js@npm:9.3.0"
  checksum: 10/60b79efcf7b56b925fca8eebd10d1f4b70aa2bf6eade7f5af0266f0092226dd2abcd9a3ee315ecb39459750d5a630ce3980b707e5d7bea32c97ffd378e8cc159
  languageName: node
  linkType: hard

"bindings@npm:^1.5.0":
  version: 1.5.0
  resolution: "bindings@npm:1.5.0"
  dependencies:
    file-uri-to-path: "npm:1.0.0"
  checksum: 10/593d5ae975ffba15fbbb4788fe5abd1e125afbab849ab967ab43691d27d6483751805d98cb92f7ac24a2439a8a8678cd0131c535d5d63de84e383b0ce2786133
  languageName: node
  linkType: hard

"bl@npm:^2.0.1, bl@npm:^2.2.1":
  version: 2.2.1
  resolution: "bl@npm:2.2.1"
  dependencies:
    readable-stream: "npm:^2.3.5"
    safe-buffer: "npm:^5.1.1"
  checksum: 10/6320002f204720a53ce73147f8b46372adb91666371cc8167756120fce1658ffcbb228a5ca6ac61a8c6db6599d11deda133d90f8965497c79e709c34993b6adf
  languageName: node
  linkType: hard

"bl@npm:^6.1.0":
  version: 6.1.0
  resolution: "bl@npm:6.1.0"
  dependencies:
    "@types/readable-stream": "npm:^4.0.0"
    buffer: "npm:^6.0.3"
    inherits: "npm:^2.0.4"
    readable-stream: "npm:^4.2.0"
  checksum: 10/36499b9a3e83802d811de4b93a1c65e6996de54d1b6030a8b7d1e630353afc7ebf7137da08aaa64994df31b68ce6ef60fa7fa81a0581fc88a658b9f7719ff23d
  languageName: node
  linkType: hard

"bluebird@npm:^2.6.2":
  version: 2.11.0
  resolution: "bluebird@npm:2.11.0"
  checksum: 10/f8271257f248f3a95caa3b54a99c96c91132f6d62c2b2aa367bc63bab6e3b9a240ae6a95d893f70715ef52647af9d4e4afe0a04267c31c94cc5873d2add96a3b
  languageName: node
  linkType: hard

"bluebird@npm:^3.5.3, bluebird@npm:^3.7.2":
  version: 3.7.2
  resolution: "bluebird@npm:3.7.2"
  checksum: 10/007c7bad22c5d799c8dd49c85b47d012a1fe3045be57447721e6afbd1d5be43237af1db62e26cb9b0d9ba812d2e4ca3bac82f6d7e016b6b88de06ee25ceb96e7
  languageName: node
  linkType: hard

"body-parser@npm:1.20.3":
  version: 1.20.3
  resolution: "body-parser@npm:1.20.3"
  dependencies:
    bytes: "npm:3.1.2"
    content-type: "npm:~1.0.5"
    debug: "npm:2.6.9"
    depd: "npm:2.0.0"
    destroy: "npm:1.2.0"
    http-errors: "npm:2.0.0"
    iconv-lite: "npm:0.4.24"
    on-finished: "npm:2.4.1"
    qs: "npm:6.13.0"
    raw-body: "npm:2.5.2"
    type-is: "npm:~1.6.18"
    unpipe: "npm:1.0.0"
  checksum: 10/8723e3d7a672eb50854327453bed85ac48d045f4958e81e7d470c56bf111f835b97e5b73ae9f6393d0011cc9e252771f46fd281bbabc57d33d3986edf1e6aeca
  languageName: node
  linkType: hard

"body-parser@npm:^2.2.0":
  version: 2.2.0
  resolution: "body-parser@npm:2.2.0"
  dependencies:
    bytes: "npm:^3.1.2"
    content-type: "npm:^1.0.5"
    debug: "npm:^4.4.0"
    http-errors: "npm:^2.0.0"
    iconv-lite: "npm:^0.6.3"
    on-finished: "npm:^2.4.1"
    qs: "npm:^6.14.0"
    raw-body: "npm:^3.0.0"
    type-is: "npm:^2.0.0"
  checksum: 10/e9d844b036bd15970df00a16f373c7ed28e1ef870974a0a1d4d6ef60d70e01087cc20a0dbb2081c49a88e3c08ce1d87caf1e2898c615dffa193f63e8faa8a84e
  languageName: node
  linkType: hard

"bops@npm:1.0.0":
  version: 1.0.0
  resolution: "bops@npm:1.0.0"
  dependencies:
    base64-js: "npm:1.0.2"
    to-utf8: "npm:0.0.1"
  checksum: 10/32eaeb110bc437f2099f68797558b1ca58bba641c50aa6986473c48f8ab5de30d0b3690a99bfdb47016b064acedf58225e4c7d6376d12b28f71406662a65f550
  languageName: node
  linkType: hard

"bops@npm:~0.0.6":
  version: 0.0.7
  resolution: "bops@npm:0.0.7"
  dependencies:
    base64-js: "npm:0.0.2"
    to-utf8: "npm:0.0.1"
  checksum: 10/f6e163a815519b1d5bfc6e65acb4c28d87a834fa91053548650ed2dde81986f358feeadcb53ace03455a253d8093f11272eafafb48179dd8139238167b89a379
  languageName: node
  linkType: hard

"bowser@npm:^2.11.0":
  version: 2.11.0
  resolution: "bowser@npm:2.11.0"
  checksum: 10/ef46500eafe35072455e7c3ae771244e97827e0626686a9a3601c436d16eb272dad7ccbd49e2130b599b617ca9daa67027de827ffc4c220e02f63c84b69a8751
  languageName: node
  linkType: hard

"brace-expansion@npm:^1.1.7":
  version: 1.1.11
  resolution: "brace-expansion@npm:1.1.11"
  dependencies:
    balanced-match: "npm:^1.0.0"
    concat-map: "npm:0.0.1"
  checksum: 10/faf34a7bb0c3fcf4b59c7808bc5d2a96a40988addf2e7e09dfbb67a2251800e0d14cd2bfc1aa79174f2f5095c54ff27f46fb1289fe2d77dac755b5eb3434cc07
  languageName: node
  linkType: hard

"brace-expansion@npm:^2.0.1":
  version: 2.0.1
  resolution: "brace-expansion@npm:2.0.1"
  dependencies:
    balanced-match: "npm:^1.0.0"
  checksum: 10/a61e7cd2e8a8505e9f0036b3b6108ba5e926b4b55089eeb5550cd04a471fe216c96d4fe7e4c7f995c728c554ae20ddfc4244cad10aef255e72b62930afd233d1
  languageName: node
  linkType: hard

"browserslist@npm:^4.24.0":
  version: 4.24.5
  resolution: "browserslist@npm:4.24.5"
  dependencies:
    caniuse-lite: "npm:^1.0.30001716"
    electron-to-chromium: "npm:^1.5.149"
    node-releases: "npm:^2.0.19"
    update-browserslist-db: "npm:^1.1.3"
  bin:
    browserslist: cli.js
  checksum: 10/93fde829b77f20e2c4e1e0eaed154681c05e4828420e4afba790d480daa5de742977a44bbac8567881b8fbec3da3dea7ca1cb578ac1fd4385ef4ae91ca691d64
  languageName: node
  linkType: hard

"bson-objectid@npm:^2.0.4":
  version: 2.0.4
  resolution: "bson-objectid@npm:2.0.4"
  checksum: 10/dfc34b9bbebddc11d322b6fe41a0d8576d9538a37e3f8d4249e666e5580bb0801db741e2f5f0d5d5fbd6dd7969ce32890673223b83c4ce023d095a43d69a7231
  languageName: node
  linkType: hard

"bson@npm:^1.0.6, bson@npm:^1.1.4":
  version: 1.1.6
  resolution: "bson@npm:1.1.6"
  checksum: 10/db94d70b2de9ff62e84f7682180cc361ccd4146d51226115d150c390501a062e942921db9f0d77ad43da0ff512859542a73e9d118f1831080e0fdaa90840863c
  languageName: node
  linkType: hard

"buffer-equal-constant-time@npm:^1.0.1":
  version: 1.0.1
  resolution: "buffer-equal-constant-time@npm:1.0.1"
  checksum: 10/80bb945f5d782a56f374b292770901065bad21420e34936ecbe949e57724b4a13874f735850dd1cc61f078773c4fb5493a41391e7bda40d1fa388d6bd80daaab
  languageName: node
  linkType: hard

"buffer@npm:^6.0.3":
  version: 6.0.3
  resolution: "buffer@npm:6.0.3"
  dependencies:
    base64-js: "npm:^1.3.1"
    ieee754: "npm:^1.2.1"
  checksum: 10/b6bc68237ebf29bdacae48ce60e5e28fc53ae886301f2ad9496618efac49427ed79096750033e7eab1897a4f26ae374ace49106a5758f38fb70c78c9fda2c3b1
  languageName: node
  linkType: hard

"bytes@npm:3.1.2, bytes@npm:^3.1.2":
  version: 3.1.2
  resolution: "bytes@npm:3.1.2"
  checksum: 10/a10abf2ba70c784471d6b4f58778c0beeb2b5d405148e66affa91f23a9f13d07603d0a0354667310ae1d6dc141474ffd44e2a074be0f6e2254edb8fc21445388
  languageName: node
  linkType: hard

"cacache@npm:^19.0.1":
  version: 19.0.1
  resolution: "cacache@npm:19.0.1"
  dependencies:
    "@npmcli/fs": "npm:^4.0.0"
    fs-minipass: "npm:^3.0.0"
    glob: "npm:^10.2.2"
    lru-cache: "npm:^10.0.1"
    minipass: "npm:^7.0.3"
    minipass-collect: "npm:^2.0.1"
    minipass-flush: "npm:^1.0.5"
    minipass-pipeline: "npm:^1.2.4"
    p-map: "npm:^7.0.2"
    ssri: "npm:^12.0.0"
    tar: "npm:^7.4.3"
    unique-filename: "npm:^4.0.0"
  checksum: 10/ea026b27b13656330c2bbaa462a88181dcaa0435c1c2e705db89b31d9bdf7126049d6d0445ba746dca21454a0cfdf1d6f47fd39d34c8c8435296b30bc5738a13
  languageName: node
  linkType: hard

"cacheable-lookup@npm:^5.0.3":
  version: 5.0.4
  resolution: "cacheable-lookup@npm:5.0.4"
  checksum: 10/618a8b3eea314060e74cb3285a6154e8343c244a34235acf91cfe626ee0705c24e3cd11e4b1a7b3900bd749ee203ae65afe13adf610c8ab173e99d4a208faf75
  languageName: node
  linkType: hard

"cacheable-request@npm:^7.0.2":
  version: 7.0.4
  resolution: "cacheable-request@npm:7.0.4"
  dependencies:
    clone-response: "npm:^1.0.2"
    get-stream: "npm:^5.1.0"
    http-cache-semantics: "npm:^4.0.0"
    keyv: "npm:^4.0.0"
    lowercase-keys: "npm:^2.0.0"
    normalize-url: "npm:^6.0.1"
    responselike: "npm:^2.0.0"
  checksum: 10/0f4f2001260ecca78b9f64fc8245e6b5a5dcde24ea53006daab71f5e0e1338095aa1512ec099c4f9895a9e5acfac9da423cb7c079e131485891e9214aca46c41
  languageName: node
  linkType: hard

"call-bind-apply-helpers@npm:^1.0.0, call-bind-apply-helpers@npm:^1.0.1, call-bind-apply-helpers@npm:^1.0.2":
  version: 1.0.2
  resolution: "call-bind-apply-helpers@npm:1.0.2"
  dependencies:
    es-errors: "npm:^1.3.0"
    function-bind: "npm:^1.1.2"
  checksum: 10/00482c1f6aa7cfb30fb1dbeb13873edf81cfac7c29ed67a5957d60635a56b2a4a480f1016ddbdb3395cc37900d46037fb965043a51c5c789ffeab4fc535d18b5
  languageName: node
  linkType: hard

"call-bind@npm:^1.0.7, call-bind@npm:^1.0.8":
  version: 1.0.8
  resolution: "call-bind@npm:1.0.8"
  dependencies:
    call-bind-apply-helpers: "npm:^1.0.0"
    es-define-property: "npm:^1.0.0"
    get-intrinsic: "npm:^1.2.4"
    set-function-length: "npm:^1.2.2"
  checksum: 10/659b03c79bbfccf0cde3a79e7d52570724d7290209823e1ca5088f94b52192dc1836b82a324d0144612f816abb2f1734447438e38d9dafe0b3f82c2a1b9e3bce
  languageName: node
  linkType: hard

"call-bound@npm:^1.0.2, call-bound@npm:^1.0.3, call-bound@npm:^1.0.4":
  version: 1.0.4
  resolution: "call-bound@npm:1.0.4"
  dependencies:
    call-bind-apply-helpers: "npm:^1.0.2"
    get-intrinsic: "npm:^1.3.0"
  checksum: 10/ef2b96e126ec0e58a7ff694db43f4d0d44f80e641370c21549ed911fecbdbc2df3ebc9bddad918d6bbdefeafb60bb3337902006d5176d72bcd2da74820991af7
  languageName: node
  linkType: hard

"callsites@npm:^3.0.0":
  version: 3.1.0
  resolution: "callsites@npm:3.1.0"
  checksum: 10/072d17b6abb459c2ba96598918b55868af677154bec7e73d222ef95a8fdb9bbf7dae96a8421085cdad8cd190d86653b5b6dc55a4484f2e5b2e27d5e0c3fc15b3
  languageName: node
  linkType: hard

"camel-case@npm:^4.1.2":
  version: 4.1.2
  resolution: "camel-case@npm:4.1.2"
  dependencies:
    pascal-case: "npm:^3.1.2"
    tslib: "npm:^2.0.3"
  checksum: 10/bcbd25cd253b3cbc69be3f535750137dbf2beb70f093bdc575f73f800acc8443d34fd52ab8f0a2413c34f1e8203139ffc88428d8863e4dfe530cfb257a379ad6
  languageName: node
  linkType: hard

"camelcase@npm:6.3.0":
  version: 6.3.0
  resolution: "camelcase@npm:6.3.0"
  checksum: 10/8c96818a9076434998511251dcb2761a94817ea17dbdc37f47ac080bd088fc62c7369429a19e2178b993497132c8cbcf5cc1f44ba963e76782ba469c0474938d
  languageName: node
  linkType: hard

"camelcase@npm:^5.0.0":
  version: 5.3.1
  resolution: "camelcase@npm:5.3.1"
  checksum: 10/e6effce26b9404e3c0f301498184f243811c30dfe6d0b9051863bd8e4034d09c8c2923794f280d6827e5aa055f6c434115ff97864a16a963366fb35fd673024b
  languageName: node
  linkType: hard

"caniuse-lite@npm:^1.0.30001716":
  version: 1.0.30001718
  resolution: "caniuse-lite@npm:1.0.30001718"
  checksum: 10/e172a4c156f743cc947e659f353ad9edb045725cc109a02cc792dcbf98569356ebfa4bb4356e3febf87427aab0951c34c1ee5630629334f25ae6f76de7d86fd0
  languageName: node
  linkType: hard

"canonical-json@npm:0.0.4":
  version: 0.0.4
  resolution: "canonical-json@npm:0.0.4"
  checksum: 10/305648a11727eeb847414f5edf0a3a9ea87808ee82c0462b2d9f90627f27e8038042ffb18f9a435f68f152af39b89a155fd3a2bbe4cd4b35499437f99c9baa47
  languageName: node
  linkType: hard

"capital-case@npm:^1.0.4":
  version: 1.0.4
  resolution: "capital-case@npm:1.0.4"
  dependencies:
    no-case: "npm:^3.0.4"
    tslib: "npm:^2.0.3"
    upper-case-first: "npm:^2.0.2"
  checksum: 10/41fa8fa87f6d24d0835a2b4a9341a3eaecb64ac29cd7c5391f35d6175a0fa98ab044e7f2602e1ec3afc886231462ed71b5b80c590b8b41af903ec2c15e5c5931
  languageName: node
  linkType: hard

"caseless@npm:~0.12.0":
  version: 0.12.0
  resolution: "caseless@npm:0.12.0"
  checksum: 10/ea1efdf430975fdbac3505cdd21007f7ac5aa29b6d4d1c091f965853cd1bf87e4b08ea07b31a6d688b038872b7cdf0589d9262d59c699d199585daad052aeb20
  languageName: node
  linkType: hard

"chalk@npm:^4.0.0, chalk@npm:^4.0.2":
  version: 4.1.2
  resolution: "chalk@npm:4.1.2"
  dependencies:
    ansi-styles: "npm:^4.1.0"
    supports-color: "npm:^7.1.0"
  checksum: 10/cb3f3e594913d63b1814d7ca7c9bafbf895f75fbf93b92991980610dfd7b48500af4e3a5d4e3a8f337990a96b168d7eb84ee55efdce965e2ee8efc20f8c8f139
  languageName: node
  linkType: hard

"change-case@npm:4.1.2":
  version: 4.1.2
  resolution: "change-case@npm:4.1.2"
  dependencies:
    camel-case: "npm:^4.1.2"
    capital-case: "npm:^1.0.4"
    constant-case: "npm:^3.0.4"
    dot-case: "npm:^3.0.4"
    header-case: "npm:^2.0.4"
    no-case: "npm:^3.0.4"
    param-case: "npm:^3.0.4"
    pascal-case: "npm:^3.1.2"
    path-case: "npm:^3.0.4"
    sentence-case: "npm:^3.0.4"
    snake-case: "npm:^3.0.4"
    tslib: "npm:^2.0.3"
  checksum: 10/e4bc4a093a1f7cce8b33896665cf9e456e3bc3cc0def2ad7691b1994cfca99b3188d0a513b16855b01a6bd20692fcde12a7d4d87a5615c4c515bbbf0e651f116
  languageName: node
  linkType: hard

"charenc@npm:0.0.2":
  version: 0.0.2
  resolution: "charenc@npm:0.0.2"
  checksum: 10/81dcadbe57e861d527faf6dd3855dc857395a1c4d6781f4847288ab23cffb7b3ee80d57c15bba7252ffe3e5e8019db767757ee7975663ad2ca0939bb8fcaf2e5
  languageName: node
  linkType: hard

"chownr@npm:^3.0.0":
  version: 3.0.0
  resolution: "chownr@npm:3.0.0"
  checksum: 10/b63cb1f73d171d140a2ed8154ee6566c8ab775d3196b0e03a2a94b5f6a0ce7777ee5685ca56849403c8d17bd457a6540672f9a60696a6137c7a409097495b82c
  languageName: node
  linkType: hard

"cldrjs@npm:^0.5.4":
  version: 0.5.5
  resolution: "cldrjs@npm:0.5.5"
  checksum: 10/b77f6d8454a07d503b430e684dca2bbea7e7a51427c398b0f08f36729f828512d20864f81e5cf025fb67939daabb925695263051e0319e920cd4c69f8416101d
  languageName: node
  linkType: hard

"cliui@npm:^6.0.0":
  version: 6.0.0
  resolution: "cliui@npm:6.0.0"
  dependencies:
    string-width: "npm:^4.2.0"
    strip-ansi: "npm:^6.0.0"
    wrap-ansi: "npm:^6.2.0"
  checksum: 10/44afbcc29df0899e87595590792a871cd8c4bc7d6ce92832d9ae268d141a77022adafca1aeaeccff618b62a613b8354e57fe22a275c199ec04baf00d381ef6ab
  languageName: node
  linkType: hard

"clone-response@npm:^1.0.2":
  version: 1.0.3
  resolution: "clone-response@npm:1.0.3"
  dependencies:
    mimic-response: "npm:^1.0.0"
  checksum: 10/4e671cac39b11c60aa8ba0a450657194a5d6504df51bca3fac5b3bd0145c4f8e8464898f87c8406b83232e3bc5cca555f51c1f9c8ac023969ebfbf7f6bdabb2e
  languageName: node
  linkType: hard

"clone@npm:2.x":
  version: 2.1.2
  resolution: "clone@npm:2.1.2"
  checksum: 10/d9c79efba655f0bf601ab299c57eb54cbaa9860fb011aee9d89ed5ac0d12df1660ab7642fddaabb9a26b7eff0e117d4520512cb70798319ff5d30a111b5310c2
  languageName: node
  linkType: hard

"cluster-key-slot@npm:^1.1.0":
  version: 1.1.2
  resolution: "cluster-key-slot@npm:1.1.2"
  checksum: 10/516ed8b5e1a14d9c3a9c96c72ef6de2d70dfcdbaa0ec3a90bc7b9216c5457e39c09a5775750c272369070308542e671146120153062ab5f2f481bed5de2c925f
  languageName: node
  linkType: hard

"collapse-white-space@npm:^1.0.0":
  version: 1.0.6
  resolution: "collapse-white-space@npm:1.0.6"
  checksum: 10/9673fb797952c5c888341435596c69388b22cd5560c8cd3f40edb72734a9c820f56a7c9525166bcb7068b5d5805372e6fd0c4b9f2869782ad070cb5d3faf26e7
  languageName: node
  linkType: hard

"color-convert@npm:^2.0.1":
  version: 2.0.1
  resolution: "color-convert@npm:2.0.1"
  dependencies:
    color-name: "npm:~1.1.4"
  checksum: 10/fa00c91b4332b294de06b443923246bccebe9fab1b253f7fe1772d37b06a2269b4039a85e309abe1fe11b267b11c08d1d0473fda3badd6167f57313af2887a64
  languageName: node
  linkType: hard

"color-name@npm:~1.1.4":
  version: 1.1.4
  resolution: "color-name@npm:1.1.4"
  checksum: 10/b0445859521eb4021cd0fb0cc1a75cecf67fceecae89b63f62b201cca8d345baf8b952c966862a9d9a2632987d4f6581f0ec8d957dfacece86f0a7919316f610
  languageName: node
  linkType: hard

"colors@npm:^1.4.0":
  version: 1.4.0
  resolution: "colors@npm:1.4.0"
  checksum: 10/90b2d5465159813a3983ea72ca8cff75f784824ad70f2cc2b32c233e95bcfbcda101ebc6d6766bc50f57263792629bfb4f1f8a4dfbd1d240f229fc7f69b785fc
  languageName: node
  linkType: hard

"combined-stream@npm:^1.0.6, combined-stream@npm:^1.0.8, combined-stream@npm:~1.0.6":
  version: 1.0.8
  resolution: "combined-stream@npm:1.0.8"
  dependencies:
    delayed-stream: "npm:~1.0.0"
  checksum: 10/2e969e637d05d09fa50b02d74c83a1186f6914aae89e6653b62595cc75a221464f884f55f231b8f4df7a49537fba60bdc0427acd2bf324c09a1dbb84837e36e4
  languageName: node
  linkType: hard

"commander@npm:^2.12.2":
  version: 2.20.3
  resolution: "commander@npm:2.20.3"
  checksum: 10/90c5b6898610cd075984c58c4f88418a4fb44af08c1b1415e9854c03171bec31b336b7f3e4cefe33de994b3f12b03c5e2d638da4316df83593b9e82554e7e95b
  languageName: node
  linkType: hard

"commondir@npm:^1.0.1":
  version: 1.0.1
  resolution: "commondir@npm:1.0.1"
  checksum: 10/4620bc4936a4ef12ce7dfcd272bb23a99f2ad68889a4e4ad766c9f8ad21af982511934d6f7050d4a8bde90011b1c15d56e61a1b4576d9913efbf697a20172d6c
  languageName: node
  linkType: hard

"compressible@npm:~2.0.18":
  version: 2.0.18
  resolution: "compressible@npm:2.0.18"
  dependencies:
    mime-db: "npm:>= 1.43.0 < 2"
  checksum: 10/58321a85b375d39230405654721353f709d0c1442129e9a17081771b816302a012471a9b8f4864c7dbe02eef7f2aaac3c614795197092262e94b409c9be108f0
  languageName: node
  linkType: hard

"compression@npm:^1.8.0":
  version: 1.8.0
  resolution: "compression@npm:1.8.0"
  dependencies:
    bytes: "npm:3.1.2"
    compressible: "npm:~2.0.18"
    debug: "npm:2.6.9"
    negotiator: "npm:~0.6.4"
    on-headers: "npm:~1.0.2"
    safe-buffer: "npm:5.2.1"
    vary: "npm:~1.1.2"
  checksum: 10/ca213b9bd03e56c7c3596399d846237b5f0b31ca4cdeaa76a9547cd3c1465fbcfcb0fe93a5d7ff64eff28383fc65b53f1ef8bb2720d11bb48ad8c0836c502506
  languageName: node
  linkType: hard

"concat-map@npm:0.0.1":
  version: 0.0.1
  resolution: "concat-map@npm:0.0.1"
  checksum: 10/9680699c8e2b3af0ae22592cb764acaf973f292a7b71b8a06720233011853a58e256c89216a10cbe889727532fd77f8bcd49a760cedfde271b8e006c20e079f2
  languageName: node
  linkType: hard

"constant-case@npm:^3.0.4":
  version: 3.0.4
  resolution: "constant-case@npm:3.0.4"
  dependencies:
    no-case: "npm:^3.0.4"
    tslib: "npm:^2.0.3"
    upper-case: "npm:^2.0.2"
  checksum: 10/6c3346d51afc28d9fae922e966c68eb77a19d94858dba230dd92d7b918b37d36db50f0311e9ecf6847e43e934b1c01406a0936973376ab17ec2c471fbcfb2cf3
  languageName: node
  linkType: hard

"content-disposition@npm:0.5.4":
  version: 0.5.4
  resolution: "content-disposition@npm:0.5.4"
  dependencies:
    safe-buffer: "npm:5.2.1"
  checksum: 10/b7f4ce176e324f19324be69b05bf6f6e411160ac94bc523b782248129eb1ef3be006f6cff431aaea5e337fe5d176ce8830b8c2a1b721626ead8933f0cbe78720
  languageName: node
  linkType: hard

"content-disposition@npm:^1.0.0":
  version: 1.0.0
  resolution: "content-disposition@npm:1.0.0"
  dependencies:
    safe-buffer: "npm:5.2.1"
  checksum: 10/0dcc1a2d7874526b0072df3011b134857b49d97a3bc135bb464a299525d4972de6f5f464fd64da6c4d8406d26a1ffb976f62afaffef7723b1021a44498d10e08
  languageName: node
  linkType: hard

"content-type@npm:^1.0.5, content-type@npm:~1.0.4, content-type@npm:~1.0.5":
  version: 1.0.5
  resolution: "content-type@npm:1.0.5"
  checksum: 10/585847d98dc7fb8035c02ae2cb76c7a9bd7b25f84c447e5ed55c45c2175e83617c8813871b4ee22f368126af6b2b167df655829007b21aa10302873ea9c62662
  languageName: node
  linkType: hard

"convert-source-map@npm:^2.0.0":
  version: 2.0.0
  resolution: "convert-source-map@npm:2.0.0"
  checksum: 10/c987be3ec061348cdb3c2bfb924bec86dea1eacad10550a85ca23edb0fe3556c3a61c7399114f3331ccb3499d7fd0285ab24566e5745929412983494c3926e15
  languageName: node
  linkType: hard

"cookie-signature@npm:1.0.6":
  version: 1.0.6
  resolution: "cookie-signature@npm:1.0.6"
  checksum: 10/f4e1b0a98a27a0e6e66fd7ea4e4e9d8e038f624058371bf4499cfcd8f3980be9a121486995202ba3fca74fbed93a407d6d54d43a43f96fd28d0bd7a06761591a
  languageName: node
  linkType: hard

"cookie-signature@npm:^1.2.1":
  version: 1.2.2
  resolution: "cookie-signature@npm:1.2.2"
  checksum: 10/be44a3c9a56f3771aea3a8bd8ad8f0a8e2679bcb967478267f41a510b4eb5ec55085386ba79c706c4ac21605ca76f4251973444b90283e0eb3eeafe8a92c7708
  languageName: node
  linkType: hard

"cookie@npm:0.7.1":
  version: 0.7.1
  resolution: "cookie@npm:0.7.1"
  checksum: 10/aec6a6aa0781761bf55d60447d6be08861d381136a0fe94aa084fddd4f0300faa2b064df490c6798adfa1ebaef9e0af9b08a189c823e0811b8b313b3d9a03380
  languageName: node
  linkType: hard

"cookie@npm:^0.7.1":
  version: 0.7.2
  resolution: "cookie@npm:0.7.2"
  checksum: 10/24b286c556420d4ba4e9bc09120c9d3db7d28ace2bd0f8ccee82422ce42322f73c8312441271e5eefafbead725980e5996cc02766dbb89a90ac7f5636ede608f
  languageName: node
  linkType: hard

"core-util-is@npm:1.0.2":
  version: 1.0.2
  resolution: "core-util-is@npm:1.0.2"
  checksum: 10/d0f7587346b44a1fe6c269267e037dd34b4787191e473c3e685f507229d88561c40eb18872fabfff02977301815d474300b7bfbd15396c13c5377393f7e87ec3
  languageName: node
  linkType: hard

"core-util-is@npm:~1.0.0":
  version: 1.0.3
  resolution: "core-util-is@npm:1.0.3"
  checksum: 10/9de8597363a8e9b9952491ebe18167e3b36e7707569eed0ebf14f8bba773611376466ae34575bca8cfe3c767890c859c74056084738f09d4e4a6f902b2ad7d99
  languageName: node
  linkType: hard

"cors@npm:^2.8.5":
  version: 2.8.5
  resolution: "cors@npm:2.8.5"
  dependencies:
    object-assign: "npm:^4"
    vary: "npm:^1"
  checksum: 10/66e88e08edee7cbce9d92b4d28a2028c88772a4c73e02f143ed8ca76789f9b59444eed6b1c167139e76fa662998c151322720093ba229f9941365ada5a6fc2c6
  languageName: node
  linkType: hard

"country-data@npm:^0.0.31":
  version: 0.0.31
  resolution: "country-data@npm:0.0.31"
  dependencies:
    currency-symbol-map: "npm:~2"
    underscore: "npm:>1.4.4"
  checksum: 10/d15918acaf1207b0ab7a337f7d1f217ddc94bc6e5b1a23b4f03e234a43caf22576a58442ccc0a40ab170a08be4884033829d89df6fbdbe1df1aabfc8ce5b3253
  languageName: node
  linkType: hard

"crm-business@workspace:.":
  version: 0.0.0-use.local
  resolution: "crm-business@workspace:."
  dependencies:
    "@babel/core": "npm:^7.27.3"
    "@babel/eslint-parser": "npm:^7.27.1"
    "@crm/loopback": "github:perkd/crm-loopback#semver:^0.8.2"
    "@crm/types": "github:perkd/crm-types#semver:^1.11.19"
    "@perkd/accesscontrol": "github:perkd/accesscontrol#semver:^0.5.0"
    "@perkd/actions": "github:perkd/actions#semver:^0.5.0"
    "@perkd/activity-registry-crm": "github:perkd/activity-registry-crm#semver:^1.1.5"
    "@perkd/commerce": "github:perkd/commerce#semver:^1.7.3"
    "@perkd/errors": "github:perkd/errors#semver:^0.5.1"
    "@perkd/eslint-config": "github:Perkd-X/eslint-config#semver:^3.1.3"
    "@perkd/event-registry-crm": "github:perkd/event-registry-crm#semver:^1.5.4"
    "@perkd/eventbus": "github:perkd/eventbus#semver:^4.9.5"
    "@perkd/fulfillments": "github:perkd/fulfillments#semver:^0.5.2"
    "@perkd/machines": "github:perkd/machines#semver:^0.4.0"
    "@perkd/metrics": "github:perkd/metrics#semver:^1.8.0"
    "@perkd/metrics-push": "github:perkd/metrics-push#semver:^1.5.0"
    "@perkd/multitenant-context": "github:perkd/multitenant-context#semver:^0.6.1"
    "@perkd/orders": "github:perkd/orders#semver:^0.5.7"
    "@perkd/providers": "github:perkd/providers#semver:^5.0.0"
    "@perkd/provisions": "github:perkd/provisions#semver:^0.5.0"
    "@perkd/sdk": "github:perkd/wallet-perkd#semver:^1.6.0"
    "@perkd/settings": "github:perkd/settings#semver:^1.6.0"
    "@perkd/sync": "github:perkd/sync#semver:^1.1.5"
    "@perkd/tenants": "github:perkd/tenants#semver:^4.9.0"
    "@perkd/touchpoints": "github:perkd/touchpoints#semver:^0.2.4"
    "@perkd/utils": "github:perkd/utils#semver:^2.0.5"
    "@perkd/wallet-widgets": "github:perkd/wallet-widgets#semver:^0.6.0"
    "@provider/aws": "github:perkd/aws#semver:^0.8.0"
    "@provider/ezreceipt": "github:perkd/invoice-ezreceipt#semver:^0.5.6"
    "@provider/feie": "github:perkd/print-feie#semver:^1.4.0"
    "@provider/google": "github:perkd/google#semver:^1.3.0"
    "@provider/grabfood": "github:perkd/sales-grabfood#semver:^1.0.17"
    "@provider/grabmart": "github:perkd/sales-grabmart#semver:^1.0.17"
    "@provider/shopify": "github:perkd/sales-shopify#semver:^1.5.3"
    "@provider/ubereats": "github:perkd/sales-ubereats#semver:^0.8.0"
    "@slack/web-api": "npm:^7.9.2"
    "@stylistic/eslint-plugin-js": "npm:^4.4.0"
    async-lock: "npm:^1.4.1"
    colors: "npm:^1.4.0"
    compression: "npm:^1.8.0"
    cors: "npm:^2.8.5"
    debug: "npm:^4.4.1"
    deep-diff: "npm:^1.0.2"
    dotenv: "npm:^16.5.0"
    eslint: "npm:^9.27.0"
    eslint-plugin-jsonc: "npm:^2.20.1"
    eslint-plugin-n: "npm:^17.18.0"
    eslint-plugin-security: "npm:^3.0.1"
    form-data: "npm:^4.0.2"
    geolib: "npm:^3.3.4"
    i18n-js: "npm:3.9.2"
    i18next: "npm:^25.2.1"
    loopback: "github:perkd/loopback#semver:^3.34.2"
    loopback-boot: "npm:^3.3.1"
    loopback-component-explorer: "npm:^6.5.1"
    loopback-connector-mongodb: "github:perkd/loopback-connector-mongodb#semver:5.6.1"
    loopback-connector-rest: "npm:^5.0.6"
    loopback-filters: "npm:^1.1.1"
    lru-cache: "npm:^11.1.0"
    nocache: "npm:^4.0.0"
    node-cache: "npm:^5.1.2"
    qrcode: "npm:^1.5.4"
    redlock: "npm:5.0.0-beta.2"
  languageName: unknown
  linkType: soft

"cross-spawn@npm:^7.0.0, cross-spawn@npm:^7.0.6":
  version: 7.0.6
  resolution: "cross-spawn@npm:7.0.6"
  dependencies:
    path-key: "npm:^3.1.0"
    shebang-command: "npm:^2.0.0"
    which: "npm:^2.0.1"
  checksum: 10/0d52657d7ae36eb130999dffff1168ec348687b48dd38e2ff59992ed916c88d328cf1d07ff4a4a10bc78de5e1c23f04b306d569e42f7a2293915c081e4dfee86
  languageName: node
  linkType: hard

"crypt@npm:0.0.2":
  version: 0.0.2
  resolution: "crypt@npm:0.0.2"
  checksum: 10/2c72768de3d28278c7c9ffd81a298b26f87ecdfe94415084f339e6632f089b43fe039f2c93f612bcb5ffe447238373d93b2e8c90894cba6cfb0ac7a74616f8b9
  languageName: node
  linkType: hard

"crypto-js@npm:^4.2.0":
  version: 4.2.0
  resolution: "crypto-js@npm:4.2.0"
  checksum: 10/c7bcc56a6e01c3c397e95aa4a74e4241321f04677f9a618a8f48a63b5781617248afb9adb0629824792e7ec20ca0d4241a49b6b2938ae6f973ec4efc5c53c924
  languageName: node
  linkType: hard

"cuint@npm:^0.2.2":
  version: 0.2.2
  resolution: "cuint@npm:0.2.2"
  checksum: 10/c1b98971f4a1b32ce71ec82eac87df87b54ee85d982e3967a6dd89f19ffd3ebbbdb82e3738e489f475611b6ed126c0deba05ed9ecffea0a721a4d43773ce0670
  languageName: node
  linkType: hard

"currency-symbol-map@npm:^5.1.0":
  version: 5.1.0
  resolution: "currency-symbol-map@npm:5.1.0"
  checksum: 10/7f6a4c58af8dc0150568a32bc5baa5dd8481abe6ae28918c2d7ccd86e8bd7e9dfa08e3bdeeb256da5dffb9974a91c11ce430023bf47fd9a19ea6f126668a4a20
  languageName: node
  linkType: hard

"currency-symbol-map@npm:~2":
  version: 2.2.0
  resolution: "currency-symbol-map@npm:2.2.0"
  checksum: 10/aa5e058962d40a779818ef68864784323f7b1f59806f727dbf0d0925bda239e5b1277cbb5073607e95090a9946c999a8b90dc1a997e4bad412ff201163512020
  languageName: node
  linkType: hard

"dashdash@npm:^1.12.0":
  version: 1.14.1
  resolution: "dashdash@npm:1.14.1"
  dependencies:
    assert-plus: "npm:^1.0.0"
  checksum: 10/137b287fa021201ce100cef772c8eeeaaafdd2aa7282864022acf3b873021e54cb809e9c060fa164840bf54ff72d00d6e2d8da1ee5a86d7200eeefa1123a8f7f
  languageName: node
  linkType: hard

"data-view-buffer@npm:^1.0.2":
  version: 1.0.2
  resolution: "data-view-buffer@npm:1.0.2"
  dependencies:
    call-bound: "npm:^1.0.3"
    es-errors: "npm:^1.3.0"
    is-data-view: "npm:^1.0.2"
  checksum: 10/c10b155a4e93999d3a215d08c23eea95f865e1f510b2e7748fcae1882b776df1afe8c99f483ace7fc0e5a3193ab08da138abebc9829d12003746c5a338c4d644
  languageName: node
  linkType: hard

"data-view-byte-length@npm:^1.0.2":
  version: 1.0.2
  resolution: "data-view-byte-length@npm:1.0.2"
  dependencies:
    call-bound: "npm:^1.0.3"
    es-errors: "npm:^1.3.0"
    is-data-view: "npm:^1.0.2"
  checksum: 10/2a47055fcf1ab3ec41b00b6f738c6461a841391a643c9ed9befec1117c1765b4d492661d97fb7cc899200c328949dca6ff189d2c6537d96d60e8a02dfe3c95f7
  languageName: node
  linkType: hard

"data-view-byte-offset@npm:^1.0.1":
  version: 1.0.1
  resolution: "data-view-byte-offset@npm:1.0.1"
  dependencies:
    call-bound: "npm:^1.0.2"
    es-errors: "npm:^1.3.0"
    is-data-view: "npm:^1.0.1"
  checksum: 10/fa3bdfa0968bea6711ee50375094b39f561bce3f15f9e558df59de9c25f0bdd4cddc002d9c1d70ac7772ebd36854a7e22d1761e7302a934e6f1c2263bcf44aa2
  languageName: node
  linkType: hard

"dayjs@npm:^1.11.13":
  version: 1.11.13
  resolution: "dayjs@npm:1.11.13"
  checksum: 10/7374d63ab179b8d909a95e74790def25c8986e329ae989840bacb8b1888be116d20e1c4eee75a69ea0dfbae13172efc50ef85619d304ee7ca3c01d5878b704f5
  languageName: node
  linkType: hard

"debug@npm:2.6.9":
  version: 2.6.9
  resolution: "debug@npm:2.6.9"
  dependencies:
    ms: "npm:2.0.0"
  checksum: 10/e07005f2b40e04f1bd14a3dd20520e9c4f25f60224cb006ce9d6781732c917964e9ec029fc7f1a151083cd929025ad5133814d4dc624a9aaf020effe4914ed14
  languageName: node
  linkType: hard

"debug@npm:4, debug@npm:^4.1.0, debug@npm:^4.1.1, debug@npm:^4.2.0, debug@npm:^4.3.1, debug@npm:^4.3.2, debug@npm:^4.3.4, debug@npm:^4.3.5, debug@npm:^4.4.0, debug@npm:^4.4.1":
  version: 4.4.1
  resolution: "debug@npm:4.4.1"
  dependencies:
    ms: "npm:^2.1.3"
  peerDependenciesMeta:
    supports-color:
      optional: true
  checksum: 10/8e2709b2144f03c7950f8804d01ccb3786373df01e406a0f66928e47001cf2d336cbed9ee137261d4f90d68d8679468c755e3548ed83ddacdc82b194d2468afe
  languageName: node
  linkType: hard

"debug@npm:^3.1.0":
  version: 3.2.7
  resolution: "debug@npm:3.2.7"
  dependencies:
    ms: "npm:^2.1.1"
  checksum: 10/d86fd7be2b85462297ea16f1934dc219335e802f629ca9a69b63ed8ed041dda492389bb2ee039217c02e5b54792b1c51aa96ae954cf28634d363a2360c7a1639
  languageName: node
  linkType: hard

"decamelize@npm:^1.2.0":
  version: 1.2.0
  resolution: "decamelize@npm:1.2.0"
  checksum: 10/ad8c51a7e7e0720c70ec2eeb1163b66da03e7616d7b98c9ef43cce2416395e84c1e9548dd94f5f6ffecfee9f8b94251fc57121a8b021f2ff2469b2bae247b8aa
  languageName: node
  linkType: hard

"decode-uri-component@npm:^0.2.2":
  version: 0.2.2
  resolution: "decode-uri-component@npm:0.2.2"
  checksum: 10/17a0e5fa400bf9ea84432226e252aa7b5e72793e16bf80b907c99b46a799aeacc139ec20ea57121e50c7bd875a1a4365928f884e92abf02e21a5a13790a0f33e
  languageName: node
  linkType: hard

"decompress-response@npm:^6.0.0":
  version: 6.0.0
  resolution: "decompress-response@npm:6.0.0"
  dependencies:
    mimic-response: "npm:^3.1.0"
  checksum: 10/d377cf47e02d805e283866c3f50d3d21578b779731e8c5072d6ce8c13cc31493db1c2f6784da9d1d5250822120cefa44f1deab112d5981015f2e17444b763812
  languageName: node
  linkType: hard

"deep-diff@npm:^1.0.2":
  version: 1.0.2
  resolution: "deep-diff@npm:1.0.2"
  checksum: 10/3e496111b5506e7399e8a5441b713b033ade3b9d60faff96a4c47189e460a889de50dab99afdb1dcddebf701a0b6a6bb7743f5090b7a620da12e3391c940f8f3
  languageName: node
  linkType: hard

"deep-is@npm:^0.1.3":
  version: 0.1.4
  resolution: "deep-is@npm:0.1.4"
  checksum: 10/ec12d074aef5ae5e81fa470b9317c313142c9e8e2afe3f8efa124db309720db96d1d222b82b84c834e5f87e7a614b44a4684b6683583118b87c833b3be40d4d8
  languageName: node
  linkType: hard

"deepmerge@npm:^4.3.1":
  version: 4.3.1
  resolution: "deepmerge@npm:4.3.1"
  checksum: 10/058d9e1b0ff1a154468bf3837aea436abcfea1ba1d165ddaaf48ca93765fdd01a30d33c36173da8fbbed951dd0a267602bc782fe288b0fc4b7e1e7091afc4529
  languageName: node
  linkType: hard

"defer-to-connect@npm:^2.0.0":
  version: 2.0.1
  resolution: "defer-to-connect@npm:2.0.1"
  checksum: 10/8a9b50d2f25446c0bfefb55a48e90afd58f85b21bcf78e9207cd7b804354f6409032a1705c2491686e202e64fc05f147aa5aa45f9aa82627563f045937f5791b
  languageName: node
  linkType: hard

"define-data-property@npm:^1.0.1, define-data-property@npm:^1.1.4":
  version: 1.1.4
  resolution: "define-data-property@npm:1.1.4"
  dependencies:
    es-define-property: "npm:^1.0.0"
    es-errors: "npm:^1.3.0"
    gopd: "npm:^1.0.1"
  checksum: 10/abdcb2505d80a53524ba871273e5da75e77e52af9e15b3aa65d8aad82b8a3a424dad7aee2cc0b71470ac7acf501e08defac362e8b6a73cdb4309f028061df4ae
  languageName: node
  linkType: hard

"define-properties@npm:^1.2.1":
  version: 1.2.1
  resolution: "define-properties@npm:1.2.1"
  dependencies:
    define-data-property: "npm:^1.0.1"
    has-property-descriptors: "npm:^1.0.0"
    object-keys: "npm:^1.1.1"
  checksum: 10/b4ccd00597dd46cb2d4a379398f5b19fca84a16f3374e2249201992f36b30f6835949a9429669ee6b41b6e837205a163eadd745e472069e70dfc10f03e5fcc12
  languageName: node
  linkType: hard

"delayed-stream@npm:~1.0.0":
  version: 1.0.0
  resolution: "delayed-stream@npm:1.0.0"
  checksum: 10/46fe6e83e2cb1d85ba50bd52803c68be9bd953282fa7096f51fc29edd5d67ff84ff753c51966061e5ba7cb5e47ef6d36a91924eddb7f3f3483b1c560f77a0020
  languageName: node
  linkType: hard

"denque@npm:^1.4.1":
  version: 1.5.1
  resolution: "denque@npm:1.5.1"
  checksum: 10/dbde01a987d95205f7563c67411e0964073a6b38e4cf2ff190cf91f71e2ce3f51c40bacd31f2a5497e0ff82366bcfd8231d3659cb03f987279130058d512aa29
  languageName: node
  linkType: hard

"denque@npm:^2.1.0":
  version: 2.1.0
  resolution: "denque@npm:2.1.0"
  checksum: 10/8ea05321576624b90acfc1ee9208b8d1d04b425cf7573b9b4fa40a2c3ed4d4b0af5190567858f532f677ed2003d4d2b73c8130b34e3c7b8d5e88cdcfbfaa1fe7
  languageName: node
  linkType: hard

"depd@npm:2.0.0, depd@npm:^2.0.0":
  version: 2.0.0
  resolution: "depd@npm:2.0.0"
  checksum: 10/c0c8ff36079ce5ada64f46cc9d6fd47ebcf38241105b6e0c98f412e8ad91f084bcf906ff644cc3a4bd876ca27a62accb8b0fff72ea6ed1a414b89d8506f4a5ca
  languageName: node
  linkType: hard

"destroy@npm:1.2.0":
  version: 1.2.0
  resolution: "destroy@npm:1.2.0"
  checksum: 10/0acb300b7478a08b92d810ab229d5afe0d2f4399272045ab22affa0d99dbaf12637659411530a6fcd597a9bdac718fc94373a61a95b4651bbc7b83684a565e38
  languageName: node
  linkType: hard

"dijkstrajs@npm:^1.0.1":
  version: 1.0.3
  resolution: "dijkstrajs@npm:1.0.3"
  checksum: 10/0d8429699a6d5897ed371de494ef3c7072e8052b42abbd978e686a9b8689e70af005fa3e93e93263ee3653673ff5f89c36db830a57ae7c2e088cb9c496307507
  languageName: node
  linkType: hard

"dom-serializer@npm:^2.0.0":
  version: 2.0.0
  resolution: "dom-serializer@npm:2.0.0"
  dependencies:
    domelementtype: "npm:^2.3.0"
    domhandler: "npm:^5.0.2"
    entities: "npm:^4.2.0"
  checksum: 10/e3bf9027a64450bca0a72297ecdc1e3abb7a2912268a9f3f5d33a2e29c1e2c3502c6e9f860fc6625940bfe0cfb57a44953262b9e94df76872fdfb8151097eeb3
  languageName: node
  linkType: hard

"domelementtype@npm:^2.3.0":
  version: 2.3.0
  resolution: "domelementtype@npm:2.3.0"
  checksum: 10/ee837a318ff702622f383409d1f5b25dd1024b692ef64d3096ff702e26339f8e345820f29a68bcdcea8cfee3531776b3382651232fbeae95612d6f0a75efb4f6
  languageName: node
  linkType: hard

"domhandler@npm:^5.0.2, domhandler@npm:^5.0.3":
  version: 5.0.3
  resolution: "domhandler@npm:5.0.3"
  dependencies:
    domelementtype: "npm:^2.3.0"
  checksum: 10/809b805a50a9c6884a29f38aec0a4e1b4537f40e1c861950ed47d10b049febe6b79ab72adaeeebb3cc8fc1cd33f34e97048a72a9265103426d93efafa78d3e96
  languageName: node
  linkType: hard

"domutils@npm:^3.2.1":
  version: 3.2.2
  resolution: "domutils@npm:3.2.2"
  dependencies:
    dom-serializer: "npm:^2.0.0"
    domelementtype: "npm:^2.3.0"
    domhandler: "npm:^5.0.3"
  checksum: 10/2e08842151aa406f50fe5e6d494f4ec73c2373199fa00d1f77b56ec604e566b7f226312ae35ab8160bb7f27a27c7285d574c8044779053e499282ca9198be210
  languageName: node
  linkType: hard

"dot-case@npm:^3.0.4":
  version: 3.0.4
  resolution: "dot-case@npm:3.0.4"
  dependencies:
    no-case: "npm:^3.0.4"
    tslib: "npm:^2.0.3"
  checksum: 10/a65e3519414856df0228b9f645332f974f2bf5433370f544a681122eab59e66038fc3349b4be1cdc47152779dac71a5864f1ccda2f745e767c46e9c6543b1169
  languageName: node
  linkType: hard

"dotenv@npm:^16.5.0":
  version: 16.5.0
  resolution: "dotenv@npm:16.5.0"
  checksum: 10/e68a16834f1a41cc2dfb01563bc150668ad675e6cd09191211467b5c0806b6ecd6ec438e021aa8e01cd0e72d2b70ef4302bec7cc0fe15b6955f85230b62dc8a9
  languageName: node
  linkType: hard

"dunder-proto@npm:^1.0.0, dunder-proto@npm:^1.0.1":
  version: 1.0.1
  resolution: "dunder-proto@npm:1.0.1"
  dependencies:
    call-bind-apply-helpers: "npm:^1.0.1"
    es-errors: "npm:^1.3.0"
    gopd: "npm:^1.2.0"
  checksum: 10/5add88a3d68d42d6e6130a0cac450b7c2edbe73364bbd2fc334564418569bea97c6943a8fcd70e27130bf32afc236f30982fc4905039b703f23e9e0433c29934
  languageName: node
  linkType: hard

"duplex@npm:~1.0.0":
  version: 1.0.0
  resolution: "duplex@npm:1.0.0"
  checksum: 10/26da9f48800ede09374bbe7ecdf308b49ce2d1a4cf46261fb252b30ba1bb9edb92242ca2637a9beaece34924ddf01012b1a7c68eafb1f36a14cc0000e88c7602
  languageName: node
  linkType: hard

"duplexer@npm:~0.0.3":
  version: 0.0.4
  resolution: "duplexer@npm:0.0.4"
  checksum: 10/001045d3c58f79c5ccdd13b26f57e57fe1fb4ddb09c45bf05f51a31ec33c7d66521a9b118a22cf690670bd8846cdb1c75f44cb7e0d6a56a59255716c0861e4e1
  languageName: node
  linkType: hard

"eastasianwidth@npm:^0.2.0":
  version: 0.2.0
  resolution: "eastasianwidth@npm:0.2.0"
  checksum: 10/9b1d3e1baefeaf7d70799db8774149cef33b97183a6addceeba0cf6b85ba23ee2686f302f14482006df32df75d32b17c509c143a3689627929e4a8efaf483952
  languageName: node
  linkType: hard

"ecc-jsbn@npm:~0.1.1":
  version: 0.1.2
  resolution: "ecc-jsbn@npm:0.1.2"
  dependencies:
    jsbn: "npm:~0.1.0"
    safer-buffer: "npm:^2.1.0"
  checksum: 10/d43591f2396196266e186e6d6928038cc11c76c3699a912cb9c13757060f7bbc7f17f47c4cb16168cdeacffc7965aef021142577e646fb3cb88810c15173eb57
  languageName: node
  linkType: hard

"ecdsa-sig-formatter@npm:1.0.11, ecdsa-sig-formatter@npm:^1.0.11":
  version: 1.0.11
  resolution: "ecdsa-sig-formatter@npm:1.0.11"
  dependencies:
    safe-buffer: "npm:^5.0.1"
  checksum: 10/878e1aab8a42773320bc04c6de420bee21aebd71810e40b1799880a8a1c4594bcd6adc3d4213a0fb8147d4c3f529d8f9a618d7f59ad5a9a41b142058aceda23f
  languageName: node
  linkType: hard

"ee-first@npm:1.1.1":
  version: 1.1.1
  resolution: "ee-first@npm:1.1.1"
  checksum: 10/1b4cac778d64ce3b582a7e26b218afe07e207a0f9bfe13cc7395a6d307849cfe361e65033c3251e00c27dd060cab43014c2d6b2647676135e18b77d2d05b3f4f
  languageName: node
  linkType: hard

"ejs@npm:^2.5.5":
  version: 2.7.4
  resolution: "ejs@npm:2.7.4"
  checksum: 10/39f5753d18cfc84c7351341f8d8df0f4abd07fd64be79dea6b32e54686de0e428ad35802893ba9cb7b199baaf3db310d304ef7bcaade5a6a4404d5a855a806fb
  languageName: node
  linkType: hard

"ejs@npm:^3.1.10, ejs@npm:^3.1.3":
  version: 3.1.10
  resolution: "ejs@npm:3.1.10"
  dependencies:
    jake: "npm:^10.8.5"
  bin:
    ejs: bin/cli.js
  checksum: 10/a9cb7d7cd13b7b1cd0be5c4788e44dd10d92f7285d2f65b942f33e127230c054f99a42db4d99f766d8dbc6c57e94799593ee66a14efd7c8dd70c4812bf6aa384
  languageName: node
  linkType: hard

"electron-to-chromium@npm:^1.5.149":
  version: 1.5.158
  resolution: "electron-to-chromium@npm:1.5.158"
  checksum: 10/d8de199f8506ee1d51277ccab0557a1bf3f6737d42979365ec71ab5ce7ef10b7e1ecfa6ea2fa509f64afd52c6a57c765b2d2a215e67b5c6a3593f79a87438e2b
  languageName: node
  linkType: hard

"email-addresses@npm:^5.0.0":
  version: 5.0.0
  resolution: "email-addresses@npm:5.0.0"
  checksum: 10/a7897e3b43893f1e9cc61f0e8c7cbe59c36c6cdd0b5ad7e4061f1976893260f496fd799fb78b2621e483a95fa6c7caec4a035ba320193d9540159dfcdb737004
  languageName: node
  linkType: hard

"emoji-regex@npm:^8.0.0":
  version: 8.0.0
  resolution: "emoji-regex@npm:8.0.0"
  checksum: 10/c72d67a6821be15ec11997877c437491c313d924306b8da5d87d2a2bcc2cec9903cb5b04ee1a088460501d8e5b44f10df82fdc93c444101a7610b80c8b6938e1
  languageName: node
  linkType: hard

"emoji-regex@npm:^9.2.2":
  version: 9.2.2
  resolution: "emoji-regex@npm:9.2.2"
  checksum: 10/915acf859cea7131dac1b2b5c9c8e35c4849e325a1d114c30adb8cd615970f6dca0e27f64f3a4949d7d6ed86ecd79a1c5c63f02e697513cddd7b5835c90948b8
  languageName: node
  linkType: hard

"encodeurl@npm:^2.0.0, encodeurl@npm:~2.0.0":
  version: 2.0.0
  resolution: "encodeurl@npm:2.0.0"
  checksum: 10/abf5cd51b78082cf8af7be6785813c33b6df2068ce5191a40ca8b1afe6a86f9230af9a9ce694a5ce4665955e5c1120871826df9c128a642e09c58d592e2807fe
  languageName: node
  linkType: hard

"encodeurl@npm:~1.0.2":
  version: 1.0.2
  resolution: "encodeurl@npm:1.0.2"
  checksum: 10/e50e3d508cdd9c4565ba72d2012e65038e5d71bdc9198cb125beb6237b5b1ade6c0d343998da9e170fb2eae52c1bed37d4d6d98a46ea423a0cddbed5ac3f780c
  languageName: node
  linkType: hard

"encoding@npm:^0.1.13":
  version: 0.1.13
  resolution: "encoding@npm:0.1.13"
  dependencies:
    iconv-lite: "npm:^0.6.2"
  checksum: 10/bb98632f8ffa823996e508ce6a58ffcf5856330fde839ae42c9e1f436cc3b5cc651d4aeae72222916545428e54fd0f6aa8862fd8d25bdbcc4589f1e3f3715e7f
  languageName: node
  linkType: hard

"end-of-stream@npm:^1.1.0":
  version: 1.4.4
  resolution: "end-of-stream@npm:1.4.4"
  dependencies:
    once: "npm:^1.4.0"
  checksum: 10/530a5a5a1e517e962854a31693dbb5c0b2fc40b46dad2a56a2deec656ca040631124f4795823acc68238147805f8b021abbe221f4afed5ef3c8e8efc2024908b
  languageName: node
  linkType: hard

"enhanced-resolve@npm:^5.17.1":
  version: 5.18.1
  resolution: "enhanced-resolve@npm:5.18.1"
  dependencies:
    graceful-fs: "npm:^4.2.4"
    tapable: "npm:^2.2.0"
  checksum: 10/50e81c7fe2239fba5670ebce78a34709906ed3a79274aa416434f7307b252e0b7824d76a7dd403eca795571dc6afd9a44183fc45a68475e8f2fdfbae6e92fcc3
  languageName: node
  linkType: hard

"entities@npm:^4.2.0":
  version: 4.5.0
  resolution: "entities@npm:4.5.0"
  checksum: 10/ede2a35c9bce1aeccd055a1b445d41c75a14a2bb1cd22e242f20cf04d236cdcd7f9c859eb83f76885327bfae0c25bf03303665ee1ce3d47c5927b98b0e3e3d48
  languageName: node
  linkType: hard

"entities@npm:^6.0.0":
  version: 6.0.0
  resolution: "entities@npm:6.0.0"
  checksum: 10/cf37a4aad887ba8573532346da1c78349dccd5b510a9bbddf92fe59b36b18a8b26fe619a862de4e7fd3b8addc6d5e0969261198bbeb690da87297011a61b7066
  languageName: node
  linkType: hard

"env-paths@npm:^2.2.0":
  version: 2.2.1
  resolution: "env-paths@npm:2.2.1"
  checksum: 10/65b5df55a8bab92229ab2b40dad3b387fad24613263d103a97f91c9fe43ceb21965cd3392b1ccb5d77088021e525c4e0481adb309625d0cb94ade1d1fb8dc17e
  languageName: node
  linkType: hard

"err-code@npm:^2.0.2":
  version: 2.0.3
  resolution: "err-code@npm:2.0.3"
  checksum: 10/1d20d825cdcce8d811bfbe86340f4755c02655a7feb2f13f8c880566d9d72a3f6c92c192a6867632e490d6da67b678271f46e01044996a6443e870331100dfdd
  languageName: node
  linkType: hard

"es-abstract@npm:^1.23.5, es-abstract@npm:^1.23.9":
  version: 1.23.10
  resolution: "es-abstract@npm:1.23.10"
  dependencies:
    array-buffer-byte-length: "npm:^1.0.2"
    arraybuffer.prototype.slice: "npm:^1.0.4"
    available-typed-arrays: "npm:^1.0.7"
    call-bind: "npm:^1.0.8"
    call-bound: "npm:^1.0.4"
    data-view-buffer: "npm:^1.0.2"
    data-view-byte-length: "npm:^1.0.2"
    data-view-byte-offset: "npm:^1.0.1"
    es-define-property: "npm:^1.0.1"
    es-errors: "npm:^1.3.0"
    es-object-atoms: "npm:^1.1.1"
    es-set-tostringtag: "npm:^2.1.0"
    es-to-primitive: "npm:^1.3.0"
    function.prototype.name: "npm:^1.1.8"
    get-intrinsic: "npm:^1.3.0"
    get-proto: "npm:^1.0.1"
    get-symbol-description: "npm:^1.1.0"
    globalthis: "npm:^1.0.4"
    gopd: "npm:^1.2.0"
    has-property-descriptors: "npm:^1.0.2"
    has-proto: "npm:^1.2.0"
    has-symbols: "npm:^1.1.0"
    hasown: "npm:^2.0.2"
    internal-slot: "npm:^1.1.0"
    is-array-buffer: "npm:^3.0.5"
    is-callable: "npm:^1.2.7"
    is-data-view: "npm:^1.0.2"
    is-regex: "npm:^1.2.1"
    is-shared-array-buffer: "npm:^1.0.4"
    is-string: "npm:^1.1.1"
    is-typed-array: "npm:^1.1.15"
    is-weakref: "npm:^1.1.1"
    math-intrinsics: "npm:^1.1.0"
    object-inspect: "npm:^1.13.4"
    object-keys: "npm:^1.1.1"
    object.assign: "npm:^4.1.7"
    own-keys: "npm:^1.0.1"
    regexp.prototype.flags: "npm:^1.5.4"
    safe-array-concat: "npm:^1.1.3"
    safe-push-apply: "npm:^1.0.0"
    safe-regex-test: "npm:^1.1.0"
    set-proto: "npm:^1.0.0"
    string.prototype.trim: "npm:^1.2.10"
    string.prototype.trimend: "npm:^1.0.9"
    string.prototype.trimstart: "npm:^1.0.8"
    typed-array-buffer: "npm:^1.0.3"
    typed-array-byte-length: "npm:^1.0.3"
    typed-array-byte-offset: "npm:^1.0.4"
    typed-array-length: "npm:^1.0.7"
    unbox-primitive: "npm:^1.1.0"
    which-typed-array: "npm:^1.1.19"
  checksum: 10/d3b6d560fa5eb6f3b4da4d4031d0a9455a7ec18f64ae0698831c0ce13d241f074a40e9711be3eb5f50c5844dde6070439ccfd6ce789a12bc9676e76379616d0c
  languageName: node
  linkType: hard

"es-define-property@npm:^1.0.0, es-define-property@npm:^1.0.1":
  version: 1.0.1
  resolution: "es-define-property@npm:1.0.1"
  checksum: 10/f8dc9e660d90919f11084db0a893128f3592b781ce967e4fccfb8f3106cb83e400a4032c559184ec52ee1dbd4b01e7776c7cd0b3327b1961b1a4a7008920fe78
  languageName: node
  linkType: hard

"es-errors@npm:^1.3.0":
  version: 1.3.0
  resolution: "es-errors@npm:1.3.0"
  checksum: 10/96e65d640156f91b707517e8cdc454dd7d47c32833aa3e85d79f24f9eb7ea85f39b63e36216ef0114996581969b59fe609a94e30316b08f5f4df1d44134cf8d5
  languageName: node
  linkType: hard

"es-object-atoms@npm:^1.0.0, es-object-atoms@npm:^1.1.1":
  version: 1.1.1
  resolution: "es-object-atoms@npm:1.1.1"
  dependencies:
    es-errors: "npm:^1.3.0"
  checksum: 10/54fe77de288451dae51c37bfbfe3ec86732dc3778f98f3eb3bdb4bf48063b2c0b8f9c93542656986149d08aa5be3204286e2276053d19582b76753f1a2728867
  languageName: node
  linkType: hard

"es-set-tostringtag@npm:^2.1.0":
  version: 2.1.0
  resolution: "es-set-tostringtag@npm:2.1.0"
  dependencies:
    es-errors: "npm:^1.3.0"
    get-intrinsic: "npm:^1.2.6"
    has-tostringtag: "npm:^1.0.2"
    hasown: "npm:^2.0.2"
  checksum: 10/86814bf8afbcd8966653f731415888019d4bc4aca6b6c354132a7a75bb87566751e320369654a101d23a91c87a85c79b178bcf40332839bd347aff437c4fb65f
  languageName: node
  linkType: hard

"es-to-primitive@npm:^1.3.0":
  version: 1.3.0
  resolution: "es-to-primitive@npm:1.3.0"
  dependencies:
    is-callable: "npm:^1.2.7"
    is-date-object: "npm:^1.0.5"
    is-symbol: "npm:^1.0.4"
  checksum: 10/17faf35c221aad59a16286cbf58ef6f080bf3c485dff202c490d074d8e74da07884e29b852c245d894eac84f73c58330ec956dfd6d02c0b449d75eb1012a3f9b
  languageName: node
  linkType: hard

"es6-promise@npm:^4.0.3":
  version: 4.2.8
  resolution: "es6-promise@npm:4.2.8"
  checksum: 10/b250c55523c496c43c9216c2646e58ec182b819e036fe5eb8d83fa16f044ecc6b8dcefc88ace2097be3d3c4d02b6aa8eeae1a66deeaf13e7bee905ebabb350a3
  languageName: node
  linkType: hard

"es6-promisify@npm:^5.0.0":
  version: 5.0.0
  resolution: "es6-promisify@npm:5.0.0"
  dependencies:
    es6-promise: "npm:^4.0.3"
  checksum: 10/fbed9d791598831413be84a5374eca8c24800ec71a16c1c528c43a98e2dadfb99331483d83ae6094ddb9b87e6f799a15d1553cebf756047e0865c753bc346b92
  languageName: node
  linkType: hard

"escalade@npm:^3.2.0":
  version: 3.2.0
  resolution: "escalade@npm:3.2.0"
  checksum: 10/9d7169e3965b2f9ae46971afa392f6e5a25545ea30f2e2dd99c9b0a95a3f52b5653681a84f5b2911a413ddad2d7a93d3514165072f349b5ffc59c75a899970d6
  languageName: node
  linkType: hard

"escape-html@npm:^1.0.3, escape-html@npm:~1.0.3":
  version: 1.0.3
  resolution: "escape-html@npm:1.0.3"
  checksum: 10/6213ca9ae00d0ab8bccb6d8d4e0a98e76237b2410302cf7df70aaa6591d509a2a37ce8998008cbecae8fc8ffaadf3fb0229535e6a145f3ce0b211d060decbb24
  languageName: node
  linkType: hard

"escape-string-regexp@npm:4.0.0, escape-string-regexp@npm:^4.0.0":
  version: 4.0.0
  resolution: "escape-string-regexp@npm:4.0.0"
  checksum: 10/98b48897d93060f2322108bf29db0feba7dd774be96cd069458d1453347b25ce8682ecc39859d4bca2203cc0ab19c237bcc71755eff49a0f8d90beadeeba5cc5
  languageName: node
  linkType: hard

"eslint-compat-utils@npm:^0.5.1":
  version: 0.5.1
  resolution: "eslint-compat-utils@npm:0.5.1"
  dependencies:
    semver: "npm:^7.5.4"
  peerDependencies:
    eslint: ">=6.0.0"
  checksum: 10/ac65ac1c6107cf19f63f5fc17cea361c9cb1336be7356f23dbb0fac10979974b4622e13e950be43cbf431801f2c07f7dab448573181ccf6edc0b86d5b5304511
  languageName: node
  linkType: hard

"eslint-compat-utils@npm:^0.6.4":
  version: 0.6.5
  resolution: "eslint-compat-utils@npm:0.6.5"
  dependencies:
    semver: "npm:^7.5.4"
  peerDependencies:
    eslint: ">=6.0.0"
  checksum: 10/bb95e89ed11e9a29b2e9184967292b4b52be1e10fb36c84e89de95b4a5b23764a456b3818a867e232aec685186bd954e19f165cbe50b39659f729d97f1125820
  languageName: node
  linkType: hard

"eslint-json-compat-utils@npm:^0.2.1":
  version: 0.2.1
  resolution: "eslint-json-compat-utils@npm:0.2.1"
  dependencies:
    esquery: "npm:^1.6.0"
  peerDependencies:
    eslint: "*"
    jsonc-eslint-parser: ^2.4.0
  peerDependenciesMeta:
    "@eslint/json":
      optional: true
  checksum: 10/083272c0cdbc6acd9fe9bfe939e0c76493a426400141203d7f0e76344d5874c5a88535c59300045e4c6f95baa5084eff512013f82bcd9e7426404c785e2ea55d
  languageName: node
  linkType: hard

"eslint-plugin-es-x@npm:^7.8.0":
  version: 7.8.0
  resolution: "eslint-plugin-es-x@npm:7.8.0"
  dependencies:
    "@eslint-community/eslint-utils": "npm:^4.1.2"
    "@eslint-community/regexpp": "npm:^4.11.0"
    eslint-compat-utils: "npm:^0.5.1"
  peerDependencies:
    eslint: ">=8"
  checksum: 10/1df8d52c4fadc06854ce801af05b05f2642aa2deb918fb7d37738596eabd70b7f21a22b150b78ec9104bac6a1b6b4fb796adea2364ede91b01d20964849ce5f7
  languageName: node
  linkType: hard

"eslint-plugin-jsonc@npm:^2.20.1":
  version: 2.20.1
  resolution: "eslint-plugin-jsonc@npm:2.20.1"
  dependencies:
    "@eslint-community/eslint-utils": "npm:^4.5.1"
    eslint-compat-utils: "npm:^0.6.4"
    eslint-json-compat-utils: "npm:^0.2.1"
    espree: "npm:^9.6.1 || ^10.3.0"
    graphemer: "npm:^1.4.0"
    jsonc-eslint-parser: "npm:^2.4.0"
    natural-compare: "npm:^1.4.0"
    synckit: "npm:^0.6.2 || ^0.7.3 || ^0.11.5"
  peerDependencies:
    eslint: ">=6.0.0"
  checksum: 10/fc02f74ca20e0003adbd2b2a0b356073814e1d6393f8f3067a15aaf8e7b33b482e4a21bbe188463fef676f0dbb83f3c9e3809d897bdd8be4b016099ee54232fd
  languageName: node
  linkType: hard

"eslint-plugin-n@npm:^17.18.0":
  version: 17.18.0
  resolution: "eslint-plugin-n@npm:17.18.0"
  dependencies:
    "@eslint-community/eslint-utils": "npm:^4.5.0"
    enhanced-resolve: "npm:^5.17.1"
    eslint-plugin-es-x: "npm:^7.8.0"
    get-tsconfig: "npm:^4.8.1"
    globals: "npm:^15.11.0"
    ignore: "npm:^5.3.2"
    minimatch: "npm:^9.0.5"
    semver: "npm:^7.6.3"
  peerDependencies:
    eslint: ">=8.23.0"
  checksum: 10/ed2a1c25b51a13174a3f3773a718f1bbcafdf0434fdb37cc109e6609bb72f59f48f412d484b9afd757b2ee4de36d9283f9e388627ca830a06b022d741d4203d2
  languageName: node
  linkType: hard

"eslint-plugin-security@npm:^3.0.1":
  version: 3.0.1
  resolution: "eslint-plugin-security@npm:3.0.1"
  dependencies:
    safe-regex: "npm:^2.1.1"
  checksum: 10/5a7eb9a9d499addad93e9a650f503b2bdc23e8ab8222a0330e216726ffcc0e154405d23c8c523ff987e894cb9c8358da883c1dd22e21423e4368cd13de14930c
  languageName: node
  linkType: hard

"eslint-scope@npm:5.1.1":
  version: 5.1.1
  resolution: "eslint-scope@npm:5.1.1"
  dependencies:
    esrecurse: "npm:^4.3.0"
    estraverse: "npm:^4.1.1"
  checksum: 10/c541ef384c92eb5c999b7d3443d80195fcafb3da335500946f6db76539b87d5826c8f2e1d23bf6afc3154ba8cd7c8e566f8dc00f1eea25fdf3afc8fb9c87b238
  languageName: node
  linkType: hard

"eslint-scope@npm:^8.3.0":
  version: 8.3.0
  resolution: "eslint-scope@npm:8.3.0"
  dependencies:
    esrecurse: "npm:^4.3.0"
    estraverse: "npm:^5.2.0"
  checksum: 10/ee1ff009e949423639a8b53453c0cb189967d9142c5d94dc3752bed9880140a0760007148ac6b0bd03557d70ede9cd7c3b1e66f9a7f3427b2dbeca2a5be22c91
  languageName: node
  linkType: hard

"eslint-visitor-keys@npm:^2.1.0":
  version: 2.1.0
  resolution: "eslint-visitor-keys@npm:2.1.0"
  checksum: 10/db4547eef5039122d518fa307e938ceb8589da5f6e8f5222efaf14dd62f748ce82e2d2becd3ff9412a50350b726bda95dbea8515a471074547daefa58aee8735
  languageName: node
  linkType: hard

"eslint-visitor-keys@npm:^3.0.0, eslint-visitor-keys@npm:^3.4.1, eslint-visitor-keys@npm:^3.4.3":
  version: 3.4.3
  resolution: "eslint-visitor-keys@npm:3.4.3"
  checksum: 10/3f357c554a9ea794b094a09bd4187e5eacd1bc0d0653c3adeb87962c548e6a1ab8f982b86963ae1337f5d976004146536dcee5d0e2806665b193fbfbf1a9231b
  languageName: node
  linkType: hard

"eslint-visitor-keys@npm:^4.2.0":
  version: 4.2.0
  resolution: "eslint-visitor-keys@npm:4.2.0"
  checksum: 10/9651b3356b01760e586b4c631c5268c0e1a85236e3292bf754f0472f465bf9a856c0ddc261fceace155334118c0151778effafbab981413dbf9288349343fa25
  languageName: node
  linkType: hard

"eslint@npm:^9.27.0":
  version: 9.27.0
  resolution: "eslint@npm:9.27.0"
  dependencies:
    "@eslint-community/eslint-utils": "npm:^4.2.0"
    "@eslint-community/regexpp": "npm:^4.12.1"
    "@eslint/config-array": "npm:^0.20.0"
    "@eslint/config-helpers": "npm:^0.2.1"
    "@eslint/core": "npm:^0.14.0"
    "@eslint/eslintrc": "npm:^3.3.1"
    "@eslint/js": "npm:9.27.0"
    "@eslint/plugin-kit": "npm:^0.3.1"
    "@humanfs/node": "npm:^0.16.6"
    "@humanwhocodes/module-importer": "npm:^1.0.1"
    "@humanwhocodes/retry": "npm:^0.4.2"
    "@types/estree": "npm:^1.0.6"
    "@types/json-schema": "npm:^7.0.15"
    ajv: "npm:^6.12.4"
    chalk: "npm:^4.0.0"
    cross-spawn: "npm:^7.0.6"
    debug: "npm:^4.3.2"
    escape-string-regexp: "npm:^4.0.0"
    eslint-scope: "npm:^8.3.0"
    eslint-visitor-keys: "npm:^4.2.0"
    espree: "npm:^10.3.0"
    esquery: "npm:^1.5.0"
    esutils: "npm:^2.0.2"
    fast-deep-equal: "npm:^3.1.3"
    file-entry-cache: "npm:^8.0.0"
    find-up: "npm:^5.0.0"
    glob-parent: "npm:^6.0.2"
    ignore: "npm:^5.2.0"
    imurmurhash: "npm:^0.1.4"
    is-glob: "npm:^4.0.0"
    json-stable-stringify-without-jsonify: "npm:^1.0.1"
    lodash.merge: "npm:^4.6.2"
    minimatch: "npm:^3.1.2"
    natural-compare: "npm:^1.4.0"
    optionator: "npm:^0.9.3"
  peerDependencies:
    jiti: "*"
  peerDependenciesMeta:
    jiti:
      optional: true
  bin:
    eslint: bin/eslint.js
  checksum: 10/75f02b851c6f8534d1289de1bd957637a56725754bea03a0a710d6740a036aca81d5e600557633fca7ab774275aa94044ca05772f88c4f464cd42834eff37145
  languageName: node
  linkType: hard

"espree@npm:^10.0.1, espree@npm:^10.3.0, espree@npm:^9.6.1 || ^10.3.0":
  version: 10.3.0
  resolution: "espree@npm:10.3.0"
  dependencies:
    acorn: "npm:^8.14.0"
    acorn-jsx: "npm:^5.3.2"
    eslint-visitor-keys: "npm:^4.2.0"
  checksum: 10/3412d44d4204c9e29d6b5dd0277400cfa0cd68495dc09eae1b9ce79d0c8985c1c5cc09cb9ba32a1cd963f48a49b0c46bdb7736afe395a300aa6bb1c0d86837e8
  languageName: node
  linkType: hard

"espree@npm:^9.0.0":
  version: 9.6.1
  resolution: "espree@npm:9.6.1"
  dependencies:
    acorn: "npm:^8.9.0"
    acorn-jsx: "npm:^5.3.2"
    eslint-visitor-keys: "npm:^3.4.1"
  checksum: 10/255ab260f0d711a54096bdeda93adff0eadf02a6f9b92f02b323e83a2b7fc258797919437ad331efec3930475feb0142c5ecaaf3cdab4befebd336d47d3f3134
  languageName: node
  linkType: hard

"esprima@npm:~ 1.0.2":
  version: 1.0.4
  resolution: "esprima@npm:1.0.4"
  bin:
    esparse: ./bin/esparse.js
    esvalidate: ./bin/esvalidate.js
  checksum: 10/968f923484e6dd9d6f8dd1e737efb5d85c1b1580cd1e43bbf5db4fc92a4973cf444dbf8c31d01abac4a93ff751acaaaeb74a0daef0b7dc0bf785ae6f0ebf8a95
  languageName: node
  linkType: hard

"esquery@npm:^1.5.0, esquery@npm:^1.6.0":
  version: 1.6.0
  resolution: "esquery@npm:1.6.0"
  dependencies:
    estraverse: "npm:^5.1.0"
  checksum: 10/c587fb8ec9ed83f2b1bc97cf2f6854cc30bf784a79d62ba08c6e358bf22280d69aee12827521cf38e69ae9761d23fb7fde593ce315610f85655c139d99b05e5a
  languageName: node
  linkType: hard

"esrecurse@npm:^4.3.0":
  version: 4.3.0
  resolution: "esrecurse@npm:4.3.0"
  dependencies:
    estraverse: "npm:^5.2.0"
  checksum: 10/44ffcd89e714ea6b30143e7f119b104fc4d75e77ee913f34d59076b40ef2d21967f84e019f84e1fd0465b42cdbf725db449f232b5e47f29df29ed76194db8e16
  languageName: node
  linkType: hard

"estraverse@npm:^4.1.1":
  version: 4.3.0
  resolution: "estraverse@npm:4.3.0"
  checksum: 10/3f67ad02b6dbfaddd9ea459cf2b6ef4ecff9a6082a7af9d22e445b9abc082ad9ca47e1825557b293fcdae477f4714e561123e30bb6a5b2f184fb2bad4a9497eb
  languageName: node
  linkType: hard

"estraverse@npm:^5.1.0, estraverse@npm:^5.2.0":
  version: 5.3.0
  resolution: "estraverse@npm:5.3.0"
  checksum: 10/37cbe6e9a68014d34dbdc039f90d0baf72436809d02edffcc06ba3c2a12eb298048f877511353b130153e532aac8d68ba78430c0dd2f44806ebc7c014b01585e
  languageName: node
  linkType: hard

"esutils@npm:^2.0.2":
  version: 2.0.3
  resolution: "esutils@npm:2.0.3"
  checksum: 10/b23acd24791db11d8f65be5ea58fd9a6ce2df5120ae2da65c16cfc5331ff59d5ac4ef50af66cd4bde238881503ec839928a0135b99a036a9cdfa22d17fd56cdb
  languageName: node
  linkType: hard

"etag@npm:^1.8.1, etag@npm:~1.8.1":
  version: 1.8.1
  resolution: "etag@npm:1.8.1"
  checksum: 10/571aeb3dbe0f2bbd4e4fadbdb44f325fc75335cd5f6f6b6a091e6a06a9f25ed5392f0863c5442acb0646787446e816f13cbfc6edce5b07658541dff573cab1ff
  languageName: node
  linkType: hard

"event-target-shim@npm:^5.0.0":
  version: 5.0.1
  resolution: "event-target-shim@npm:5.0.1"
  checksum: 10/49ff46c3a7facbad3decb31f597063e761785d7fdb3920d4989d7b08c97a61c2f51183e2f3a03130c9088df88d4b489b1b79ab632219901f184f85158508f4c8
  languageName: node
  linkType: hard

"eventemitter2@npm:^6.4.9":
  version: 6.4.9
  resolution: "eventemitter2@npm:6.4.9"
  checksum: 10/b829b1c6b11e15926b635092b5ad62b4463d1c928859831dcae606e988cf41893059e3541f5a8209d21d2f15314422ddd4d84d20830b4bf44978608d15b06b08
  languageName: node
  linkType: hard

"eventemitter3@npm:^4.0.4":
  version: 4.0.7
  resolution: "eventemitter3@npm:4.0.7"
  checksum: 10/8030029382404942c01d0037079f1b1bc8fed524b5849c237b80549b01e2fc49709e1d0c557fa65ca4498fc9e24cff1475ef7b855121fcc15f9d61f93e282346
  languageName: node
  linkType: hard

"eventemitter3@npm:^5.0.1":
  version: 5.0.1
  resolution: "eventemitter3@npm:5.0.1"
  checksum: 10/ac6423ec31124629c84c7077eed1e6987f6d66c31cf43c6fcbf6c87791d56317ce808d9ead483652436df171b526fc7220eccdc9f3225df334e81582c3cf7dd5
  languageName: node
  linkType: hard

"events@npm:^3.3.0":
  version: 3.3.0
  resolution: "events@npm:3.3.0"
  checksum: 10/a3d47e285e28d324d7180f1e493961a2bbb4cad6412090e4dec114f4db1f5b560c7696ee8e758f55e23913ede856e3689cd3aa9ae13c56b5d8314cd3b3ddd1be
  languageName: node
  linkType: hard

"execa@npm:^4.0.0":
  version: 4.1.0
  resolution: "execa@npm:4.1.0"
  dependencies:
    cross-spawn: "npm:^7.0.0"
    get-stream: "npm:^5.0.0"
    human-signals: "npm:^1.1.1"
    is-stream: "npm:^2.0.0"
    merge-stream: "npm:^2.0.0"
    npm-run-path: "npm:^4.0.0"
    onetime: "npm:^5.1.0"
    signal-exit: "npm:^3.0.2"
    strip-final-newline: "npm:^2.0.0"
  checksum: 10/ed58e41fe424797f3d837c8fb622548eeb72fa03324f2676af95f806568904eb55f196127a097f87d4517cab524c169ece13e6c9e201867de57b089584864b8f
  languageName: node
  linkType: hard

"exponential-backoff@npm:^3.1.1":
  version: 3.1.2
  resolution: "exponential-backoff@npm:3.1.2"
  checksum: 10/ca2f01f1aa4dafd3f3917bd531ab5be08c6f5f4b2389d2e974f903de3cbeb50b9633374353516b6afd70905775e33aba11afab1232d3acf0aa2963b98a611c51
  languageName: node
  linkType: hard

"express@npm:4.21.2":
  version: 4.21.2
  resolution: "express@npm:4.21.2"
  dependencies:
    accepts: "npm:~1.3.8"
    array-flatten: "npm:1.1.1"
    body-parser: "npm:1.20.3"
    content-disposition: "npm:0.5.4"
    content-type: "npm:~1.0.4"
    cookie: "npm:0.7.1"
    cookie-signature: "npm:1.0.6"
    debug: "npm:2.6.9"
    depd: "npm:2.0.0"
    encodeurl: "npm:~2.0.0"
    escape-html: "npm:~1.0.3"
    etag: "npm:~1.8.1"
    finalhandler: "npm:1.3.1"
    fresh: "npm:0.5.2"
    http-errors: "npm:2.0.0"
    merge-descriptors: "npm:1.0.3"
    methods: "npm:~1.1.2"
    on-finished: "npm:2.4.1"
    parseurl: "npm:~1.3.3"
    path-to-regexp: "npm:0.1.12"
    proxy-addr: "npm:~2.0.7"
    qs: "npm:6.13.0"
    range-parser: "npm:~1.2.1"
    safe-buffer: "npm:5.2.1"
    send: "npm:0.19.0"
    serve-static: "npm:1.16.2"
    setprototypeof: "npm:1.2.0"
    statuses: "npm:2.0.1"
    type-is: "npm:~1.6.18"
    utils-merge: "npm:1.0.1"
    vary: "npm:~1.1.2"
  checksum: 10/34571c442fc8c9f2c4b442d2faa10ea1175cf8559237fc6a278f5ce6254a8ffdbeb9a15d99f77c1a9f2926ab183e3b7ba560e3261f1ad4149799e3412ab66bd1
  languageName: node
  linkType: hard

"express@npm:^5.1.0":
  version: 5.1.0
  resolution: "express@npm:5.1.0"
  dependencies:
    accepts: "npm:^2.0.0"
    body-parser: "npm:^2.2.0"
    content-disposition: "npm:^1.0.0"
    content-type: "npm:^1.0.5"
    cookie: "npm:^0.7.1"
    cookie-signature: "npm:^1.2.1"
    debug: "npm:^4.4.0"
    encodeurl: "npm:^2.0.0"
    escape-html: "npm:^1.0.3"
    etag: "npm:^1.8.1"
    finalhandler: "npm:^2.1.0"
    fresh: "npm:^2.0.0"
    http-errors: "npm:^2.0.0"
    merge-descriptors: "npm:^2.0.0"
    mime-types: "npm:^3.0.0"
    on-finished: "npm:^2.4.1"
    once: "npm:^1.4.0"
    parseurl: "npm:^1.3.3"
    proxy-addr: "npm:^2.0.7"
    qs: "npm:^6.14.0"
    range-parser: "npm:^1.2.1"
    router: "npm:^2.2.0"
    send: "npm:^1.1.0"
    serve-static: "npm:^2.2.0"
    statuses: "npm:^2.0.1"
    type-is: "npm:^2.0.1"
    vary: "npm:^1.1.2"
  checksum: 10/6dba00bbdf308f43a84ed3f07a7e9870d5208f2a0b8f60f39459dda089750379747819863fad250849d3c9163833f33f94ce69d73938df31e0c5a430800d7e56
  languageName: node
  linkType: hard

"extend@npm:^3.0.2, extend@npm:~3.0.2":
  version: 3.0.2
  resolution: "extend@npm:3.0.2"
  checksum: 10/59e89e2dc798ec0f54b36d82f32a27d5f6472c53974f61ca098db5d4648430b725387b53449a34df38fd0392045434426b012f302b3cc049a6500ccf82877e4e
  languageName: node
  linkType: hard

"extsprintf@npm:1.3.0":
  version: 1.3.0
  resolution: "extsprintf@npm:1.3.0"
  checksum: 10/26967d6c7ecbfb5bc5b7a6c43503dc5fafd9454802037e9fa1665e41f615da4ff5918bd6cb871a3beabed01a31eca1ccd0bdfb41231f50ad50d405a430f78377
  languageName: node
  linkType: hard

"extsprintf@npm:^1.2.0":
  version: 1.4.1
  resolution: "extsprintf@npm:1.4.1"
  checksum: 10/bfd6d55f3c0c04d826fe0213264b383c03f32825af6b1ff777f3f2dc49467e599361993568d75b7b19a8ea1bb08c8e7cd8c3d87d179ced91bb0dcf81ca6938e0
  languageName: node
  linkType: hard

"eyes@npm:^0.1.8":
  version: 0.1.8
  resolution: "eyes@npm:0.1.8"
  checksum: 10/58480c1f4c8e80ae9d4147afa0e0cc3403e5a3d1fa9e0c17dd8418f87273762c40ab035919ed407f6ed0992086495b93ff7163eb2a1027f58ae70e3c847d6c08
  languageName: node
  linkType: hard

"fast-deep-equal@npm:^3.1.1, fast-deep-equal@npm:^3.1.3":
  version: 3.1.3
  resolution: "fast-deep-equal@npm:3.1.3"
  checksum: 10/e21a9d8d84f53493b6aa15efc9cfd53dd5b714a1f23f67fb5dc8f574af80df889b3bce25dc081887c6d25457cce704e636395333abad896ccdec03abaf1f3f9d
  languageName: node
  linkType: hard

"fast-json-patch@npm:^3.1.1":
  version: 3.1.1
  resolution: "fast-json-patch@npm:3.1.1"
  checksum: 10/3e56304e1c95ad1862a50e5b3f557a74c65c0ff2ba5b15caab983b43e70e86ddbc5bc887e9f7064f0aacfd0f0435a29ab2f000fe463379e72b906486345e6671
  languageName: node
  linkType: hard

"fast-json-stable-stringify@npm:^2.0.0":
  version: 2.1.0
  resolution: "fast-json-stable-stringify@npm:2.1.0"
  checksum: 10/2c20055c1fa43c922428f16ca8bb29f2807de63e5c851f665f7ac9790176c01c3b40335257736b299764a8d383388dabc73c8083b8e1bc3d99f0a941444ec60e
  languageName: node
  linkType: hard

"fast-levenshtein@npm:^2.0.6":
  version: 2.0.6
  resolution: "fast-levenshtein@npm:2.0.6"
  checksum: 10/eb7e220ecf2bab5159d157350b81d01f75726a4382f5a9266f42b9150c4523b9795f7f5d9fbbbeaeac09a441b2369f05ee02db48ea938584205530fe5693cfe1
  languageName: node
  linkType: hard

"fast-safe-stringify@npm:^2.0.6":
  version: 2.1.1
  resolution: "fast-safe-stringify@npm:2.1.1"
  checksum: 10/dc1f063c2c6ac9533aee14d406441f86783a8984b2ca09b19c2fe281f9ff59d315298bc7bc22fd1f83d26fe19ef2f20e2ddb68e96b15040292e555c5ced0c1e4
  languageName: node
  linkType: hard

"fast-xml-parser@npm:4.4.1":
  version: 4.4.1
  resolution: "fast-xml-parser@npm:4.4.1"
  dependencies:
    strnum: "npm:^1.0.5"
  bin:
    fxparser: src/cli/cli.js
  checksum: 10/0c05ab8703630d8c857fafadbd78d0020d3a8e54310c3842179cd4a0d9d97e96d209ce885e91241f4aa9dd8dfc2fd924a682741a423d65153cad34da2032ec44
  languageName: node
  linkType: hard

"fdir@npm:^6.4.4":
  version: 6.4.4
  resolution: "fdir@npm:6.4.4"
  peerDependencies:
    picomatch: ^3 || ^4
  peerDependenciesMeta:
    picomatch:
      optional: true
  checksum: 10/d0000d6b790059b35f4ed19acc8847a66452e0bc68b28766c929ffd523e5ec2083811fc8a545e4a1d4945ce70e887b3a610c145c681073b506143ae3076342ed
  languageName: node
  linkType: hard

"file-entry-cache@npm:^8.0.0":
  version: 8.0.0
  resolution: "file-entry-cache@npm:8.0.0"
  dependencies:
    flat-cache: "npm:^4.0.0"
  checksum: 10/********************************************************************************************************************************
  languageName: node
  linkType: hard

"file-uri-to-path@npm:1.0.0":
  version: 1.0.0
  resolution: "file-uri-to-path@npm:1.0.0"
  checksum: 10/b648580bdd893a008c92c7ecc96c3ee57a5e7b6c4c18a9a09b44fb5d36d79146f8e442578bc0e173dc027adf3987e254ba1dfd6e3ec998b7c282873010502144
  languageName: node
  linkType: hard

"filelist@npm:^1.0.4":
  version: 1.0.4
  resolution: "filelist@npm:1.0.4"
  dependencies:
    minimatch: "npm:^5.0.1"
  checksum: 10/4b436fa944b1508b95cffdfc8176ae6947b92825483639ef1b9a89b27d82f3f8aa22b21eed471993f92709b431670d4e015b39c087d435a61e1bb04564cf51de
  languageName: node
  linkType: hard

"filter-obj@npm:^1.1.0":
  version: 1.1.0
  resolution: "filter-obj@npm:1.1.0"
  checksum: 10/9d681939eec2b4b129cb4f307b7e93d954a0657421d4e5357d86093b26d3f4f570909ed43717dcfd62428b3cf8cddd9841b35f9d40d12ac62cfabaa677942593
  languageName: node
  linkType: hard

"finalhandler@npm:1.3.1":
  version: 1.3.1
  resolution: "finalhandler@npm:1.3.1"
  dependencies:
    debug: "npm:2.6.9"
    encodeurl: "npm:~2.0.0"
    escape-html: "npm:~1.0.3"
    on-finished: "npm:2.4.1"
    parseurl: "npm:~1.3.3"
    statuses: "npm:2.0.1"
    unpipe: "npm:~1.0.0"
  checksum: 10/4babe72969b7373b5842bc9f75c3a641a4d0f8eb53af6b89fa714d4460ce03fb92b28de751d12ba415e96e7e02870c436d67412120555e2b382640535697305b
  languageName: node
  linkType: hard

"finalhandler@npm:^2.1.0":
  version: 2.1.0
  resolution: "finalhandler@npm:2.1.0"
  dependencies:
    debug: "npm:^4.4.0"
    encodeurl: "npm:^2.0.0"
    escape-html: "npm:^1.0.3"
    on-finished: "npm:^2.4.1"
    parseurl: "npm:^1.3.3"
    statuses: "npm:^2.0.1"
  checksum: 10/b2bd68c310e2c463df0ab747ab05f8defbc540b8c3f2442f86e7d084ac8acbc31f8cae079931b7f5a406521501941e3395e963de848a0aaf45dd414adeb5ff4e
  languageName: node
  linkType: hard

"find-up@npm:^4.1.0":
  version: 4.1.0
  resolution: "find-up@npm:4.1.0"
  dependencies:
    locate-path: "npm:^5.0.0"
    path-exists: "npm:^4.0.0"
  checksum: 10/4c172680e8f8c1f78839486e14a43ef82e9decd0e74145f40707cc42e7420506d5ec92d9a11c22bd2c48fb0c384ea05dd30e10dd152fefeec6f2f75282a8b844
  languageName: node
  linkType: hard

"find-up@npm:^5.0.0":
  version: 5.0.0
  resolution: "find-up@npm:5.0.0"
  dependencies:
    locate-path: "npm:^6.0.0"
    path-exists: "npm:^4.0.0"
  checksum: 10/07955e357348f34660bde7920783204ff5a26ac2cafcaa28bace494027158a97b9f56faaf2d89a6106211a8174db650dd9f503f9c0d526b1202d5554a00b9095
  languageName: node
  linkType: hard

"flat-cache@npm:^4.0.0":
  version: 4.0.1
  resolution: "flat-cache@npm:4.0.1"
  dependencies:
    flatted: "npm:^3.2.9"
    keyv: "npm:^4.5.4"
  checksum: 10/58ce851d9045fffc7871ce2bd718bc485ad7e777bf748c054904b87c351ff1080c2c11da00788d78738bfb51b71e4d5ea12d13b98eb36e3358851ffe495b62dc
  languageName: node
  linkType: hard

"flat@npm:5.0.2":
  version: 5.0.2
  resolution: "flat@npm:5.0.2"
  bin:
    flat: cli.js
  checksum: 10/********************************************************************************************************************************
  languageName: node
  linkType: hard

"flatted@npm:^3.2.9":
  version: 3.3.3
  resolution: "flatted@npm:3.3.3"
  checksum: 10/********************************************************************************************************************************
  languageName: node
  linkType: hard

"follow-redirects@npm:^1.15.6":
  version: 1.15.9
  resolution: "follow-redirects@npm:1.15.9"
  peerDependenciesMeta:
    debug:
      optional: true
  checksum: 10/e3ab42d1097e90d28b913903841e6779eb969b62a64706a3eb983e894a5db000fbd89296f45f08885a0e54cd558ef62e81be1165da9be25a6c44920da10f424c
  languageName: node
  linkType: hard

"for-each@npm:^0.3.3, for-each@npm:^0.3.5":
  version: 0.3.5
  resolution: "for-each@npm:0.3.5"
  dependencies:
    is-callable: "npm:^1.2.7"
  checksum: 10/330cc2439f85c94f4609de3ee1d32c5693ae15cdd7fe3d112c4fd9efd4ce7143f2c64ef6c2c9e0cfdb0058437f33ef05b5bdae5b98fcc903fb2143fbaf0fea0f
  languageName: node
  linkType: hard

"foreground-child@npm:^3.1.0":
  version: 3.3.1
  resolution: "foreground-child@npm:3.3.1"
  dependencies:
    cross-spawn: "npm:^7.0.6"
    signal-exit: "npm:^4.0.1"
  checksum: 10/427b33f997a98073c0424e5c07169264a62cda806d8d2ded159b5b903fdfc8f0a1457e06b5fc35506497acb3f1e353f025edee796300209ac6231e80edece835
  languageName: node
  linkType: hard

"forever-agent@npm:~0.6.1":
  version: 0.6.1
  resolution: "forever-agent@npm:0.6.1"
  checksum: 10/c1e1644d5e074ac063ecbc3fb8582013ef91fff0e3fa41e76db23d2f62bc6d9677aac86db950917deed4fe1fdd772df780cfaa352075f23deec9c015313afb97
  languageName: node
  linkType: hard

"form-data@npm:^4.0.0, form-data@npm:^4.0.2":
  version: 4.0.2
  resolution: "form-data@npm:4.0.2"
  dependencies:
    asynckit: "npm:^0.4.0"
    combined-stream: "npm:^1.0.8"
    es-set-tostringtag: "npm:^2.1.0"
    mime-types: "npm:^2.1.12"
  checksum: 10/82c65b426af4a40090e517a1bc9057f76970b4c6043e37aa49859c447d88553e77d4cc5626395079a53d2b0889ba5f2a49f3900db3ad3f3f1bf76613532572fb
  languageName: node
  linkType: hard

"form-data@npm:~2.3.2":
  version: 2.3.3
  resolution: "form-data@npm:2.3.3"
  dependencies:
    asynckit: "npm:^0.4.0"
    combined-stream: "npm:^1.0.6"
    mime-types: "npm:^2.1.12"
  checksum: 10/1b6f3ccbf4540e535887b42218a2431a3f6cfdea320119c2affa2a7a374ad8fdd1e60166fc865181f45d49b1684c3e90e7b2190d3fe016692957afb9cf0d0d02
  languageName: node
  linkType: hard

"forwarded@npm:0.2.0":
  version: 0.2.0
  resolution: "forwarded@npm:0.2.0"
  checksum: 10/29ba9fd347117144e97cbb8852baae5e8b2acb7d1b591ef85695ed96f5b933b1804a7fac4a15dd09ca7ac7d0cdc104410e8102aae2dd3faa570a797ba07adb81
  languageName: node
  linkType: hard

"franc@npm:5.0.0":
  version: 5.0.0
  resolution: "franc@npm:5.0.0"
  dependencies:
    trigram-utils: "npm:^1.0.0"
  checksum: 10/5beaf301b76bb6553f92bc43ae91167a2edc8121c69d7322c700e5a554e419e6f19dc49624a99587df6d076977a0d5a5479a86552c59b48ae25d8ffeaedf2b0c
  languageName: node
  linkType: hard

"fresh@npm:0.5.2":
  version: 0.5.2
  resolution: "fresh@npm:0.5.2"
  checksum: 10/64c88e489b5d08e2f29664eb3c79c705ff9a8eb15d3e597198ef76546d4ade295897a44abb0abd2700e7ef784b2e3cbf1161e4fbf16f59129193fd1030d16da1
  languageName: node
  linkType: hard

"fresh@npm:^2.0.0":
  version: 2.0.0
  resolution: "fresh@npm:2.0.0"
  checksum: 10/44e1468488363074641991c1340d2a10c5a6f6d7c353d89fd161c49d120c58ebf9890720f7584f509058385836e3ce50ddb60e9f017315a4ba8c6c3461813bfc
  languageName: node
  linkType: hard

"fs-minipass@npm:^3.0.0":
  version: 3.0.3
  resolution: "fs-minipass@npm:3.0.3"
  dependencies:
    minipass: "npm:^7.0.3"
  checksum: 10/af143246cf6884fe26fa281621d45cfe111d34b30535a475bfa38dafe343dadb466c047a924ffc7d6b7b18265df4110224ce3803806dbb07173bf2087b648d7f
  languageName: node
  linkType: hard

"fs.realpath@npm:^1.0.0":
  version: 1.0.0
  resolution: "fs.realpath@npm:1.0.0"
  checksum: 10/e703107c28e362d8d7b910bbcbfd371e640a3bb45ae157a362b5952c0030c0b6d4981140ec319b347bce7adc025dd7813da1ff908a945ac214d64f5402a51b96
  languageName: node
  linkType: hard

"function-bind@npm:^1.1.2":
  version: 1.1.2
  resolution: "function-bind@npm:1.1.2"
  checksum: 10/185e20d20f10c8d661d59aac0f3b63b31132d492e1b11fcc2a93cb2c47257ebaee7407c38513efd2b35cafdf972d9beb2ea4593c1e0f3bf8f2744836928d7454
  languageName: node
  linkType: hard

"function.prototype.name@npm:^1.1.6, function.prototype.name@npm:^1.1.8":
  version: 1.1.8
  resolution: "function.prototype.name@npm:1.1.8"
  dependencies:
    call-bind: "npm:^1.0.8"
    call-bound: "npm:^1.0.3"
    define-properties: "npm:^1.2.1"
    functions-have-names: "npm:^1.2.3"
    hasown: "npm:^2.0.2"
    is-callable: "npm:^1.2.7"
  checksum: 10/25b9e5bea936732a6f0c0c08db58cc0d609ac1ed458c6a07ead46b32e7b9bf3fe5887796c3f83d35994efbc4fdde81c08ac64135b2c399b8f2113968d44082bc
  languageName: node
  linkType: hard

"functions-have-names@npm:^1.2.3":
  version: 1.2.3
  resolution: "functions-have-names@npm:1.2.3"
  checksum: 10/0ddfd3ed1066a55984aaecebf5419fbd9344a5c38dd120ffb0739fac4496758dcf371297440528b115e4367fc46e3abc86a2cc0ff44612181b175ae967a11a05
  languageName: node
  linkType: hard

"gaxios@npm:^6.0.0, gaxios@npm:^6.0.3, gaxios@npm:^6.1.1":
  version: 6.7.1
  resolution: "gaxios@npm:6.7.1"
  dependencies:
    extend: "npm:^3.0.2"
    https-proxy-agent: "npm:^7.0.1"
    is-stream: "npm:^2.0.0"
    node-fetch: "npm:^2.6.9"
    uuid: "npm:^9.0.1"
  checksum: 10/c85599162208884eadee91215ebbfa1faa412551df4044626cb561300e15193726e8f23d63b486533e066dadad130f58ed872a23acab455238d8d48b531a0695
  languageName: node
  linkType: hard

"gcp-metadata@npm:^6.1.0":
  version: 6.1.1
  resolution: "gcp-metadata@npm:6.1.1"
  dependencies:
    gaxios: "npm:^6.1.1"
    google-logging-utils: "npm:^0.0.2"
    json-bigint: "npm:^1.0.0"
  checksum: 10/f6b1a604d5888db261a9a3ca0a494338b5cdbf815efa393aa38051d814387545bbfd9f25874bf8ea36441f2052625add42658e8973648e53f9b90f151b4bad1b
  languageName: node
  linkType: hard

"gensync@npm:^1.0.0-beta.2":
  version: 1.0.0-beta.2
  resolution: "gensync@npm:1.0.0-beta.2"
  checksum: 10/17d8333460204fbf1f9160d067e1e77f908a5447febb49424b8ab043026049835c9ef3974445c57dbd39161f4d2b04356d7de12b2eecaa27a7a7ea7d871cbedd
  languageName: node
  linkType: hard

"geolib@npm:^3.3.4":
  version: 3.3.4
  resolution: "geolib@npm:3.3.4"
  checksum: 10/a40ca6a84afc0a511509b34839839923ea9251d837cd6b2e56ebc968911ba8a10209ea604878f52606efe41204eb20d1246b2e545ff1c1027f0913c5e4a97907
  languageName: node
  linkType: hard

"get-caller-file@npm:^2.0.1":
  version: 2.0.5
  resolution: "get-caller-file@npm:2.0.5"
  checksum: 10/b9769a836d2a98c3ee734a88ba712e62703f1df31b94b784762c433c27a386dd6029ff55c2a920c392e33657d80191edbf18c61487e198844844516f843496b9
  languageName: node
  linkType: hard

"get-intrinsic@npm:^1.2.4, get-intrinsic@npm:^1.2.5, get-intrinsic@npm:^1.2.6, get-intrinsic@npm:^1.2.7, get-intrinsic@npm:^1.3.0":
  version: 1.3.0
  resolution: "get-intrinsic@npm:1.3.0"
  dependencies:
    call-bind-apply-helpers: "npm:^1.0.2"
    es-define-property: "npm:^1.0.1"
    es-errors: "npm:^1.3.0"
    es-object-atoms: "npm:^1.1.1"
    function-bind: "npm:^1.1.2"
    get-proto: "npm:^1.0.1"
    gopd: "npm:^1.2.0"
    has-symbols: "npm:^1.1.0"
    hasown: "npm:^2.0.2"
    math-intrinsics: "npm:^1.1.0"
  checksum: 10/6e9dd920ff054147b6f44cb98104330e87caafae051b6d37b13384a45ba15e71af33c3baeac7cb630a0aaa23142718dcf25b45cfdd86c184c5dcb4e56d953a10
  languageName: node
  linkType: hard

"get-proto@npm:^1.0.0, get-proto@npm:^1.0.1":
  version: 1.0.1
  resolution: "get-proto@npm:1.0.1"
  dependencies:
    dunder-proto: "npm:^1.0.1"
    es-object-atoms: "npm:^1.0.0"
  checksum: 10/4fc96afdb58ced9a67558698b91433e6b037aaa6f1493af77498d7c85b141382cf223c0e5946f334fb328ee85dfe6edd06d218eaf09556f4bc4ec6005d7f5f7b
  languageName: node
  linkType: hard

"get-stream@npm:^5.0.0, get-stream@npm:^5.1.0":
  version: 5.2.0
  resolution: "get-stream@npm:5.2.0"
  dependencies:
    pump: "npm:^3.0.0"
  checksum: 10/13a73148dca795e41421013da6e3ebff8ccb7fba4d2f023fd0c6da2c166ec4e789bec9774a73a7b49c08daf2cae552f8a3e914042ac23b5f59dd278cc8f9cbfb
  languageName: node
  linkType: hard

"get-symbol-description@npm:^1.1.0":
  version: 1.1.0
  resolution: "get-symbol-description@npm:1.1.0"
  dependencies:
    call-bound: "npm:^1.0.3"
    es-errors: "npm:^1.3.0"
    get-intrinsic: "npm:^1.2.6"
  checksum: 10/a353e3a9595a74720b40fb5bae3ba4a4f826e186e83814d93375182384265676f59e49998b9cdfac4a2225ce95a3d32a68f502a2c5619303987f1c183ab80494
  languageName: node
  linkType: hard

"get-tsconfig@npm:^4.8.1":
  version: 4.10.1
  resolution: "get-tsconfig@npm:4.10.1"
  dependencies:
    resolve-pkg-maps: "npm:^1.0.0"
  checksum: 10/04d63f47fdecaefbd1f73ec02949be4ec4db7d6d9fbc8d4e81f9a4bb1c6f876e48943712f2f9236643d3e4d61d9a7b06da08564d08b034631ebe3f5605bef237
  languageName: node
  linkType: hard

"get-value@npm:^4.0.1":
  version: 4.0.1
  resolution: "get-value@npm:4.0.1"
  checksum: 10/f78559a83f65afdcc00e26df31928a7ed2f2cc3948c3d29068d639b8d363ddbf3a2786477f67cb605e297fda6a384a4b2062ed76928fc821c8550e210134b3ef
  languageName: node
  linkType: hard

"getpass@npm:^0.1.1":
  version: 0.1.7
  resolution: "getpass@npm:0.1.7"
  dependencies:
    assert-plus: "npm:^1.0.0"
  checksum: 10/ab18d55661db264e3eac6012c2d3daeafaab7a501c035ae0ccb193c3c23e9849c6e29b6ac762b9c2adae460266f925d55a3a2a3a3c8b94be2f222df94d70c046
  languageName: node
  linkType: hard

"glob-parent@npm:^6.0.2":
  version: 6.0.2
  resolution: "glob-parent@npm:6.0.2"
  dependencies:
    is-glob: "npm:^4.0.3"
  checksum: 10/c13ee97978bef4f55106b71e66428eb1512e71a7466ba49025fc2aec59a5bfb0954d5abd58fc5ee6c9b076eef4e1f6d3375c2e964b88466ca390da4419a786a8
  languageName: node
  linkType: hard

"glob@npm:^10.2.2":
  version: 10.4.5
  resolution: "glob@npm:10.4.5"
  dependencies:
    foreground-child: "npm:^3.1.0"
    jackspeak: "npm:^3.1.2"
    minimatch: "npm:^9.0.4"
    minipass: "npm:^7.1.2"
    package-json-from-dist: "npm:^1.0.0"
    path-scurry: "npm:^1.11.1"
  bin:
    glob: dist/esm/bin.mjs
  checksum: 10/698dfe11828b7efd0514cd11e573eaed26b2dff611f0400907281ce3eab0c1e56143ef9b35adc7c77ecc71fba74717b510c7c223d34ca8a98ec81777b293d4ac
  languageName: node
  linkType: hard

"glob@npm:^7.0.5":
  version: 7.2.3
  resolution: "glob@npm:7.2.3"
  dependencies:
    fs.realpath: "npm:^1.0.0"
    inflight: "npm:^1.0.4"
    inherits: "npm:2"
    minimatch: "npm:^3.1.1"
    once: "npm:^1.3.0"
    path-is-absolute: "npm:^1.0.0"
  checksum: 10/59452a9202c81d4508a43b8af7082ca5c76452b9fcc4a9ab17655822e6ce9b21d4f8fbadabe4fe3faef448294cec249af305e2cd824b7e9aaf689240e5e96a7b
  languageName: node
  linkType: hard

"globalize@npm:^1.6.0":
  version: 1.7.0
  resolution: "globalize@npm:1.7.0"
  dependencies:
    cldrjs: "npm:^0.5.4"
  checksum: 10/75b5bf74a3967ebe7bf7eded5c3b9076e5a9a4523dd16f88b3b5006eeb0243ebf9826b0d31ceb0d86d1045d7b7e6b81d0fa46a18c201312b605ef4a9ca3e3a82
  languageName: node
  linkType: hard

"globals@npm:^11.1.0":
  version: 11.12.0
  resolution: "globals@npm:11.12.0"
  checksum: 10/9f054fa38ff8de8fa356502eb9d2dae0c928217b8b5c8de1f09f5c9b6c8a96d8b9bd3afc49acbcd384a98a81fea713c859e1b09e214c60509517bb8fc2bc13c2
  languageName: node
  linkType: hard

"globals@npm:^14.0.0":
  version: 14.0.0
  resolution: "globals@npm:14.0.0"
  checksum: 10/03939c8af95c6df5014b137cac83aa909090c3a3985caef06ee9a5a669790877af8698ab38007e4c0186873adc14c0b13764acc754b16a754c216cc56aa5f021
  languageName: node
  linkType: hard

"globals@npm:^15.11.0":
  version: 15.15.0
  resolution: "globals@npm:15.15.0"
  checksum: 10/7f561c87b2fd381b27fc2db7df8a4ea7a9bb378667b8a7193e61fd2ca3a876479174e2a303a74345fbea6e1242e16db48915c1fd3bf35adcf4060a795b425e18
  languageName: node
  linkType: hard

"globalthis@npm:^1.0.4":
  version: 1.0.4
  resolution: "globalthis@npm:1.0.4"
  dependencies:
    define-properties: "npm:^1.2.1"
    gopd: "npm:^1.0.1"
  checksum: 10/1f1fd078fb2f7296306ef9dd51019491044ccf17a59ed49d375b576ca108ff37e47f3d29aead7add40763574a992f16a5367dd1e2173b8634ef18556ab719ac4
  languageName: node
  linkType: hard

"google-auth-library@npm:^9.7.0":
  version: 9.15.1
  resolution: "google-auth-library@npm:9.15.1"
  dependencies:
    base64-js: "npm:^1.3.0"
    ecdsa-sig-formatter: "npm:^1.0.11"
    gaxios: "npm:^6.1.1"
    gcp-metadata: "npm:^6.1.0"
    gtoken: "npm:^7.0.0"
    jws: "npm:^4.0.0"
  checksum: 10/6b977dd20f4f1ab6b2d2b78650d1e1c79ca84b951720b1064b85ebbb32af469547db7505a6609265e806be11c823bd6e07323b5073a98729b43b29fe34f05717
  languageName: node
  linkType: hard

"google-logging-utils@npm:^0.0.2":
  version: 0.0.2
  resolution: "google-logging-utils@npm:0.0.2"
  checksum: 10/f8f5ec3087ef4563d12ee1afc603e6b42b4d703c1f10c9f37b3080e6f4a2e9554e0fd9dcdce97ded5a46ead465c706ff2bc791ad2ca478ed8dc62fdc4b06cac6
  languageName: node
  linkType: hard

"googleapis-common@npm:^7.0.0":
  version: 7.2.0
  resolution: "googleapis-common@npm:7.2.0"
  dependencies:
    extend: "npm:^3.0.2"
    gaxios: "npm:^6.0.3"
    google-auth-library: "npm:^9.7.0"
    qs: "npm:^6.7.0"
    url-template: "npm:^2.0.8"
    uuid: "npm:^9.0.0"
  checksum: 10/4b914be6681f2a5a02bd0954a4a5cee1725d8623cb9d0a7c2fd7132de110e8d5707566cba39784e58147be39e74bc5513ad30fdcdaa6edcbb47ecf687003cb6c
  languageName: node
  linkType: hard

"gopd@npm:^1.0.1, gopd@npm:^1.2.0":
  version: 1.2.0
  resolution: "gopd@npm:1.2.0"
  checksum: 10/94e296d69f92dc1c0768fcfeecfb3855582ab59a7c75e969d5f96ce50c3d201fd86d5a2857c22565764d5bb8a816c7b1e58f133ec318cd56274da36c5e3fb1a1
  languageName: node
  linkType: hard

"got@npm:^11.1.4":
  version: 11.8.6
  resolution: "got@npm:11.8.6"
  dependencies:
    "@sindresorhus/is": "npm:^4.0.0"
    "@szmarczak/http-timer": "npm:^4.0.5"
    "@types/cacheable-request": "npm:^6.0.1"
    "@types/responselike": "npm:^1.0.0"
    cacheable-lookup: "npm:^5.0.3"
    cacheable-request: "npm:^7.0.2"
    decompress-response: "npm:^6.0.0"
    http2-wrapper: "npm:^1.0.0-beta.5.2"
    lowercase-keys: "npm:^2.0.0"
    p-cancelable: "npm:^2.0.0"
    responselike: "npm:^2.0.0"
  checksum: 10/a30c74029d81bd5fe50dea1a0c970595d792c568e188ff8be254b5bc11e6158d1b014570772d4a30d0a97723e7dd34e7c8cc1a2f23018f60aece3070a7a5c2a5
  languageName: node
  linkType: hard

"graceful-fs@npm:^4.2.4, graceful-fs@npm:^4.2.6":
  version: 4.2.11
  resolution: "graceful-fs@npm:4.2.11"
  checksum: 10/bf152d0ed1dc159239db1ba1f74fdbc40cb02f626770dcd5815c427ce0688c2635a06ed69af364396da4636d0408fcf7d4afdf7881724c3307e46aff30ca49e2
  languageName: node
  linkType: hard

"graphemer@npm:^1.4.0":
  version: 1.4.0
  resolution: "graphemer@npm:1.4.0"
  checksum: 10/6dd60dba97007b21e3a829fab3f771803cc1292977fe610e240ea72afd67e5690ac9eeaafc4a99710e78962e5936ab5a460787c2a1180f1cb0ccfac37d29f897
  languageName: node
  linkType: hard

"gtoken@npm:^7.0.0":
  version: 7.1.0
  resolution: "gtoken@npm:7.1.0"
  dependencies:
    gaxios: "npm:^6.0.0"
    jws: "npm:^4.0.0"
  checksum: 10/640392261e55c9242137a81a4af8feb053b57061762cedddcbb6a0d62c2314316161808ac2529eea67d06d69fdc56d82361af50f2d840a04a87ea29e124d7382
  languageName: node
  linkType: hard

"har-schema@npm:^2.0.0":
  version: 2.0.0
  resolution: "har-schema@npm:2.0.0"
  checksum: 10/d8946348f333fb09e2bf24cc4c67eabb47c8e1d1aa1c14184c7ffec1140a49ec8aa78aa93677ae452d71d5fc0fdeec20f0c8c1237291fc2bcb3f502a5d204f9b
  languageName: node
  linkType: hard

"har-validator@npm:~5.1.3":
  version: 5.1.5
  resolution: "har-validator@npm:5.1.5"
  dependencies:
    ajv: "npm:^6.12.3"
    har-schema: "npm:^2.0.0"
  checksum: 10/b998a7269ca560d7f219eedc53e2c664cd87d487e428ae854a6af4573fc94f182fe9d2e3b92ab968249baec7ebaf9ead69cf975c931dc2ab282ec182ee988280
  languageName: node
  linkType: hard

"has-bigints@npm:^1.0.2":
  version: 1.1.0
  resolution: "has-bigints@npm:1.1.0"
  checksum: 10/90fb1b24d40d2472bcd1c8bd9dd479037ec240215869bdbff97b2be83acef57d28f7e96bdd003a21bed218d058b49097f4acc8821c05b1629cc5d48dd7bfcccd
  languageName: node
  linkType: hard

"has-flag@npm:^4.0.0":
  version: 4.0.0
  resolution: "has-flag@npm:4.0.0"
  checksum: 10/261a1357037ead75e338156b1f9452c016a37dcd3283a972a30d9e4a87441ba372c8b81f818cd0fbcd9c0354b4ae7e18b9e1afa1971164aef6d18c2b6095a8ad
  languageName: node
  linkType: hard

"has-property-descriptors@npm:^1.0.0, has-property-descriptors@npm:^1.0.2":
  version: 1.0.2
  resolution: "has-property-descriptors@npm:1.0.2"
  dependencies:
    es-define-property: "npm:^1.0.0"
  checksum: 10/2d8c9ab8cebb572e3362f7d06139a4592105983d4317e68f7adba320fe6ddfc8874581e0971e899e633fd5f72e262830edce36d5a0bc863dad17ad20572484b2
  languageName: node
  linkType: hard

"has-proto@npm:^1.2.0":
  version: 1.2.0
  resolution: "has-proto@npm:1.2.0"
  dependencies:
    dunder-proto: "npm:^1.0.0"
  checksum: 10/7eaed07728eaa28b77fadccabce53f30de467ff186a766872669a833ac2e87d8922b76a22cc58339d7e0277aefe98d6d00762113b27a97cdf65adcf958970935
  languageName: node
  linkType: hard

"has-symbols@npm:^1.0.3, has-symbols@npm:^1.1.0":
  version: 1.1.0
  resolution: "has-symbols@npm:1.1.0"
  checksum: 10/959385c98696ebbca51e7534e0dc723ada325efa3475350951363cce216d27373e0259b63edb599f72eb94d6cde8577b4b2375f080b303947e560f85692834fa
  languageName: node
  linkType: hard

"has-tostringtag@npm:^1.0.2":
  version: 1.0.2
  resolution: "has-tostringtag@npm:1.0.2"
  dependencies:
    has-symbols: "npm:^1.0.3"
  checksum: 10/c74c5f5ceee3c8a5b8bc37719840dc3749f5b0306d818974141dda2471a1a2ca6c8e46b9d6ac222c5345df7a901c9b6f350b1e6d62763fec877e26609a401bfe
  languageName: node
  linkType: hard

"hasown@npm:^2.0.2":
  version: 2.0.2
  resolution: "hasown@npm:2.0.2"
  dependencies:
    function-bind: "npm:^1.1.2"
  checksum: 10/7898a9c1788b2862cf0f9c345a6bec77ba4a0c0983c7f19d610c382343d4f98fa260686b225dfb1f88393a66679d2ec58ee310c1d6868c081eda7918f32cc70a
  languageName: node
  linkType: hard

"header-case@npm:^2.0.4":
  version: 2.0.4
  resolution: "header-case@npm:2.0.4"
  dependencies:
    capital-case: "npm:^1.0.4"
    tslib: "npm:^2.0.3"
  checksum: 10/571c83eeb25e8130d172218712f807c0b96d62b020981400bccc1503a7cf14b09b8b10498a962d2739eccf231d950e3848ba7d420b58a6acd2f9283439546cd9
  languageName: node
  linkType: hard

"hot-shots@npm:^10.2.1":
  version: 10.2.1
  resolution: "hot-shots@npm:10.2.1"
  dependencies:
    unix-dgram: "npm:2.x"
  dependenciesMeta:
    unix-dgram:
      optional: true
  checksum: 10/0d20861efb62d4c3762cca2cf1ad0b6ddbe70f22252ea712fc995f27b6b575c50c8cb3e452adf0d9b829a724c17b1fcfe7874d00ca7f28185f8ee03f710ec6f1
  languageName: node
  linkType: hard

"htmlparser2@npm:^10.0.0":
  version: 10.0.0
  resolution: "htmlparser2@npm:10.0.0"
  dependencies:
    domelementtype: "npm:^2.3.0"
    domhandler: "npm:^5.0.3"
    domutils: "npm:^3.2.1"
    entities: "npm:^6.0.0"
  checksum: 10/768870f0e020dca19dc45df206cb6ac466c5dba6566c8fca4ca880347eed409f9977028d08644ac516bca8628ac9c7ded5a3847dc3ee1c043f049abf9e817154
  languageName: node
  linkType: hard

"http-cache-semantics@npm:^4.0.0, http-cache-semantics@npm:^4.1.1":
  version: 4.2.0
  resolution: "http-cache-semantics@npm:4.2.0"
  checksum: 10/4efd2dfcfeea9d5e88c84af450b9980be8a43c2c8179508b1c57c7b4421c855f3e8efe92fa53e0b3f4a43c85824ada930eabbc306d1b3beab750b6dcc5187693
  languageName: node
  linkType: hard

"http-errors@npm:2.0.0, http-errors@npm:^2.0.0":
  version: 2.0.0
  resolution: "http-errors@npm:2.0.0"
  dependencies:
    depd: "npm:2.0.0"
    inherits: "npm:2.0.4"
    setprototypeof: "npm:1.2.0"
    statuses: "npm:2.0.1"
    toidentifier: "npm:1.0.1"
  checksum: 10/0e7f76ee8ff8a33e58a3281a469815b893c41357378f408be8f6d4aa7d1efafb0da064625518e7078381b6a92325949b119dc38fcb30bdbc4e3a35f78c44c439
  languageName: node
  linkType: hard

"http-proxy-agent@npm:^7.0.0":
  version: 7.0.2
  resolution: "http-proxy-agent@npm:7.0.2"
  dependencies:
    agent-base: "npm:^7.1.0"
    debug: "npm:^4.3.4"
  checksum: 10/d062acfa0cb82beeb558f1043c6ba770ea892b5fb7b28654dbc70ea2aeea55226dd34c02a294f6c1ca179a5aa483c4ea641846821b182edbd9cc5d89b54c6848
  languageName: node
  linkType: hard

"http-signature@npm:~1.2.0":
  version: 1.2.0
  resolution: "http-signature@npm:1.2.0"
  dependencies:
    assert-plus: "npm:^1.0.0"
    jsprim: "npm:^1.2.2"
    sshpk: "npm:^1.7.0"
  checksum: 10/2ff7112e6b0d8f08b382dfe705078c655501f2ddd76cf589d108445a9dd388a0a9be928c37108261519a7f53e6bbd1651048d74057b804807cce1ec49e87a95b
  languageName: node
  linkType: hard

"http-status@npm:^1.1.2":
  version: 1.8.1
  resolution: "http-status@npm:1.8.1"
  checksum: 10/a150d4cad98e0a8c9c2a9d2051fa124a99cefad8b3f5d1393a0efb4f74c268c53efecb259519139b453fef951e825f4be17c5d2b5c15811799ad8c6afb0c0096
  languageName: node
  linkType: hard

"http2-wrapper@npm:^1.0.0-beta.5.2":
  version: 1.0.3
  resolution: "http2-wrapper@npm:1.0.3"
  dependencies:
    quick-lru: "npm:^5.1.1"
    resolve-alpn: "npm:^1.0.0"
  checksum: 10/8097ee2699440c2e64bda52124990cc5b0fb347401c7797b1a0c1efd5a0f79a4ebaa68e8a6ac3e2dde5f09460c1602764da6da2412bad628ed0a3b0ae35e72d4
  languageName: node
  linkType: hard

"httpntlm@npm:1.6.1":
  version: 1.6.1
  resolution: "httpntlm@npm:1.6.1"
  dependencies:
    httpreq: "npm:>=0.4.22"
    underscore: "npm:~1.7.0"
  checksum: 10/ff8e85737d866dfc52112fd247bb1816b79d9150167a02308e3380b7a320b090bea3f82205d7a4aabf9d4b2d60510d07bf8fdc86de7b7ac2d3ee585c83c5b1d8
  languageName: node
  linkType: hard

"httpreq@npm:>=0.4.22":
  version: 1.1.1
  resolution: "httpreq@npm:1.1.1"
  checksum: 10/a25facf4b43b5e23a3728fe2f8c07cef597d7d2d123ca9dea4c5ff5f13d05bef4a7f7ce77f9ad4c4df63ba69cf1d9ca7d88b441d4175785d824bc607e31568a0
  languageName: node
  linkType: hard

"https-proxy-agent@npm:^7.0.1":
  version: 7.0.6
  resolution: "https-proxy-agent@npm:7.0.6"
  dependencies:
    agent-base: "npm:^7.1.2"
    debug: "npm:4"
  checksum: 10/784b628cbd55b25542a9d85033bdfd03d4eda630fb8b3c9477959367f3be95dc476ed2ecbb9836c359c7c698027fc7b45723a302324433590f45d6c1706e8c13
  languageName: node
  linkType: hard

"human-signals@npm:^1.1.1":
  version: 1.1.1
  resolution: "human-signals@npm:1.1.1"
  checksum: 10/6a58224dffcef5588910b1028bda8623c9a7053460a1fe3367e61921a6b5f6b93aba30f323868a958f968d7de3f5f78421f11d4d9f7e9563b1bd2b00ed9a4deb
  languageName: node
  linkType: hard

"humanize-ms@npm:^1.2.1":
  version: 1.2.1
  resolution: "humanize-ms@npm:1.2.1"
  dependencies:
    ms: "npm:^2.0.0"
  checksum: 10/9c7a74a2827f9294c009266c82031030eae811ca87b0da3dceb8d6071b9bde22c9f3daef0469c3c533cc67a97d8a167cd9fc0389350e5f415f61a79b171ded16
  languageName: node
  linkType: hard

"i18n-js@npm:3.9.2":
  version: 3.9.2
  resolution: "i18n-js@npm:3.9.2"
  checksum: 10/4f9fbb64ea1dad09c3fe0e761bcea151b0565c42b8f5b020809cb3224f8d9cae4c0763a545ac2e76cef1439fd583e060017352e2a8d0da1885a459e00136e78d
  languageName: node
  linkType: hard

"i18next@npm:^25.2.1":
  version: 25.2.1
  resolution: "i18next@npm:25.2.1"
  dependencies:
    "@babel/runtime": "npm:^7.27.1"
  peerDependencies:
    typescript: ^5
  peerDependenciesMeta:
    typescript:
      optional: true
  checksum: 10/7abc9dedee928d23c926917fcc640ff45e22320c814f3f254ade74bc255247edd78e8dd58c835bbfe1898489f6cb2e874e547f9fa68a1a6820409c1306dec28c
  languageName: node
  linkType: hard

"iconv-lite@npm:0.4.24":
  version: 0.4.24
  resolution: "iconv-lite@npm:0.4.24"
  dependencies:
    safer-buffer: "npm:>= 2.1.2 < 3"
  checksum: 10/6d3a2dac6e5d1fb126d25645c25c3a1209f70cceecc68b8ef51ae0da3cdc078c151fade7524a30b12a3094926336831fca09c666ef55b37e2c69638b5d6bd2e3
  languageName: node
  linkType: hard

"iconv-lite@npm:0.6.3, iconv-lite@npm:^0.6.2, iconv-lite@npm:^0.6.3":
  version: 0.6.3
  resolution: "iconv-lite@npm:0.6.3"
  dependencies:
    safer-buffer: "npm:>= 2.1.2 < 3.0.0"
  checksum: 10/24e3292dd3dadaa81d065c6f8c41b274a47098150d444b96e5f53b4638a9a71482921ea6a91a1f59bb71d9796de25e04afd05919fa64c360347ba65d3766f10f
  languageName: node
  linkType: hard

"ieee754@npm:^1.2.1":
  version: 1.2.1
  resolution: "ieee754@npm:1.2.1"
  checksum: 10/d9f2557a59036f16c282aaeb107832dc957a93d73397d89bbad4eb1130560560eb695060145e8e6b3b498b15ab95510226649a0b8f52ae06583575419fe10fc4
  languageName: node
  linkType: hard

"ignore@npm:^5.2.0, ignore@npm:^5.3.2":
  version: 5.3.2
  resolution: "ignore@npm:5.3.2"
  checksum: 10/cceb6a457000f8f6a50e1196429750d782afce5680dd878aa4221bd79972d68b3a55b4b1458fc682be978f4d3c6a249046aa0880637367216444ab7b014cfc98
  languageName: node
  linkType: hard

"import-fresh@npm:^3.2.1":
  version: 3.3.1
  resolution: "import-fresh@npm:3.3.1"
  dependencies:
    parent-module: "npm:^1.0.0"
    resolve-from: "npm:^4.0.0"
  checksum: 10/a06b19461b4879cc654d46f8a6244eb55eb053437afd4cbb6613cad6be203811849ed3e4ea038783092879487299fda24af932b86bdfff67c9055ba3612b8c87
  languageName: node
  linkType: hard

"imurmurhash@npm:^0.1.4":
  version: 0.1.4
  resolution: "imurmurhash@npm:0.1.4"
  checksum: 10/2d30b157a91fe1c1d7c6f653cbf263f039be6c5bfa959245a16d4ee191fc0f2af86c08545b6e6beeb041c56b574d2d5b9f95343d378ab49c0f37394d541e7fc8
  languageName: node
  linkType: hard

"inflection@npm:2.0.1":
  version: 2.0.1
  resolution: "inflection@npm:2.0.1"
  checksum: 10/b6ccc9432d5b3247541f6f290dcb96927a47a6d71875e5a2b0ac092d4e7ed3171a11112a85c44e9f33d3e981feffd6c646282703fdf0c323b3a66993f4e7ccea
  languageName: node
  linkType: hard

"inflection@npm:^3.0.2":
  version: 3.0.2
  resolution: "inflection@npm:3.0.2"
  checksum: 10/9460aaef8488f0c077aca183f73f250436cf31aa125732fd29d49b13e67001462375198119e89b06266b2fcedcc5386f6d20d640617e5856db9054c915527906
  languageName: node
  linkType: hard

"inflight@npm:^1.0.4":
  version: 1.0.6
  resolution: "inflight@npm:1.0.6"
  dependencies:
    once: "npm:^1.3.0"
    wrappy: "npm:1"
  checksum: 10/d2ebd65441a38c8336c223d1b80b921b9fa737e37ea466fd7e253cb000c64ae1f17fa59e68130ef5bda92cfd8d36b83d37dab0eb0a4558bcfec8e8cdfd2dcb67
  languageName: node
  linkType: hard

"inherits@npm:2, inherits@npm:2.0.4, inherits@npm:^2.0.3, inherits@npm:^2.0.4, inherits@npm:~2.0.3":
  version: 2.0.4
  resolution: "inherits@npm:2.0.4"
  checksum: 10/cd45e923bee15186c07fa4c89db0aace24824c482fb887b528304694b2aa6ff8a898da8657046a5dcf3e46cd6db6c61629551f9215f208d7c3f157cf9b290521
  languageName: node
  linkType: hard

"internal-slot@npm:^1.1.0":
  version: 1.1.0
  resolution: "internal-slot@npm:1.1.0"
  dependencies:
    es-errors: "npm:^1.3.0"
    hasown: "npm:^2.0.2"
    side-channel: "npm:^1.1.0"
  checksum: 10/1d5219273a3dab61b165eddf358815eefc463207db33c20fcfca54717da02e3f492003757721f972fd0bf21e4b426cab389c5427b99ceea4b8b670dc88ee6d4a
  languageName: node
  linkType: hard

"invert-kv@npm:^3.0.0":
  version: 3.0.1
  resolution: "invert-kv@npm:3.0.1"
  checksum: 10/9801e7876b80ee70df8bcd029a928a35a1252c9c8a92e5d7779fdcd7771ab73e834744bf1f868d6a4b849b275e5ed976ca4f2bfd86814140f863c449196484f5
  languageName: node
  linkType: hard

"ioredis@npm:^5.6.1":
  version: 5.6.1
  resolution: "ioredis@npm:5.6.1"
  dependencies:
    "@ioredis/commands": "npm:^1.1.1"
    cluster-key-slot: "npm:^1.1.0"
    debug: "npm:^4.3.4"
    denque: "npm:^2.1.0"
    lodash.defaults: "npm:^4.2.0"
    lodash.isarguments: "npm:^3.1.0"
    redis-errors: "npm:^1.2.0"
    redis-parser: "npm:^3.0.0"
    standard-as-callback: "npm:^2.1.0"
  checksum: 10/632186e21f2d8b94a12918b5feef0f5df70315e303988989adb0d190377759ffa601660be7536489c0768803a0fb6eb94f861b33c1fcfc673d518fdd31c541e2
  languageName: node
  linkType: hard

"ip-address@npm:^9.0.5":
  version: 9.0.5
  resolution: "ip-address@npm:9.0.5"
  dependencies:
    jsbn: "npm:1.1.0"
    sprintf-js: "npm:^1.1.3"
  checksum: 10/1ed81e06721af012306329b31f532b5e24e00cb537be18ddc905a84f19fe8f83a09a1699862bf3a1ec4b9dea93c55a3fa5faf8b5ea380431469df540f38b092c
  languageName: node
  linkType: hard

"ipaddr.js@npm:1.9.1":
  version: 1.9.1
  resolution: "ipaddr.js@npm:1.9.1"
  checksum: 10/864d0cced0c0832700e9621913a6429ccdc67f37c1bd78fb8c6789fff35c9d167cb329134acad2290497a53336813ab4798d2794fd675d5eb33b5fdf0982b9ca
  languageName: node
  linkType: hard

"is-array-buffer@npm:^3.0.4, is-array-buffer@npm:^3.0.5":
  version: 3.0.5
  resolution: "is-array-buffer@npm:3.0.5"
  dependencies:
    call-bind: "npm:^1.0.8"
    call-bound: "npm:^1.0.3"
    get-intrinsic: "npm:^1.2.6"
  checksum: 10/ef1095c55b963cd0dcf6f88a113e44a0aeca91e30d767c475e7d746d28d1195b10c5076b94491a7a0cd85020ca6a4923070021d74651d093dc909e9932cf689b
  languageName: node
  linkType: hard

"is-async-function@npm:^2.0.0":
  version: 2.1.1
  resolution: "is-async-function@npm:2.1.1"
  dependencies:
    async-function: "npm:^1.0.0"
    call-bound: "npm:^1.0.3"
    get-proto: "npm:^1.0.1"
    has-tostringtag: "npm:^1.0.2"
    safe-regex-test: "npm:^1.1.0"
  checksum: 10/7c2ac7efdf671e03265e74a043bcb1c0a32e226bc2a42dfc5ec8644667df668bbe14b91c08e6c1414f392f8cf86cd1d489b3af97756e2c7a49dd1ba63fd40ca6
  languageName: node
  linkType: hard

"is-bigint@npm:^1.1.0":
  version: 1.1.0
  resolution: "is-bigint@npm:1.1.0"
  dependencies:
    has-bigints: "npm:^1.0.2"
  checksum: 10/10cf327310d712fe227cfaa32d8b11814c214392b6ac18c827f157e1e85363cf9c8e2a22df526689bd5d25e53b58cc110894787afb54e138e7c504174dba15fd
  languageName: node
  linkType: hard

"is-boolean-object@npm:^1.2.1":
  version: 1.2.2
  resolution: "is-boolean-object@npm:1.2.2"
  dependencies:
    call-bound: "npm:^1.0.3"
    has-tostringtag: "npm:^1.0.2"
  checksum: 10/051fa95fdb99d7fbf653165a7e6b2cba5d2eb62f7ffa81e793a790f3fb5366c91c1b7b6af6820aa2937dd86c73aa3ca9d9ca98f500988457b1c59692c52ba911
  languageName: node
  linkType: hard

"is-buffer@npm:~1.1.6":
  version: 1.1.6
  resolution: "is-buffer@npm:1.1.6"
  checksum: 10/f63da109e74bbe8947036ed529d43e4ae0c5fcd0909921dce4917ad3ea212c6a87c29f525ba1d17c0858c18331cf1046d4fc69ef59ed26896b25c8288a627133
  languageName: node
  linkType: hard

"is-callable@npm:^1.2.7":
  version: 1.2.7
  resolution: "is-callable@npm:1.2.7"
  checksum: 10/48a9297fb92c99e9df48706241a189da362bff3003354aea4048bd5f7b2eb0d823cd16d0a383cece3d76166ba16d85d9659165ac6fcce1ac12e6c649d66dbdb9
  languageName: node
  linkType: hard

"is-data-view@npm:^1.0.1, is-data-view@npm:^1.0.2":
  version: 1.0.2
  resolution: "is-data-view@npm:1.0.2"
  dependencies:
    call-bound: "npm:^1.0.2"
    get-intrinsic: "npm:^1.2.6"
    is-typed-array: "npm:^1.1.13"
  checksum: 10/357e9a48fa38f369fd6c4c3b632a3ab2b8adca14997db2e4b3fe94c4cd0a709af48e0fb61b02c64a90c0dd542fd489d49c2d03157b05ae6c07f5e4dec9e730a8
  languageName: node
  linkType: hard

"is-date-object@npm:^1.0.5, is-date-object@npm:^1.1.0":
  version: 1.1.0
  resolution: "is-date-object@npm:1.1.0"
  dependencies:
    call-bound: "npm:^1.0.2"
    has-tostringtag: "npm:^1.0.2"
  checksum: 10/3a811b2c3176fb31abee1d23d3dc78b6c65fd9c07d591fcb67553cab9e7f272728c3dd077d2d738b53f9a2103255b0a6e8dfc9568a7805c56a78b2563e8d1dec
  languageName: node
  linkType: hard

"is-electron@npm:2.2.2":
  version: 2.2.2
  resolution: "is-electron@npm:2.2.2"
  checksum: 10/de5aa8bd8d72c96675b8d0f93fab4cc21f62be5440f65bc05c61338ca27bd851a64200f31f1bf9facbaa01b3dbfed7997b2186741d84b93b63e0aff1db6a9494
  languageName: node
  linkType: hard

"is-extendable@npm:^1.0.0":
  version: 1.0.1
  resolution: "is-extendable@npm:1.0.1"
  dependencies:
    is-plain-object: "npm:^2.0.4"
  checksum: 10/db07bc1e9de6170de70eff7001943691f05b9d1547730b11be01c0ebfe67362912ba743cf4be6fd20a5e03b4180c685dad80b7c509fe717037e3eee30ad8e84f
  languageName: node
  linkType: hard

"is-extglob@npm:^2.1.1":
  version: 2.1.1
  resolution: "is-extglob@npm:2.1.1"
  checksum: 10/df033653d06d0eb567461e58a7a8c9f940bd8c22274b94bf7671ab36df5719791aae15eef6d83bbb5e23283967f2f984b8914559d4449efda578c775c4be6f85
  languageName: node
  linkType: hard

"is-finalizationregistry@npm:^1.1.0":
  version: 1.1.1
  resolution: "is-finalizationregistry@npm:1.1.1"
  dependencies:
    call-bound: "npm:^1.0.3"
  checksum: 10/0bfb145e9a1ba852ddde423b0926d2169ae5fe9e37882cde9e8f69031281a986308df4d982283e152396e88b86562ed2256cbaa5e6390fb840a4c25ab54b8a80
  languageName: node
  linkType: hard

"is-fullwidth-code-point@npm:^3.0.0":
  version: 3.0.0
  resolution: "is-fullwidth-code-point@npm:3.0.0"
  checksum: 10/44a30c29457c7fb8f00297bce733f0a64cd22eca270f83e58c105e0d015e45c019491a4ab2faef91ab51d4738c670daff901c799f6a700e27f7314029e99e348
  languageName: node
  linkType: hard

"is-generator-function@npm:^1.0.10":
  version: 1.1.0
  resolution: "is-generator-function@npm:1.1.0"
  dependencies:
    call-bound: "npm:^1.0.3"
    get-proto: "npm:^1.0.0"
    has-tostringtag: "npm:^1.0.2"
    safe-regex-test: "npm:^1.1.0"
  checksum: 10/5906ff51a856a5fbc6b90a90fce32040b0a6870da905f98818f1350f9acadfc9884f7c3dec833fce04b83dd883937b86a190b6593ede82e8b1af8b6c4ecf7cbd
  languageName: node
  linkType: hard

"is-glob@npm:^4.0.0, is-glob@npm:^4.0.3":
  version: 4.0.3
  resolution: "is-glob@npm:4.0.3"
  dependencies:
    is-extglob: "npm:^2.1.1"
  checksum: 10/3ed74f2b0cdf4f401f38edb0442ddfde3092d79d7d35c9919c86641efdbcbb32e45aa3c0f70ce5eecc946896cd5a0f26e4188b9f2b881876f7cb6c505b82da11
  languageName: node
  linkType: hard

"is-map@npm:^2.0.3":
  version: 2.0.3
  resolution: "is-map@npm:2.0.3"
  checksum: 10/8de7b41715b08bcb0e5edb0fb9384b80d2d5bcd10e142188f33247d19ff078abaf8e9b6f858e2302d8d05376a26a55cd23a3c9f8ab93292b02fcd2cc9e4e92bb
  languageName: node
  linkType: hard

"is-number-object@npm:^1.1.1":
  version: 1.1.1
  resolution: "is-number-object@npm:1.1.1"
  dependencies:
    call-bound: "npm:^1.0.3"
    has-tostringtag: "npm:^1.0.2"
  checksum: 10/a5922fb8779ab1ea3b8a9c144522b3d0bea5d9f8f23f7a72470e61e1e4df47714e28e0154ac011998b709cce260c3c9447ad3cd24a96c2f2a0abfdb2cbdc76c8
  languageName: node
  linkType: hard

"is-plain-object@npm:^2.0.4":
  version: 2.0.4
  resolution: "is-plain-object@npm:2.0.4"
  dependencies:
    isobject: "npm:^3.0.1"
  checksum: 10/2a401140cfd86cabe25214956ae2cfee6fbd8186809555cd0e84574f88de7b17abacb2e477a6a658fa54c6083ecbda1e6ae404c7720244cd198903848fca70ca
  languageName: node
  linkType: hard

"is-primitive@npm:^3.0.1":
  version: 3.0.1
  resolution: "is-primitive@npm:3.0.1"
  checksum: 10/c4da6a6e6d487f31d85b9259b67695fffcc75dca6c9612b0a002e3050c734227b9911be09b877539ec6309710229c19f4edd0f9e26ed2a67924ee0916baf0bed
  languageName: node
  linkType: hard

"is-promise@npm:^4.0.0":
  version: 4.0.0
  resolution: "is-promise@npm:4.0.0"
  checksum: 10/0b46517ad47b00b6358fd6553c83ec1f6ba9acd7ffb3d30a0bf519c5c69e7147c132430452351b8a9fc198f8dd6c4f76f8e6f5a7f100f8c77d57d9e0f4261a8a
  languageName: node
  linkType: hard

"is-regex@npm:^1.2.1":
  version: 1.2.1
  resolution: "is-regex@npm:1.2.1"
  dependencies:
    call-bound: "npm:^1.0.2"
    gopd: "npm:^1.2.0"
    has-tostringtag: "npm:^1.0.2"
    hasown: "npm:^2.0.2"
  checksum: 10/c42b7efc5868a5c9a4d8e6d3e9816e8815c611b09535c00fead18a1138455c5cb5e1887f0023a467ad3f9c419d62ba4dc3d9ba8bafe55053914d6d6454a945d2
  languageName: node
  linkType: hard

"is-retry-allowed@npm:^2.2.0":
  version: 2.2.0
  resolution: "is-retry-allowed@npm:2.2.0"
  checksum: 10/6d8685530871f0b040346cc72322d90122473e921149affa16de363d6c2a6e46bc76abdfaac3259b93994ec8e7f70fbe67bbb080190e440533ff728e6a64494d
  languageName: node
  linkType: hard

"is-set@npm:^2.0.3":
  version: 2.0.3
  resolution: "is-set@npm:2.0.3"
  checksum: 10/5685df33f0a4a6098a98c72d94d67cad81b2bc72f1fb2091f3d9283c4a1c582123cd709145b02a9745f0ce6b41e3e43f1c944496d1d74d4ea43358be61308669
  languageName: node
  linkType: hard

"is-shared-array-buffer@npm:^1.0.4":
  version: 1.0.4
  resolution: "is-shared-array-buffer@npm:1.0.4"
  dependencies:
    call-bound: "npm:^1.0.3"
  checksum: 10/0380d7c60cc692856871526ffcd38a8133818a2ee42d47bb8008248a0cd2121d8c8b5f66b6da3cac24bc5784553cacb6faaf678f66bc88c6615b42af2825230e
  languageName: node
  linkType: hard

"is-stream@npm:^2, is-stream@npm:^2.0.0":
  version: 2.0.1
  resolution: "is-stream@npm:2.0.1"
  checksum: 10/b8e05ccdf96ac330ea83c12450304d4a591f9958c11fd17bed240af8d5ffe08aedafa4c0f4cfccd4d28dc9d4d129daca1023633d5c11601a6cbc77521f6fae66
  languageName: node
  linkType: hard

"is-string@npm:^1.1.1":
  version: 1.1.1
  resolution: "is-string@npm:1.1.1"
  dependencies:
    call-bound: "npm:^1.0.3"
    has-tostringtag: "npm:^1.0.2"
  checksum: 10/5277cb9e225a7cc8a368a72623b44a99f2cfa139659c6b203553540681ad4276bfc078420767aad0e73eef5f0bd07d4abf39a35d37ec216917879d11cebc1f8b
  languageName: node
  linkType: hard

"is-symbol@npm:^1.0.4, is-symbol@npm:^1.1.1":
  version: 1.1.1
  resolution: "is-symbol@npm:1.1.1"
  dependencies:
    call-bound: "npm:^1.0.2"
    has-symbols: "npm:^1.1.0"
    safe-regex-test: "npm:^1.1.0"
  checksum: 10/db495c0d8cd0a7a66b4f4ef7fccee3ab5bd954cb63396e8ac4d32efe0e9b12fdfceb851d6c501216a71f4f21e5ff20fc2ee845a3d52d455e021c466ac5eb2db2
  languageName: node
  linkType: hard

"is-typed-array@npm:^1.1.13, is-typed-array@npm:^1.1.14, is-typed-array@npm:^1.1.15":
  version: 1.1.15
  resolution: "is-typed-array@npm:1.1.15"
  dependencies:
    which-typed-array: "npm:^1.1.16"
  checksum: 10/e8cf60b9ea85667097a6ad68c209c9722cfe8c8edf04d6218366469e51944c5cc25bae45ffb845c23f811d262e4314d3b0168748eb16711aa34d12724cdf0735
  languageName: node
  linkType: hard

"is-typedarray@npm:~1.0.0":
  version: 1.0.0
  resolution: "is-typedarray@npm:1.0.0"
  checksum: 10/4b433bfb0f9026f079f4eb3fbaa4ed2de17c9995c3a0b5c800bec40799b4b2a8b4e051b1ada77749deb9ded4ae52fe2096973f3a93ff83df1a5a7184a669478c
  languageName: node
  linkType: hard

"is-weakmap@npm:^2.0.2":
  version: 2.0.2
  resolution: "is-weakmap@npm:2.0.2"
  checksum: 10/a7b7e23206c542dcf2fa0abc483142731788771527e90e7e24f658c0833a0d91948a4f7b30d78f7a65255a48512e41a0288b778ba7fc396137515c12e201fd11
  languageName: node
  linkType: hard

"is-weakref@npm:^1.0.2, is-weakref@npm:^1.1.1":
  version: 1.1.1
  resolution: "is-weakref@npm:1.1.1"
  dependencies:
    call-bound: "npm:^1.0.3"
  checksum: 10/543506fd8259038b371bb083aac25b16cb4fd8b12fc58053aa3d45ac28dfd001cd5c6dffbba7aeea4213c74732d46b6cb2cfb5b412eed11f2db524f3f97d09a0
  languageName: node
  linkType: hard

"is-weakset@npm:^2.0.3":
  version: 2.0.4
  resolution: "is-weakset@npm:2.0.4"
  dependencies:
    call-bound: "npm:^1.0.3"
    get-intrinsic: "npm:^1.2.6"
  checksum: 10/1d5e1d0179beeed3661125a6faa2e59bfb48afda06fc70db807f178aa0ebebc3758fb6358d76b3d528090d5ef85148c345dcfbf90839592fe293e3e5e82f2134
  languageName: node
  linkType: hard

"isarray@npm:^2.0.5":
  version: 2.0.5
  resolution: "isarray@npm:2.0.5"
  checksum: 10/1d8bc7911e13bb9f105b1b3e0b396c787a9e63046af0b8fe0ab1414488ab06b2b099b87a2d8a9e31d21c9a6fad773c7fc8b257c4880f2d957274479d28ca3414
  languageName: node
  linkType: hard

"isarray@npm:~1.0.0":
  version: 1.0.0
  resolution: "isarray@npm:1.0.0"
  checksum: 10/f032df8e02dce8ec565cf2eb605ea939bdccea528dbcf565cdf92bfa2da9110461159d86a537388ef1acef8815a330642d7885b29010e8f7eac967c9993b65ab
  languageName: node
  linkType: hard

"isemail@npm:^3.2.0":
  version: 3.2.0
  resolution: "isemail@npm:3.2.0"
  dependencies:
    punycode: "npm:2.x.x"
  checksum: 10/1a94cc2bb47f0dbc316faaab06610abbe55da5d01f0e154ef414ba331e101e848de34a30d7a5da3ab645b922281fc098c81a8be6d928b85a8f3d1289c952e8bc
  languageName: node
  linkType: hard

"isexe@npm:^2.0.0":
  version: 2.0.0
  resolution: "isexe@npm:2.0.0"
  checksum: 10/7c9f715c03aff08f35e98b1fadae1b9267b38f0615d501824f9743f3aab99ef10e303ce7db3f186763a0b70a19de5791ebfc854ff884d5a8c4d92211f642ec92
  languageName: node
  linkType: hard

"isexe@npm:^3.1.1":
  version: 3.1.1
  resolution: "isexe@npm:3.1.1"
  checksum: 10/7fe1931ee4e88eb5aa524cd3ceb8c882537bc3a81b02e438b240e47012eef49c86904d0f0e593ea7c3a9996d18d0f1f3be8d3eaa92333977b0c3a9d353d5563e
  languageName: node
  linkType: hard

"isobject@npm:^3.0.1":
  version: 3.0.1
  resolution: "isobject@npm:3.0.1"
  checksum: 10/db85c4c970ce30693676487cca0e61da2ca34e8d4967c2e1309143ff910c207133a969f9e4ddb2dc6aba670aabce4e0e307146c310350b298e74a31f7d464703
  languageName: node
  linkType: hard

"isstream@npm:~0.1.2":
  version: 0.1.2
  resolution: "isstream@npm:0.1.2"
  checksum: 10/22d9c181015226d4534a227539256897bbbcb7edd1066ca4fc4d3a06dbd976325dfdd16b3983c7d236a89f256805c1a685a772e0364e98873d3819b064ad35a1
  languageName: node
  linkType: hard

"jackspeak@npm:^3.1.2":
  version: 3.4.3
  resolution: "jackspeak@npm:3.4.3"
  dependencies:
    "@isaacs/cliui": "npm:^8.0.2"
    "@pkgjs/parseargs": "npm:^0.11.0"
  dependenciesMeta:
    "@pkgjs/parseargs":
      optional: true
  checksum: 10/96f8786eaab98e4bf5b2a5d6d9588ea46c4d06bbc4f2eb861fdd7b6b182b16f71d8a70e79820f335d52653b16d4843b29dd9cdcf38ae80406756db9199497cf3
  languageName: node
  linkType: hard

"jake@npm:^10.8.5":
  version: 10.9.2
  resolution: "jake@npm:10.9.2"
  dependencies:
    async: "npm:^3.2.3"
    chalk: "npm:^4.0.2"
    filelist: "npm:^1.0.4"
    minimatch: "npm:^3.1.2"
  bin:
    jake: bin/cli.js
  checksum: 10/3be324708f99f031e0aec49ef8fd872eb4583cbe8a29a0c875f554f6ac638ee4ea5aa759bb63723fd54f77ca6d7db851eaa78353301734ed3700db9cb109a0cd
  languageName: node
  linkType: hard

"jayson@npm:2.1.2":
  version: 2.1.2
  resolution: "jayson@npm:2.1.2"
  dependencies:
    "@types/node": "npm:^10.3.5"
    JSONStream: "npm:^1.3.1"
    commander: "npm:^2.12.2"
    es6-promisify: "npm:^5.0.0"
    eyes: "npm:^0.1.8"
    json-stringify-safe: "npm:^5.0.1"
    lodash: "npm:^4.17.11"
    uuid: "npm:^3.2.1"
  bin:
    jayson: ./bin/jayson.js
  checksum: 10/4497c0de1cc622c506ebbd937ccf5f4f9b86895aa1dcdad19ff09bbf99deacbbbd511884236cf47e01d02e963845960e0a8df090297d0b7226d712134fb98147
  languageName: node
  linkType: hard

"js-tokens@npm:^4.0.0":
  version: 4.0.0
  resolution: "js-tokens@npm:4.0.0"
  checksum: 10/af37d0d913fb56aec6dc0074c163cc71cd23c0b8aad5c2350747b6721d37ba118af35abdd8b33c47ec2800de07dedb16a527ca9c530ee004093e04958bd0cbf2
  languageName: node
  linkType: hard

"js-yaml@npm:^4.1.0":
  version: 4.1.0
  resolution: "js-yaml@npm:4.1.0"
  dependencies:
    argparse: "npm:^2.0.1"
  bin:
    js-yaml: bin/js-yaml.js
  checksum: 10/c138a34a3fd0d08ebaf71273ad4465569a483b8a639e0b118ff65698d257c2791d3199e3f303631f2cb98213fa7b5f5d6a4621fd0fff819421b990d30d967140
  languageName: node
  linkType: hard

"js-yaml@npm:~2.1.0":
  version: 2.1.3
  resolution: "js-yaml@npm:2.1.3"
  dependencies:
    argparse: "npm:~ 0.1.11"
    esprima: "npm:~ 1.0.2"
  bin:
    js-yaml: bin/js-yaml.js
  checksum: 10/5e59311627d725ffe3eba0c36208092079441ea35ceb8bcb9b0add0ac2868b1df577d82d37b352e451000d50b04896d8ee408ec1b18e5b864213a6e66919f421
  languageName: node
  linkType: hard

"js2xmlparser@npm:4.0.1":
  version: 4.0.1
  resolution: "js2xmlparser@npm:4.0.1"
  dependencies:
    xmlcreate: "npm:^2.0.3"
  checksum: 10/1951e175d3a01ef026cd08d91769d5a11072329b8b6cad1db37a84a72fde1ab6edcb72f4503d31cb3ef0091f43470190c50f94281f783bd202097d93e25a9439
  languageName: node
  linkType: hard

"js2xmlparser@npm:^4.0.0":
  version: 4.0.2
  resolution: "js2xmlparser@npm:4.0.2"
  dependencies:
    xmlcreate: "npm:^2.0.4"
  checksum: 10/42ccb1372844b6e1d9166254b01fe31d485a0e398fba4f2b095bcca081a2c2f4414b0bd4a32263cd20e01cee681684608255778034fec050e3f5929fd776936c
  languageName: node
  linkType: hard

"jsbn@npm:1.1.0":
  version: 1.1.0
  resolution: "jsbn@npm:1.1.0"
  checksum: 10/bebe7ae829bbd586ce8cbe83501dd8cb8c282c8902a8aeeed0a073a89dc37e8103b1244f3c6acd60278bcbfe12d93a3f83c9ac396868a3b3bbc3c5e5e3b648ef
  languageName: node
  linkType: hard

"jsbn@npm:~0.1.0":
  version: 0.1.1
  resolution: "jsbn@npm:0.1.1"
  checksum: 10/5450133242845100e694f0ef9175f44c012691a9b770b2571e677314e6f70600abb10777cdfc9a0c6a9f2ac6d134577403633de73e2fcd0f97875a67744e2d14
  languageName: node
  linkType: hard

"jsep@npm:^1.4.0":
  version: 1.4.0
  resolution: "jsep@npm:1.4.0"
  checksum: 10/935824fe6ac28fcff3cd13878f508f99f6c13e7f0f53ec9fca0d3db465e6dd15f8af030bcdc75a38b07c78359c656647435923a26aceb91607027021f00c17f2
  languageName: node
  linkType: hard

"jsesc@npm:^3.0.2":
  version: 3.1.0
  resolution: "jsesc@npm:3.1.0"
  bin:
    jsesc: bin/jsesc
  checksum: 10/20bd37a142eca5d1794f354db8f1c9aeb54d85e1f5c247b371de05d23a9751ecd7bd3a9c4fc5298ea6fa09a100dafb4190fa5c98c6610b75952c3487f3ce7967
  languageName: node
  linkType: hard

"json-bigint@npm:^1.0.0":
  version: 1.0.0
  resolution: "json-bigint@npm:1.0.0"
  dependencies:
    bignumber.js: "npm:^9.0.0"
  checksum: 10/cd3973b88e5706f8f89d2a9c9431f206ef385bd5c584db1b258891a5e6642507c32316b82745239088c697f5ddfe967351e1731f5789ba7855aed56ad5f70e1f
  languageName: node
  linkType: hard

"json-buffer@npm:3.0.1":
  version: 3.0.1
  resolution: "json-buffer@npm:3.0.1"
  checksum: 10/82876154521b7b68ba71c4f969b91572d1beabadd87bd3a6b236f85fbc7dc4695089191ed60bb59f9340993c51b33d479f45b6ba9f3548beb519705281c32c3c
  languageName: node
  linkType: hard

"json-buffer@npm:~2.0.4":
  version: 2.0.11
  resolution: "json-buffer@npm:2.0.11"
  checksum: 10/0183618c645a50ef30e44f766db9da69d8074202977367da6fc7cb6dd04d48c844e2624a995d6c3c0c7bf2b1cfe3447fa7736d411701cd8a060bb956a290047d
  languageName: node
  linkType: hard

"json-schema-traverse@npm:^0.4.1":
  version: 0.4.1
  resolution: "json-schema-traverse@npm:0.4.1"
  checksum: 10/7486074d3ba247769fda17d5181b345c9fb7d12e0da98b22d1d71a5db9698d8b4bd900a3ec1a4ffdd60846fc2556274a5c894d0c48795f14cb03aeae7b55260b
  languageName: node
  linkType: hard

"json-schema@npm:0.4.0":
  version: 0.4.0
  resolution: "json-schema@npm:0.4.0"
  checksum: 10/8b3b64eff4a807dc2a3045b104ed1b9335cd8d57aa74c58718f07f0f48b8baa3293b00af4dcfbdc9144c3aafea1e97982cc27cc8e150fc5d93c540649507a458
  languageName: node
  linkType: hard

"json-stable-stringify-without-jsonify@npm:^1.0.1":
  version: 1.0.1
  resolution: "json-stable-stringify-without-jsonify@npm:1.0.1"
  checksum: 10/12786c2e2f22c27439e6db0532ba321f1d0617c27ad8cb1c352a0e9249a50182fd1ba8b52a18899291604b0c32eafa8afd09e51203f19109a0537f68db2b652d
  languageName: node
  linkType: hard

"json-stringify-safe@npm:^5.0.1, json-stringify-safe@npm:~5.0.1":
  version: 5.0.1
  resolution: "json-stringify-safe@npm:5.0.1"
  checksum: 10/59169a081e4eeb6f9559ae1f938f656191c000e0512aa6df9f3c8b2437a4ab1823819c6b9fd1818a4e39593ccfd72e9a051fdd3e2d1e340ed913679e888ded8c
  languageName: node
  linkType: hard

"json5@npm:^2.2.3":
  version: 2.2.3
  resolution: "json5@npm:2.2.3"
  bin:
    json5: lib/cli.js
  checksum: 10/1db67b853ff0de3534085d630691d3247de53a2ed1390ba0ddff681ea43e9b3e30ecbdb65c5e9aab49435e44059c23dbd6fee8ee619419ba37465bb0dd7135da
  languageName: node
  linkType: hard

"jsonc-eslint-parser@npm:^2.4.0":
  version: 2.4.0
  resolution: "jsonc-eslint-parser@npm:2.4.0"
  dependencies:
    acorn: "npm:^8.5.0"
    eslint-visitor-keys: "npm:^3.0.0"
    espree: "npm:^9.0.0"
    semver: "npm:^7.3.5"
  checksum: 10/bd1d41c852c3488414605a1754617aa7c240ed6730a25a7fd7fb76473e92efdc5ba1728ad3f08f8069de3a19abf1fd275c2b145eb51e2f7f6ca293c8105e1ffe
  languageName: node
  linkType: hard

"jsonparse@npm:^1.2.0":
  version: 1.3.1
  resolution: "jsonparse@npm:1.3.1"
  checksum: 10/24531e956f0f19d79e22c157cebd81b37af3486ae22f9bc1028f8c2a4d1b70df48b168ff86f8568d9c2248182de9b6da9f50f685d5e4b9d1d2d339d2a29d15bc
  languageName: node
  linkType: hard

"jsonpath-plus@npm:^10.0.0":
  version: 10.3.0
  resolution: "jsonpath-plus@npm:10.3.0"
  dependencies:
    "@jsep-plugin/assignment": "npm:^1.3.0"
    "@jsep-plugin/regex": "npm:^1.0.4"
    jsep: "npm:^1.4.0"
  bin:
    jsonpath: bin/jsonpath-cli.js
    jsonpath-plus: bin/jsonpath-cli.js
  checksum: 10/082302334414c7c5ab0cc8239563118f7f14bb2949d001b009f436491d00f94a7a293eed3eaf61ffdaf72f6fda9d25198a4280c4f68a4c403154ca7ed2bd0dc9
  languageName: node
  linkType: hard

"jsprim@npm:^1.2.2":
  version: 1.4.2
  resolution: "jsprim@npm:1.4.2"
  dependencies:
    assert-plus: "npm:1.0.0"
    extsprintf: "npm:1.3.0"
    json-schema: "npm:0.4.0"
    verror: "npm:1.10.0"
  checksum: 10/df2bf234eab1b5078d01bcbff3553d50a243f7b5c10a169745efeda6344d62798bd1d85bcca6a8446f3b5d0495e989db45f9de8dae219f0f9796e70e0c776089
  languageName: node
  linkType: hard

"jwa@npm:^2.0.0":
  version: 2.0.1
  resolution: "jwa@npm:2.0.1"
  dependencies:
    buffer-equal-constant-time: "npm:^1.0.1"
    ecdsa-sig-formatter: "npm:1.0.11"
    safe-buffer: "npm:^5.0.1"
  checksum: 10/b04312a1de85f912b96aa3a7211717b8336945fab5b4f7cbc7800f4c80934060c0a3111576fad8d76e41ad62887d6da4b21fd4c47e45c174197f8be7dc0c1694
  languageName: node
  linkType: hard

"jws@npm:^4.0.0":
  version: 4.0.0
  resolution: "jws@npm:4.0.0"
  dependencies:
    jwa: "npm:^2.0.0"
    safe-buffer: "npm:^5.0.1"
  checksum: 10/1d15f4cdea376c6bd6a81002bd2cb0bf3d51d83da8f0727947b5ba3e10cf366721b8c0d099bf8c1eb99eb036e2c55e5fd5efd378ccff75a2b4e0bd10002348b9
  languageName: node
  linkType: hard

"keyv@npm:^4.0.0, keyv@npm:^4.5.4":
  version: 4.5.4
  resolution: "keyv@npm:4.5.4"
  dependencies:
    json-buffer: "npm:3.0.1"
  checksum: 10/167eb6ef64cc84b6fa0780ee50c9de456b422a1e18802209234f7c2cf7eae648c7741f32e50d7e24ccb22b24c13154070b01563d642755b156c357431a191e75
  languageName: node
  linkType: hard

"lcid@npm:^3.0.0":
  version: 3.1.1
  resolution: "lcid@npm:3.1.1"
  dependencies:
    invert-kv: "npm:^3.0.0"
  checksum: 10/ac2fc533882a4a62ba2ccd522e1400618c8e2e417a488fcc31c5474bb8bd62a618216d3e39f6838d15ab0f6f4536b4f60435f491f1b93c9eef6d9d6506c5c9f1
  languageName: node
  linkType: hard

"levenshtein-edit-distance@npm:^2.0.0":
  version: 2.0.5
  resolution: "levenshtein-edit-distance@npm:2.0.5"
  bin:
    levenshtein-edit-distance: cli.js
  checksum: 10/50618c01cd0c9bae6d4371d75af62c17c25a8f91bfd8d06400315b8b15976900cff951b48e102e074e9c5c6758260fff1675cfad186732afe124a5708e1032fd
  languageName: node
  linkType: hard

"levn@npm:^0.4.1":
  version: 0.4.1
  resolution: "levn@npm:0.4.1"
  dependencies:
    prelude-ls: "npm:^1.2.1"
    type-check: "npm:~0.4.0"
  checksum: 10/2e4720ff79f21ae08d42374b0a5c2f664c5be8b6c8f565bb4e1315c96ed3a8acaa9de788ffed82d7f2378cf36958573de07ef92336cb5255ed74d08b8318c9ee
  languageName: node
  linkType: hard

"libphonenumber-js@npm:^1.12.7, libphonenumber-js@npm:^1.12.8":
  version: 1.12.8
  resolution: "libphonenumber-js@npm:1.12.8"
  checksum: 10/476e5268f5a86509de80ed5e573afbd2aff457d88cbe116208d167563f538705d3b29c9cc45e8d58dc6b5d00babc348443e49239c367c4e2e26e8c71e96372bc
  languageName: node
  linkType: hard

"limiter@npm:^3.0.0":
  version: 3.0.0
  resolution: "limiter@npm:3.0.0"
  checksum: 10/d45865d74b147b715dc2077cf9db1ed5c05c667f303b2ab69d60c65c6cbef0de099f4124065828294a02fcb093dfb2309034bbd382d1b2528f10988030ea8bd1
  languageName: node
  linkType: hard

"locale@npm:^0.1.0":
  version: 0.1.0
  resolution: "locale@npm:0.1.0"
  checksum: 10/486e6a604b9ce757be5cbc9cb9d3c1aade13e69b28a8fc9063eaeebdacae6c2b18f660599c409e5f563cf07a530e12cfab75846d99a2b58a237d35285887551d
  languageName: node
  linkType: hard

"locate-path@npm:^5.0.0":
  version: 5.0.0
  resolution: "locate-path@npm:5.0.0"
  dependencies:
    p-locate: "npm:^4.1.0"
  checksum: 10/83e51725e67517287d73e1ded92b28602e3ae5580b301fe54bfb76c0c723e3f285b19252e375712316774cf52006cb236aed5704692c32db0d5d089b69696e30
  languageName: node
  linkType: hard

"locate-path@npm:^6.0.0":
  version: 6.0.0
  resolution: "locate-path@npm:6.0.0"
  dependencies:
    p-locate: "npm:^5.0.0"
  checksum: 10/72eb661788a0368c099a184c59d2fee760b3831c9c1c33955e8a19ae4a21b4116e53fa736dc086cdeb9fce9f7cc508f2f92d2d3aae516f133e16a2bb59a39f5a
  languageName: node
  linkType: hard

"lodash.defaults@npm:^4.2.0":
  version: 4.2.0
  resolution: "lodash.defaults@npm:4.2.0"
  checksum: 10/6a2a9ea5ad7585aff8d76836c9e1db4528e5f5fa50fc4ad81183152ba8717d83aef8aec4fa88bf3417ed946fd4b4358f145ee08fbc77fb82736788714d3e12db
  languageName: node
  linkType: hard

"lodash.isarguments@npm:^3.1.0":
  version: 3.1.0
  resolution: "lodash.isarguments@npm:3.1.0"
  checksum: 10/e5186d5fe0384dcb0652501d9d04ebb984863ebc9c9faa2d4b9d5dfd81baef9ffe8e2887b9dc471d62ed092bc0788e5f1d42e45c72457a2884bbb54ac132ed92
  languageName: node
  linkType: hard

"lodash.merge@npm:^4.6.2":
  version: 4.6.2
  resolution: "lodash.merge@npm:4.6.2"
  checksum: 10/d0ea2dd0097e6201be083865d50c3fb54fbfbdb247d9cc5950e086c991f448b7ab0cdab0d57eacccb43473d3f2acd21e134db39f22dac2d6c9ba6bf26978e3d6
  languageName: node
  linkType: hard

"lodash@npm:^4.17.10, lodash@npm:^4.17.11, lodash@npm:^4.17.14, lodash@npm:^4.17.20, lodash@npm:^4.17.21":
  version: 4.17.21
  resolution: "lodash@npm:4.17.21"
  checksum: 10/c08619c038846ea6ac754abd6dd29d2568aa705feb69339e836dfa8d8b09abbb2f859371e86863eda41848221f9af43714491467b5b0299122431e202bb0c532
  languageName: node
  linkType: hard

"loopback-boot@npm:^3.3.1":
  version: 3.3.1
  resolution: "loopback-boot@npm:3.3.1"
  dependencies:
    async: "npm:^2.4.0"
    bluebird: "npm:^3.5.3"
    commondir: "npm:^1.0.1"
    debug: "npm:^4.1.1"
    lodash: "npm:^4.17.11"
    semver: "npm:^5.1.0"
    strong-globalize: "npm:^4.1.1"
    toposort: "npm:^2.0.2"
  checksum: 10/af9acea229a7c8c6f5fa34ffe7530275b5917a1787c1e5f7a7f290d6de9060e6aa07673123aca0069aabc4b55ef1a5b07000784a4c799d536055219dfa800bb8
  languageName: node
  linkType: hard

"loopback-component-explorer@npm:^6.5.1":
  version: 6.5.1
  resolution: "loopback-component-explorer@npm:6.5.1"
  dependencies:
    debug: "npm:^3.1.0"
    lodash: "npm:^4.17.11"
    loopback-swagger: "npm:^5.0.0"
    strong-globalize: "npm:^4.1.1"
    swagger-ui: "npm:^2.2.5"
  checksum: 10/65cde8e52e3c6834ac8fdcdafc612188280c4b4511e437dd2995786aca8d740791dff39bcd14b913707ae0be6ad49e7414251c3d147ca3c3c82f589af2edcecf
  languageName: node
  linkType: hard

"loopback-connector-mongodb@github:perkd/loopback-connector-mongodb#semver:5.6.1":
  version: 5.6.0
  resolution: "loopback-connector-mongodb@https://github.com/perkd/loopback-connector-mongodb.git#commit=79af69e289d81d561789d02fdd331dba4aeef2e2"
  dependencies:
    async: "npm:^3.1.0"
    bson: "npm:^1.0.6"
    debug: "npm:^4.1.0"
    loopback-connector: "npm:^5.0.0"
    mongodb: "npm:^3.2.4"
    strong-globalize: "npm:^6.0.0"
  checksum: 10/4e85836c8e3958db87903335e46cf16a0c69d723435df5933f1e8aec87992e4de63b68c6acce19323054b87621c363524dd2c1d3b9b4fbede08ae6196dbf7065
  languageName: node
  linkType: hard

"loopback-connector-remote@npm:^3.4.1":
  version: 3.4.1
  resolution: "loopback-connector-remote@npm:3.4.1"
  dependencies:
    loopback-datasource-juggler: "npm:^3.0.0"
    strong-remoting: "npm:^3.0.0"
  checksum: 10/af6ff4fad188b277691041e7fd10030cee380e29d7f28dbaf6017bccfda14e0ba4f66f84013e52aada36d83821eba092bfd6a2527f63b292b053fbe8002ffb4c
  languageName: node
  linkType: hard

"loopback-connector-rest@npm:^5.0.6":
  version: 5.0.6
  resolution: "loopback-connector-rest@npm:5.0.6"
  dependencies:
    debug: "npm:^4.1.0"
    jsonpath-plus: "npm:^10.0.0"
    lodash: "npm:^4.17.11"
    methods: "npm:^1.1.1"
    mime: "npm:^2.3.1"
    postman-request: "npm:^2.88.1-postman.33"
    qs: "npm:^6.1.0"
    strong-globalize: "npm:^6.0.5"
    traverse: "npm:^0.6.6"
  checksum: 10/7dbcd213102df2776258e3daffb7fc7a9c0186ffe192f5686038ea604fe6715b89803e6d94dec7993be3ca173020dcd707aa2c4d3541347509fd2b2f53a97c7b
  languageName: node
  linkType: hard

"loopback-connector@npm:^5.0.0":
  version: 5.3.3
  resolution: "loopback-connector@npm:5.3.3"
  dependencies:
    async: "npm:^3.2.4"
    bluebird: "npm:^3.7.2"
    debug: "npm:^4.3.4"
    msgpack5: "npm:^4.5.1"
    strong-globalize: "npm:^6.0.5"
    uuid: "npm:^9.0.0"
  checksum: 10/98383ce4c2e3f7cb4ac9d69cdf19e1dcd5310bd4ee2953d1d2861f6e3c1fe23606866b35596185cc35bd456209209e7488066d507ca44836f812241f87941315
  languageName: node
  linkType: hard

"loopback-connector@npm:^6.2.4":
  version: 6.2.4
  resolution: "loopback-connector@npm:6.2.4"
  dependencies:
    async: "npm:^3.2.6"
    bluebird: "npm:^3.7.2"
    debug: "npm:^4.4.0"
    msgpack5: "npm:^4.5.1"
    strong-globalize: "npm:^6.0.6"
    uuid: "npm:^11.1.0"
  checksum: 10/aa9bbf53d75e700f545a71fccc50608482ffd05d3e587627bfa6be32448cdf82bd7289b0900e42cc6fc06ed3317e8250737566ea078017431582a5c24de6c4bf
  languageName: node
  linkType: hard

"loopback-datasource-juggler@github:perkd/loopback-datasource-juggler#semver:5.2.0":
  version: 5.2.0
  resolution: "loopback-datasource-juggler@https://github.com/perkd/loopback-datasource-juggler.git#commit=7a68e3ea376cf96c936fd43677af576f3d5512dc"
  dependencies:
    async: "npm:^3.2.6"
    change-case: "npm:4.1.2"
    debug: "npm:^4.4.1"
    depd: "npm:^2.0.0"
    inflection: "npm:^3.0.2"
    lodash: "npm:^4.17.21"
    loopback-connector: "npm:^6.2.4"
    minimatch: "npm:^10.0.1"
    nanoid: "npm:3.3.8"
    neotraverse: "npm:^0.6.18"
    qs: "npm:^6.14.0"
    strong-globalize: "npm:^6.0.6"
    uuid: "npm:^11.1.0"
  checksum: 10/40e400e8a0d89eb9380646503f52aff15f6378801f3c7e49c92843704c4c353a13c40fa93d49808407c5827cd52a2f66bfad3c9b87ca7a2d02c4a319495349bb
  languageName: node
  linkType: hard

"loopback-datatype-geopoint@npm:^1.0.0":
  version: 1.0.0
  resolution: "loopback-datatype-geopoint@npm:1.0.0"
  checksum: 10/79598b6c8f9d1c43a8ff9d1300495f7b8bb60d0f9d75b101910e948e6425347ca76007e5a737e6848ef6ae631585cd7fd2f77c07deece354542289c1773d3683
  languageName: node
  linkType: hard

"loopback-filters@npm:^1.1.1":
  version: 1.1.1
  resolution: "loopback-filters@npm:1.1.1"
  dependencies:
    debug: "npm:^3.1.0"
  checksum: 10/8e979f2dae4328f64acfa301525bd8f1ea06350696e290d24ba6451acc61aed6a61981e1b9a56952cb11637fed1f0aeeeb01a93df18cfc32ff6d644d4efd279b
  languageName: node
  linkType: hard

"loopback-phase@npm:3.4.0, loopback-phase@npm:^3.4.0":
  version: 3.4.0
  resolution: "loopback-phase@npm:3.4.0"
  dependencies:
    async: "npm:^2.6.1"
    debug: "npm:^3.1.0"
    strong-globalize: "npm:^4.1.1"
  checksum: 10/8992b1a5516e560f418f09892857578431d1ef2f563d3ccf641b5881cef6b6fc6aa07d54eff43c0110d33746078dd56dd5f755aae803855840cdee08c275dfa8
  languageName: node
  linkType: hard

"loopback-swagger@npm:^5.0.0":
  version: 5.9.0
  resolution: "loopback-swagger@npm:5.9.0"
  dependencies:
    async: "npm:^2.1.4"
    debug: "npm:^3.1.0"
    ejs: "npm:^2.5.5"
    lodash: "npm:^4.17.11"
    strong-globalize: "npm:^4.1.1"
  checksum: 10/735b7ca148e27a8ca7da0e58d7656ce5c74e0c50e7a5780f3ccff0f07b97c18c0406b5be36837bbe3422d4db90b621d421980b6645d5ba17d3279a37f2bca04a
  languageName: node
  linkType: hard

"loopback@github:perkd/loopback#semver:^3.34.2":
  version: 3.34.2
  resolution: "loopback@https://github.com/perkd/loopback.git#commit=240d14f6579782abadd28505d42b71a347d36d71"
  dependencies:
    bcryptjs: "npm:^3.0.2"
    body-parser: "npm:^2.2.0"
    canonical-json: "npm:0.0.4"
    debug: "npm:^4.4.0"
    depd: "npm:^2.0.0"
    ejs: "npm:^3.1.10"
    express: "npm:4.21.2"
    inflection: "npm:2.0.1"
    isemail: "npm:^3.2.0"
    loopback-connector-remote: "npm:^3.4.1"
    loopback-datasource-juggler: "github:perkd/loopback-datasource-juggler#semver:^5.1.5"
    loopback-filters: "npm:^1.1.1"
    loopback-phase: "npm:3.4.0"
    nodemailer: "npm:^6.10.1"
    nodemailer-direct-transport: "npm:^3.3.2"
    nodemailer-stub-transport: "npm:^1.1.0"
    serve-favicon: "npm:^2.5.0"
    stable: "npm:^0.1.8"
    strong-globalize: "npm:6.0.5"
    strong-remoting: "github:perkd/strong-remoting#semver:^3.20.2"
    uid2: "npm:1.0.0"
    underscore.string: "npm:^3.3.6"
  checksum: 10/dc3dfe185de929d2e57beebcc19f3a760d4c193ca9e223cd424411f754e04ac40c5ba94562f020817fcd47c8b26fa8bc6f9eee7d0cf7675e3754a77c1de49eb8
  languageName: node
  linkType: hard

"lower-case@npm:^2.0.2":
  version: 2.0.2
  resolution: "lower-case@npm:2.0.2"
  dependencies:
    tslib: "npm:^2.0.3"
  checksum: 10/83a0a5f159ad7614bee8bf976b96275f3954335a84fad2696927f609ddae902802c4f3312d86668722e668bef41400254807e1d3a7f2e8c3eede79691aa1f010
  languageName: node
  linkType: hard

"lowercase-keys@npm:^2.0.0":
  version: 2.0.0
  resolution: "lowercase-keys@npm:2.0.0"
  checksum: 10/1c233d2da35056e8c49fae8097ee061b8c799b2f02e33c2bf32f9913c7de8fb481ab04dab7df35e94156c800f5f34e99acbf32b21781d87c3aa43ef7b748b79e
  languageName: node
  linkType: hard

"lru-cache@npm:^10.0.1, lru-cache@npm:^10.2.0":
  version: 10.4.3
  resolution: "lru-cache@npm:10.4.3"
  checksum: 10/e6e90267360476720fa8e83cc168aa2bf0311f3f2eea20a6ba78b90a885ae72071d9db132f40fda4129c803e7dcec3a6b6a6fbb44ca90b081630b810b5d6a41a
  languageName: node
  linkType: hard

"lru-cache@npm:^11.1.0":
  version: 11.1.0
  resolution: "lru-cache@npm:11.1.0"
  checksum: 10/5011011675ca98428902de774d0963b68c3a193cd959347cb63b781dad4228924124afab82159fd7b8b4db18285d9aff462b877b8f6efd2b41604f806c1d9db4
  languageName: node
  linkType: hard

"lru-cache@npm:^5.1.1":
  version: 5.1.1
  resolution: "lru-cache@npm:5.1.1"
  dependencies:
    yallist: "npm:^3.0.2"
  checksum: 10/951d2673dcc64a7fb888bf3d13bc2fdf923faca97d89cdb405ba3dfff77e2b26e5798d405e78fcd7094c9e7b8b4dab2ddc5a4f8a11928af24a207b7c738ca3f8
  languageName: node
  linkType: hard

"make-fetch-happen@npm:^14.0.3":
  version: 14.0.3
  resolution: "make-fetch-happen@npm:14.0.3"
  dependencies:
    "@npmcli/agent": "npm:^3.0.0"
    cacache: "npm:^19.0.1"
    http-cache-semantics: "npm:^4.1.1"
    minipass: "npm:^7.0.2"
    minipass-fetch: "npm:^4.0.0"
    minipass-flush: "npm:^1.0.5"
    minipass-pipeline: "npm:^1.2.4"
    negotiator: "npm:^1.0.0"
    proc-log: "npm:^5.0.0"
    promise-retry: "npm:^2.0.1"
    ssri: "npm:^12.0.0"
  checksum: 10/fce0385840b6d86b735053dfe941edc2dd6468fda80fe74da1eeff10cbd82a75760f406194f2bc2fa85b99545b2bc1f84c08ddf994b21830775ba2d1a87e8bdf
  languageName: node
  linkType: hard

"map-age-cleaner@npm:^0.1.3":
  version: 0.1.3
  resolution: "map-age-cleaner@npm:0.1.3"
  dependencies:
    p-defer: "npm:^1.0.0"
  checksum: 10/cb2804a5bcb3cbdfe4b59066ea6d19f5e7c8c196cd55795ea4c28f792b192e4c442426ae52524e5e1acbccf393d3bddacefc3d41f803e66453f6c4eda3650bc1
  languageName: node
  linkType: hard

"math-intrinsics@npm:^1.1.0":
  version: 1.1.0
  resolution: "math-intrinsics@npm:1.1.0"
  checksum: 10/11df2eda46d092a6035479632e1ec865b8134bdfc4bd9e571a656f4191525404f13a283a515938c3a8de934dbfd9c09674d9da9fa831e6eb7e22b50b197d2edd
  languageName: node
  linkType: hard

"md5@npm:^2.3.0":
  version: 2.3.0
  resolution: "md5@npm:2.3.0"
  dependencies:
    charenc: "npm:0.0.2"
    crypt: "npm:0.0.2"
    is-buffer: "npm:~1.1.6"
  checksum: 10/88dce9fb8df1a084c2385726dcc18c7f54e0b64c261b5def7cdfe4928c4ee1cd68695c34108b4fab7ecceb05838c938aa411c6143df9fdc0026c4ddb4e4e72fa
  languageName: node
  linkType: hard

"media-typer@npm:0.3.0":
  version: 0.3.0
  resolution: "media-typer@npm:0.3.0"
  checksum: 10/38e0984db39139604756903a01397e29e17dcb04207bb3e081412ce725ab17338ecc47220c1b186b6bbe79a658aad1b0d41142884f5a481f36290cdefbe6aa46
  languageName: node
  linkType: hard

"media-typer@npm:^1.1.0":
  version: 1.1.0
  resolution: "media-typer@npm:1.1.0"
  checksum: 10/a58dd60804df73c672942a7253ccc06815612326dc1c0827984b1a21704466d7cde351394f47649e56cf7415e6ee2e26e000e81b51b3eebb5a93540e8bf93cbd
  languageName: node
  linkType: hard

"mem@npm:^5.0.0":
  version: 5.1.1
  resolution: "mem@npm:5.1.1"
  dependencies:
    map-age-cleaner: "npm:^0.1.3"
    mimic-fn: "npm:^2.1.0"
    p-is-promise: "npm:^2.1.0"
  checksum: 10/8dc22ded29d7c0a9aeb4ae2a69ad232d32b117a5a8842d7997b7a37aa943dc306326ea24616b42de3d819e9dad0dbba083aa25dd5fe55ca1dfa654d89e5a0969
  languageName: node
  linkType: hard

"memory-pager@npm:^1.0.2":
  version: 1.5.0
  resolution: "memory-pager@npm:1.5.0"
  checksum: 10/ffe3461b6aa4e400138d1d9c59890b1cbeae3256592a0dfae49577f4bec93952de65f31f682f0b15451d2a7cf018be775ed1e1411705e45514b14fb70883a66b
  languageName: node
  linkType: hard

"merge-descriptors@npm:1.0.3":
  version: 1.0.3
  resolution: "merge-descriptors@npm:1.0.3"
  checksum: 10/52117adbe0313d5defa771c9993fe081e2d2df9b840597e966aadafde04ae8d0e3da46bac7ca4efc37d4d2b839436582659cd49c6a43eacb3fe3050896a105d1
  languageName: node
  linkType: hard

"merge-descriptors@npm:^2.0.0":
  version: 2.0.0
  resolution: "merge-descriptors@npm:2.0.0"
  checksum: 10/e383332e700a94682d0125a36c8be761142a1320fc9feeb18e6e36647c9edf064271645f5669b2c21cf352116e561914fd8aa831b651f34db15ef4038c86696a
  languageName: node
  linkType: hard

"merge-stream@npm:^2.0.0":
  version: 2.0.0
  resolution: "merge-stream@npm:2.0.0"
  checksum: 10/6fa4dcc8d86629705cea944a4b88ef4cb0e07656ebf223fa287443256414283dd25d91c1cd84c77987f2aec5927af1a9db6085757cb43d90eb170ebf4b47f4f4
  languageName: node
  linkType: hard

"methods@npm:^1.1.1, methods@npm:~1.1.2":
  version: 1.1.2
  resolution: "methods@npm:1.1.2"
  checksum: 10/a385dd974faa34b5dd021b2bbf78c722881bf6f003bfe6d391d7da3ea1ed625d1ff10ddd13c57531f628b3e785be38d3eed10ad03cebd90b76932413df9a1820
  languageName: node
  linkType: hard

"mime-db@npm:1.52.0":
  version: 1.52.0
  resolution: "mime-db@npm:1.52.0"
  checksum: 10/54bb60bf39e6f8689f6622784e668a3d7f8bed6b0d886f5c3c446cb3284be28b30bf707ed05d0fe44a036f8469976b2629bbea182684977b084de9da274694d7
  languageName: node
  linkType: hard

"mime-db@npm:>= 1.43.0 < 2, mime-db@npm:^1.54.0":
  version: 1.54.0
  resolution: "mime-db@npm:1.54.0"
  checksum: 10/9e7834be3d66ae7f10eaa69215732c6d389692b194f876198dca79b2b90cbf96688d9d5d05ef7987b20f749b769b11c01766564264ea5f919c88b32a29011311
  languageName: node
  linkType: hard

"mime-types@npm:^2.1.12, mime-types@npm:~2.1.19, mime-types@npm:~2.1.24, mime-types@npm:~2.1.34":
  version: 2.1.35
  resolution: "mime-types@npm:2.1.35"
  dependencies:
    mime-db: "npm:1.52.0"
  checksum: 10/89aa9651b67644035de2784a6e665fc685d79aba61857e02b9c8758da874a754aed4a9aced9265f5ed1171fd934331e5516b84a7f0218031b6fa0270eca1e51a
  languageName: node
  linkType: hard

"mime-types@npm:^3.0.0, mime-types@npm:^3.0.1":
  version: 3.0.1
  resolution: "mime-types@npm:3.0.1"
  dependencies:
    mime-db: "npm:^1.54.0"
  checksum: 10/fa1d3a928363723a8046c346d87bf85d35014dae4285ad70a3ff92bd35957992b3094f8417973cfe677330916c6ef30885109624f1fb3b1e61a78af509dba120
  languageName: node
  linkType: hard

"mime@npm:1.6.0":
  version: 1.6.0
  resolution: "mime@npm:1.6.0"
  bin:
    mime: cli.js
  checksum: 10/b7d98bb1e006c0e63e2c91b590fe1163b872abf8f7ef224d53dd31499c2197278a6d3d0864c45239b1a93d22feaf6f9477e9fc847eef945838150b8c02d03170
  languageName: node
  linkType: hard

"mime@npm:^2.3.1":
  version: 2.6.0
  resolution: "mime@npm:2.6.0"
  bin:
    mime: cli.js
  checksum: 10/7da117808b5cd0203bb1b5e33445c330fe213f4d8ee2402a84d62adbde9716ca4fb90dd6d9ab4e77a4128c6c5c24a9c4c9f6a4d720b095b1b342132d02dba58d
  languageName: node
  linkType: hard

"mimic-fn@npm:^2.1.0":
  version: 2.1.0
  resolution: "mimic-fn@npm:2.1.0"
  checksum: 10/d2421a3444848ce7f84bd49115ddacff29c15745db73f54041edc906c14b131a38d05298dae3081667627a59b2eb1ca4b436ff2e1b80f69679522410418b478a
  languageName: node
  linkType: hard

"mimic-response@npm:^1.0.0":
  version: 1.0.1
  resolution: "mimic-response@npm:1.0.1"
  checksum: 10/034c78753b0e622bc03c983663b1cdf66d03861050e0c8606563d149bc2b02d63f62ce4d32be4ab50d0553ae0ffe647fc34d1f5281184c6e1e8cf4d85e8d9823
  languageName: node
  linkType: hard

"mimic-response@npm:^3.1.0":
  version: 3.1.0
  resolution: "mimic-response@npm:3.1.0"
  checksum: 10/7e719047612411fe071332a7498cf0448bbe43c485c0d780046c76633a771b223ff49bd00267be122cedebb897037fdb527df72335d0d0f74724604ca70b37ad
  languageName: node
  linkType: hard

"minimatch@npm:^10.0.1":
  version: 10.0.1
  resolution: "minimatch@npm:10.0.1"
  dependencies:
    brace-expansion: "npm:^2.0.1"
  checksum: 10/082e7ccbc090d5f8c4e4e029255d5a1d1e3af37bda837da2b8b0085b1503a1210c91ac90d9ebfe741d8a5f286ece820a1abb4f61dc1f82ce602a055d461d93f3
  languageName: node
  linkType: hard

"minimatch@npm:^3.1.1, minimatch@npm:^3.1.2":
  version: 3.1.2
  resolution: "minimatch@npm:3.1.2"
  dependencies:
    brace-expansion: "npm:^1.1.7"
  checksum: 10/e0b25b04cd4ec6732830344e5739b13f8690f8a012d73445a4a19fbc623f5dd481ef7a5827fde25954cd6026fede7574cc54dc4643c99d6c6b653d6203f94634
  languageName: node
  linkType: hard

"minimatch@npm:^5.0.1":
  version: 5.1.6
  resolution: "minimatch@npm:5.1.6"
  dependencies:
    brace-expansion: "npm:^2.0.1"
  checksum: 10/126b36485b821daf96d33b5c821dac600cc1ab36c87e7a532594f9b1652b1fa89a1eebcaad4dff17c764dce1a7ac1531327f190fed5f97d8f6e5f889c116c429
  languageName: node
  linkType: hard

"minimatch@npm:^9.0.4, minimatch@npm:^9.0.5":
  version: 9.0.5
  resolution: "minimatch@npm:9.0.5"
  dependencies:
    brace-expansion: "npm:^2.0.1"
  checksum: 10/dd6a8927b063aca6d910b119e1f2df6d2ce7d36eab91de83167dd136bb85e1ebff97b0d3de1cb08bd1f7e018ca170b4962479fefab5b2a69e2ae12cb2edc8348
  languageName: node
  linkType: hard

"minipass-collect@npm:^2.0.1":
  version: 2.0.1
  resolution: "minipass-collect@npm:2.0.1"
  dependencies:
    minipass: "npm:^7.0.3"
  checksum: 10/b251bceea62090f67a6cced7a446a36f4cd61ee2d5cea9aee7fff79ba8030e416327a1c5aa2908dc22629d06214b46d88fdab8c51ac76bacbf5703851b5ad342
  languageName: node
  linkType: hard

"minipass-fetch@npm:^4.0.0":
  version: 4.0.1
  resolution: "minipass-fetch@npm:4.0.1"
  dependencies:
    encoding: "npm:^0.1.13"
    minipass: "npm:^7.0.3"
    minipass-sized: "npm:^1.0.3"
    minizlib: "npm:^3.0.1"
  dependenciesMeta:
    encoding:
      optional: true
  checksum: 10/7ddfebdbb87d9866e7b5f7eead5a9e3d9d507992af932a11d275551f60006cf7d9178e66d586dbb910894f3e3458d27c0ddf93c76e94d49d0a54a541ddc1263d
  languageName: node
  linkType: hard

"minipass-flush@npm:^1.0.5":
  version: 1.0.5
  resolution: "minipass-flush@npm:1.0.5"
  dependencies:
    minipass: "npm:^3.0.0"
  checksum: 10/56269a0b22bad756a08a94b1ffc36b7c9c5de0735a4dd1ab2b06c066d795cfd1f0ac44a0fcae13eece5589b908ecddc867f04c745c7009be0b566421ea0944cf
  languageName: node
  linkType: hard

"minipass-pipeline@npm:^1.2.4":
  version: 1.2.4
  resolution: "minipass-pipeline@npm:1.2.4"
  dependencies:
    minipass: "npm:^3.0.0"
  checksum: 10/b14240dac0d29823c3d5911c286069e36d0b81173d7bdf07a7e4a91ecdef92cdff4baaf31ea3746f1c61e0957f652e641223970870e2353593f382112257971b
  languageName: node
  linkType: hard

"minipass-sized@npm:^1.0.3":
  version: 1.0.3
  resolution: "minipass-sized@npm:1.0.3"
  dependencies:
    minipass: "npm:^3.0.0"
  checksum: 10/40982d8d836a52b0f37049a0a7e5d0f089637298e6d9b45df9c115d4f0520682a78258905e5c8b180fb41b593b0a82cc1361d2c74b45f7ada66334f84d1ecfdd
  languageName: node
  linkType: hard

"minipass@npm:^3.0.0":
  version: 3.3.6
  resolution: "minipass@npm:3.3.6"
  dependencies:
    yallist: "npm:^4.0.0"
  checksum: 10/a5c6ef069f70d9a524d3428af39f2b117ff8cd84172e19b754e7264a33df460873e6eb3d6e55758531580970de50ae950c496256bb4ad3691a2974cddff189f0
  languageName: node
  linkType: hard

"minipass@npm:^5.0.0 || ^6.0.2 || ^7.0.0, minipass@npm:^7.0.2, minipass@npm:^7.0.3, minipass@npm:^7.0.4, minipass@npm:^7.1.2":
  version: 7.1.2
  resolution: "minipass@npm:7.1.2"
  checksum: 10/c25f0ee8196d8e6036661104bacd743785b2599a21de5c516b32b3fa2b83113ac89a2358465bc04956baab37ffb956ae43be679b2262bf7be15fce467ccd7950
  languageName: node
  linkType: hard

"minizlib@npm:^3.0.1":
  version: 3.0.2
  resolution: "minizlib@npm:3.0.2"
  dependencies:
    minipass: "npm:^7.1.2"
  checksum: 10/c075bed1594f68dcc8c35122333520112daefd4d070e5d0a228bd4cf5580e9eed3981b96c0ae1d62488e204e80fd27b2b9d0068ca9a5ef3993e9565faf63ca41
  languageName: node
  linkType: hard

"mkdirp@npm:^1.0.4":
  version: 1.0.4
  resolution: "mkdirp@npm:1.0.4"
  bin:
    mkdirp: bin/cmd.js
  checksum: 10/d71b8dcd4b5af2fe13ecf3bd24070263489404fe216488c5ba7e38ece1f54daf219e72a833a3a2dc404331e870e9f44963a33399589490956bff003a3404d3b2
  languageName: node
  linkType: hard

"mkdirp@npm:^3.0.1":
  version: 3.0.1
  resolution: "mkdirp@npm:3.0.1"
  bin:
    mkdirp: dist/cjs/src/bin.js
  checksum: 10/16fd79c28645759505914561e249b9a1f5fe3362279ad95487a4501e4467abeb714fd35b95307326b8fd03f3c7719065ef11a6f97b7285d7888306d1bd2232ba
  languageName: node
  linkType: hard

"mongodb@npm:^3.2.4":
  version: 3.7.4
  resolution: "mongodb@npm:3.7.4"
  dependencies:
    bl: "npm:^2.2.1"
    bson: "npm:^1.1.4"
    denque: "npm:^1.4.1"
    optional-require: "npm:^1.1.8"
    safe-buffer: "npm:^5.1.2"
    saslprep: "npm:^1.0.0"
  dependenciesMeta:
    saslprep:
      optional: true
  peerDependenciesMeta:
    aws4:
      optional: true
    bson-ext:
      optional: true
    kerberos:
      optional: true
    mongodb-client-encryption:
      optional: true
    mongodb-extjson:
      optional: true
    snappy:
      optional: true
  checksum: 10/4b157ce0e200a0a5991c3ba442c110188af06381383dce4d4d76a20903d87d956b1c86f1e50f4d7a40ddd9dd815247563756859e07a76d35b18ee79951e7b1e3
  languageName: node
  linkType: hard

"ms@npm:2.0.0":
  version: 2.0.0
  resolution: "ms@npm:2.0.0"
  checksum: 10/0e6a22b8b746d2e0b65a430519934fefd41b6db0682e3477c10f60c76e947c4c0ad06f63ffdf1d78d335f83edee8c0aa928aa66a36c7cd95b69b26f468d527f4
  languageName: node
  linkType: hard

"ms@npm:2.1.1":
  version: 2.1.1
  resolution: "ms@npm:2.1.1"
  checksum: 10/0078a23cd916a9a7435c413caa14c57d4b4f6e2470e0ab554b6964163c8a4436448ac7ae020e883685475da6b6796cc396b670f579cb275db288a21e3e57721e
  languageName: node
  linkType: hard

"ms@npm:2.1.3, ms@npm:^2.0.0, ms@npm:^2.1.1, ms@npm:^2.1.3":
  version: 2.1.3
  resolution: "ms@npm:2.1.3"
  checksum: 10/aa92de608021b242401676e35cfa5aa42dd70cbdc082b916da7fb925c542173e36bce97ea3e804923fe92c0ad991434e4a38327e15a1b5b5f945d66df615ae6d
  languageName: node
  linkType: hard

"msgpack-js@npm:0.3.0":
  version: 0.3.0
  resolution: "msgpack-js@npm:0.3.0"
  dependencies:
    bops: "npm:~0.0.6"
  checksum: 10/d551691365bf08c465f701e52c3369ebe0474259df12961d021e89b7f7fdaa18d26a4a415870516963353d756e69772158a57860ba8772a6a4cce8a0383eec05
  languageName: node
  linkType: hard

"msgpack-stream@npm:~0.0.10":
  version: 0.0.13
  resolution: "msgpack-stream@npm:0.0.13"
  dependencies:
    bops: "npm:1.0.0"
    msgpack-js: "npm:0.3.0"
    through: "npm:2.3.4"
  checksum: 10/d42879cfe9b3b8088684398d6d588c548feab2eaaf6ad3b50e5a047bf1eb488fc8e8567550cb7331669c49859dde833dc0b0c004b476f4b5cb0d926af63999fa
  languageName: node
  linkType: hard

"msgpack5@npm:^4.5.1":
  version: 4.5.1
  resolution: "msgpack5@npm:4.5.1"
  dependencies:
    bl: "npm:^2.0.1"
    inherits: "npm:^2.0.3"
    readable-stream: "npm:^2.3.6"
    safe-buffer: "npm:^5.1.2"
  checksum: 10/e09cf32920aaef38e66c36a8e2f4b6a620106ff180c8dabd62fd129322d469ad8e26331b60ecc8201287244713f137942f0341ef2129d758f0d973b3d260647d
  languageName: node
  linkType: hard

"mux-demux@npm:^3.7.9":
  version: 3.7.9
  resolution: "mux-demux@npm:3.7.9"
  dependencies:
    duplex: "npm:~1.0.0"
    json-buffer: "npm:~2.0.4"
    msgpack-stream: "npm:~0.0.10"
    stream-combiner: "npm:0.0.2"
    stream-serializer: "npm:~1.1.1"
    through: "npm:~2.3.1"
    xtend: "npm:~1.0.3"
  checksum: 10/ab8ff0c8e69bbacc0e499f906b9fd911c6fed087ccea242368d293c6f17b03285faa921ab9f8b7c4f6785c2bd16c0edf77eec7722cc2870321affb5b106905bb
  languageName: node
  linkType: hard

"n-gram@npm:^1.0.0":
  version: 1.1.2
  resolution: "n-gram@npm:1.1.2"
  checksum: 10/e9f324cd4ebc57b2df927fb792c47a7d6fb65bc460d00ab8eda89d4214a15421b00c6c3415516011a7d3ce17bbf93ec787caf82eb31a4043199efcc2f716bc3f
  languageName: node
  linkType: hard

"nan@npm:^2.16.0":
  version: 2.22.2
  resolution: "nan@npm:2.22.2"
  dependencies:
    node-gyp: "npm:latest"
  checksum: 10/bee49de633650213970596ffbdf036bfe2109ff283a40f7742c3aa6d1fc15b9836f62bfee82192b879f56ab5f9fa9a1e5c58a908a50e5c87d91fb2118ef70827
  languageName: node
  linkType: hard

"nanoid@npm:3.3.8":
  version: 3.3.8
  resolution: "nanoid@npm:3.3.8"
  bin:
    nanoid: bin/nanoid.cjs
  checksum: 10/2d1766606cf0d6f47b6f0fdab91761bb81609b2e3d367027aff45e6ee7006f660fb7e7781f4a34799fe6734f1268eeed2e37a5fdee809ade0c2d4eb11b0f9c40
  languageName: node
  linkType: hard

"natural-compare@npm:^1.4.0":
  version: 1.4.0
  resolution: "natural-compare@npm:1.4.0"
  checksum: 10/23ad088b08f898fc9b53011d7bb78ec48e79de7627e01ab5518e806033861bef68d5b0cd0e2205c2f36690ac9571ff6bcb05eb777ced2eeda8d4ac5b44592c3d
  languageName: node
  linkType: hard

"negotiator@npm:0.6.3":
  version: 0.6.3
  resolution: "negotiator@npm:0.6.3"
  checksum: 10/2723fb822a17ad55c93a588a4bc44d53b22855bf4be5499916ca0cab1e7165409d0b288ba2577d7b029f10ce18cf2ed8e703e5af31c984e1e2304277ef979837
  languageName: node
  linkType: hard

"negotiator@npm:^1.0.0":
  version: 1.0.0
  resolution: "negotiator@npm:1.0.0"
  checksum: 10/b5734e87295324fabf868e36fb97c84b7d7f3156ec5f4ee5bf6e488079c11054f818290fc33804cef7b1ee21f55eeb14caea83e7dafae6492a409b3e573153e5
  languageName: node
  linkType: hard

"negotiator@npm:~0.6.4":
  version: 0.6.4
  resolution: "negotiator@npm:0.6.4"
  checksum: 10/d98c04a136583afd055746168f1067d58ce4bfe6e4c73ca1d339567f81ea1f7e665b5bd1e81f4771c67b6c2ea89b21cb2adaea2b16058c7dc31317778f931dab
  languageName: node
  linkType: hard

"neotraverse@npm:^0.6.18":
  version: 0.6.18
  resolution: "neotraverse@npm:0.6.18"
  checksum: 10/a19649cdadb9a3ce3c54c2d6093a2eb1e12364ace384301a7515d40c752bfbac45d12c6eb9c4b004beba7bd4d1871323ebd46ad1446e0de5bc5143b0367647cb
  languageName: node
  linkType: hard

"no-case@npm:^3.0.4":
  version: 3.0.4
  resolution: "no-case@npm:3.0.4"
  dependencies:
    lower-case: "npm:^2.0.2"
    tslib: "npm:^2.0.3"
  checksum: 10/0b2ebc113dfcf737d48dde49cfebf3ad2d82a8c3188e7100c6f375e30eafbef9e9124aadc3becef237b042fd5eb0aad2fd78669c20972d045bbe7fea8ba0be5c
  languageName: node
  linkType: hard

"nocache@npm:^4.0.0":
  version: 4.0.0
  resolution: "nocache@npm:4.0.0"
  checksum: 10/5b018b7900fbc580c327b1a87decb20e8d9d233face8b8dfa286a54304f2cc11bfaea9828d0d4b635ca55a73539269a6b64a85185c4a363cc288fd706f6e3825
  languageName: node
  linkType: hard

"node-abort-controller@npm:^3.0.1":
  version: 3.1.1
  resolution: "node-abort-controller@npm:3.1.1"
  checksum: 10/0a2cdb7ec0aeaf3cb31e1ca0e192f5add48f1c5c9c9ed822129f9dddbd9432f69b7425982f94ce803c56a2104884530aa67cd57696e5774b2e5b8ec2f58de042
  languageName: node
  linkType: hard

"node-cache@npm:^5.1.2":
  version: 5.1.2
  resolution: "node-cache@npm:5.1.2"
  dependencies:
    clone: "npm:2.x"
  checksum: 10/6ac71a9e65fdd8940883c3c188de4888ff592f5bf52e4d42436c49e2a575d635e7327acea490c49fa7c01d5fa81f7b6e060fd35cf6f6ec401fbd5f77a3ebeecf
  languageName: node
  linkType: hard

"node-fetch@npm:^2.6.9":
  version: 2.7.0
  resolution: "node-fetch@npm:2.7.0"
  dependencies:
    whatwg-url: "npm:^5.0.0"
  peerDependencies:
    encoding: ^0.1.0
  peerDependenciesMeta:
    encoding:
      optional: true
  checksum: 10/b24f8a3dc937f388192e59bcf9d0857d7b6940a2496f328381641cb616efccc9866e89ec43f2ec956bbd6c3d3ee05524ce77fe7b29ccd34692b3a16f237d6676
  languageName: node
  linkType: hard

"node-gyp@npm:latest":
  version: 11.2.0
  resolution: "node-gyp@npm:11.2.0"
  dependencies:
    env-paths: "npm:^2.2.0"
    exponential-backoff: "npm:^3.1.1"
    graceful-fs: "npm:^4.2.6"
    make-fetch-happen: "npm:^14.0.3"
    nopt: "npm:^8.0.0"
    proc-log: "npm:^5.0.0"
    semver: "npm:^7.3.5"
    tar: "npm:^7.4.3"
    tinyglobby: "npm:^0.2.12"
    which: "npm:^5.0.0"
  bin:
    node-gyp: bin/node-gyp.js
  checksum: 10/806fd8e3adc9157e17bf0d4a2c899cf6b98a0bbe9f453f630094ce791866271f6cddcaf2133e6513715d934fcba2014d287c7053d5d7934937b3a34d5a3d84ad
  languageName: node
  linkType: hard

"node-releases@npm:^2.0.19":
  version: 2.0.19
  resolution: "node-releases@npm:2.0.19"
  checksum: 10/c2b33b4f0c40445aee56141f13ca692fa6805db88510e5bbb3baadb2da13e1293b738e638e15e4a8eb668bb9e97debb08e7a35409b477b5cc18f171d35a83045
  languageName: node
  linkType: hard

"nodemailer-direct-transport@npm:^3.3.2":
  version: 3.3.2
  resolution: "nodemailer-direct-transport@npm:3.3.2"
  dependencies:
    nodemailer-shared: "npm:1.1.0"
    smtp-connection: "npm:2.12.0"
  checksum: 10/6c8f27fbf840099be1308d97a348e19c402f7d4dd385d8e45f52852520d29061222023fae29574c7a38f589e78e6f09fdbe7581495fc015d69afe913c5c6563f
  languageName: node
  linkType: hard

"nodemailer-fetch@npm:1.6.0":
  version: 1.6.0
  resolution: "nodemailer-fetch@npm:1.6.0"
  checksum: 10/409d431630ad75aaeeb31bf8c7d53d472371be8a2a72b89c902b98b81b3e7715f7fb96cab9f01fffe39d65a95bb0869f2e7c8dc30025d878a6e9ce09eeb584ea
  languageName: node
  linkType: hard

"nodemailer-shared@npm:1.1.0":
  version: 1.1.0
  resolution: "nodemailer-shared@npm:1.1.0"
  dependencies:
    nodemailer-fetch: "npm:1.6.0"
  checksum: 10/7aa7ee43f0426fda659e78837559a4363f42422dcdaba98d67ab25da7a55876741f38bac09bdabca29e3a812e5a22d16d7c54d42fe745faf62a4c73241c46345
  languageName: node
  linkType: hard

"nodemailer-stub-transport@npm:^1.1.0":
  version: 1.1.0
  resolution: "nodemailer-stub-transport@npm:1.1.0"
  checksum: 10/7b434e6cb5b5939c8d20e2d61719b10b66675b8f946a0471775f1ee3a62bd41019fff5eac6e80f40fead7d023103bc1c1f1ce77f80351344afcad382a5ff13fd
  languageName: node
  linkType: hard

"nodemailer@npm:^6.10.1":
  version: 6.10.1
  resolution: "nodemailer@npm:6.10.1"
  checksum: 10/d9911701641e06143a2deb0bd5deb518310972316c6e6eabc594af24353b0d67867f26cb8d72b0cfa385abef945149ac51ae40a40d2199e1088aef5829e58a3d
  languageName: node
  linkType: hard

"nopt@npm:^8.0.0":
  version: 8.1.0
  resolution: "nopt@npm:8.1.0"
  dependencies:
    abbrev: "npm:^3.0.0"
  bin:
    nopt: bin/nopt.js
  checksum: 10/26ab456c51a96f02a9e5aa8d1b80ef3219f2070f3f3528a040e32fb735b1e651e17bdf0f1476988d3a46d498f35c65ed662d122f340d38ce4a7e71dd7b20c4bc
  languageName: node
  linkType: hard

"normalize-url@npm:^6.0.1":
  version: 6.1.0
  resolution: "normalize-url@npm:6.1.0"
  checksum: 10/5ae699402c9d5ffa330adc348fcd6fc6e6a155ab7c811b96e30b7ecab60ceef821d8f86443869671dda71bbc47f4b9625739c82ad247e883e9aefe875bfb8659
  languageName: node
  linkType: hard

"npm-run-path@npm:^4.0.0":
  version: 4.0.1
  resolution: "npm-run-path@npm:4.0.1"
  dependencies:
    path-key: "npm:^3.0.0"
  checksum: 10/5374c0cea4b0bbfdfae62da7bbdf1e1558d338335f4cacf2515c282ff358ff27b2ecb91ffa5330a8b14390ac66a1e146e10700440c1ab868208430f56b5f4d23
  languageName: node
  linkType: hard

"oauth-sign@npm:~0.9.0":
  version: 0.9.0
  resolution: "oauth-sign@npm:0.9.0"
  checksum: 10/1809a366d258f41fdf4ab5310cff3d1e15f96b187503bc7333cef4351de7bd0f52cb269bc95800f1fae5fb04dd886287df1471985fd67e8484729fdbcf857119
  languageName: node
  linkType: hard

"object-assign@npm:^4":
  version: 4.1.1
  resolution: "object-assign@npm:4.1.1"
  checksum: 10/fcc6e4ea8c7fe48abfbb552578b1c53e0d194086e2e6bbbf59e0a536381a292f39943c6e9628af05b5528aa5e3318bb30d6b2e53cadaf5b8fe9e12c4b69af23f
  languageName: node
  linkType: hard

"object-inspect@npm:^1.13.3, object-inspect@npm:^1.13.4":
  version: 1.13.4
  resolution: "object-inspect@npm:1.13.4"
  checksum: 10/aa13b1190ad3e366f6c83ad8a16ed37a19ed57d267385aa4bfdccda833d7b90465c057ff6c55d035a6b2e52c1a2295582b294217a0a3a1ae7abdd6877ef781fb
  languageName: node
  linkType: hard

"object-keys@npm:^1.1.1":
  version: 1.1.1
  resolution: "object-keys@npm:1.1.1"
  checksum: 10/3d81d02674115973df0b7117628ea4110d56042e5326413e4b4313f0bcdf7dd78d4a3acef2c831463fa3796a66762c49daef306f4a0ea1af44877d7086d73bde
  languageName: node
  linkType: hard

"object.assign@npm:^4.1.7":
  version: 4.1.7
  resolution: "object.assign@npm:4.1.7"
  dependencies:
    call-bind: "npm:^1.0.8"
    call-bound: "npm:^1.0.3"
    define-properties: "npm:^1.2.1"
    es-object-atoms: "npm:^1.0.0"
    has-symbols: "npm:^1.1.0"
    object-keys: "npm:^1.1.1"
  checksum: 10/3fe28cdd779f2a728a9a66bd688679ba231a2b16646cd1e46b528fe7c947494387dda4bc189eff3417f3717ef4f0a8f2439347cf9a9aa3cef722fbfd9f615587
  languageName: node
  linkType: hard

"object.omit@npm:^3.0.0":
  version: 3.0.0
  resolution: "object.omit@npm:3.0.0"
  dependencies:
    is-extendable: "npm:^1.0.0"
  checksum: 10/1feb3a5891e3892451e34d3b460caae06f2a8a829854eced577cd30654a2ed37750e3d7f0d462e692b3b6623ad74777457c0ad32a9e622136b8171209d356890
  languageName: node
  linkType: hard

"object.pick@npm:^1.3.0":
  version: 1.3.0
  resolution: "object.pick@npm:1.3.0"
  dependencies:
    isobject: "npm:^3.0.1"
  checksum: 10/92d7226a6b581d0d62694a5632b6a1594c81b3b5a4eb702a7662e0b012db532557067d6f773596c577f75322eba09cdca37ca01ea79b6b29e3e17365f15c615e
  languageName: node
  linkType: hard

"on-finished@npm:2.4.1, on-finished@npm:^2.4.1":
  version: 2.4.1
  resolution: "on-finished@npm:2.4.1"
  dependencies:
    ee-first: "npm:1.1.1"
  checksum: 10/8e81472c5028125c8c39044ac4ab8ba51a7cdc19a9fbd4710f5d524a74c6d8c9ded4dd0eed83f28d3d33ac1d7a6a439ba948ccb765ac6ce87f30450a26bfe2ea
  languageName: node
  linkType: hard

"on-headers@npm:~1.0.2":
  version: 1.0.2
  resolution: "on-headers@npm:1.0.2"
  checksum: 10/870766c16345855e2012e9422ba1ab110c7e44ad5891a67790f84610bd70a72b67fdd71baf497295f1d1bf38dd4c92248f825d48729c53c0eae5262fb69fa171
  languageName: node
  linkType: hard

"once@npm:^1.3.0, once@npm:^1.3.1, once@npm:^1.4.0":
  version: 1.4.0
  resolution: "once@npm:1.4.0"
  dependencies:
    wrappy: "npm:1"
  checksum: 10/cd0a88501333edd640d95f0d2700fbde6bff20b3d4d9bdc521bdd31af0656b5706570d6c6afe532045a20bb8dc0849f8332d6f2a416e0ba6d3d3b98806c7db68
  languageName: node
  linkType: hard

"onetime@npm:^5.1.0":
  version: 5.1.2
  resolution: "onetime@npm:5.1.2"
  dependencies:
    mimic-fn: "npm:^2.1.0"
  checksum: 10/e9fd0695a01cf226652f0385bf16b7a24153dbbb2039f764c8ba6d2306a8506b0e4ce570de6ad99c7a6eb49520743afdb66edd95ee979c1a342554ed49a9aadd
  languageName: node
  linkType: hard

"optional-require@npm:^1.1.8":
  version: 1.1.8
  resolution: "optional-require@npm:1.1.8"
  dependencies:
    require-at: "npm:^1.0.6"
  checksum: 10/656ecba9c2aa0ca0ec25bf7cba987e0cdda91c49caae361e6c5839fe02dc8bc467d3d77694b1fe47923a3815cd10a6e292b838a1251cb4abba951fc371fb162a
  languageName: node
  linkType: hard

"optionator@npm:^0.9.3":
  version: 0.9.4
  resolution: "optionator@npm:0.9.4"
  dependencies:
    deep-is: "npm:^0.1.3"
    fast-levenshtein: "npm:^2.0.6"
    levn: "npm:^0.4.1"
    prelude-ls: "npm:^1.2.1"
    type-check: "npm:^0.4.0"
    word-wrap: "npm:^1.2.5"
  checksum: 10/a8398559c60aef88d7f353a4f98dcdff6090a4e70f874c827302bf1213d9106a1c4d5fcb68dacb1feb3c30a04c4102f41047aa55d4c576b863d6fc876e001af6
  languageName: node
  linkType: hard

"options@npm:0.0.6":
  version: 0.0.6
  resolution: "options@npm:0.0.6"
  checksum: 10/8601fdc0a3e14987b7f2509676e5e5d8afe601c64600d9bad3a0aad7e8ed8631ad47e2fa155c63e4043832122d6f6e3251d276307a032d0bb50cc252980e3712
  languageName: node
  linkType: hard

"os-locale@npm:^5.0.0":
  version: 5.0.0
  resolution: "os-locale@npm:5.0.0"
  dependencies:
    execa: "npm:^4.0.0"
    lcid: "npm:^3.0.0"
    mem: "npm:^5.0.0"
  checksum: 10/3bdf5776675755a755aa4edc5ad3465dea4262abd4c63fcc9839440e3f868f5d04ef14d49d79a9d428eb7577fb2428d3d796c8a885c5e3390c756bf281f1707a
  languageName: node
  linkType: hard

"own-keys@npm:^1.0.1":
  version: 1.0.1
  resolution: "own-keys@npm:1.0.1"
  dependencies:
    get-intrinsic: "npm:^1.2.6"
    object-keys: "npm:^1.1.1"
    safe-push-apply: "npm:^1.0.0"
  checksum: 10/ab4bb3b8636908554fc19bf899e225444195092864cb61503a0d048fdaf662b04be2605b636a4ffeaf6e8811f6fcfa8cbb210ec964c0eb1a41eb853e1d5d2f41
  languageName: node
  linkType: hard

"p-cancelable@npm:^2.0.0":
  version: 2.1.1
  resolution: "p-cancelable@npm:2.1.1"
  checksum: 10/7f1b64db17fc54acf359167d62898115dcf2a64bf6b3b038e4faf36fc059e5ed762fb9624df8ed04b25bee8de3ab8d72dea9879a2a960cd12e23c420a4aca6ed
  languageName: node
  linkType: hard

"p-defer@npm:^1.0.0":
  version: 1.0.0
  resolution: "p-defer@npm:1.0.0"
  checksum: 10/1d8fb7138a0ccebb65479160fd93f245303c06c977c976105d75838f7f504a9a6ef11b7e058f98b4c957a6a8df268c616da1ee339285d565f9e5ba00304e027b
  languageName: node
  linkType: hard

"p-finally@npm:^1.0.0":
  version: 1.0.0
  resolution: "p-finally@npm:1.0.0"
  checksum: 10/93a654c53dc805dd5b5891bab16eb0ea46db8f66c4bfd99336ae929323b1af2b70a8b0654f8f1eae924b2b73d037031366d645f1fd18b3d30cbd15950cc4b1d4
  languageName: node
  linkType: hard

"p-is-promise@npm:^2.1.0":
  version: 2.1.0
  resolution: "p-is-promise@npm:2.1.0"
  checksum: 10/c9a8248c8b5e306475a5d55ce7808dbce4d4da2e3d69526e4991a391a7809bfd6cfdadd9bf04f1c96a3db366c93d9a0f5ee81d949e7b1684c4e0f61f747199ef
  languageName: node
  linkType: hard

"p-limit@npm:3.1.0, p-limit@npm:^3.0.2":
  version: 3.1.0
  resolution: "p-limit@npm:3.1.0"
  dependencies:
    yocto-queue: "npm:^0.1.0"
  checksum: 10/7c3690c4dbf62ef625671e20b7bdf1cbc9534e83352a2780f165b0d3ceba21907e77ad63401708145ca4e25bfc51636588d89a8c0aeb715e6c37d1c066430360
  languageName: node
  linkType: hard

"p-limit@npm:^2.2.0":
  version: 2.3.0
  resolution: "p-limit@npm:2.3.0"
  dependencies:
    p-try: "npm:^2.0.0"
  checksum: 10/84ff17f1a38126c3314e91ecfe56aecbf36430940e2873dadaa773ffe072dc23b7af8e46d4b6485d302a11673fe94c6b67ca2cfbb60c989848b02100d0594ac1
  languageName: node
  linkType: hard

"p-locate@npm:^4.1.0":
  version: 4.1.0
  resolution: "p-locate@npm:4.1.0"
  dependencies:
    p-limit: "npm:^2.2.0"
  checksum: 10/513bd14a455f5da4ebfcb819ef706c54adb09097703de6aeaa5d26fe5ea16df92b48d1ac45e01e3944ce1e6aa2a66f7f8894742b8c9d6e276e16cd2049a2b870
  languageName: node
  linkType: hard

"p-locate@npm:^5.0.0":
  version: 5.0.0
  resolution: "p-locate@npm:5.0.0"
  dependencies:
    p-limit: "npm:^3.0.2"
  checksum: 10/1623088f36cf1cbca58e9b61c4e62bf0c60a07af5ae1ca99a720837356b5b6c5ba3eb1b2127e47a06865fee59dd0453cad7cc844cda9d5a62ac1a5a51b7c86d3
  languageName: node
  linkType: hard

"p-map@npm:^7.0.2":
  version: 7.0.3
  resolution: "p-map@npm:7.0.3"
  checksum: 10/2ef48ccfc6dd387253d71bf502604f7893ed62090b2c9d73387f10006c342606b05233da0e4f29388227b61eb5aeface6197e166520c465c234552eeab2fe633
  languageName: node
  linkType: hard

"p-queue@npm:^6":
  version: 6.6.2
  resolution: "p-queue@npm:6.6.2"
  dependencies:
    eventemitter3: "npm:^4.0.4"
    p-timeout: "npm:^3.2.0"
  checksum: 10/60fe227ffce59fbc5b1b081305b61a2f283ff145005853702b7d4d3f99a0176bd21bb126c99a962e51fe1e01cb8aa10f0488b7bbe73b5dc2e84b5cc650b8ffd2
  languageName: node
  linkType: hard

"p-retry@npm:^4":
  version: 4.6.2
  resolution: "p-retry@npm:4.6.2"
  dependencies:
    "@types/retry": "npm:0.12.0"
    retry: "npm:^0.13.1"
  checksum: 10/45c270bfddaffb4a895cea16cb760dcc72bdecb6cb45fef1971fa6ea2e91ddeafddefe01e444ac73e33b1b3d5d29fb0dd18a7effb294262437221ddc03ce0f2e
  languageName: node
  linkType: hard

"p-timeout@npm:^3.2.0":
  version: 3.2.0
  resolution: "p-timeout@npm:3.2.0"
  dependencies:
    p-finally: "npm:^1.0.0"
  checksum: 10/3dd0eaa048780a6f23e5855df3dd45c7beacff1f820476c1d0d1bcd6648e3298752ba2c877aa1c92f6453c7dd23faaf13d9f5149fc14c0598a142e2c5e8d649c
  languageName: node
  linkType: hard

"p-try@npm:^2.0.0":
  version: 2.2.0
  resolution: "p-try@npm:2.2.0"
  checksum: 10/f8a8e9a7693659383f06aec604ad5ead237c7a261c18048a6e1b5b85a5f8a067e469aa24f5bc009b991ea3b058a87f5065ef4176793a200d4917349881216cae
  languageName: node
  linkType: hard

"package-json-from-dist@npm:^1.0.0":
  version: 1.0.1
  resolution: "package-json-from-dist@npm:1.0.1"
  checksum: 10/58ee9538f2f762988433da00e26acc788036914d57c71c246bf0be1b60cdbd77dd60b6a3e1a30465f0b248aeb80079e0b34cb6050b1dfa18c06953bb1cbc7602
  languageName: node
  linkType: hard

"param-case@npm:^3.0.4":
  version: 3.0.4
  resolution: "param-case@npm:3.0.4"
  dependencies:
    dot-case: "npm:^3.0.4"
    tslib: "npm:^2.0.3"
  checksum: 10/b34227fd0f794e078776eb3aa6247442056cb47761e9cd2c4c881c86d84c64205f6a56ef0d70b41ee7d77da02c3f4ed2f88e3896a8fefe08bdfb4deca037c687
  languageName: node
  linkType: hard

"parent-module@npm:^1.0.0":
  version: 1.0.1
  resolution: "parent-module@npm:1.0.1"
  dependencies:
    callsites: "npm:^3.0.0"
  checksum: 10/6ba8b255145cae9470cf5551eb74be2d22281587af787a2626683a6c20fbb464978784661478dd2a3f1dad74d1e802d403e1b03c1a31fab310259eec8ac560ff
  languageName: node
  linkType: hard

"parseurl@npm:^1.3.3, parseurl@npm:~1.3.2, parseurl@npm:~1.3.3":
  version: 1.3.3
  resolution: "parseurl@npm:1.3.3"
  checksum: 10/407cee8e0a3a4c5cd472559bca8b6a45b82c124e9a4703302326e9ab60fc1081442ada4e02628efef1eb16197ddc7f8822f5a91fd7d7c86b51f530aedb17dfa2
  languageName: node
  linkType: hard

"pascal-case@npm:^3.1.2":
  version: 3.1.2
  resolution: "pascal-case@npm:3.1.2"
  dependencies:
    no-case: "npm:^3.0.4"
    tslib: "npm:^2.0.3"
  checksum: 10/ba98bfd595fc91ef3d30f4243b1aee2f6ec41c53b4546bfa3039487c367abaa182471dcfc830a1f9e1a0df00c14a370514fa2b3a1aacc68b15a460c31116873e
  languageName: node
  linkType: hard

"path-case@npm:^3.0.4":
  version: 3.0.4
  resolution: "path-case@npm:3.0.4"
  dependencies:
    dot-case: "npm:^3.0.4"
    tslib: "npm:^2.0.3"
  checksum: 10/61de0526222629f65038a66f63330dd22d5b54014ded6636283e1d15364da38b3cf29e4433aa3f9d8b0dba407ae2b059c23b0104a34ee789944b1bc1c5c7e06d
  languageName: node
  linkType: hard

"path-exists@npm:^4.0.0":
  version: 4.0.0
  resolution: "path-exists@npm:4.0.0"
  checksum: 10/505807199dfb7c50737b057dd8d351b82c033029ab94cb10a657609e00c1bc53b951cfdbccab8de04c5584d5eff31128ce6afd3db79281874a5ef2adbba55ed1
  languageName: node
  linkType: hard

"path-is-absolute@npm:^1.0.0":
  version: 1.0.1
  resolution: "path-is-absolute@npm:1.0.1"
  checksum: 10/060840f92cf8effa293bcc1bea81281bd7d363731d214cbe5c227df207c34cd727430f70c6037b5159c8a870b9157cba65e775446b0ab06fd5ecc7e54615a3b8
  languageName: node
  linkType: hard

"path-key@npm:^3.0.0, path-key@npm:^3.1.0":
  version: 3.1.1
  resolution: "path-key@npm:3.1.1"
  checksum: 10/55cd7a9dd4b343412a8386a743f9c746ef196e57c823d90ca3ab917f90ab9f13dd0ded27252ba49dbdfcab2b091d998bc446f6220cd3cea65db407502a740020
  languageName: node
  linkType: hard

"path-scurry@npm:^1.11.1":
  version: 1.11.1
  resolution: "path-scurry@npm:1.11.1"
  dependencies:
    lru-cache: "npm:^10.2.0"
    minipass: "npm:^5.0.0 || ^6.0.2 || ^7.0.0"
  checksum: 10/5e8845c159261adda6f09814d7725683257fcc85a18f329880ab4d7cc1d12830967eae5d5894e453f341710d5484b8fdbbd4d75181b4d6e1eb2f4dc7aeadc434
  languageName: node
  linkType: hard

"path-to-regexp@npm:0.1.12":
  version: 0.1.12
  resolution: "path-to-regexp@npm:0.1.12"
  checksum: 10/2e30f6a0144679c1f95c98e166b96e6acd1e72be9417830fefc8de7ac1992147eb9a4c7acaa59119fb1b3c34eec393b2129ef27e24b2054a3906fc4fb0d1398e
  languageName: node
  linkType: hard

"path-to-regexp@npm:^8.0.0":
  version: 8.2.0
  resolution: "path-to-regexp@npm:8.2.0"
  checksum: 10/23378276a172b8ba5f5fb824475d1818ca5ccee7bbdb4674701616470f23a14e536c1db11da9c9e6d82b82c556a817bbf4eee6e41b9ed20090ef9427cbb38e13
  languageName: node
  linkType: hard

"performance-now@npm:^2.1.0":
  version: 2.1.0
  resolution: "performance-now@npm:2.1.0"
  checksum: 10/534e641aa8f7cba160f0afec0599b6cecefbb516a2e837b512be0adbe6c1da5550e89c78059c7fabc5c9ffdf6627edabe23eb7c518c4500067a898fa65c2b550
  languageName: node
  linkType: hard

"picocolors@npm:^1.1.1":
  version: 1.1.1
  resolution: "picocolors@npm:1.1.1"
  checksum: 10/e1cf46bf84886c79055fdfa9dcb3e4711ad259949e3565154b004b260cd356c5d54b31a1437ce9782624bf766272fe6b0154f5f0c744fb7af5d454d2b60db045
  languageName: node
  linkType: hard

"picomatch@npm:^4.0.2":
  version: 4.0.2
  resolution: "picomatch@npm:4.0.2"
  checksum: 10/ce617b8da36797d09c0baacb96ca8a44460452c89362d7cb8f70ca46b4158ba8bc3606912de7c818eb4a939f7f9015cef3c766ec8a0c6bfc725fdc078e39c717
  languageName: node
  linkType: hard

"pinyin-pro@npm:^3.26.0":
  version: 3.26.0
  resolution: "pinyin-pro@npm:3.26.0"
  checksum: 10/06f6b08e0b0cd151eb2f202cd6606f455b5d9c996a45c8cac4f52de4c40e432a4561480b75e15410e15649a851c078392149d32dd188f32cf81cec640bb083e0
  languageName: node
  linkType: hard

"pngjs@npm:^5.0.0":
  version: 5.0.0
  resolution: "pngjs@npm:5.0.0"
  checksum: 10/345781644740779752505af2fea3e9043f6c7cc349b18e1fb8842796360d1624791f0c24d33c0f27b05658373f90ffaa177a849e932e5fea1f540cef3975f3c9
  languageName: node
  linkType: hard

"possible-typed-array-names@npm:^1.0.0":
  version: 1.1.0
  resolution: "possible-typed-array-names@npm:1.1.0"
  checksum: 10/2f44137b8d3dd35f4a7ba7469eec1cd9cfbb46ec164b93a5bc1f4c3d68599c9910ee3b91da1d28b4560e9cc8414c3cd56fedc07259c67e52cc774476270d3302
  languageName: node
  linkType: hard

"postman-request@npm:^2.88.1-postman.33":
  version: 2.88.1-postman.8-beta.1
  resolution: "postman-request@npm:2.88.1-postman.8-beta.1"
  dependencies:
    aws-sign2: "npm:~0.7.0"
    aws4: "npm:^1.8.0"
    caseless: "npm:~0.12.0"
    combined-stream: "npm:~1.0.6"
    extend: "npm:~3.0.2"
    forever-agent: "npm:~0.6.1"
    form-data: "npm:~2.3.2"
    har-validator: "npm:~5.1.3"
    http-signature: "npm:~1.2.0"
    is-typedarray: "npm:~1.0.0"
    isstream: "npm:~0.1.2"
    json-stringify-safe: "npm:~5.0.1"
    mime-types: "npm:~2.1.19"
    oauth-sign: "npm:~0.9.0"
    performance-now: "npm:^2.1.0"
    postman-url-encoder: "npm:1.0.1"
    qs: "npm:~6.5.2"
    safe-buffer: "npm:^5.1.2"
    stream-length: "npm:^1.0.2"
    tough-cookie: "npm:~2.5.0"
    tunnel-agent: "npm:^0.6.0"
    uuid: "npm:^3.3.2"
  checksum: 10/ffae2656cef5788d11a95bfaad192902163d2270c85864b8b6d870683534e02d1dc1a430057aa2c3dc1848e6e9f2aa505a5fbed3f096b47ec44151bd2ba7de9f
  languageName: node
  linkType: hard

"postman-url-encoder@npm:1.0.1":
  version: 1.0.1
  resolution: "postman-url-encoder@npm:1.0.1"
  checksum: 10/cb82028850f6ceb3acb6671c47eda66a37c632aee4cb2d26f515db6f07ac50fa5f4e4623db5fe902feeb4108dc6ce739f6b7f0af9df34709463ba6ab6e29c350
  languageName: node
  linkType: hard

"prelude-ls@npm:^1.2.1":
  version: 1.2.1
  resolution: "prelude-ls@npm:1.2.1"
  checksum: 10/0b9d2c76801ca652a7f64892dd37b7e3fab149a37d2424920099bf894acccc62abb4424af2155ab36dea8744843060a2d8ddc983518d0b1e22265a22324b72ed
  languageName: node
  linkType: hard

"proc-log@npm:^5.0.0":
  version: 5.0.0
  resolution: "proc-log@npm:5.0.0"
  checksum: 10/35610bdb0177d3ab5d35f8827a429fb1dc2518d9e639f2151ac9007f01a061c30e0c635a970c9b00c39102216160f6ec54b62377c92fac3b7bfc2ad4b98d195c
  languageName: node
  linkType: hard

"process-nextick-args@npm:~2.0.0":
  version: 2.0.1
  resolution: "process-nextick-args@npm:2.0.1"
  checksum: 10/1d38588e520dab7cea67cbbe2efdd86a10cc7a074c09657635e34f035277b59fbb57d09d8638346bf7090f8e8ebc070c96fa5fd183b777fff4f5edff5e9466cf
  languageName: node
  linkType: hard

"process@npm:^0.11.10":
  version: 0.11.10
  resolution: "process@npm:0.11.10"
  checksum: 10/dbaa7e8d1d5cf375c36963ff43116772a989ef2bb47c9bdee20f38fd8fc061119cf38140631cf90c781aca4d3f0f0d2c834711952b728953f04fd7d238f59f5b
  languageName: node
  linkType: hard

"prometheus-query@npm:^3.4.1":
  version: 3.5.0
  resolution: "prometheus-query@npm:3.5.0"
  dependencies:
    axios: "npm:^1.8.4"
  checksum: 10/879f7654d93cee09ec5e1fd11c963862ca6fc1aa47c629bf056b4654ab18fa4cc26be40ea2c65368395f7851dd227f632ebaed70278e54fc277c167f8ce137ff
  languageName: node
  linkType: hard

"promise-retry@npm:^2.0.1":
  version: 2.0.1
  resolution: "promise-retry@npm:2.0.1"
  dependencies:
    err-code: "npm:^2.0.2"
    retry: "npm:^0.12.0"
  checksum: 10/96e1a82453c6c96eef53a37a1d6134c9f2482f94068f98a59145d0986ca4e497bf110a410adf73857e588165eab3899f0ebcf7b3890c1b3ce802abc0d65967d4
  languageName: node
  linkType: hard

"proxy-addr@npm:^2.0.7, proxy-addr@npm:~2.0.7":
  version: 2.0.7
  resolution: "proxy-addr@npm:2.0.7"
  dependencies:
    forwarded: "npm:0.2.0"
    ipaddr.js: "npm:1.9.1"
  checksum: 10/f24a0c80af0e75d31e3451398670d73406ec642914da11a2965b80b1898ca6f66a0e3e091a11a4327079b2b268795f6fa06691923fef91887215c3d0e8ea3f68
  languageName: node
  linkType: hard

"proxy-from-env@npm:^1.1.0":
  version: 1.1.0
  resolution: "proxy-from-env@npm:1.1.0"
  checksum: 10/f0bb4a87cfd18f77bc2fba23ae49c3b378fb35143af16cc478171c623eebe181678f09439707ad80081d340d1593cd54a33a0113f3ccb3f4bc9451488780ee23
  languageName: node
  linkType: hard

"psl@npm:^1.1.28":
  version: 1.15.0
  resolution: "psl@npm:1.15.0"
  dependencies:
    punycode: "npm:^2.3.1"
  checksum: 10/5e7467eb5196eb7900d156783d12907d445c0122f76c73203ce96b148a6ccf8c5450cc805887ffada38ff92d634afcf33720c24053cb01d5b6598d1c913c5caf
  languageName: node
  linkType: hard

"pump@npm:^3.0.0":
  version: 3.0.2
  resolution: "pump@npm:3.0.2"
  dependencies:
    end-of-stream: "npm:^1.1.0"
    once: "npm:^1.3.1"
  checksum: 10/e0c4216874b96bd25ddf31a0b61a5613e26cc7afa32379217cf39d3915b0509def3565f5f6968fafdad2894c8bbdbd67d340e84f3634b2a29b950cffb6442d9f
  languageName: node
  linkType: hard

"punycode@npm:2.x.x, punycode@npm:^2.1.0, punycode@npm:^2.1.1, punycode@npm:^2.3.1":
  version: 2.3.1
  resolution: "punycode@npm:2.3.1"
  checksum: 10/febdc4362bead22f9e2608ff0171713230b57aff9dddc1c273aa2a651fbd366f94b7d6a71d78342a7c0819906750351ca7f2edd26ea41b626d87d6a13d1bd059
  languageName: node
  linkType: hard

"qrcode@npm:^1.5.4":
  version: 1.5.4
  resolution: "qrcode@npm:1.5.4"
  dependencies:
    dijkstrajs: "npm:^1.0.1"
    pngjs: "npm:^5.0.0"
    yargs: "npm:^15.3.1"
  bin:
    qrcode: bin/qrcode
  checksum: 10/9a1b61760e4ea334545a0f54bbc11c537aba0a17cf52cab9fa1b07f8a1337eed0bc6f7fde41b197f2c82c249bc48728983bfaf861bb7ecb29dc597b2ae33c424
  languageName: node
  linkType: hard

"qs@npm:6.13.0":
  version: 6.13.0
  resolution: "qs@npm:6.13.0"
  dependencies:
    side-channel: "npm:^1.0.6"
  checksum: 10/f548b376e685553d12e461409f0d6e5c59ec7c7d76f308e2a888fd9db3e0c5e89902bedd0754db3a9038eda5f27da2331a6f019c8517dc5e0a16b3c9a6e9cef8
  languageName: node
  linkType: hard

"qs@npm:^6.1.0, qs@npm:^6.14.0, qs@npm:^6.5.2, qs@npm:^6.7.0":
  version: 6.14.0
  resolution: "qs@npm:6.14.0"
  dependencies:
    side-channel: "npm:^1.1.0"
  checksum: 10/a60e49bbd51c935a8a4759e7505677b122e23bf392d6535b8fc31c1e447acba2c901235ecb192764013cd2781723dc1f61978b5fdd93cc31d7043d31cdc01974
  languageName: node
  linkType: hard

"qs@npm:~6.5.2":
  version: 6.5.3
  resolution: "qs@npm:6.5.3"
  checksum: 10/485c990fba7ad17671e16c92715fb064c1600337738f5d140024eb33a49fbc1ed31890d3db850117c760caeb9c9cc9f4ba22a15c20dd119968e41e3d3fe60b28
  languageName: node
  linkType: hard

"query-string@npm:<8.x":
  version: 7.1.3
  resolution: "query-string@npm:7.1.3"
  dependencies:
    decode-uri-component: "npm:^0.2.2"
    filter-obj: "npm:^1.1.0"
    split-on-first: "npm:^1.0.0"
    strict-uri-encode: "npm:^2.0.0"
  checksum: 10/3b6f2c167e76ca4094c5f1a9eb276efcbb9ebfd8b1a28c413f3c4e4e7d6428c8187bf46c8cbc9f92a229369dd0015de10a7fd712c8cee98d5d84c2ac6140357e
  languageName: node
  linkType: hard

"quick-lru@npm:^5.1.1":
  version: 5.1.1
  resolution: "quick-lru@npm:5.1.1"
  checksum: 10/a516faa25574be7947969883e6068dbe4aa19e8ef8e8e0fd96cddd6d36485e9106d85c0041a27153286b0770b381328f4072aa40d3b18a19f5f7d2b78b94b5ed
  languageName: node
  linkType: hard

"range-parser@npm:^1.2.1, range-parser@npm:~1.2.1":
  version: 1.2.1
  resolution: "range-parser@npm:1.2.1"
  checksum: 10/ce21ef2a2dd40506893157970dc76e835c78cf56437e26e19189c48d5291e7279314477b06ac38abd6a401b661a6840f7b03bd0b1249da9b691deeaa15872c26
  languageName: node
  linkType: hard

"raw-body@npm:2.5.2":
  version: 2.5.2
  resolution: "raw-body@npm:2.5.2"
  dependencies:
    bytes: "npm:3.1.2"
    http-errors: "npm:2.0.0"
    iconv-lite: "npm:0.4.24"
    unpipe: "npm:1.0.0"
  checksum: 10/863b5171e140546a4d99f349b720abac4410338e23df5e409cfcc3752538c9caf947ce382c89129ba976f71894bd38b5806c774edac35ebf168d02aa1ac11a95
  languageName: node
  linkType: hard

"raw-body@npm:^3.0.0":
  version: 3.0.0
  resolution: "raw-body@npm:3.0.0"
  dependencies:
    bytes: "npm:3.1.2"
    http-errors: "npm:2.0.0"
    iconv-lite: "npm:0.6.3"
    unpipe: "npm:1.0.0"
  checksum: 10/2443429bbb2f9ae5c50d3d2a6c342533dfbde6b3173740b70fa0302b30914ff400c6d31a46b3ceacbe7d0925dc07d4413928278b494b04a65736fc17ca33e30c
  languageName: node
  linkType: hard

"readable-stream@npm:^2.3.5, readable-stream@npm:^2.3.6":
  version: 2.3.8
  resolution: "readable-stream@npm:2.3.8"
  dependencies:
    core-util-is: "npm:~1.0.0"
    inherits: "npm:~2.0.3"
    isarray: "npm:~1.0.0"
    process-nextick-args: "npm:~2.0.0"
    safe-buffer: "npm:~5.1.1"
    string_decoder: "npm:~1.1.1"
    util-deprecate: "npm:~1.0.1"
  checksum: 10/8500dd3a90e391d6c5d889256d50ec6026c059fadee98ae9aa9b86757d60ac46fff24fafb7a39fa41d54cb39d8be56cc77be202ebd4cd8ffcf4cb226cbaa40d4
  languageName: node
  linkType: hard

"readable-stream@npm:^4.2.0":
  version: 4.7.0
  resolution: "readable-stream@npm:4.7.0"
  dependencies:
    abort-controller: "npm:^3.0.0"
    buffer: "npm:^6.0.3"
    events: "npm:^3.3.0"
    process: "npm:^0.11.10"
    string_decoder: "npm:^1.3.0"
  checksum: 10/bdf096c8ff59452ce5d08f13da9597f9fcfe400b4facfaa88e74ec057e5ad1fdfa140ffe28e5ed806cf4d2055f0b812806e962bca91dce31bc4cef08e53be3a4
  languageName: node
  linkType: hard

"redis-errors@npm:^1.0.0, redis-errors@npm:^1.2.0":
  version: 1.2.0
  resolution: "redis-errors@npm:1.2.0"
  checksum: 10/001c11f63ddd52d7c80eb4f4ede3a9433d29a458a7eea06b9154cb37c9802a218d93b7988247aa8c958d4b5d274b18354e8853c148f1096fda87c6e675cfd3ee
  languageName: node
  linkType: hard

"redis-parser@npm:^3.0.0":
  version: 3.0.0
  resolution: "redis-parser@npm:3.0.0"
  dependencies:
    redis-errors: "npm:^1.0.0"
  checksum: 10/b10846844b4267f19ce1a6529465819c3d78c3e89db7eb0c3bb4eb19f83784797ec411274d15a77dbe08038b48f95f76014b83ca366dc955a016a3a0a0234650
  languageName: node
  linkType: hard

"redlock@npm:5.0.0-beta.2":
  version: 5.0.0-beta.2
  resolution: "redlock@npm:5.0.0-beta.2"
  dependencies:
    node-abort-controller: "npm:^3.0.1"
  checksum: 10/c1df94fb229182ccf59b744f600b45f2cda1d9c7e5b87b9bbf5c3ef92eb47339af7b33b80dc71e97c08c1f476443be68987dad6c49cfc1a89e9a647127884963
  languageName: node
  linkType: hard

"referer-parser@npm:^0.0.3":
  version: 0.0.3
  resolution: "referer-parser@npm:0.0.3"
  dependencies:
    js-yaml: "npm:~2.1.0"
  checksum: 10/c77dc3e7fccfa0b42443e05b86a58c0703fc6c7010094b13dd72c5ecb787c6aab3dcbbb94cfc0d50f8b43d67b25c814907b7e9b48569bb7e38742d4cdc31738d
  languageName: node
  linkType: hard

"reflect.getprototypeof@npm:^1.0.6, reflect.getprototypeof@npm:^1.0.9":
  version: 1.0.10
  resolution: "reflect.getprototypeof@npm:1.0.10"
  dependencies:
    call-bind: "npm:^1.0.8"
    define-properties: "npm:^1.2.1"
    es-abstract: "npm:^1.23.9"
    es-errors: "npm:^1.3.0"
    es-object-atoms: "npm:^1.0.0"
    get-intrinsic: "npm:^1.2.7"
    get-proto: "npm:^1.0.1"
    which-builtin-type: "npm:^1.2.1"
  checksum: 10/80a4e2be716f4fe46a89a08ccad0863b47e8ce0f49616cab2d65dab0fbd53c6fdba0f52935fd41d37a2e4e22355c272004f920d63070de849f66eea7aeb4a081
  languageName: node
  linkType: hard

"regexp-tree@npm:~0.1.1":
  version: 0.1.27
  resolution: "regexp-tree@npm:0.1.27"
  bin:
    regexp-tree: bin/regexp-tree
  checksum: 10/08c70c8adb5a0d4af1061bf9eb05d3b6e1d948c433d6b7008e4b5eb12a49429c2d6ca8e9106339a432aa0d07bd6e1bccc638d8f4ab0d045f3adad22182b300a2
  languageName: node
  linkType: hard

"regexp.prototype.flags@npm:^1.5.4":
  version: 1.5.4
  resolution: "regexp.prototype.flags@npm:1.5.4"
  dependencies:
    call-bind: "npm:^1.0.8"
    define-properties: "npm:^1.2.1"
    es-errors: "npm:^1.3.0"
    get-proto: "npm:^1.0.1"
    gopd: "npm:^1.2.0"
    set-function-name: "npm:^2.0.2"
  checksum: 10/8ab897ca445968e0b96f6237641510f3243e59c180ee2ee8d83889c52ff735dd1bf3657fcd36db053e35e1d823dd53f2565d0b8021ea282c9fe62401c6c3bd6d
  languageName: node
  linkType: hard

"request@npm:^2.88.2":
  version: 2.88.2
  resolution: "request@npm:2.88.2"
  dependencies:
    aws-sign2: "npm:~0.7.0"
    aws4: "npm:^1.8.0"
    caseless: "npm:~0.12.0"
    combined-stream: "npm:~1.0.6"
    extend: "npm:~3.0.2"
    forever-agent: "npm:~0.6.1"
    form-data: "npm:~2.3.2"
    har-validator: "npm:~5.1.3"
    http-signature: "npm:~1.2.0"
    is-typedarray: "npm:~1.0.0"
    isstream: "npm:~0.1.2"
    json-stringify-safe: "npm:~5.0.1"
    mime-types: "npm:~2.1.19"
    oauth-sign: "npm:~0.9.0"
    performance-now: "npm:^2.1.0"
    qs: "npm:~6.5.2"
    safe-buffer: "npm:^5.1.2"
    tough-cookie: "npm:~2.5.0"
    tunnel-agent: "npm:^0.6.0"
    uuid: "npm:^3.3.2"
  checksum: 10/005b8b237b56f1571cfd4ecc09772adaa2e82dcb884fc14ea2bb25e23dbf7c2009f9929e0b6d3fd5802e33ed8ee705a3b594c8f9467c1458cd973872bf89db8e
  languageName: node
  linkType: hard

"require-at@npm:^1.0.6":
  version: 1.0.6
  resolution: "require-at@npm:1.0.6"
  checksum: 10/7753a6ebad99855ef015d5533a787c65e883c94c23371368eebf6f1c7e2a078811013b204823152cbab206a00e825e8e5ca09416fd835a489fa30bf064fbe6d9
  languageName: node
  linkType: hard

"require-directory@npm:^2.1.1":
  version: 2.1.1
  resolution: "require-directory@npm:2.1.1"
  checksum: 10/a72468e2589270d91f06c7d36ec97a88db53ae5d6fe3787fadc943f0b0276b10347f89b363b2a82285f650bdcc135ad4a257c61bdd4d00d6df1fa24875b0ddaf
  languageName: node
  linkType: hard

"require-main-filename@npm:^2.0.0":
  version: 2.0.0
  resolution: "require-main-filename@npm:2.0.0"
  checksum: 10/8604a570c06a69c9d939275becc33a65676529e1c3e5a9f42d58471674df79357872b96d70bb93a0380a62d60dc9031c98b1a9dad98c946ffdd61b7ac0c8cedd
  languageName: node
  linkType: hard

"resolve-alpn@npm:^1.0.0":
  version: 1.2.1
  resolution: "resolve-alpn@npm:1.2.1"
  checksum: 10/744e87888f0b6fa0b256ab454ca0b9c0b80808715e2ef1f3672773665c92a941f6181194e30ccae4a8cd0adbe0d955d3f133102636d2ee0cca0119fec0bc9aec
  languageName: node
  linkType: hard

"resolve-from@npm:^4.0.0":
  version: 4.0.0
  resolution: "resolve-from@npm:4.0.0"
  checksum: 10/91eb76ce83621eea7bbdd9b55121a5c1c4a39e54a9ce04a9ad4517f102f8b5131c2cf07622c738a6683991bf54f2ce178f5a42803ecbd527ddc5105f362cc9e3
  languageName: node
  linkType: hard

"resolve-pkg-maps@npm:^1.0.0":
  version: 1.0.0
  resolution: "resolve-pkg-maps@npm:1.0.0"
  checksum: 10/0763150adf303040c304009231314d1e84c6e5ebfa2d82b7d94e96a6e82bacd1dcc0b58ae257315f3c8adb89a91d8d0f12928241cba2df1680fbe6f60bf99b0e
  languageName: node
  linkType: hard

"responselike@npm:^2.0.0":
  version: 2.0.1
  resolution: "responselike@npm:2.0.1"
  dependencies:
    lowercase-keys: "npm:^2.0.0"
  checksum: 10/b122535466e9c97b55e69c7f18e2be0ce3823c5d47ee8de0d9c0b114aa55741c6db8bfbfce3766a94d1272e61bfb1ebf0a15e9310ac5629fbb7446a861b4fd3a
  languageName: node
  linkType: hard

"retry-axios@npm:<3.x":
  version: 2.6.0
  resolution: "retry-axios@npm:2.6.0"
  peerDependencies:
    axios: "*"
  checksum: 10/694244257ca7b8b8f7934f681936d79cd49c3d3085e91ef9ab3d714fd9ba3e641af37ebc90d6bb102312a072a3b17967b1318634b38c0333d0242ec1978de81d
  languageName: node
  linkType: hard

"retry@npm:^0.12.0":
  version: 0.12.0
  resolution: "retry@npm:0.12.0"
  checksum: 10/1f914879f97e7ee931ad05fe3afa629bd55270fc6cf1c1e589b6a99fab96d15daad0fa1a52a00c729ec0078045fe3e399bd4fd0c93bcc906957bdc17f89cb8e6
  languageName: node
  linkType: hard

"retry@npm:^0.13.1":
  version: 0.13.1
  resolution: "retry@npm:0.13.1"
  checksum: 10/6125ec2e06d6e47e9201539c887defba4e47f63471db304c59e4b82fc63c8e89ca06a77e9d34939a9a42a76f00774b2f46c0d4a4cbb3e287268bd018ed69426d
  languageName: node
  linkType: hard

"rfdc@npm:^1.4.1":
  version: 1.4.1
  resolution: "rfdc@npm:1.4.1"
  checksum: 10/2f3d11d3d8929b4bfeefc9acb03aae90f971401de0add5ae6c5e38fec14f0405e6a4aad8fdb76344bfdd20c5193110e3750cbbd28ba86d73729d222b6cf4a729
  languageName: node
  linkType: hard

"router@npm:^2.2.0":
  version: 2.2.0
  resolution: "router@npm:2.2.0"
  dependencies:
    debug: "npm:^4.4.0"
    depd: "npm:^2.0.0"
    is-promise: "npm:^4.0.0"
    parseurl: "npm:^1.3.3"
    path-to-regexp: "npm:^8.0.0"
  checksum: 10/8949bd1d3da5403cc024e2989fee58d7fda0f3ffe9f2dc5b8a192f295f400b3cde307b0b554f7d44851077640f36962ca469a766b3d57410d7d96245a7ba6c91
  languageName: node
  linkType: hard

"safe-array-concat@npm:^1.1.3":
  version: 1.1.3
  resolution: "safe-array-concat@npm:1.1.3"
  dependencies:
    call-bind: "npm:^1.0.8"
    call-bound: "npm:^1.0.2"
    get-intrinsic: "npm:^1.2.6"
    has-symbols: "npm:^1.1.0"
    isarray: "npm:^2.0.5"
  checksum: 10/fac4f40f20a3f7da024b54792fcc61059e814566dcbb04586bfefef4d3b942b2408933f25b7b3dd024affd3f2a6bbc916bef04807855e4f192413941369db864
  languageName: node
  linkType: hard

"safe-buffer@npm:5.1.1":
  version: 5.1.1
  resolution: "safe-buffer@npm:5.1.1"
  checksum: 10/e8acac337b7d7e108fcfe2b8b2cb20952abb1ed11dc60968b7adffb19b9477893d44136987a420f90ff4d7a0a1a932f147b3a222f73001f59fb4822097a1616d
  languageName: node
  linkType: hard

"safe-buffer@npm:5.2.1, safe-buffer@npm:^5.0.1, safe-buffer@npm:^5.1.1, safe-buffer@npm:^5.1.2, safe-buffer@npm:~5.2.0":
  version: 5.2.1
  resolution: "safe-buffer@npm:5.2.1"
  checksum: 10/32872cd0ff68a3ddade7a7617b8f4c2ae8764d8b7d884c651b74457967a9e0e886267d3ecc781220629c44a865167b61c375d2da6c720c840ecd73f45d5d9451
  languageName: node
  linkType: hard

"safe-buffer@npm:~5.1.0, safe-buffer@npm:~5.1.1":
  version: 5.1.2
  resolution: "safe-buffer@npm:5.1.2"
  checksum: 10/7eb5b48f2ed9a594a4795677d5a150faa7eb54483b2318b568dc0c4fc94092a6cce5be02c7288a0500a156282f5276d5688bce7259299568d1053b2150ef374a
  languageName: node
  linkType: hard

"safe-push-apply@npm:^1.0.0":
  version: 1.0.0
  resolution: "safe-push-apply@npm:1.0.0"
  dependencies:
    es-errors: "npm:^1.3.0"
    isarray: "npm:^2.0.5"
  checksum: 10/2bd4e53b6694f7134b9cf93631480e7fafc8637165f0ee91d5a4af5e7f33d37de9562d1af5021178dd4217d0230cde8d6530fa28cfa1ebff9a431bf8fff124b4
  languageName: node
  linkType: hard

"safe-regex-test@npm:^1.1.0":
  version: 1.1.0
  resolution: "safe-regex-test@npm:1.1.0"
  dependencies:
    call-bound: "npm:^1.0.2"
    es-errors: "npm:^1.3.0"
    is-regex: "npm:^1.2.1"
  checksum: 10/ebdb61f305bf4756a5b023ad86067df5a11b26898573afe9e52a548a63c3bd594825d9b0e2dde2eb3c94e57e0e04ac9929d4107c394f7b8e56a4613bed46c69a
  languageName: node
  linkType: hard

"safe-regex@npm:^2.1.1":
  version: 2.1.1
  resolution: "safe-regex@npm:2.1.1"
  dependencies:
    regexp-tree: "npm:~0.1.1"
  checksum: 10/180d264110cdac9935877e5c37d17b89bd7e3a9bac982439e61517e4e0dfb0821e89ed49cb84c2d9690d18b33a0edf46d4decc6989e295ba2c866c08ed8b441a
  languageName: node
  linkType: hard

"safer-buffer@npm:>= 2.1.2 < 3, safer-buffer@npm:>= 2.1.2 < 3.0.0, safer-buffer@npm:^2.0.2, safer-buffer@npm:^2.1.0, safer-buffer@npm:~2.1.0":
  version: 2.1.2
  resolution: "safer-buffer@npm:2.1.2"
  checksum: 10/7eaf7a0cf37cc27b42fb3ef6a9b1df6e93a1c6d98c6c6702b02fe262d5fcbd89db63320793b99b21cb5348097d0a53de81bd5f4e8b86e20cc9412e3f1cfb4e83
  languageName: node
  linkType: hard

"saslprep@npm:^1.0.0":
  version: 1.0.3
  resolution: "saslprep@npm:1.0.3"
  dependencies:
    sparse-bitfield: "npm:^3.0.3"
  checksum: 10/d6cae5f0adc960f355b7a78c25616c2aea31e7eeb6322eb2d553f09f1db249594651c1e5d54910e9a47b1dc6131beda82db13ffafbceea92f2a673d69c839982
  languageName: node
  linkType: hard

"sax@npm:>=0.6.0":
  version: 1.4.1
  resolution: "sax@npm:1.4.1"
  checksum: 10/b1c784b545019187b53a0c28edb4f6314951c971e2963a69739c6ce222bfbc767e54d320e689352daba79b7d5e06d22b5d7113b99336219d6e93718e2f99d335
  languageName: node
  linkType: hard

"semver@npm:^5.1.0":
  version: 5.7.2
  resolution: "semver@npm:5.7.2"
  bin:
    semver: bin/semver
  checksum: 10/fca14418a174d4b4ef1fecb32c5941e3412d52a4d3d85165924ce3a47fbc7073372c26faf7484ceb4bbc2bde25880c6b97e492473dc7e9708fdfb1c6a02d546e
  languageName: node
  linkType: hard

"semver@npm:^6.3.1":
  version: 6.3.1
  resolution: "semver@npm:6.3.1"
  bin:
    semver: bin/semver.js
  checksum: 10/1ef3a85bd02a760c6ef76a45b8c1ce18226de40831e02a00bad78485390b98b6ccaa31046245fc63bba4a47a6a592b6c7eedc65cc47126e60489f9cc1ce3ed7e
  languageName: node
  linkType: hard

"semver@npm:^7.3.5, semver@npm:^7.5.4, semver@npm:^7.6.3":
  version: 7.7.2
  resolution: "semver@npm:7.7.2"
  bin:
    semver: bin/semver.js
  checksum: 10/7a24cffcaa13f53c09ce55e05efe25cd41328730b2308678624f8b9f5fc3093fc4d189f47950f0b811ff8f3c3039c24a2c36717ba7961615c682045bf03e1dda
  languageName: node
  linkType: hard

"send@npm:0.19.0":
  version: 0.19.0
  resolution: "send@npm:0.19.0"
  dependencies:
    debug: "npm:2.6.9"
    depd: "npm:2.0.0"
    destroy: "npm:1.2.0"
    encodeurl: "npm:~1.0.2"
    escape-html: "npm:~1.0.3"
    etag: "npm:~1.8.1"
    fresh: "npm:0.5.2"
    http-errors: "npm:2.0.0"
    mime: "npm:1.6.0"
    ms: "npm:2.1.3"
    on-finished: "npm:2.4.1"
    range-parser: "npm:~1.2.1"
    statuses: "npm:2.0.1"
  checksum: 10/1f6064dea0ae4cbe4878437aedc9270c33f2a6650a77b56a16b62d057527f2766d96ee282997dd53ec0339082f2aad935bc7d989b46b48c82fc610800dc3a1d0
  languageName: node
  linkType: hard

"send@npm:^1.1.0, send@npm:^1.2.0":
  version: 1.2.0
  resolution: "send@npm:1.2.0"
  dependencies:
    debug: "npm:^4.3.5"
    encodeurl: "npm:^2.0.0"
    escape-html: "npm:^1.0.3"
    etag: "npm:^1.8.1"
    fresh: "npm:^2.0.0"
    http-errors: "npm:^2.0.0"
    mime-types: "npm:^3.0.1"
    ms: "npm:^2.1.3"
    on-finished: "npm:^2.4.1"
    range-parser: "npm:^1.2.1"
    statuses: "npm:^2.0.1"
  checksum: 10/9fa3b1a3b9a06b7b4ab00c25e8228326d9665a9745753a34d1ffab8ac63c7c206727331d1dc5be73647f1b658d259a1aa8e275b0e0eee51349370af02e9da506
  languageName: node
  linkType: hard

"sentence-case@npm:^3.0.4":
  version: 3.0.4
  resolution: "sentence-case@npm:3.0.4"
  dependencies:
    no-case: "npm:^3.0.4"
    tslib: "npm:^2.0.3"
    upper-case-first: "npm:^2.0.2"
  checksum: 10/3cfe6c0143e649132365695706702d7f729f484fa7b25f43435876efe7af2478243eefb052bacbcce10babf9319fd6b5b6bc59b94c80a1c819bcbb40651465d5
  languageName: node
  linkType: hard

"serve-favicon@npm:^2.5.0":
  version: 2.5.0
  resolution: "serve-favicon@npm:2.5.0"
  dependencies:
    etag: "npm:~1.8.1"
    fresh: "npm:0.5.2"
    ms: "npm:2.1.1"
    parseurl: "npm:~1.3.2"
    safe-buffer: "npm:5.1.1"
  checksum: 10/dcb2734bf977a949a0a3bd50f2faf2893314101bdaa034c56baa4fba9bee2ab7f91a013d806b858c793fa50809170e907ced53c4e8ed1797fe0b472b5c6d9936
  languageName: node
  linkType: hard

"serve-static@npm:1.16.2":
  version: 1.16.2
  resolution: "serve-static@npm:1.16.2"
  dependencies:
    encodeurl: "npm:~2.0.0"
    escape-html: "npm:~1.0.3"
    parseurl: "npm:~1.3.3"
    send: "npm:0.19.0"
  checksum: 10/7fa9d9c68090f6289976b34fc13c50ac8cd7f16ae6bce08d16459300f7fc61fbc2d7ebfa02884c073ec9d6ab9e7e704c89561882bbe338e99fcacb2912fde737
  languageName: node
  linkType: hard

"serve-static@npm:^2.2.0":
  version: 2.2.0
  resolution: "serve-static@npm:2.2.0"
  dependencies:
    encodeurl: "npm:^2.0.0"
    escape-html: "npm:^1.0.3"
    parseurl: "npm:^1.3.3"
    send: "npm:^1.2.0"
  checksum: 10/9f1a900738c5bb02258275ce3bd1273379c4c3072b622e15d44e8f47d89a1ba2d639ec2d63b11c263ca936096b40758acb7a0d989cd6989018a65a12f9433ada
  languageName: node
  linkType: hard

"set-blocking@npm:^2.0.0":
  version: 2.0.0
  resolution: "set-blocking@npm:2.0.0"
  checksum: 10/8980ebf7ae9eb945bb036b6e283c547ee783a1ad557a82babf758a065e2fb6ea337fd82cac30dd565c1e606e423f30024a19fff7afbf4977d784720c4026a8ef
  languageName: node
  linkType: hard

"set-function-length@npm:^1.2.2":
  version: 1.2.2
  resolution: "set-function-length@npm:1.2.2"
  dependencies:
    define-data-property: "npm:^1.1.4"
    es-errors: "npm:^1.3.0"
    function-bind: "npm:^1.1.2"
    get-intrinsic: "npm:^1.2.4"
    gopd: "npm:^1.0.1"
    has-property-descriptors: "npm:^1.0.2"
  checksum: 10/505d62b8e088468917ca4e3f8f39d0e29f9a563b97dbebf92f4bd2c3172ccfb3c5b8e4566d5fcd00784a00433900e7cb8fbc404e2dbd8c3818ba05bb9d4a8a6d
  languageName: node
  linkType: hard

"set-function-name@npm:^2.0.2":
  version: 2.0.2
  resolution: "set-function-name@npm:2.0.2"
  dependencies:
    define-data-property: "npm:^1.1.4"
    es-errors: "npm:^1.3.0"
    functions-have-names: "npm:^1.2.3"
    has-property-descriptors: "npm:^1.0.2"
  checksum: 10/c7614154a53ebf8c0428a6c40a3b0b47dac30587c1a19703d1b75f003803f73cdfa6a93474a9ba678fa565ef5fbddc2fae79bca03b7d22ab5fd5163dbe571a74
  languageName: node
  linkType: hard

"set-proto@npm:^1.0.0":
  version: 1.0.0
  resolution: "set-proto@npm:1.0.0"
  dependencies:
    dunder-proto: "npm:^1.0.1"
    es-errors: "npm:^1.3.0"
    es-object-atoms: "npm:^1.0.0"
  checksum: 10/b87f8187bca595ddc3c0721ece4635015fd9d7cb294e6dd2e394ce5186a71bbfa4dc8a35010958c65e43ad83cde09642660e61a952883c24fd6b45ead15f045c
  languageName: node
  linkType: hard

"set-value@npm:^4.1.0":
  version: 4.1.0
  resolution: "set-value@npm:4.1.0"
  dependencies:
    is-plain-object: "npm:^2.0.4"
    is-primitive: "npm:^3.0.1"
  checksum: 10/67eebb0d78be89242478daf8ab5357c59a35add1d7bdccdb56236e6004128a79bdf298c952e286b60b63ed17b24dcdb399734fbdacc9b76e7fd2e3e01546a42e
  languageName: node
  linkType: hard

"setprototypeof@npm:1.2.0":
  version: 1.2.0
  resolution: "setprototypeof@npm:1.2.0"
  checksum: 10/fde1630422502fbbc19e6844346778f99d449986b2f9cdcceb8326730d2f3d9964dbcb03c02aaadaefffecd0f2c063315ebea8b3ad895914bf1afc1747fc172e
  languageName: node
  linkType: hard

"shebang-command@npm:^2.0.0":
  version: 2.0.0
  resolution: "shebang-command@npm:2.0.0"
  dependencies:
    shebang-regex: "npm:^3.0.0"
  checksum: 10/6b52fe87271c12968f6a054e60f6bde5f0f3d2db483a1e5c3e12d657c488a15474121a1d55cd958f6df026a54374ec38a4a963988c213b7570e1d51575cea7fa
  languageName: node
  linkType: hard

"shebang-regex@npm:^3.0.0":
  version: 3.0.0
  resolution: "shebang-regex@npm:3.0.0"
  checksum: 10/1a2bcae50de99034fcd92ad4212d8e01eedf52c7ec7830eedcf886622804fe36884278f2be8be0ea5fde3fd1c23911643a4e0f726c8685b61871c8908af01222
  languageName: node
  linkType: hard

"shopify-api-node@npm:^3.15.0":
  version: 3.15.0
  resolution: "shopify-api-node@npm:3.15.0"
  dependencies:
    got: "npm:^11.1.4"
    lodash: "npm:^4.17.10"
    qs: "npm:^6.5.2"
    stopcock: "npm:^1.0.0"
  checksum: 10/422fafbf9a597eb9f353c972d307c55d633900836ca1e2588dee62345fce6995615bd602ce4c0f37cc99d01eefc766abb6db43a9598c1103c92f8ab16ecb6cd3
  languageName: node
  linkType: hard

"side-channel-list@npm:^1.0.0":
  version: 1.0.0
  resolution: "side-channel-list@npm:1.0.0"
  dependencies:
    es-errors: "npm:^1.3.0"
    object-inspect: "npm:^1.13.3"
  checksum: 10/603b928997abd21c5a5f02ae6b9cc36b72e3176ad6827fab0417ead74580cc4fb4d5c7d0a8a2ff4ead34d0f9e35701ed7a41853dac8a6d1a664fcce1a044f86f
  languageName: node
  linkType: hard

"side-channel-map@npm:^1.0.1":
  version: 1.0.1
  resolution: "side-channel-map@npm:1.0.1"
  dependencies:
    call-bound: "npm:^1.0.2"
    es-errors: "npm:^1.3.0"
    get-intrinsic: "npm:^1.2.5"
    object-inspect: "npm:^1.13.3"
  checksum: 10/5771861f77feefe44f6195ed077a9e4f389acc188f895f570d56445e251b861754b547ea9ef73ecee4e01fdada6568bfe9020d2ec2dfc5571e9fa1bbc4a10615
  languageName: node
  linkType: hard

"side-channel-weakmap@npm:^1.0.2":
  version: 1.0.2
  resolution: "side-channel-weakmap@npm:1.0.2"
  dependencies:
    call-bound: "npm:^1.0.2"
    es-errors: "npm:^1.3.0"
    get-intrinsic: "npm:^1.2.5"
    object-inspect: "npm:^1.13.3"
    side-channel-map: "npm:^1.0.1"
  checksum: 10/a815c89bc78c5723c714ea1a77c938377ea710af20d4fb886d362b0d1f8ac73a17816a5f6640f354017d7e292a43da9c5e876c22145bac00b76cfb3468001736
  languageName: node
  linkType: hard

"side-channel@npm:^1.0.6, side-channel@npm:^1.1.0":
  version: 1.1.0
  resolution: "side-channel@npm:1.1.0"
  dependencies:
    es-errors: "npm:^1.3.0"
    object-inspect: "npm:^1.13.3"
    side-channel-list: "npm:^1.0.0"
    side-channel-map: "npm:^1.0.1"
    side-channel-weakmap: "npm:^1.0.2"
  checksum: 10/7d53b9db292c6262f326b6ff3bc1611db84ece36c2c7dc0e937954c13c73185b0406c56589e2bb8d071d6fee468e14c39fb5d203ee39be66b7b8174f179afaba
  languageName: node
  linkType: hard

"sift@npm:^17.1.3":
  version: 17.1.3
  resolution: "sift@npm:17.1.3"
  checksum: 10/278a0308cb4c7a48620caeb25339072fcef50d9a1296bc78c9710b1bb974ef5b3e375acf6646fe0a698e0f3ea19af81c11a348980dc28ff80c979e050f7f8585
  languageName: node
  linkType: hard

"signal-exit@npm:^3.0.2":
  version: 3.0.7
  resolution: "signal-exit@npm:3.0.7"
  checksum: 10/a2f098f247adc367dffc27845853e9959b9e88b01cb301658cfe4194352d8d2bb32e18467c786a7fe15f1d44b233ea35633d076d5e737870b7139949d1ab6318
  languageName: node
  linkType: hard

"signal-exit@npm:^4.0.1":
  version: 4.1.0
  resolution: "signal-exit@npm:4.1.0"
  checksum: 10/c9fa63bbbd7431066174a48ba2dd9986dfd930c3a8b59de9c29d7b6854ec1c12a80d15310869ea5166d413b99f041bfa3dd80a7947bcd44ea8e6eb3ffeabfa1f
  languageName: node
  linkType: hard

"similarity@npm:^1.2.1":
  version: 1.2.1
  resolution: "similarity@npm:1.2.1"
  dependencies:
    levenshtein-edit-distance: "npm:^2.0.0"
  bin:
    similarity: cli.js
  checksum: 10/7010abfb53ea72fcecb3b9f59e0753f589256b8a134886a5282cd1ee21226fdc7f11bfe45f673631928d8ae88283f23e6b6f5c4e84ab3f15d175b2d520e1bffe
  languageName: node
  linkType: hard

"smart-buffer@npm:^4.2.0":
  version: 4.2.0
  resolution: "smart-buffer@npm:4.2.0"
  checksum: 10/927484aa0b1640fd9473cee3e0a0bcad6fce93fd7bbc18bac9ad0c33686f5d2e2c422fba24b5899c184524af01e11dd2bd051c2bf2b07e47aff8ca72cbfc60d2
  languageName: node
  linkType: hard

"smtp-connection@npm:2.12.0":
  version: 2.12.0
  resolution: "smtp-connection@npm:2.12.0"
  dependencies:
    httpntlm: "npm:1.6.1"
    nodemailer-shared: "npm:1.1.0"
  checksum: 10/2304e2bd83878929af616e0bdb3944c4d927bfab74c983d38114c2ccf5c7b0137a028474daf45b7c2074f6eea554683fbf3926f3dac53af0f66335626e6cd0cf
  languageName: node
  linkType: hard

"snake-case@npm:^3.0.4":
  version: 3.0.4
  resolution: "snake-case@npm:3.0.4"
  dependencies:
    dot-case: "npm:^3.0.4"
    tslib: "npm:^2.0.3"
  checksum: 10/0a7a79900bbb36f8aaa922cf111702a3647ac6165736d5dc96d3ef367efc50465cac70c53cd172c382b022dac72ec91710608e5393de71f76d7142e6fd80e8a3
  languageName: node
  linkType: hard

"socks-proxy-agent@npm:^8.0.3":
  version: 8.0.5
  resolution: "socks-proxy-agent@npm:8.0.5"
  dependencies:
    agent-base: "npm:^7.1.2"
    debug: "npm:^4.3.4"
    socks: "npm:^2.8.3"
  checksum: 10/ee99e1dacab0985b52cbe5a75640be6e604135e9489ebdc3048635d186012fbaecc20fbbe04b177dee434c319ba20f09b3e7dfefb7d932466c0d707744eac05c
  languageName: node
  linkType: hard

"socks@npm:^2.8.3":
  version: 2.8.4
  resolution: "socks@npm:2.8.4"
  dependencies:
    ip-address: "npm:^9.0.5"
    smart-buffer: "npm:^4.2.0"
  checksum: 10/ab3af97aeb162f32c80e176c717ccf16a11a6ebb4656a62b94c0f96495ea2a1f4a8206c04b54438558485d83d0c5f61920c07a1a5d3963892a589b40cc6107dd
  languageName: node
  linkType: hard

"sparse-bitfield@npm:^3.0.3":
  version: 3.0.3
  resolution: "sparse-bitfield@npm:3.0.3"
  dependencies:
    memory-pager: "npm:^1.0.2"
  checksum: 10/174da88dbbcc783d5dbd26921931cc83830280b8055fb05333786ebe6fc015b9601b24972b3d55920dd2d9f5fb120576fbfa2469b08e5222c9cadf3f05210aab
  languageName: node
  linkType: hard

"split-on-first@npm:^1.0.0":
  version: 1.1.0
  resolution: "split-on-first@npm:1.1.0"
  checksum: 10/16ff85b54ddcf17f9147210a4022529b343edbcbea4ce977c8f30e38408b8d6e0f25f92cd35b86a524d4797f455e29ab89eb8db787f3c10708e0b47ebf528d30
  languageName: node
  linkType: hard

"sprintf-js@npm:^1.1.1, sprintf-js@npm:^1.1.3":
  version: 1.1.3
  resolution: "sprintf-js@npm:1.1.3"
  checksum: 10/e7587128c423f7e43cc625fe2f87e6affdf5ca51c1cc468e910d8aaca46bb44a7fbcfa552f787b1d3987f7043aeb4527d1b99559e6621e01b42b3f45e5a24cbb
  languageName: node
  linkType: hard

"sprintf-js@npm:~1.0.2":
  version: 1.0.3
  resolution: "sprintf-js@npm:1.0.3"
  checksum: 10/c34828732ab8509c2741e5fd1af6b767c3daf2c642f267788f933a65b1614943c282e74c4284f4fa749c264b18ee016a0d37a3e5b73aee446da46277d3a85daa
  languageName: node
  linkType: hard

"sse@npm:0.0.8":
  version: 0.0.8
  resolution: "sse@npm:0.0.8"
  dependencies:
    options: "npm:0.0.6"
  checksum: 10/aa4e28d841973030db7ee9251b36d42e37cb278af55b9a2e1e5d1602a0a3c27853da88ab2d61d6bea93605cb2235d59bebf4a8fd59912f63f552aff91c55eb22
  languageName: node
  linkType: hard

"sshpk@npm:^1.7.0":
  version: 1.18.0
  resolution: "sshpk@npm:1.18.0"
  dependencies:
    asn1: "npm:~0.2.3"
    assert-plus: "npm:^1.0.0"
    bcrypt-pbkdf: "npm:^1.0.0"
    dashdash: "npm:^1.12.0"
    ecc-jsbn: "npm:~0.1.1"
    getpass: "npm:^0.1.1"
    jsbn: "npm:~0.1.0"
    safer-buffer: "npm:^2.0.2"
    tweetnacl: "npm:~0.14.0"
  bin:
    sshpk-conv: bin/sshpk-conv
    sshpk-sign: bin/sshpk-sign
    sshpk-verify: bin/sshpk-verify
  checksum: 10/858339d43e3c6b6a848772a66f69442ce74f1a37655d9f35ba9d1f85329499ff0000af9f8ab83dbb39ad24c0c370edabe0be1e39863f70c6cded9924b8458c34
  languageName: node
  linkType: hard

"ssri@npm:^12.0.0":
  version: 12.0.0
  resolution: "ssri@npm:12.0.0"
  dependencies:
    minipass: "npm:^7.0.3"
  checksum: 10/7024c1a6e39b3f18aa8f1c8290e884fe91b0f9ca5a6c6d410544daad54de0ba664db879afe16412e187c6c292fd60b937f047ee44292e5c2af2dcc6d8e1a9b48
  languageName: node
  linkType: hard

"stable@npm:^0.1.8":
  version: 0.1.8
  resolution: "stable@npm:0.1.8"
  checksum: 10/2ff482bb100285d16dd75cd8f7c60ab652570e8952c0bfa91828a2b5f646a0ff533f14596ea4eabd48bb7f4aeea408dce8f8515812b975d958a4cc4fa6b9dfeb
  languageName: node
  linkType: hard

"standard-as-callback@npm:^2.1.0":
  version: 2.1.0
  resolution: "standard-as-callback@npm:2.1.0"
  checksum: 10/88bec83ee220687c72d94fd86a98d5272c91d37ec64b66d830dbc0d79b62bfa6e47f53b71646011835fc9ce7fae62739545d13124262b53be4fbb3e2ebad551c
  languageName: node
  linkType: hard

"statuses@npm:2.0.1, statuses@npm:^2.0.1":
  version: 2.0.1
  resolution: "statuses@npm:2.0.1"
  checksum: 10/18c7623fdb8f646fb213ca4051be4df7efb3484d4ab662937ca6fbef7ced9b9e12842709872eb3020cc3504b93bde88935c9f6417489627a7786f24f8031cbcb
  languageName: node
  linkType: hard

"stopcock@npm:^1.0.0":
  version: 1.1.0
  resolution: "stopcock@npm:1.1.0"
  checksum: 10/9566f483e3a58fc717070d199fdb1174b3e83c3d7dbed3cec9fcce124032e2ba5de6083c5a4082ca1cdfa166297c112e26be4f47656e75192ba302242f8656f8
  languageName: node
  linkType: hard

"stream-combiner@npm:0.0.2":
  version: 0.0.2
  resolution: "stream-combiner@npm:0.0.2"
  dependencies:
    duplexer: "npm:~0.0.3"
  checksum: 10/44b6981c4017892ab9f9915848b87e9a6720977029b5c3be0991cb67f5f4d1938f64e92ecd51ee4782d2f49500e4a44aee5ce00992a11af0554b1c5b00fc55f8
  languageName: node
  linkType: hard

"stream-length@npm:^1.0.2":
  version: 1.0.2
  resolution: "stream-length@npm:1.0.2"
  dependencies:
    bluebird: "npm:^2.6.2"
  checksum: 10/676c8c4eb86c9a3398096bc9c809cd9a61bb6a10f6d731ded1d350a93c49d2496d9a9b0185308b84accc4d97d4e37cecd0b9bc3b3928fa66310821eec1e2fb65
  languageName: node
  linkType: hard

"stream-serializer@npm:~1.1.1":
  version: 1.1.2
  resolution: "stream-serializer@npm:1.1.2"
  checksum: 10/00894859e07b35425482c29fbc52e151f20ef48009743e89d1fa68d5b3ea4cbb44bc8675863d037310ecaa11fc86414e3fa0aa9005144f3de881a0adedfdd9a4
  languageName: node
  linkType: hard

"strict-uri-encode@npm:^2.0.0":
  version: 2.0.0
  resolution: "strict-uri-encode@npm:2.0.0"
  checksum: 10/eaac4cf978b6fbd480f1092cab8b233c9b949bcabfc9b598dd79a758f7243c28765ef7639c876fa72940dac687181b35486ea01ff7df3e65ce3848c64822c581
  languageName: node
  linkType: hard

"string-width-cjs@npm:string-width@^4.2.0, string-width@npm:^4.1.0, string-width@npm:^4.2.0":
  version: 4.2.3
  resolution: "string-width@npm:4.2.3"
  dependencies:
    emoji-regex: "npm:^8.0.0"
    is-fullwidth-code-point: "npm:^3.0.0"
    strip-ansi: "npm:^6.0.1"
  checksum: 10/e52c10dc3fbfcd6c3a15f159f54a90024241d0f149cf8aed2982a2d801d2e64df0bf1dc351cf8e95c3319323f9f220c16e740b06faecd53e2462df1d2b5443fb
  languageName: node
  linkType: hard

"string-width@npm:^5.0.1, string-width@npm:^5.1.2":
  version: 5.1.2
  resolution: "string-width@npm:5.1.2"
  dependencies:
    eastasianwidth: "npm:^0.2.0"
    emoji-regex: "npm:^9.2.2"
    strip-ansi: "npm:^7.0.1"
  checksum: 10/7369deaa29f21dda9a438686154b62c2c5f661f8dda60449088f9f980196f7908fc39fdd1803e3e01541970287cf5deae336798337e9319a7055af89dafa7193
  languageName: node
  linkType: hard

"string.prototype.trim@npm:^1.2.10":
  version: 1.2.10
  resolution: "string.prototype.trim@npm:1.2.10"
  dependencies:
    call-bind: "npm:^1.0.8"
    call-bound: "npm:^1.0.2"
    define-data-property: "npm:^1.1.4"
    define-properties: "npm:^1.2.1"
    es-abstract: "npm:^1.23.5"
    es-object-atoms: "npm:^1.0.0"
    has-property-descriptors: "npm:^1.0.2"
  checksum: 10/47bb63cd2470a64bc5e2da1e570d369c016ccaa85c918c3a8bb4ab5965120f35e66d1f85ea544496fac84b9207a6b722adf007e6c548acd0813e5f8a82f9712a
  languageName: node
  linkType: hard

"string.prototype.trimend@npm:^1.0.9":
  version: 1.0.9
  resolution: "string.prototype.trimend@npm:1.0.9"
  dependencies:
    call-bind: "npm:^1.0.8"
    call-bound: "npm:^1.0.2"
    define-properties: "npm:^1.2.1"
    es-object-atoms: "npm:^1.0.0"
  checksum: 10/140c73899b6747de9e499c7c2e7a83d549c47a26fa06045b69492be9cfb9e2a95187499a373983a08a115ecff8bc3bd7b0fb09b8ff72fb2172abe766849272ef
  languageName: node
  linkType: hard

"string.prototype.trimstart@npm:^1.0.8":
  version: 1.0.8
  resolution: "string.prototype.trimstart@npm:1.0.8"
  dependencies:
    call-bind: "npm:^1.0.7"
    define-properties: "npm:^1.2.1"
    es-object-atoms: "npm:^1.0.0"
  checksum: 10/160167dfbd68e6f7cb9f51a16074eebfce1571656fc31d40c3738ca9e30e35496f2c046fe57b6ad49f65f238a152be8c86fd9a2dd58682b5eba39dad995b3674
  languageName: node
  linkType: hard

"string_decoder@npm:^1.3.0":
  version: 1.3.0
  resolution: "string_decoder@npm:1.3.0"
  dependencies:
    safe-buffer: "npm:~5.2.0"
  checksum: 10/54d23f4a6acae0e93f999a585e673be9e561b65cd4cca37714af1e893ab8cd8dfa52a9e4f58f48f87b4a44918d3a9254326cb80ed194bf2e4c226e2b21767e56
  languageName: node
  linkType: hard

"string_decoder@npm:~1.1.1":
  version: 1.1.1
  resolution: "string_decoder@npm:1.1.1"
  dependencies:
    safe-buffer: "npm:~5.1.0"
  checksum: 10/7c41c17ed4dea105231f6df208002ebddd732e8e9e2d619d133cecd8e0087ddfd9587d2feb3c8caf3213cbd841ada6d057f5142cae68a4e62d3540778d9819b4
  languageName: node
  linkType: hard

"strip-ansi-cjs@npm:strip-ansi@^6.0.1, strip-ansi@npm:^6.0.0, strip-ansi@npm:^6.0.1":
  version: 6.0.1
  resolution: "strip-ansi@npm:6.0.1"
  dependencies:
    ansi-regex: "npm:^5.0.1"
  checksum: 10/ae3b5436d34fadeb6096367626ce987057713c566e1e7768818797e00ac5d62023d0f198c4e681eae9e20701721980b26a64a8f5b91238869592a9c6800719a2
  languageName: node
  linkType: hard

"strip-ansi@npm:^7.0.1":
  version: 7.1.0
  resolution: "strip-ansi@npm:7.1.0"
  dependencies:
    ansi-regex: "npm:^6.0.1"
  checksum: 10/475f53e9c44375d6e72807284024ac5d668ee1d06010740dec0b9744f2ddf47de8d7151f80e5f6190fc8f384e802fdf9504b76a7e9020c9faee7103623338be2
  languageName: node
  linkType: hard

"strip-final-newline@npm:^2.0.0":
  version: 2.0.0
  resolution: "strip-final-newline@npm:2.0.0"
  checksum: 10/69412b5e25731e1938184b5d489c32e340605bb611d6140344abc3421b7f3c6f9984b21dff296dfcf056681b82caa3bb4cc996a965ce37bcfad663e92eae9c64
  languageName: node
  linkType: hard

"strip-json-comments@npm:^3.1.1":
  version: 3.1.1
  resolution: "strip-json-comments@npm:3.1.1"
  checksum: 10/492f73e27268f9b1c122733f28ecb0e7e8d8a531a6662efbd08e22cccb3f9475e90a1b82cab06a392f6afae6d2de636f977e231296400d0ec5304ba70f166443
  languageName: node
  linkType: hard

"striptags@npm:^3.2.0":
  version: 3.2.0
  resolution: "striptags@npm:3.2.0"
  checksum: 10/0d430af5c2d702cfe6e3591b5d353773811d912264b9112e11596ac4d1c6e4185a9f9d92125d324d93cfebeb8ee658ba397f1a5d7a4e44ad7cff98ce40afcfc9
  languageName: node
  linkType: hard

"strnum@npm:^1.0.5":
  version: 1.1.2
  resolution: "strnum@npm:1.1.2"
  checksum: 10/ccd6297a1fdaf0fc8ea0ea904acdae76878d49a4b0d98a70155df4bc081fd88eac5ec99fb150f3d1d1af065c1898d38420705259ba6c39aa850c671bcd54e35d
  languageName: node
  linkType: hard

"strong-error-handler@npm:3.5.0":
  version: 3.5.0
  resolution: "strong-error-handler@npm:3.5.0"
  dependencies:
    "@types/express": "npm:^4.16.0"
    accepts: "npm:^1.3.3"
    debug: "npm:^4.1.1"
    ejs: "npm:^3.1.3"
    fast-safe-stringify: "npm:^2.0.6"
    http-status: "npm:^1.1.2"
    js2xmlparser: "npm:^4.0.0"
    strong-globalize: "npm:^6.0.1"
  checksum: 10/0c8d5a9ee0d2e234c6731a290465d6b4321703ef25d309a12c202ffc51a36f4ceb140c692a2b738341f440682b8638261b070752c72e54169e981fc3b9144ff1
  languageName: node
  linkType: hard

"strong-globalize@npm:6.0.6":
  version: 6.0.6
  resolution: "strong-globalize@npm:6.0.6"
  dependencies:
    accept-language: "npm:^3.0.18"
    debug: "npm:^4.2.0"
    globalize: "npm:^1.6.0"
    lodash: "npm:^4.17.20"
    md5: "npm:^2.3.0"
    mkdirp: "npm:^1.0.4"
    os-locale: "npm:^5.0.0"
    yamljs: "npm:^0.3.0"
  checksum: 10/07880c8c8e82c8dbc6e6f9c51b1147266a60d664e1bf1583e76c73b17a0aac327205ab2e1914fa2155a78bb4bfd967e8f488d3d8a1357176598b0fbaffc3eb47
  languageName: node
  linkType: hard

"strong-remoting@github:perkd/strong-remoting#semver:3.20.2":
  version: 3.20.2
  resolution: "strong-remoting@https://github.com/perkd/strong-remoting.git#commit=38a10e81f6a82a97a90822200faecd2c6cb0ee1e"
  dependencies:
    async: "npm:^3.2.6"
    body-parser: "npm:^2.2.0"
    debug: "npm:^4.4.0"
    depd: "npm:^2.0.0"
    escape-string-regexp: "npm:4.0.0"
    eventemitter2: "npm:^6.4.9"
    express: "npm:^5.1.0"
    inflection: "npm:2.0.1"
    jayson: "npm:2.1.2"
    js2xmlparser: "npm:4.0.1"
    loopback-datatype-geopoint: "npm:^1.0.0"
    loopback-phase: "npm:^3.4.0"
    mux-demux: "npm:^3.7.9"
    qs: "npm:^6.14.0"
    request: "npm:^2.88.2"
    sse: "npm:0.0.8"
    strong-error-handler: "npm:3.5.0"
    strong-globalize: "npm:6.0.6"
    traverse: "npm:^0.6.11"
    xml2js: "npm:0.4.23"
  checksum: 10/f8bc58512f3a6b5ecad82b294dee61e62ecdc86f1396df459c1488a9b09555f38a44c366d584c40264afc18ac525d2c14636bc5871d4e81da869fed429a046ea
  languageName: node
  linkType: hard

"supports-color@npm:^7.1.0":
  version: 7.2.0
  resolution: "supports-color@npm:7.2.0"
  dependencies:
    has-flag: "npm:^4.0.0"
  checksum: 10/c8bb7afd564e3b26b50ca6ee47572c217526a1389fe018d00345856d4a9b08ffbd61fadaf283a87368d94c3dcdb8f5ffe2650a5a65863e21ad2730ca0f05210a
  languageName: node
  linkType: hard

"swagger-ui@npm:^2.2.5":
  version: 2.2.10
  resolution: "swagger-ui@npm:2.2.10"
  checksum: 10/6f810ac3cc28af8c7fef348411c00f73afa3d62a426308ff238308ee58f5962503e1dc6ee741aff0ef8cd2ea9f5137ad7e5cdfdd24e6b419ac79e74748df4e2e
  languageName: node
  linkType: hard

"synckit@npm:^0.6.2 || ^0.7.3 || ^0.11.5":
  version: 0.11.6
  resolution: "synckit@npm:0.11.6"
  dependencies:
    "@pkgr/core": "npm:^0.2.4"
  checksum: 10/e3fc13b064cb401ad4214e846f3e70037a44a221fcd303ad638c02d14e3f497ddd501b5b9f5e324e03fa2a3ca101df43f0850faa1696abaf0230a16ce9693408
  languageName: node
  linkType: hard

"tapable@npm:^2.2.0":
  version: 2.2.2
  resolution: "tapable@npm:2.2.2"
  checksum: 10/065a0dc44aba1b32020faa1c27c719e8f76e5345347515d8494bf158524f36e9f22ad9eaa5b5494f9d5d67bf0640afdd5698505948c46d720b6b7e69d19349a6
  languageName: node
  linkType: hard

"tar@npm:^7.4.3":
  version: 7.4.3
  resolution: "tar@npm:7.4.3"
  dependencies:
    "@isaacs/fs-minipass": "npm:^4.0.0"
    chownr: "npm:^3.0.0"
    minipass: "npm:^7.1.2"
    minizlib: "npm:^3.0.1"
    mkdirp: "npm:^3.0.1"
    yallist: "npm:^5.0.0"
  checksum: 10/12a2a4fc6dee23e07cc47f1aeb3a14a1afd3f16397e1350036a8f4cdfee8dcac7ef5978337a4e7b2ac2c27a9a6d46388fc2088ea7c80cb6878c814b1425f8ecf
  languageName: node
  linkType: hard

"through@npm:2.3.4":
  version: 2.3.4
  resolution: "through@npm:2.3.4"
  checksum: 10/5302b5caa3ada0b96f2350e8afb8c6ccbf885fc965f6cfeb03e0c14dc65bbdb1154028b8f27b85eb324d397b6545ea7926b29c7f48ddefb63a23e3a15dae417d
  languageName: node
  linkType: hard

"through@npm:>=2.2.7 <3, through@npm:~2.3.1":
  version: 2.3.8
  resolution: "through@npm:2.3.8"
  checksum: 10/5da78346f70139a7d213b65a0106f3c398d6bc5301f9248b5275f420abc2c4b1e77c2abc72d218dedc28c41efb2e7c312cb76a7730d04f9c2d37d247da3f4198
  languageName: node
  linkType: hard

"tinyglobby@npm:^0.2.12":
  version: 0.2.14
  resolution: "tinyglobby@npm:0.2.14"
  dependencies:
    fdir: "npm:^6.4.4"
    picomatch: "npm:^4.0.2"
  checksum: 10/3d306d319718b7cc9d79fb3f29d8655237aa6a1f280860a217f93417039d0614891aee6fc47c5db315f4fcc6ac8d55eb8e23e2de73b2c51a431b42456d9e5764
  languageName: node
  linkType: hard

"to-utf8@npm:0.0.1":
  version: 0.0.1
  resolution: "to-utf8@npm:0.0.1"
  checksum: 10/781e12fb923027333f63098070b95a07e236223be389fd2a27f7cfacb8fca102d9f2df52e918ce4ee1a3b0b7007705a8944f4c37b1d1bed89c7517a760ed2eac
  languageName: node
  linkType: hard

"toidentifier@npm:1.0.1":
  version: 1.0.1
  resolution: "toidentifier@npm:1.0.1"
  checksum: 10/952c29e2a85d7123239b5cfdd889a0dde47ab0497f0913d70588f19c53f7e0b5327c95f4651e413c74b785147f9637b17410ac8c846d5d4a20a5a33eb6dc3a45
  languageName: node
  linkType: hard

"toposort@npm:^2.0.2":
  version: 2.0.2
  resolution: "toposort@npm:2.0.2"
  checksum: 10/6f128353e4ed9739e49a28fb756b0a00f3752b29fc9b862ff781446598ee3b486cd229697feebc4eabd916eac5de219f3dae450c585bf13673f6b133a7226e06
  languageName: node
  linkType: hard

"tough-cookie@npm:~2.5.0":
  version: 2.5.0
  resolution: "tough-cookie@npm:2.5.0"
  dependencies:
    psl: "npm:^1.1.28"
    punycode: "npm:^2.1.1"
  checksum: 10/024cb13a4d1fe9af57f4323dff765dd9b217cc2a69be77e3b8a1ca45600aa33a097b6ad949f225d885e904f4bd3ceccef104741ef202d8378e6ca78e850ff82f
  languageName: node
  linkType: hard

"tr46@npm:~0.0.3":
  version: 0.0.3
  resolution: "tr46@npm:0.0.3"
  checksum: 10/8f1f5aa6cb232f9e1bdc86f485f916b7aa38caee8a778b378ffec0b70d9307873f253f5cbadbe2955ece2ac5c83d0dc14a77513166ccd0a0c7fe197e21396695
  languageName: node
  linkType: hard

"traverse@npm:^0.6.11, traverse@npm:^0.6.6":
  version: 0.6.11
  resolution: "traverse@npm:0.6.11"
  dependencies:
    gopd: "npm:^1.2.0"
    typedarray.prototype.slice: "npm:^1.0.5"
    which-typed-array: "npm:^1.1.18"
  checksum: 10/f70d9ea9dd7e2f14e805815b9734bcac5f459b5946b32b4dbfc6e66f1738dc21921c7353722b61e7bd4d3b79ff5b51234078e0a243d72eaeccef802353cc9075
  languageName: node
  linkType: hard

"trigram-utils@npm:^1.0.0":
  version: 1.0.3
  resolution: "trigram-utils@npm:1.0.3"
  dependencies:
    collapse-white-space: "npm:^1.0.0"
    n-gram: "npm:^1.0.0"
    trim: "npm:0.0.1"
  checksum: 10/0249ed2a7e5a382ef00f986d5c3fd7403d11505a7b0da9855621ecb8b3e46abc1b380a5c804dcc2ffa76a6ee05a0912b1d5dff405f19357ca6a2569abcbd9a5c
  languageName: node
  linkType: hard

"trim@npm:0.0.1":
  version: 0.0.1
  resolution: "trim@npm:0.0.1"
  checksum: 10/2b4646dff99a222e8e1526edd4e3a43bbd925af0b8e837c340455d250157e7deefaa4da49bb891ab841e5c27b1afc5e9e32d4b57afb875d2dfcabf4e319b8f7f
  languageName: node
  linkType: hard

"tslib@npm:^2.0.3, tslib@npm:^2.6.2, tslib@npm:^2.8.1":
  version: 2.8.1
  resolution: "tslib@npm:2.8.1"
  checksum: 10/3e2e043d5c2316461cb54e5c7fe02c30ef6dccb3384717ca22ae5c6b5bc95232a6241df19c622d9c73b809bea33b187f6dbc73030963e29950c2141bc32a79f7
  languageName: node
  linkType: hard

"tunnel-agent@npm:^0.6.0":
  version: 0.6.0
  resolution: "tunnel-agent@npm:0.6.0"
  dependencies:
    safe-buffer: "npm:^5.0.1"
  checksum: 10/7f0d9ed5c22404072b2ae8edc45c071772affd2ed14a74f03b4e71b4dd1a14c3714d85aed64abcaaee5fec2efc79002ba81155c708f4df65821b444abb0cfade
  languageName: node
  linkType: hard

"tweetnacl@npm:^0.14.3, tweetnacl@npm:~0.14.0":
  version: 0.14.5
  resolution: "tweetnacl@npm:0.14.5"
  checksum: 10/04ee27901cde46c1c0a64b9584e04c96c5fe45b38c0d74930710751ea991408b405747d01dfae72f80fc158137018aea94f9c38c651cb9c318f0861a310c3679
  languageName: node
  linkType: hard

"type-check@npm:^0.4.0, type-check@npm:~0.4.0":
  version: 0.4.0
  resolution: "type-check@npm:0.4.0"
  dependencies:
    prelude-ls: "npm:^1.2.1"
  checksum: 10/14687776479d048e3c1dbfe58a2409e00367810d6960c0f619b33793271ff2a27f81b52461f14a162f1f89a9b1d8da1b237fc7c99b0e1fdcec28ec63a86b1fec
  languageName: node
  linkType: hard

"type-is@npm:^2.0.0, type-is@npm:^2.0.1":
  version: 2.0.1
  resolution: "type-is@npm:2.0.1"
  dependencies:
    content-type: "npm:^1.0.5"
    media-typer: "npm:^1.1.0"
    mime-types: "npm:^3.0.0"
  checksum: 10/bacdb23c872dacb7bd40fbd9095e6b2fca2895eedbb689160c05534d7d4810a7f4b3fd1ae87e96133c505958f6d602967a68db5ff577b85dd6be76eaa75d58af
  languageName: node
  linkType: hard

"type-is@npm:~1.6.18":
  version: 1.6.18
  resolution: "type-is@npm:1.6.18"
  dependencies:
    media-typer: "npm:0.3.0"
    mime-types: "npm:~2.1.24"
  checksum: 10/0bd9eeae5efd27d98fd63519f999908c009e148039d8e7179a074f105362d4fcc214c38b24f6cda79c87e563cbd12083a4691381ed28559220d4a10c2047bed4
  languageName: node
  linkType: hard

"typed-array-buffer@npm:^1.0.3":
  version: 1.0.3
  resolution: "typed-array-buffer@npm:1.0.3"
  dependencies:
    call-bound: "npm:^1.0.3"
    es-errors: "npm:^1.3.0"
    is-typed-array: "npm:^1.1.14"
  checksum: 10/3fb91f0735fb413b2bbaaca9fabe7b8fc14a3fa5a5a7546bab8a57e755be0e3788d893195ad9c2b842620592de0e68d4c077d4c2c41f04ec25b8b5bb82fa9a80
  languageName: node
  linkType: hard

"typed-array-byte-length@npm:^1.0.3":
  version: 1.0.3
  resolution: "typed-array-byte-length@npm:1.0.3"
  dependencies:
    call-bind: "npm:^1.0.8"
    for-each: "npm:^0.3.3"
    gopd: "npm:^1.2.0"
    has-proto: "npm:^1.2.0"
    is-typed-array: "npm:^1.1.14"
  checksum: 10/269dad101dda73e3110117a9b84db86f0b5c07dad3a9418116fd38d580cab7fc628a4fc167e29b6d7c39da2f53374b78e7cb578b3c5ec7a556689d985d193519
  languageName: node
  linkType: hard

"typed-array-byte-offset@npm:^1.0.4":
  version: 1.0.4
  resolution: "typed-array-byte-offset@npm:1.0.4"
  dependencies:
    available-typed-arrays: "npm:^1.0.7"
    call-bind: "npm:^1.0.8"
    for-each: "npm:^0.3.3"
    gopd: "npm:^1.2.0"
    has-proto: "npm:^1.2.0"
    is-typed-array: "npm:^1.1.15"
    reflect.getprototypeof: "npm:^1.0.9"
  checksum: 10/c2869aa584cdae24ecfd282f20a0f556b13a49a9d5bca1713370bb3c89dff0ccbc5ceb45cb5b784c98f4579e5e3e2a07e438c3a5b8294583e2bd4abbd5104fb5
  languageName: node
  linkType: hard

"typed-array-length@npm:^1.0.7":
  version: 1.0.7
  resolution: "typed-array-length@npm:1.0.7"
  dependencies:
    call-bind: "npm:^1.0.7"
    for-each: "npm:^0.3.3"
    gopd: "npm:^1.0.1"
    is-typed-array: "npm:^1.1.13"
    possible-typed-array-names: "npm:^1.0.0"
    reflect.getprototypeof: "npm:^1.0.6"
  checksum: 10/d6b2f0e81161682d2726eb92b1dc2b0890890f9930f33f9bcf6fc7272895ce66bc368066d273e6677776de167608adc53fcf81f1be39a146d64b630edbf2081c
  languageName: node
  linkType: hard

"typedarray.prototype.slice@npm:^1.0.5":
  version: 1.0.5
  resolution: "typedarray.prototype.slice@npm:1.0.5"
  dependencies:
    call-bind: "npm:^1.0.8"
    define-properties: "npm:^1.2.1"
    es-abstract: "npm:^1.23.9"
    es-errors: "npm:^1.3.0"
    get-proto: "npm:^1.0.1"
    math-intrinsics: "npm:^1.1.0"
    typed-array-buffer: "npm:^1.0.3"
    typed-array-byte-offset: "npm:^1.0.4"
  checksum: 10/df1a35ccb86bd30280310d628d6def7a9a78fe3c787fe63b67559473ffe5cb0d3b6351f38a1105cd9f15c6d3707420ca60720bfffe41f79e376071f6a9c8104f
  languageName: node
  linkType: hard

"uid2@npm:1.0.0":
  version: 1.0.0
  resolution: "uid2@npm:1.0.0"
  checksum: 10/7efad0da3839ef2bebc6fae4bd29905702cd64233b3907e3300aa2d7ea1a00c1ae8c41a5e16ca34ac2db2d25c5607d5989673e1df51a2a076fefbeed51605ec3
  languageName: node
  linkType: hard

"unbox-primitive@npm:^1.1.0":
  version: 1.1.0
  resolution: "unbox-primitive@npm:1.1.0"
  dependencies:
    call-bound: "npm:^1.0.3"
    has-bigints: "npm:^1.0.2"
    has-symbols: "npm:^1.1.0"
    which-boxed-primitive: "npm:^1.1.1"
  checksum: 10/fadb347020f66b2c8aeacf8b9a79826fa34cc5e5457af4eb0bbc4e79bd87fed0fa795949825df534320f7c13f199259516ad30abc55a6e7b91d8d996ca069e50
  languageName: node
  linkType: hard

"underscore.string@npm:^3.3.6":
  version: 3.3.6
  resolution: "underscore.string@npm:3.3.6"
  dependencies:
    sprintf-js: "npm:^1.1.1"
    util-deprecate: "npm:^1.0.2"
  checksum: 10/4de7b855ef9890e4db61f3451a86193f4ba398c9615aa78bfb663d24670c3c0614aa02ffa7bd71ec2b1bddb58f41e3f2c074e9b1599722b27d5b5d1895237bfb
  languageName: node
  linkType: hard

"underscore.string@npm:~2.4.0":
  version: 2.4.0
  resolution: "underscore.string@npm:2.4.0"
  checksum: 10/573febd70e44d9aaa9c1a8d90b69f3287ad64af21a940e96527d0b2ab84ba26863f3f2eeae198702b7182409790207ba02cbc11cae374619d75a3d8676c5bcd9
  languageName: node
  linkType: hard

"underscore@npm:>1.4.4":
  version: 1.13.7
  resolution: "underscore@npm:1.13.7"
  checksum: 10/1ce3368dbe73d1e99678fa5d341a9682bd27316032ad2de7883901918f0f5d50e80320ccc543f53c1862ab057a818abc560462b5f83578afe2dd8dd7f779766c
  languageName: node
  linkType: hard

"underscore@npm:~1.7.0":
  version: 1.7.0
  resolution: "underscore@npm:1.7.0"
  checksum: 10/4b0bb1a30631a4edd64ab1349d3e676ce54cb6dfae50d865a80696418d4e77b689b58dbf5f67e2d336030c4f191e014a1cc8c98d069aee8330358898ce19ab7e
  languageName: node
  linkType: hard

"undici-types@npm:~6.21.0":
  version: 6.21.0
  resolution: "undici-types@npm:6.21.0"
  checksum: 10/ec8f41aa4359d50f9b59fa61fe3efce3477cc681908c8f84354d8567bb3701fafdddf36ef6bff307024d3feb42c837cf6f670314ba37fc8145e219560e473d14
  languageName: node
  linkType: hard

"unique-filename@npm:^4.0.0":
  version: 4.0.0
  resolution: "unique-filename@npm:4.0.0"
  dependencies:
    unique-slug: "npm:^5.0.0"
  checksum: 10/6a62094fcac286b9ec39edbd1f8f64ff92383baa430af303dfed1ffda5e47a08a6b316408554abfddd9730c78b6106bef4ca4d02c1231a735ddd56ced77573df
  languageName: node
  linkType: hard

"unique-slug@npm:^5.0.0":
  version: 5.0.0
  resolution: "unique-slug@npm:5.0.0"
  dependencies:
    imurmurhash: "npm:^0.1.4"
  checksum: 10/beafdf3d6f44990e0a5ce560f8f881b4ee811be70b6ba0db25298c31c8cf525ed963572b48cd03be1c1349084f9e339be4241666d7cf1ebdad20598d3c652b27
  languageName: node
  linkType: hard

"unix-dgram@npm:2.x":
  version: 2.0.6
  resolution: "unix-dgram@npm:2.0.6"
  dependencies:
    bindings: "npm:^1.5.0"
    nan: "npm:^2.16.0"
    node-gyp: "npm:latest"
  checksum: 10/f679d24cb1f0592a4a633a488f61dfabbadf31efc027125cea89c83168d27781f2029e6e9da64b507c7da7aa561bca327ce7553141f00d18d4f2165fd462c1eb
  languageName: node
  linkType: hard

"unpipe@npm:1.0.0, unpipe@npm:~1.0.0":
  version: 1.0.0
  resolution: "unpipe@npm:1.0.0"
  checksum: 10/4fa18d8d8d977c55cb09715385c203197105e10a6d220087ec819f50cb68870f02942244f1017565484237f1f8c5d3cd413631b1ae104d3096f24fdfde1b4aa2
  languageName: node
  linkType: hard

"update-browserslist-db@npm:^1.1.3":
  version: 1.1.3
  resolution: "update-browserslist-db@npm:1.1.3"
  dependencies:
    escalade: "npm:^3.2.0"
    picocolors: "npm:^1.1.1"
  peerDependencies:
    browserslist: ">= 4.21.0"
  bin:
    update-browserslist-db: cli.js
  checksum: 10/87af2776054ffb9194cf95e0201547d041f72ee44ce54b144da110e65ea7ca01379367407ba21de5c9edd52c74d95395366790de67f3eb4cc4afa0fe4424e76f
  languageName: node
  linkType: hard

"upper-case-first@npm:^2.0.2":
  version: 2.0.2
  resolution: "upper-case-first@npm:2.0.2"
  dependencies:
    tslib: "npm:^2.0.3"
  checksum: 10/4487db4701effe3b54ced4b3e4aa4d9ab06c548f97244d04aafb642eedf96a76d5a03cf5f38f10f415531d5792d1ac6e1b50f2a76984dc6964ad530f12876409
  languageName: node
  linkType: hard

"upper-case@npm:^2.0.2":
  version: 2.0.2
  resolution: "upper-case@npm:2.0.2"
  dependencies:
    tslib: "npm:^2.0.3"
  checksum: 10/508723a2b03ab90cf1d6b7e0397513980fab821cbe79c87341d0e96cedefadf0d85f9d71eac24ab23f526a041d585a575cfca120a9f920e44eb4f8a7cf89121c
  languageName: node
  linkType: hard

"uri-js@npm:^4.2.2":
  version: 4.4.1
  resolution: "uri-js@npm:4.4.1"
  dependencies:
    punycode: "npm:^2.1.0"
  checksum: 10/b271ca7e3d46b7160222e3afa3e531505161c9a4e097febae9664e4b59912f4cbe94861361a4175edac3a03fee99d91e44b6a58c17a634bc5a664b19fc76fbcb
  languageName: node
  linkType: hard

"url-template@npm:^2.0.8":
  version: 2.0.8
  resolution: "url-template@npm:2.0.8"
  checksum: 10/fc6a4cf6c3c3c3d7f0a0bb4405c41b81934e583b454e52ace7b2e5d7ed32ec9c2970ff1826d240c5823955fcb13531a1fc4ff6ba4569b1886a2976665353e952
  languageName: node
  linkType: hard

"util-deprecate@npm:^1.0.2, util-deprecate@npm:~1.0.1":
  version: 1.0.2
  resolution: "util-deprecate@npm:1.0.2"
  checksum: 10/474acf1146cb2701fe3b074892217553dfcf9a031280919ba1b8d651a068c9b15d863b7303cb15bd00a862b498e6cf4ad7b4a08fb134edd5a6f7641681cb54a2
  languageName: node
  linkType: hard

"utils-merge@npm:1.0.1":
  version: 1.0.1
  resolution: "utils-merge@npm:1.0.1"
  checksum: 10/5d6949693d58cb2e636a84f3ee1c6e7b2f9c16cb1d42d0ecb386d8c025c69e327205aa1c69e2868cc06a01e5e20681fbba55a4e0ed0cce913d60334024eae798
  languageName: node
  linkType: hard

"uuid@npm:^11.1.0":
  version: 11.1.0
  resolution: "uuid@npm:11.1.0"
  bin:
    uuid: dist/esm/bin/uuid
  checksum: 10/d2da43b49b154d154574891ced66d0c83fc70caaad87e043400cf644423b067542d6f3eb641b7c819224a7cd3b4c2f21906acbedd6ec9c6a05887aa9115a9cf5
  languageName: node
  linkType: hard

"uuid@npm:^3.2.1, uuid@npm:^3.3.2":
  version: 3.4.0
  resolution: "uuid@npm:3.4.0"
  bin:
    uuid: ./bin/uuid
  checksum: 10/4f2b86432b04cc7c73a0dd1bcf11f1fc18349d65d2e4e32dd0fc658909329a1e0cc9244aa93f34c0cccfdd5ae1af60a149251a5f420ec3ac4223a3dab198fb2e
  languageName: node
  linkType: hard

"uuid@npm:^9.0.0, uuid@npm:^9.0.1":
  version: 9.0.1
  resolution: "uuid@npm:9.0.1"
  bin:
    uuid: dist/bin/uuid
  checksum: 10/9d0b6adb72b736e36f2b1b53da0d559125ba3e39d913b6072f6f033e0c87835b414f0836b45bcfaf2bdf698f92297fea1c3cc19b0b258bc182c9c43cc0fab9f2
  languageName: node
  linkType: hard

"validator@npm:^13.15.0":
  version: 13.15.0
  resolution: "validator@npm:13.15.0"
  checksum: 10/cc4435e37a72d65c01d0a90d0f65da1fe09a805de35be1ec37e5379e1a612ab280277d7034a5369cd491a4f94c3b551f75dc4851090efafedf529a1ef76b1e0f
  languageName: node
  linkType: hard

"vary@npm:^1, vary@npm:^1.1.2, vary@npm:~1.1.2":
  version: 1.1.2
  resolution: "vary@npm:1.1.2"
  checksum: 10/31389debef15a480849b8331b220782230b9815a8e0dbb7b9a8369559aed2e9a7800cd904d4371ea74f4c3527db456dc8e7ac5befce5f0d289014dbdf47b2242
  languageName: node
  linkType: hard

"verror@npm:1.10.0":
  version: 1.10.0
  resolution: "verror@npm:1.10.0"
  dependencies:
    assert-plus: "npm:^1.0.0"
    core-util-is: "npm:1.0.2"
    extsprintf: "npm:^1.2.0"
  checksum: 10/da548149dd9c130a8a2587c9ee71ea30128d1526925707e2d01ed9c5c45c9e9f86733c66a328247cdd5f7c1516fb25b0f959ba754bfbe15072aa99ff96468a29
  languageName: node
  linkType: hard

"webidl-conversions@npm:^3.0.0":
  version: 3.0.1
  resolution: "webidl-conversions@npm:3.0.1"
  checksum: 10/b65b9f8d6854572a84a5c69615152b63371395f0c5dcd6729c45789052296df54314db2bc3e977df41705eacb8bc79c247cee139a63fa695192f95816ed528ad
  languageName: node
  linkType: hard

"whatwg-url@npm:^5.0.0":
  version: 5.0.0
  resolution: "whatwg-url@npm:5.0.0"
  dependencies:
    tr46: "npm:~0.0.3"
    webidl-conversions: "npm:^3.0.0"
  checksum: 10/f95adbc1e80820828b45cc671d97da7cd5e4ef9deb426c31bcd5ab00dc7103042291613b3ef3caec0a2335ed09e0d5ed026c940755dbb6d404e2b27f940fdf07
  languageName: node
  linkType: hard

"which-boxed-primitive@npm:^1.1.0, which-boxed-primitive@npm:^1.1.1":
  version: 1.1.1
  resolution: "which-boxed-primitive@npm:1.1.1"
  dependencies:
    is-bigint: "npm:^1.1.0"
    is-boolean-object: "npm:^1.2.1"
    is-number-object: "npm:^1.1.1"
    is-string: "npm:^1.1.1"
    is-symbol: "npm:^1.1.1"
  checksum: 10/a877c0667bc089518c83ad4d845cf8296b03efe3565c1de1940c646e00a2a1ae9ed8a185bcfa27cbf352de7906f0616d83b9d2f19ca500ee02a551fb5cf40740
  languageName: node
  linkType: hard

"which-builtin-type@npm:^1.2.1":
  version: 1.2.1
  resolution: "which-builtin-type@npm:1.2.1"
  dependencies:
    call-bound: "npm:^1.0.2"
    function.prototype.name: "npm:^1.1.6"
    has-tostringtag: "npm:^1.0.2"
    is-async-function: "npm:^2.0.0"
    is-date-object: "npm:^1.1.0"
    is-finalizationregistry: "npm:^1.1.0"
    is-generator-function: "npm:^1.0.10"
    is-regex: "npm:^1.2.1"
    is-weakref: "npm:^1.0.2"
    isarray: "npm:^2.0.5"
    which-boxed-primitive: "npm:^1.1.0"
    which-collection: "npm:^1.0.2"
    which-typed-array: "npm:^1.1.16"
  checksum: 10/22c81c5cb7a896c5171742cd30c90d992ff13fb1ea7693e6cf80af077791613fb3f89aa9b4b7f890bd47b6ce09c6322c409932359580a2a2a54057f7b52d1cbe
  languageName: node
  linkType: hard

"which-collection@npm:^1.0.2":
  version: 1.0.2
  resolution: "which-collection@npm:1.0.2"
  dependencies:
    is-map: "npm:^2.0.3"
    is-set: "npm:^2.0.3"
    is-weakmap: "npm:^2.0.2"
    is-weakset: "npm:^2.0.3"
  checksum: 10/674bf659b9bcfe4055f08634b48a8588e879161b9fefed57e9ec4ff5601e4d50a05ccd76cf10f698ef5873784e5df3223336d56c7ce88e13bcf52ebe582fc8d7
  languageName: node
  linkType: hard

"which-module@npm:^2.0.0":
  version: 2.0.1
  resolution: "which-module@npm:2.0.1"
  checksum: 10/1967b7ce17a2485544a4fdd9063599f0f773959cca24176dbe8f405e55472d748b7c549cd7920ff6abb8f1ab7db0b0f1b36de1a21c57a8ff741f4f1e792c52be
  languageName: node
  linkType: hard

"which-typed-array@npm:^1.1.16, which-typed-array@npm:^1.1.18, which-typed-array@npm:^1.1.19":
  version: 1.1.19
  resolution: "which-typed-array@npm:1.1.19"
  dependencies:
    available-typed-arrays: "npm:^1.0.7"
    call-bind: "npm:^1.0.8"
    call-bound: "npm:^1.0.4"
    for-each: "npm:^0.3.5"
    get-proto: "npm:^1.0.1"
    gopd: "npm:^1.2.0"
    has-tostringtag: "npm:^1.0.2"
  checksum: 10/12be30fb88567f9863186bee1777f11bea09dd59ed8b3ce4afa7dd5cade75e2f4cc56191a2da165113cc7cf79987ba021dac1e22b5b62aa7e5c56949f2469a68
  languageName: node
  linkType: hard

"which@npm:^2.0.1":
  version: 2.0.2
  resolution: "which@npm:2.0.2"
  dependencies:
    isexe: "npm:^2.0.0"
  bin:
    node-which: ./bin/node-which
  checksum: 10/4782f8a1d6b8fc12c65e968fea49f59752bf6302dc43036c3bf87da718a80710f61a062516e9764c70008b487929a73546125570acea95c5b5dcc8ac3052c70f
  languageName: node
  linkType: hard

"which@npm:^5.0.0":
  version: 5.0.0
  resolution: "which@npm:5.0.0"
  dependencies:
    isexe: "npm:^3.1.1"
  bin:
    node-which: bin/which.js
  checksum: 10/6ec99e89ba32c7e748b8a3144e64bfc74aa63e2b2eacbb61a0060ad0b961eb1a632b08fb1de067ed59b002cec3e21de18299216ebf2325ef0f78e0f121e14e90
  languageName: node
  linkType: hard

"word-wrap@npm:^1.2.5":
  version: 1.2.5
  resolution: "word-wrap@npm:1.2.5"
  checksum: 10/1ec6f6089f205f83037be10d0c4b34c9183b0b63fca0834a5b3cee55dd321429d73d40bb44c8fc8471b5203d6e8f8275717f49a8ff4b2b0ab41d7e1b563e0854
  languageName: node
  linkType: hard

"wrap-ansi-cjs@npm:wrap-ansi@^7.0.0":
  version: 7.0.0
  resolution: "wrap-ansi@npm:7.0.0"
  dependencies:
    ansi-styles: "npm:^4.0.0"
    string-width: "npm:^4.1.0"
    strip-ansi: "npm:^6.0.0"
  checksum: 10/cebdaeca3a6880da410f75209e68cd05428580de5ad24535f22696d7d9cab134d1f8498599f344c3cf0fb37c1715807a183778d8c648d6cc0cb5ff2bb4236540
  languageName: node
  linkType: hard

"wrap-ansi@npm:^6.2.0":
  version: 6.2.0
  resolution: "wrap-ansi@npm:6.2.0"
  dependencies:
    ansi-styles: "npm:^4.0.0"
    string-width: "npm:^4.1.0"
    strip-ansi: "npm:^6.0.0"
  checksum: 10/0d64f2d438e0b555e693b95aee7b2689a12c3be5ac458192a1ce28f542a6e9e59ddfecc37520910c2c88eb1f82a5411260566dba5064e8f9895e76e169e76187
  languageName: node
  linkType: hard

"wrap-ansi@npm:^8.1.0":
  version: 8.1.0
  resolution: "wrap-ansi@npm:8.1.0"
  dependencies:
    ansi-styles: "npm:^6.1.0"
    string-width: "npm:^5.0.1"
    strip-ansi: "npm:^7.0.1"
  checksum: 10/7b1e4b35e9bb2312d2ee9ee7dc95b8cb5f8b4b5a89f7dde5543fe66c1e3715663094defa50d75454ac900bd210f702d575f15f3f17fa9ec0291806d2578d1ddf
  languageName: node
  linkType: hard

"wrappy@npm:1":
  version: 1.0.2
  resolution: "wrappy@npm:1.0.2"
  checksum: 10/159da4805f7e84a3d003d8841557196034155008f817172d4e986bd591f74aa82aa7db55929a54222309e01079a65a92a9e6414da5a6aa4b01ee44a511ac3ee5
  languageName: node
  linkType: hard

"ws@npm:^8.18.1":
  version: 8.18.2
  resolution: "ws@npm:8.18.2"
  peerDependencies:
    bufferutil: ^4.0.1
    utf-8-validate: ">=5.0.2"
  peerDependenciesMeta:
    bufferutil:
      optional: true
    utf-8-validate:
      optional: true
  checksum: 10/018e04ec95561d88248d53a2eaf094b4ae131e9b062f2679e6e8a62f04649bc543448f1e038125225ac6bbb25f54c1e65d7a2cc9dbc1e28b43e5e6b7162ad88e
  languageName: node
  linkType: hard

"xml2js@npm:0.4.23":
  version: 0.4.23
  resolution: "xml2js@npm:0.4.23"
  dependencies:
    sax: "npm:>=0.6.0"
    xmlbuilder: "npm:~11.0.0"
  checksum: 10/52896ef39429f860f32471dd7bb2b89ef25b7e15528e3a4366de0bd5e55a251601565e7814763e70f9e75310c3afe649a42b8826442b74b41eff8a0ae333fccc
  languageName: node
  linkType: hard

"xmlbuilder@npm:~11.0.0":
  version: 11.0.1
  resolution: "xmlbuilder@npm:11.0.1"
  checksum: 10/c8c3d208783718db5b285101a736cd8e6b69a5c265199a0739abaa93d1a1b7de5489fd16df4e776e18b2c98cb91f421a7349e99fd8c1ebeb44ecfed72a25091a
  languageName: node
  linkType: hard

"xmlcreate@npm:^2.0.3, xmlcreate@npm:^2.0.4":
  version: 2.0.4
  resolution: "xmlcreate@npm:2.0.4"
  checksum: 10/4b508f92848fcc05d98b5c0bee40242de327410dda3d16659bb9d3c88faeba26f4af793111ad443be673a60f300b9fd51a6349875c63f34bcbe61a321b94c7ef
  languageName: node
  linkType: hard

"xtend@npm:~1.0.3":
  version: 1.0.3
  resolution: "xtend@npm:1.0.3"
  checksum: 10/487305737dddaf5c58d0a4c939aea0d59037248822ef1813fb51e45040d3a18a29ee81f6e50502e731a67803df7e77f34ea574ca37ea5bcdb941e705be760afe
  languageName: node
  linkType: hard

"xxhashjs@npm:^0.2.2":
  version: 0.2.2
  resolution: "xxhashjs@npm:0.2.2"
  dependencies:
    cuint: "npm:^0.2.2"
  checksum: 10/974dba1b7dd10f550714456366135fc70ba809e6e4db26e18a760a1f57e18dbc7fa6732738abc3f8fee27bb6a28d185240356ff4a57d7ce54282049e1da99886
  languageName: node
  linkType: hard

"y18n@npm:^4.0.0":
  version: 4.0.3
  resolution: "y18n@npm:4.0.3"
  checksum: 10/392870b2a100bbc643bc035fe3a89cef5591b719c7bdc8721bcdb3d27ab39fa4870acdca67b0ee096e146d769f311d68eda6b8195a6d970f227795061923013f
  languageName: node
  linkType: hard

"yallist@npm:^3.0.2":
  version: 3.1.1
  resolution: "yallist@npm:3.1.1"
  checksum: 10/9af0a4329c3c6b779ac4736c69fae4190ac03029fa27c1aef4e6bcc92119b73dea6fe5db5fe881fb0ce2a0e9539a42cdf60c7c21eda04d1a0b8c082e38509efb
  languageName: node
  linkType: hard

"yallist@npm:^4.0.0":
  version: 4.0.0
  resolution: "yallist@npm:4.0.0"
  checksum: 10/4cb02b42b8a93b5cf50caf5d8e9beb409400a8a4d85e83bb0685c1457e9ac0b7a00819e9f5991ac25ffabb56a78e2f017c1acc010b3a1babfe6de690ba531abd
  languageName: node
  linkType: hard

"yallist@npm:^5.0.0":
  version: 5.0.0
  resolution: "yallist@npm:5.0.0"
  checksum: 10/1884d272d485845ad04759a255c71775db0fac56308764b4c77ea56a20d56679fad340213054c8c9c9c26fcfd4c4b2a90df993b7e0aaf3cdb73c618d1d1a802a
  languageName: node
  linkType: hard

"yamljs@npm:^0.3.0":
  version: 0.3.0
  resolution: "yamljs@npm:0.3.0"
  dependencies:
    argparse: "npm:^1.0.7"
    glob: "npm:^7.0.5"
  bin:
    json2yaml: ./bin/json2yaml
    yaml2json: ./bin/yaml2json
  checksum: 10/041ccb467b04e0ebfa8224fceca03a28fb28666f46d8ac82ba19b2b118d44604566c17def5cb5ae6681fcedd903affbb42f757706b1e5440dcd304d5f802ef3c
  languageName: node
  linkType: hard

"yargs-parser@npm:^18.1.2":
  version: 18.1.3
  resolution: "yargs-parser@npm:18.1.3"
  dependencies:
    camelcase: "npm:^5.0.0"
    decamelize: "npm:^1.2.0"
  checksum: 10/235bcbad5b7ca13e5abc54df61d42f230857c6f83223a38e4ed7b824681875b7f8b6ed52139d88a3ad007050f28dc0324b3c805deac7db22ae3b4815dae0e1bf
  languageName: node
  linkType: hard

"yargs@npm:^15.3.1":
  version: 15.4.1
  resolution: "yargs@npm:15.4.1"
  dependencies:
    cliui: "npm:^6.0.0"
    decamelize: "npm:^1.2.0"
    find-up: "npm:^4.1.0"
    get-caller-file: "npm:^2.0.1"
    require-directory: "npm:^2.1.1"
    require-main-filename: "npm:^2.0.0"
    set-blocking: "npm:^2.0.0"
    string-width: "npm:^4.2.0"
    which-module: "npm:^2.0.0"
    y18n: "npm:^4.0.0"
    yargs-parser: "npm:^18.1.2"
  checksum: 10/bbcc82222996c0982905b668644ca363eebe6ffd6a572fbb52f0c0e8146661d8ce5af2a7df546968779bb03d1e4186f3ad3d55dfaadd1c4f0d5187c0e3a5ba16
  languageName: node
  linkType: hard

"yocto-queue@npm:^0.1.0":
  version: 0.1.0
  resolution: "yocto-queue@npm:0.1.0"
  checksum: 10/f77b3d8d00310def622123df93d4ee654fc6a0096182af8bd60679ddcdfb3474c56c6c7190817c84a2785648cdee9d721c0154eb45698c62176c322fb46fc700
  languageName: node
  linkType: hard
