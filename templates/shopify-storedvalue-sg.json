{"program": {"name": "Stored Value", "shortName": "Stored Value", "key": "{{key}}", "type": "free", "priority": 1, "policies": {"profile": {"member": true, "membership": true}, "qualifiers": {"join": {"handler": "do<PERSON><PERSON><PERSON>", "options": {}}, "renew": {"handler": "<PERSON><PERSON><PERSON><PERSON>", "options": {}}}}, "storedValue": {"kind": "giftcard", "enabled": true, "currency": {"code": "SGD", "precision": 2}, "style": {"color": "#FFFFFF", "format": "$", "fontSize": 34}, "singleUse": false, "sharedOnly": false}, "options": {"digital": true, "memberships": {"multiple": "disallowed", "perPerson": 1}}, "visible": true, "tierList": [{"level": 1, "name": "Member Card", "tenure": {"join": {"start": null, "end": {"duration": "P364D", "endOf": "day"}}, "renew": {"start": {"base": "membership.endTime", "duration": "P1D", "startOf": "day"}, "end": {"duration": "P364D", "endOf": "day"}}, "upgrade": null, "downgrade": null}, "qualifiers": {"join": "{\"$and\":[{\"$or\":[{\"memberships.current\":{\"$not\":{\"$elemMatch\":{\"key\":\"{{key}}\",\"tierLevel\":1,\"state\":\"active\"}}}},{\"memberships.current\":{\"$size\":0}}]},{\"behavior.type\":{\"$ne\":\"purchase\"}}]}", "renew": "{\"memberships.current\":{\"$elemMatch\":{\"key\":\"{{key}}\",\"tierLevel\":1,\"state\":\"active\",\"expiredAgo\":{\"$gte\":1}}},\"$or\":[{\"member.behaviors.purchase.last.at\":{\"$ltDays\":540}},{\"memberships.current\":{\"$elemMatch\":{\"key\":\"{{key}}\",\"tierLevel\":1,\"qualifier\":\"join\"}}}]}"}, "toQualify": [], "options": {"excludeJoinPurchase": false, "forceUpgrade": false, "card": {"concatEndTime": false, "deleteOnExpire": false, "deleteOnRenew": false}}, "digitalCard": {"regions": [], "forms": [], "sharePolicy": {"limit": null, "max": {"perPerson": null, "generation": null}}, "autoIssue": true, "masterId": "6345469aa56c87001d9107a8"}, "products": [{"productId": "6395a42e55c5b6afa13e3de0", "variantId": "6395a4d255c5b6afa13f0b30", "qualifier": "join"}], "numberingId": "634545ef3061b7001c2ae1fc"}]}, "numbering": {"length": 10, "prefix": "P"}, "payment": {"storedValue": {"provider": "perkdpay"}, "merchant": {"type": "merchant", "provider": "stripe", "credentials": {"secretKey": "sk_test_vZPLmcje9VmLfFUSM5JEpMOL", "connectedAccountId": "acct_1Jhsj3ISFjOMcQca"}, "options": {"connectChargeType": "direct", "pricings": {"card": {"percent": 0.034, "amount": 0.5}, "alipay": {"percent": 0.022, "amount": 0.35}, "grabpay": {"percent": 0.033, "amount": 0}}, "fees": {"includeStripe": false}}}, "manual": {"type": "manual", "provider": "perkdpay", "credentials": {"accountId": "tok_manual"}, "options": {}}}, "product": {"product": {"title": "Top Up", "brand": "{{brand}}", "vendor": "{{brandShort}}", "description": "", "availability": "instock", "availabilityDate": null, "priceRange": {"min": 1, "max": 10000, "currency": "SGD"}, "isLowQuantity": false, "isSoldOut": false, "isBackOrder": false, "attributes": [], "visible": true}, "variants": [{"title": "Top Up", "sku": "topup", "businessId": "{{businessId}}", "taxable": false, "fulfillmentService": "digital", "productId": "{{productId}}", "digital": {"kind": "storedvalue"}, "prices": [{"name": "base", "price": {"value": 1000, "currency": "SGD"}, "salePrice": {"value": null, "currency": null, "startAt": null, "endAt": null}, "fee": {}, "paymentMethods": [], "countries": []}], "inventory": {"policy": "allow", "lowQuantityWarningThreshold": 0, "quantity": 0}}]}}