{"program": {"name": "免費會員", "shortName": "免費會員", "key": "", "type": "free", "state": "ready", "priority": 1, "tags": {"category": ["lifestyle"]}, "policies": {"qualifiers": {"join": {"handler": "do<PERSON><PERSON><PERSON>", "options": {}}, "renew": {"handler": "<PERSON><PERSON><PERSON><PERSON>", "options": {}}}, "profile": {"member": true, "membership": true}}, "options": {"digital": true, "memberships": {"multiple": "disallowed", "perPerson": 1}}, "tierList": [{"level": 1, "name": "會員卡", "tenure": {"join": {"start": null, "end": {"duration": "P364D", "endOf": "day"}}, "renew": {"start": {"base": "membership.endTime", "duration": "P1D", "startOf": "day"}, "end": {"duration": "P364D", "endOf": "day"}}, "upgrade": null, "downgrade": null}, "qualifiers": {"join": "{\"$and\":[{\"$or\":[{\"memberships.current\":{\"$not\":{\"$elemMatch\":{\"key\":\"{key}\",\"tierLevel\":1,\"state\":\"active\"}}}},{\"memberships.current\":{\"$size\":0}}]}]}", "renew": "{\"$and\":[{\"memberships.current\":{\"$elemMatch\":{\"key\":\"{key}\",\"tierLevel\":1,\"state\":\"active\",\"expiredAgo\":{\"$gte\":1}}}}]}"}, "digitalCard": {"discover": {"enabled": true, "regions": [{"country": "TW"}], "applet": "cardintro"}, "features": {"managed": true}, "barcodeType": "QRCODE", "forms": [{"name": "register", "schema": {"type": "object", "required": ["<PERSON><PERSON><PERSON>", "gender", "birth", "mobile", "email", "policy"], "properties": {"familyName": {}, "givenName": {}, "gender": {}, "birth": {}, "mobile": {}, "mobileOptIn": {}, "email": {}, "emailOptIn": {}, "policy": {"url": "https://web.perkd.me/i/poc/tc_zh-Hant.html"}}}, "view": {"elements": [{"layout": "section", "name": "profile", "elements": ["<PERSON><PERSON>ame", "<PERSON><PERSON><PERSON>", "gender", "birth"]}, {"layout": "section", "name": "communication", "elements": ["mobile", "mobileOptIn", "email", "emailOptIn"]}, {"layout": "section", "name": "terms", "elements": ["policy"]}]}, "mapping": {}, "globalize": {"default": "zh-Han<PERSON>", "t": {}}}, {"name": "edit", "schema": {"type": "object", "required": ["<PERSON><PERSON><PERSON>", "gender", "birth", "mobile", "email"], "properties": {"familyName": {}, "givenName": {}, "gender": {}, "birth": {}, "mobile": {}, "mobileOptIn": {}, "email": {}, "emailOptIn": {}}}, "view": {"elements": [{"layout": "section", "name": "profile", "elements": ["<PERSON><PERSON>ame", "<PERSON><PERSON><PERSON>", "gender", "birth"]}, {"layout": "section", "name": "communication", "elements": ["mobile", "mobileOptIn", "email", "emailOptIn"]}]}, "mapping": {}, "globalize": {"default": "zh-Han<PERSON>", "t": {}}}], "sharePolicies": [{"mode": "invite", "toCardMasterId": "{{cardMasterId}}", "limit": null, "remain": null, "max": {"perPerson": null, "generation": null}, "flows": {"sharer": null, "recipient": null}}], "autoIssue": true, "widgets": [{"name": "Offer", "key": "offer", "kind": "offer"}, {"name": "Message", "key": "message", "kind": "message"}, {"name": "<PERSON><PERSON>", "key": "reward", "kind": "reward"}, {"name": "Share (Invite)", "key": "invite", "kind": "share"}], "globalize": {"t": {"zh-Hant": {"name": "會員卡", "brand": {"short": "品牌", "long": "品牌全名"}, "description": ""}}, "default": "zh-Han<PERSON>"}}}]}, "numberings": [{"level": 1, "length": 10, "prefix": "C"}]}