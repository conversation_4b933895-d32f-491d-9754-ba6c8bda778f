{"program": {"name": "Paid Membership", "shortName": "Paid Membership", "key": "{{key}}", "type": "paid", "priority": 1, "policies": {"qualifiers": {"join": {"handler": "do<PERSON><PERSON><PERSON>", "options": {}}, "renew": {"handler": "<PERSON><PERSON><PERSON><PERSON>", "options": {"daysBeforeExpiry": -60}}}, "profile": {"member": true, "membership": true}}, "options": {"digital": true, "memberships": {"multiple": "disallowed", "perPerson": 1}}, "storedValue": {"enabled": false}, "visible": true, "tierList": [{"level": 2, "name": "Paid Membership", "tenure": {"join": {"start": null, "end": {"duration": "P364D", "endOf": "day"}}, "renew": {"start": {"base": "membership.endTime", "duration": "P1D", "startOf": "day"}, "end": {"duration": "P364D", "endOf": "day"}}, "upgrade": null, "downgrade": null}, "qualifiers": {"join": "{\"$and\":[{\"$or\":[{\"memberships.current\":{\"$not\":{\"$elemMatch\":{\"key\":\"{{key}}\",\"tierLevel\":2,\"state\":\"active\"}}}},{\"memberships.current\":{\"$size\":0}}]},{\"behavior.type\":\"payment\",\"behavior.membership.action\":\"join\"}]}", "renew": "{\"$and\":[{\"memberships.current\":{\"$elemMatch\":{\"key\":\"{{key}}\",\"tierLevel\":2,\"state\":\"active\",\"expiresIn\":{\"$lte\":90}}},\"memberships.future\":{\"$size\":0}},{\"behavior.type\":\"payment\",\"behavior.membership.action\":\"renew\"}]}"}, "toQualify": [], "options": {"excludeJoinPurchase": false, "forceUpgrade": false, "card": {"concatEndTime": true, "deleteOnExpire": false, "deleteOnRenew": false, "updateStartTime": {"join": true}}}, "digitalCard": {"regions": [], "forms": [], "sharePolicy": {"limit": null, "max": {"perPerson": null, "generation": null}}, "autoIssue": true, "masterId": "6345469aa56c87001d9107a8"}, "products": [{"qualifier": "join", "productId": "5aed5176cfa947c7ce0a48e3", "variantId": "5aed5f2ccfa947c7ce0ab63c"}, {"qualifier": "renew", "productId": "5aed5176cfa947c7ce0a48e3", "variantId": "5aed5f2dcfa947c7ce0ab643"}], "numberingId": "634545ef3061b7001c2ae1fc"}]}, "numbering": {"length": 10, "prefix": "B"}, "payment": {"merchant": {"type": "merchant", "provider": "stripe", "credentials": {"secretKey": "sk_test_vZPLmcje9VmLfFUSM5JEpMOL", "connectedAccountId": "acct_1CShuWAvIMJcMzwZ"}, "options": {"connectChargeType": "direct", "pricings": {"card": {"percent": 0.034, "amount": 2.35}, "alipay": {"percent": 0.022, "amount": 2}}, "fees": {"includeStripe": true}}}}}