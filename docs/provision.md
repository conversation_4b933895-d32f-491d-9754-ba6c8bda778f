# Provisioning Documentation

## Overview

The provisioning system is a multi-level architecture that handles the setup and configuration of various components in the CRM system. It follows a hierarchical approach with three main levels:

```mermaid
graph TD
    L0[Level 0 - Account Provisioning]
    L1[Level 1 - Business Provisioning]
    L2[Level 2 - Feature Provisioning]

    L0 --> L1
    L1 --> L2
```

## Provisioning Levels

### Level 0 - Account Provisioning

Initial account setup and core configuration:

```mermaid
graph TD
    subgraph Account[Account Setup]
        A1[CRM Account Creation]
        A2[Admin User Setup]
        A3[Business Profile]
        A1 --> A2 --> A3
    end

    subgraph Core[Core Configuration]
        C1[Core Settings]
        C2[Campaign Templates]
        C3[Payment Gateway]
        C4[Redis Sync]
        
        C1 --> C2
        C1 --> C3
        C1 --> C4
    end

    subgraph Access[Access Control]
        R1[Role Configuration]
        R2[Permission Setup]
        R3[Access Policies]
        
        R1 --> R2 --> R3
    end

    A3 --> C1
    C1 --> R1
```

- CRM Account creation and initialization
- Admin user (business owner) setup
- Core settings provisioning:
  - Locale settings
  - SMS configuration
  - Email configuration
- Campaign templates initialization
- Payment gateway skeleton setup
- Role-based access configuration
- Business profile creation with logo
- Redis settings synchronization

### Level 1 - Business Provisioning

Business-specific integrations and configurations:

```mermaid
graph TD
    subgraph Providers[Provider Integration]
        P1[Payment Provider]
        P2[Order Provider]
        P3[Fulfillment Provider]
        P4[Print Provider]
        P5[Invoice Provider]
        
        P1 --- P2 --- P3
        P3 --- P4 --- P5
    end

    subgraph Config[Business Configuration]
        B1[Business Settings]
        B2[Provider Credentials]
        B3[Environment Config]
        
        B1 --> B2 --> B3
    end

    subgraph Tenant[Multi-tenant Setup]
        T1[Tenant Configuration]
        T2[Resource Isolation]
        T3[Tenant Settings]
        
        T1 --> T2 --> T3
    end

    B1 --> P1
    B2 --> P1
    B3 --> P1
    T1 --> B1
```

- Payment provider integration
- Order provider setup
- Fulfillment provider configuration
- Print provider initialization
- Invoice provider setup
- Business settings management
- Provider credentials handling
- Multi-tenant configuration

#### Payment Provider Setup
- Payment gateway initialization
- Provider credentials configuration
- Fee structure setup
- Multi-currency configuration
- Settlement account setup
- Webhook endpoint configuration

```mermaid
graph TD
    subgraph Payment[Payment Setup]
        P1[Gateway Init]
        P2[Credentials Config] 
        P3[Fee Structure]
        P4[Currency Config]
        P5[Settlement Setup]
        P6[Webhook Config]
        
        P1 --> P2 --> P3
        P3 --> P4 --> P5 --> P6
    end
```

### Level 2 - Feature Provisioning

Specific feature enablement and configuration:

```mermaid
graph TD
    subgraph Membership[Membership Program]
        M1[Program Setup]
        M2[Tier Management]
        M3[Points & Rewards]
        M4[Status Rules]
        
        M1 --> M2 --> M3 --> M4
    end

    subgraph Fulfillment[Fulfillment System]
        F1[Kitchen Ops]
        F2[Delivery Service]
        F3[Store Fulfillment]
        F4[Queue Management]
        
        F1 --> F2 --> F3 --> F4
    end

    subgraph Value[Stored Value]
        V1[Wallet Setup]
        V2[Balance Management]
        V3[Transaction Rules]
        V4[Transfer System]
        
        V1 --> V2 --> V3 --> V4
    end

    M1 --> V1
    F1 --> V2
    V3 --> M3
```

#### Membership Program
- Program type configuration
- Tier level management
- Points and rewards setup
- Member status rules

#### Fulfillment Configuration
- Kitchen operations setup
- Delivery service enablement
- Store fulfillment configuration
- Queue management

#### Stored Value Operations
- Wallet initialization
- Balance management
- Transaction rules
- Transfer capabilities

## Provider Management

### Supported Providers

The system supports various types of providers:

1. Payment Providers
2. Order Providers
3. Print Providers
4. Fulfillment Providers
5. Invoice Providers

### Provider Configuration

Each provider requires:
- Provider name/key
- API credentials (keys, tokens, certificates)
- Environment configuration (live/test mode)
- Service enablement status
- Webhook endpoints and configurations
- Rate limiting settings
- Retry policies
- Specific services configuration
- Optional modules
- Provider-specific options

### Provider Operations

- Provider provisioning/deprovisioning
- Credentials and configuration management
- Service enablement/disablement
- Environment-based configurations (live/test modes)
- Multi-provider support
- Provider-specific settings
- Integration status monitoring

## Settings Management

### Core Settings
- Business profile configuration
- Theme and branding settings
- Payment provider settings
- Locale and timezone settings
- Email and SMS configurations
- Staff notification preferences
- Rate limiting configurations
- Webhook endpoints
- Error handling policies
- Logging preferences
- Monitoring settings

### Settings Synchronization

Settings are synchronized across the system using Redis and the Event Bus:

```mermaid
sequenceDiagram
    participant C as Client
    participant BS as Business Service
    participant EB as Event Bus
    participant R as Redis
    participant DB as Database
    participant EP as External Provider

    C->>BS: Update Business Settings
    BS->>DB: Store Settings
    BS->>EB: Publish Settings Change
    EB->>R: Sync Settings
    EB->>EP: Notify Changes
    EP-->>BS: Acknowledge
    BS-->>C: Success Response
```

## Error Handling

### Provisioning Errors
- Validation errors
- Network timeouts
- Provider API errors
- Configuration conflicts
- Resource limits
- Authentication failures
- Authorization issues

### Error Recovery
- Automatic retry mechanisms
- Rollback procedures
- Manual intervention protocols
- Error notification system
- Recovery validation

## Deprovisioning

The system supports complete deprovisioning of resources:

- Account decommissioning
- Provider deprovisioning
- Feature deactivation
- Settings cleanup
- Resource cleanup
- Tenant removal
- Database cleanup

## Security Considerations

- Credentials are stored in secure parameter stores
- Environment-specific secrets management
- Role-based access control
- Tenant isolation
- Provider credential security
- API key management

## Best Practices

1. Always test provisioning in the test environment first
2. Use appropriate environment variables for different stages
3. Follow the proper provisioning order (Level 0 → Level 1 → Level 2)
4. Implement proper error handling and rollback mechanisms
5. Maintain proper documentation for provider-specific configurations
6. Regular auditing of provisioned resources
7. Monitor provisioning status and health
8. Implement proper logging and tracking
9. Follow security best practices for credential management
10. Maintain proper backup and recovery procedures 