[{"name": "timezone", "value": {"data": "Asia/Taipei"}}, {"name": "currency", "value": {"symbol": "NT$", "scale": "100"}}, {"name": "sms", "value": {"sender": [{"language": "zh-Han<PERSON>", "label": "<PERSON><PERSON><PERSON>"}]}}, {"name": "email", "value": {"from": {"name": "湛盧咖啡", "email": "<EMAIL>"}, "replyTo": {"name": "湛盧咖啡", "email": "<EMAIL>"}, "enableEdit": true}}, {"name": "theme", "value": {"light": "#000000", "dark": "#B27200"}}, {"name": "dashboard", "value": {"main": [{"type": "root", "displayName": "AllSources", "children": []}, {"type": "place", "displayName": "Location", "model": "place", "filters": true}], "sales": [{"type": "root", "displayName": "AllSources", "children": []}, {"type": "place", "displayName": "Location", "model": "place", "filters": true}], "membership": [{"type": "root", "displayName": "AllSources", "children": []}, {"type": "place", "displayName": "Location", "model": "place", "filters": true}, {"type": "perkd", "displayName": "Perkd", "children": []}, {"type": "import", "displayName": "DataImport", "children": []}, {"type": "advertisement", "displayName": "Advertisement", "children": [{"displayName": "byFacebook", "type": "facebook", "model": null, "filters": false}]}], "person": [{"type": "root", "displayName": "AllSources", "children": []}]}}]