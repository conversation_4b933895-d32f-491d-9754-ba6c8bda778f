# API Documentation - Business Service (CRM)

The Business Service handles all business-related operations and management. It provides comprehensive APIs for managing business profiles, staff, stores, payments, and system configurations. This service is designed to support multi-tenant architecture, enabling businesses to operate independently while sharing the platform's infrastructure.

## Table of Contents
- [Authentication](#authentication)
- [Response Format](#response-format)
- [Error Handling](#error-handling)
- [Business Operations](#business-operations)
- [Staff Management](#staff-management)
- [Store Operations](#store-operations)
- [Payment Operations](#payment-operations)
- [Provider Operations](#provider-operations)
- [Dashboard Operations](#dashboard-operations)
- [System Operations](#system-operations)
- [Platform Services](#platform-services)
- [Transaction Support](#transaction-support)

## Authentication

All API endpoints require authentication using <PERSON><PERSON> token in the Authorization header:

```http
Authorization: Bearer <access_token>
```

## Response Format

### Success Response
All successful responses follow this format:

```json
{
  "success": true,
  "data": {
    // Response data
  }
}
```

### Error Response
All error responses follow this format:

```json
{
  "success": false,
  "error": {
    "code": "ERROR_CODE",
    "message": "Human readable message",
    "details": {
      // Additional error details
    }
  }
}
```

Common error codes:
- `UNAUTHORIZED` - Invalid or missing authentication
- `FORBIDDEN` - Insufficient permissions
- `INVALID_REQUEST` - Invalid request parameters
- `NOT_FOUND` - Requested resource not found
- `INTERNAL_ERROR` - Server error


## Business Operations

### Get Main Business Profile
Retrieves the main business profile information.

```http
GET /Businesses/main
```

Response:
```json
{
  "success": true,
  "data": {
    "id": "business123",
    "name": "Business Name",
    "contact": {
      "email": "<EMAIL>",
      "phone": "+1234567890"
    },
    "settings": {
      "locale": "en-US",
      "timezone": "UTC+8"
    }
  }
}
```

### Update Business Profile
Add missing endpoint for updating business profile:

```http
PUT /Businesses/main

{
  "name": "Updated Business Name",
  "contact": {
    "email": "<EMAIL>",
    "phone": "+1234567890"
  },
  "brand": {
    "logo": "https://...",
    "colors": {
      "primary": "#000000"
    }
  }
}
```

### Get Business Style Configuration
Add missing endpoint for retrieving style configuration:

```http
GET /Businesses/style
```

### Business Settings Management

#### Add Business Settings
Add new settings for the business.

```http
POST /Businesses/settings

{
  "name": "theme",
  "value": {
    "mode": "dark",
    "primaryColor": "#000000"
  }
}
```

Response:
```json
{
  "success": true,
  "data": {
    "name": "theme",
    "value": {
      "mode": "dark",
      "primaryColor": "#000000"
    },
    "updatedAt": "2024-03-20T08:00:00Z"
  }
}
```

#### Get Settings by Name
Retrieve specific settings by name.

```http
GET /Businesses/settings/:name
```

Response:
```json
{
  "success": true,
  "data": {
    "name": "theme",
    "value": {
      "mode": "dark",
      "primaryColor": "#000000"
    }
  }
}
```

#### Update Settings
Update existing settings.

```http
POST /Businesses/settings/:name

{
  "value": {
    "mode": "light",
    "primaryColor": "#ffffff"
  }
}
```

#### Remove Settings
Delete specific settings.

```http
DELETE /Businesses/settings/:name
```

#### Sync Settings to Redis
Force synchronization of settings to Redis cache.

```http
POST /Businesses/settings/redis/sync
```

## Staff Management

### Get Staff by Roles
Retrieve staff members filtered by roles.

```http
GET /Businesses/getStaff
Query Parameters:
  - roles: Array of role names (e.g. ?roles=manager,cashier)
```

### Staff Check-in
Record staff check-in at store.

```http
POST /Businesses/staff/checkin

{
  "staffId": "staff123",
  "storeId": "store456",
  "timestamp": "2024-03-20T08:00:00Z"
}
```

### Staff Check-out
Record staff check-out from store.

```http
POST /Businesses/staff/checkout

{
  "staffId": "staff123",
  "storeId": "store456",
  "timestamp": "2024-03-20T17:00:00Z"
}
```

### Send Staff Notifications
Send notifications to staff members.

```http
POST /Businesses/staff/notify

{
  "recipients": ["staff123", "staff456"],
  "message": {
    "title": "Schedule Update",
    "body": "Your shift has been updated",
    "data": {
      "type": "SCHEDULE_UPDATE",
      "scheduleId": "sched123"
    }
  }
}
```

### Issue Staff Card
Issue membership card to staff.

```http
POST /Businesses/staff/card/issue

{
  "staffId": "staff123",
  "cardType": "STAFF_PREMIUM"
}
```

### Manage Staff Roles

#### Assign Role
```http
POST /Businesses/staff/roles/assign

{
  "staffId": "staff123",
  "roles": ["manager", "cashier"]
}
```

#### Remove Role
```http
POST /Businesses/staff/roles/remove

{
  "staffId": "staff123",
  "roles": ["cashier"]
}
```

#### List Available Roles
```http
GET /Businesses/staff/roles
```

## Store Operations

### Get Operating Stores
Retrieve all currently operating stores.

```http
GET /Businesses/getStores
```

### Create/Find Provider Store
Create or find store by provider.

```http
POST /Businesses/places/findOrCreate/provider

{
  "provider": "SHOPIFY",
  "storeData": {
    "name": "Downtown Store",
    "address": "123 Main St",
    "coordinates": {
      "lat": 1.2345,
      "lng": 103.4567
    }
  }
}
```

### Get Store by Provider ID
Retrieve store information by provider and store ID.

```http
GET /Businesses/places/provider/:provider/:storeId
```

## Payment Operations

### Wallet Management

#### Create Wallet
Create new wallet for business.

```http
POST /Businesses/payment/wallet

{
  "currency": "USD",
  "type": "MAIN"
}
```

#### Get Wallet Details
Retrieve wallet information.

```http
GET /Businesses/payment/wallet
```

#### Get Wallet Balance
Get current wallet balance.

```http
GET /Businesses/payment/balance
```

### Payment Flow

#### Initialize Payment Request
Start new payment request.

```http
POST /Businesses/payment/request

{
  "amount": 100.50,
  "currency": "USD",
  "paymentMethod": "CREDIT_CARD",
  "metadata": {
    "orderId": "order123"
  }
}
```

#### Authorize Payment
Authorize payment amount.

```http
POST /Businesses/payment/authorize

{
  "paymentRequestId": "pay123",
  "paymentMethod": {
    "type": "CREDIT_CARD",
    "token": "tok_123"
  }
}
```

#### Capture Payment
Capture previously authorized payment.

```http
POST /Businesses/payment/capture

{
  "paymentRequestId": "pay123",
  "amount": 100.50
}
```

#### Cancel Payment
Cancel pending payment request.

```http
DELETE /Businesses/payment/cancel

{
  "paymentRequestId": "pay123",
  "reason": "CUSTOMER_REQUEST"
}
```

#### Process Refund
Process refund for completed payment.

```http
POST /Businesses/payment/refund

{
  "paymentId": "pay123",
  "amount": 50.25,
  "reason": "CUSTOMER_REQUEST"
}
```

### Stored Value Operations

#### Initialize Stored Value
Initialize wallet with initial amount.

```http
POST /Businesses/payment/storedvalue/init

{
  "walletId": "wallet123",
  "amount": 100.00,
  "currency": "USD"
}
```

#### Top Up Wallet
Add funds to existing wallet.

```http
POST /Businesses/payment/storedvalue/topup

{
  "walletId": "wallet123",
  "amount": 50.00,
  "paymentMethod": {
    "type": "CREDIT_CARD",
    "token": "tok_123"
  }
}
```

#### Deduct from Wallet
Deduct amount from wallet.

```http
POST /Businesses/payment/storedvalue/deduct

{
  "walletId": "wallet123",
  "amount": 25.00,
  "reference": "purchase123"
}
```

#### Transfer Between Wallets
Transfer amount between wallets.

```http
POST /Businesses/payment/storedvalue/transfer

{
  "fromWalletId": "wallet123",
  "toWalletId": "wallet456",
  "amount": 75.00
}
```

### Perkd Callback Endpoints

These endpoints are used by the Perkd platform to interact with the Business service.

#### Make Payment to Business
```http
POST /Businesses/perkd/pay

{
  "userId": "user123",
  "payments": [
    {
      "method": "card",
      "amount": 100.00
    }
  ],
  "order": {
    "items": [
      {
        "name": "Product 1",
        "price": 100.00
      }
    ]
  }
}
```

#### Commit Pending Payment
```http
POST /Businesses/perkd/pay/commit

{
  "userId": "user123",
  "payment": {
    "method": "card",
    "provider": "stripe",
    "intent": {
      "id": "intent_123"
    }
  }
}
```

#### Cancel Pending Payment
```http
POST /Businesses/perkd/pay/cancel

{
  "userId": "user123",
  "payment": {
    "method": "card",
    "provider": "stripe",
    "intent": {
      "id": "intent_123"
    }
  }
}
```

#### Transfer Between Cards
```http
POST /Businesses/perkd/pay/send

{
  "userId": "user123",
  "fromCardId": "card123",
  "toCardId": "card456",
  "amount": 50.00
}
```

#### Retrieve Balance
```http
GET /Businesses/perkd/pay/balance?userId=user123&cardId=card123
```

#### Store Payment Method
```http
POST /Businesses/perkd/pay/method

{
  "userId": "user123",
  "type": "card",
  "provider": "stripe",
  "metadata": {
    "installationId": "install123"
  }
}
```

#### Remove Payment Method
```http
DELETE /Businesses/perkd/pay/method?userId=user123&type=card&paymentMethodId=pm_123
```

### Perkd Pay API Endpoints (for vending machines)

These endpoints are specifically designed for vending machine integrations.

#### Capture Authorized Payment
```http
POST /Businesses/pay/commit

{
  "intentId": "intent_123",
  "order": {
    "amount": 5.00,
    "currency": "USD",
    "items": [
      {
        "name": "Soda",
        "price": 5.00
      }
    ]
  }
}
```

#### Cancel Authorized Payment
```http
POST /Businesses/pay/cancel

{
  "intentId": "intent_123"
}
```

### Payment Methods

> **Note**: The following endpoints are documented for reference but may not be fully implemented in the current version. Refer to the implementation code for the most accurate information.

#### Get Available Payment Methods
```http
GET /Businesses/payment/methods
```

Response:
```json
{
  "success": true,
  "data": {
    "methods": [
      {
        "type": "APPLE_PAY",
        "enabled": true,
        "fees": {
          "percentage": 0.1,
          "fixed": 0.10
        }
      },
      {
        "type": "GOOGLE_PAY",
        "enabled": true,
        "fees": {
          "percentage": 0.1,
          "fixed": 0.10
        }
      }
    ]
  }
}
```

#### Get Fee Structure
```http
GET /Businesses/payment/fees
```

Response:
```json
{
  "success": true,
  "data": {
    "fees": {
      "APPLE_PAY": {
        "percentage": 0.1,
        "fixed": 0.10
      },
      "GOOGLE_PAY": {
        "percentage": 0.1,
        "fixed": 0.10
      },
      "ALIPAY": {
        "percentage": 0.1,
        "fixed": 0.10
      }
    }
  }
}
```

## Provider Operations

### Get Available Providers
```http
GET /api/providers
```

### Enable Provider
```http
POST /api/providers/:provider/enable

{
  "credentials": {
    "apiKey": "key123",
    "apiSecret": "secret456"
  },
  "settings": {
    "environment": "live",
    "webhookUrl": "https://..."
  }
}
```

### Disable Provider
```http
POST /api/providers/:provider/disable
```

## Dashboard Operations

### Get Business Dashboard
```http
GET /api/businesses/dashboard
```

### Update Dashboard Layout
```http
PUT /api/businesses/dashboard/layout

{
  "widgets": [
    {
      "id": "sales",
      "position": 1,
      "size": "large"
    }
  ]
}
```

## System Operations

### Service Health Check
```http
GET /api/status
```

### Service Health Details
Add detailed health check endpoint:

```http
GET /api/status/details

Response:
{
  "success": true,
  "data": {
    "service": "healthy",
    "dependencies": {
      "redis": "connected",
      "database": "connected",
      "providers": {
        "payment": "available",
        "notification": "degraded"
      }
    },
    "metrics": {
      "uptime": "10d 4h",
      "requestCount": 15420,
      "errorRate": 0.02
    }
  }
}
```

### Get Service Configuration
Retrieve service configuration.

```http
GET /api/config
```

### System Maintenance
Add maintenance mode endpoints:

```http
POST /api/system/maintenance/start
POST /api/system/maintenance/end
GET /api/system/maintenance/status
```

## Platform Services

### Tenant Management

#### List Tenants
```http
GET /api/tenants
```

#### Add Tenant
```http
POST /api/tenants

{
  "code": "tenant123",
  "name": "New Tenant",
  "settings": {
    "locale": "en-US",
    "timezone": "UTC+8"
  }
}
```

#### Remove Tenant
```http
DELETE /api/tenants/{code}
```

#### Get Tenant Settings
```http
GET /tenants/{code}/settings
```

#### Update Tenant Settings
```http
POST /tenants/{code}/settings
```

### Settings Management

#### List Settings
```http
GET /settings
```

#### Add Setting
```http
POST /settings
```

#### Get Setting by Key
```http
GET /settings/{key}
```

#### Update Setting
```http
POST /settings/{key}
```

#### Delete Setting
```http
DELETE /settings/{key}
```


## Transaction Support

The platform provides robust transaction support through the `ConnectionManager`. Here are examples of common transaction patterns:

### Basic Transaction

```javascript
// Simple transaction with automatic handling
await Business.create(data)  // Automatically wrapped in transaction

// Explicit transaction
await Business.withTransaction(async (session) => {
	const business = await Business.create(data, { session })
	return business
})
```

### Complex Transactions

```javascript
// Multiple operations in single transaction
await Business.withTransaction(async (session) => {
	const business = await Business.create(businessData, { session })
	const staff = await Staff.create({
		...staffData,
		businessId: business.id
	}, { session })

	await Activity.create({
		type: 'business_creation',
		businessId: business.id,
		staffId: staff.id
	}, { session })

	return business
})
```

### Error Handling

```javascript
try {
	await Business.withTransaction(async (session) => {
		const business = await Business.findById(id, { session })
		if (!business) throw new Error('Business not found')

		await business.updateAttributes(updates, { session })
		await Activity.create({
			type: 'business_update',
			businessId: business.id
		}, { session })
	})
} catch (err) {
	// Transaction automatically rolled back
	console.error('Update failed:', err)
	throw err
}
```

### Nested Transactions

```javascript
async function createBusinessWithStaff(businessData, staffData) {
	return Business.withTransaction(async (session) => {
		const business = await Business.create(businessData, { session })

		try {
			await createStaffMembers(business.id, staffData, session)
		} catch (err) {
			// Entire transaction will be rolled back
			throw new Error(`Failed to create staff: ${err.message}`)
		}

		return business
	})
}

async function createStaffMembers(businessId, staffData, parentSession) {
	// Use parent session to maintain transaction
	const session = parentSession || await Staff.app.connectionManager.getSession()

	try {
		const staff = await Promise.all(
			staffData.map(data =>
				Staff.create({
					...data,
					businessId
				}, { session })
			)
		)
		return staff
	} finally {
		// Only release if we created the session
		if (!parentSession) {
			await Staff.app.connectionManager.releaseSession(session)
		}
	}
}
```

### Retry Support

```javascript
// Automatic retry on failure
await Business.withTransaction(async (session) => {
	const business = await connectionManager.retryOperation(
		tenant,
		async () => Business.findById(id, { session })
	)

	if (!business) throw new Error('Business not found')
	return business.updateAttributes(updates, { session })
})
```

### Connection Management

```javascript
// Ensure connection before operations
const ds = await Business.ensureConnection()
const business = await Business.create(data)

// Manual connection management
const pool = await connectionManager.getPool(tenant)
try {
	await connectionManager.validateConnection(pool, tenant)
	// Use connection
} finally {
	connectionManager.recordCompletion(tenant)
}
```

### Health Checks

```javascript
// Check connection health
const isHealthy = await connectionManager.validateConnection(pool, tenant)

// Monitor pool status
const metrics = connectionManager.getMetrics(tenant)
console.log('Active requests:', metrics.activeRequests)
console.log('Total requests:', metrics.totalRequests)
console.log('Session count:', metrics.sessionCount)
console.log('Last validated:', new Date(metrics.lastValidated))
```
