/**
 *  @module Mixin:ObjectIdType
 *  <AUTHOR>
 *  Reference: https://www.npmjs.com/package/loopback-objectid-mixin
 */

module.exports = function(Model, options) {
	module.exports = (Model, options) => {
		const objectId = Model.getDataSource().connector.getDefaultIdType();
		const isMongoConnector = Model.getDataSource().connector.name === 'mongodb';
		const isPropertiesExists = options && options.properties && Array.isArray(options.properties);

		if (!isPropertiesExists) {
			console.log('❌ ObjectIdMix', 'Properties not found for ' + Model.definition.name);
		}

		if (isMongoConnector) {
			options.properties.each(propertyKey => {
				Model.defineProperty(propertyKey, {
					type: objectId,
				});
			});
		} else {
			console.log('❌ ObjectIdMix', 'loopback-connector-mongodb not found for current model');
		}
	};
};
