{"name": "Numbering", "plural": "Numbering", "base": "PersistedModel", "idInjection": true, "strict": true, "options": {"mongodb": {"allowExtendedOperators": true}}, "mixins": {"Multitenant": true, "FindOneAndUpdate": true}, "properties": {"length": {"type": "number", "required": true}, "last": {"type": "number", "required": true, "default": 0}, "prefix": {"type": "string"}, "suffix": {"type": "string"}, "lastNumber": {"type": "number"}, "runoutThreshold": {"type": "number"}, "reset": {"type": {"daily": {"type": "boolean"}, "firstNumber": {"type": "number"}}}, "lastAt": {"type": "date", "description": "Time last number issued, used for daily reset"}, "ownerId": {"type": "string"}}, "relations": {}, "acls": [], "indexes": {"ownerId": {"keys": {"ownerId": 1}}}, "methods": {}}