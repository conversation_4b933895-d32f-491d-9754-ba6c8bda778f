{"name": "Account", "plural": "Accounts", "base": "PersistedModel", "idInjection": true, "strict": false, "mixins": {}, "options": {}, "properties": {"id": {"type": "String", "id": true, "generated": true}}, "validations": [], "relations": {}, "acls": [], "indexes": {}, "scopes": {}, "methods": {"register": {"description": "Register an account", "http": {"path": "/register", "verb": "post"}, "accepts": [{"arg": "data", "type": "object", "http": {"source": "body"}, "required": true}, {"arg": "req", "type": "object", "http": {"source": "req"}}], "returns": {"type": "object", "root": true}}}}