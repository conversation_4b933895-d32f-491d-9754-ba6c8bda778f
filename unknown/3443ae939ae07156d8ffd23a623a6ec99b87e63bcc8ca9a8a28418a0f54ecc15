{"name": "Visit", "plural": "Visits", "base": "PersistedModel", "idInjection": true, "strict": true, "mixins": {"Multitenant": true}, "options": {"validateUpsert": true}, "properties": {"kind": {"type": "string", "enum": ["nearyby", "instore"]}, "geo": {"type": "Geometry"}, "from": {"type": "Date", "description": "time entering location"}, "until": {"type": "Date", "description": "Time of exit (or expire)"}, "duration": {"type": "Number", "description": "integer, in seconds"}}, "relations": {"person": {"type": "belongsTo", "model": "Person", "foreignKey": "personId"}, "place": {"type": "belongsTo", "model": "Place", "foreignKey": "placeId"}}, "validations": [], "acls": [], "indexes": {}, "scopes": {}, "methods": {}}