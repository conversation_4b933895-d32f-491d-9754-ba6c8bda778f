/**
 *  @module Mixin:Locale
 *
 * Time zone - https://en.wikipedia.org/wiki/List_of_tz_database_time_zones
 */
const { isEmptyObj } = require('@perkd/utils'),
	{ EmbedLib } = appRequire('lib/common/embedLibrary')

module.exports = function(Model, opt) {
	const localLib = new EmbedLib(Model, 'locales', opt, null)
	localLib.setup()

	Model.prototype._locale = function() {
		const { locale } = this
		return locale && !isEmptyObj(locale) ? locale : null
	}

	Model.prototype.defaultLanguage = async function(language, options) {
		const { locale } = this,
			{ languages } = locale

		// getter
		if (!language) {
			const result = (this._locale() && languages)
				? (languages.length > 0 ? languages[0] : null)
				: null

			return result
		}

		// setter

		// update existing
		if (this._locale() && locale.languages) {
			const pos = languages.findIndex(lang => (lang === language)),
				moved = pos > 0 ? languages.splice(pos, 1)[0] : language

			languages.unshift(moved)
			await this.updateAttributes({ locale })
			return moved
		}

		// create new
		if (this._locale()) {
			this._locale().languages = []
		}

		const defaultLoc = this._locale() || {
			languages: [],
			currency: '',
			country: '',
			timeZone: null,
		}
		defaultLoc.languages.push(language)
		await this.upsertLocales(defaultLoc, options)
		return language
	}
}
