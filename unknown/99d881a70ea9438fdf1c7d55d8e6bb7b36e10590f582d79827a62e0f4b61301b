/**
 *  @module Mixin:Callback
 */
const { Context } = require('@perkd/multitenant-context'),
	{ bm, isEmptyObj, substitute } = require('@perkd/utils'),
	{ CrmRemote } = require('@perkd/api-request')

const LIMIT = 20,
	INTERVAL = 1000

module.exports = function(Model, options) {

	const CALLBACK = {
		rate: {
			limit: options.limit || LIMIT,
			interval: options.interval || INTERVAL,
		},
		useSettings: options.useSettings,	// true => callbacks are defined in settings.json (not instances)
	}

	if (CALLBACK.useSettings) {
		// ---  callbacks defined in settings.json, define Static Methods, NO TENANT CODE

		Model._callback = function(name) {
			const { callbacks } = appSettings()
			return callbacks ? callbacks.find(cb => cb.name === name) : null
		}

		Model.doCallback = async function(data = {}, name, optional = false, param = {}) {
			const callback = Model._callback(name)

			if (callback) return doCallback(callback, data, param)

			if (!optional) {
				return Model.rejectErr('callback_not_config', { name, data, param }, true)
			}
		}
	}
	else {
		// ---  callbacks defined in instances, define Instance Methods

		// supports both single callback ('callback', name ignored) OR list of callbacks ('callbackList')
		Model.prototype._callback = function(name) {
			const { callbacks: defaultCallbacks = [] } = appSettings(),
				callbacks = this.callbacks || defaultCallbacks

			return this.callback || (callbacks ? (callbacks.find(cb => cb.name === name) || defaultCallbacks.find(cb => cb.name === name)) : null)
		}

		Model.prototype.doCallback = async function(data = {}, name, optional = false, param = {}) {
			const { tenant = {} } = this,
				found = this._callback(name),
				callback = found.toJSON ? found.toJSON() : found

			if (callback) {
				if (!isEmptyObj(tenant)) param.tenant = tenant

				return doCallback(callback, data, param)
			}

			if (!optional) {
				return this.rejectErr('callback_not_config', { name, data, param }, true)
			}
		}
	}

	// -----  Private functions  -----

	async function doCallback(callback, data = {}, param = {}) {
		const { name = 'default' } = callback,
			payload = data.toJSON ? data.toJSON() : data

		callback = substitute(callback, { ...payload, ...param })

		const { method, url, headers, rate } = callback,
			rateLimit = rate || CALLBACK.rate,
			{ tenant } = param,
			tenantCode = tenant?.code || tenant || Context.tenant,
			api = new CrmRemote({ tenantCode }, { rate: rateLimit }),
			config = { method, url, headers, data: payload },
			_start = bm.mark()

		stats(name, 'count')

		try {
			const res = await api.request(config)

			stats(name, 'latency', bm.diff(_start))
			return res
		}
		catch (err) {
			stats(name, 'error')

			const { context = {} } = err,
				{ response = {} } = context,
				{ data = {}, status: statusCode } = response,
				{ code, message, details, stack } = data.error ?? {},
				error = new Error(message)

			error.code = code
			error.statusCode = statusCode
			error.details = details
			error.stack = stack

			console.log('Error [doCallback]: %j', { callback, data, param }, err)

			throw error
		}
	}

	function stats(name, metric, value) {
		if (name) {
			Model.app.emit('stats', {
				metric: 'callback.' + metric,
				child: name,
				value,
			})
		}
	}
}
