{"name": "Recommend", "plural": "Recommend", "base": "Model", "idInjection": false, "strict": true, "options": {}, "properties": {"name": {"type": "string", "required": true, "description": "Name of recommended item"}, "id": {"type": "String", "required": true, "description": "Id of recommended item"}, "score": {"type": "number", "description": "Recommendation score"}, "response": {"type": "string", "required": true, "enum": ["candidate", "pending", "accepted", "declined", "viewed", "purchased", "expired"], "description": "Reponse of recipient"}, "createdAt": {"type": "date"}, "expiresAt": {"type": "date", "description": "Time recommendation expires"}, "recommendedAt": {"type": "date"}, "respondedAt": {"type": "date"}, "responseTime": {"type": "number", "description": "Time lapsed (seconds) between recommendedAt and respondedAt"}}, "scopes": {}, "methods": {}}