{"name": "<PERSON><PERSON>", "plural": "<PERSON><PERSON>", "description": "", "base": "PersistedModel", "idInjection": true, "strict": false, "mixins": {"MultitenantRemote": true}, "properties": {}, "hidden": [], "settings": {}, "validations": [], "relations": {}, "acls": [], "scopes": {}, "indexes": {}, "methods": {"removeObjAll": {"description": "Remove object and its copies for a device.", "http": {"path": "/obj/all", "verb": "delete"}, "accepts": [{"arg": "personId", "type": "any", "required": true}, {"arg": "model", "type": "string", "required": true}, {"arg": "key", "type": "string", "required": true}], "returns": {"type": "object", "root": true}}}}