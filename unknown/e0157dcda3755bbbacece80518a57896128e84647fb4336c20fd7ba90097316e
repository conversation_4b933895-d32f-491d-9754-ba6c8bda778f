/**
 * Eventbus monitor
 *
 * Author: <PERSON><PERSON><PERSON>@waveo.com
 */

const redis = require('redis');
const argv = require('minimist')(process.argv.slice(2));

if (argv._.length === 0 || argv.help) {
	console.log('\nusage:');
	console.log('	1) node eventbus-monitor.js all');
	console.log('	2) node eventbus-monitor.js a.b.c  *y.z  x.*.z  x.y.*');
	console.log('	3) node eventbus-monitor.js -h eventbus.perkd.intranet all\n');
	return;
}

const EVENTBUS = argv.h || argv.host || 'eventbus';
const subClient = redis.createClient(6379, EVENTBUS);
const channelCount = {};

subClient.on('ready', () => {
	subClient.on('pmessage', (pattern, channel, message) => {
		echo(channel, message);
	});

	subClient.on('message', (channel, message) => {
		echo(channel, message);
	});

	subClient.on('subscribe', pattern => {
		console.log('Monitoring: ' + pattern);
	});

	subClient.on('psubscribe', pattern => {
		console.log('Monitoring: ' + (pattern === '*' ? 'all events' : pattern));
	});

	argv._.forEach(pattern => {
		if (pattern === 'all') pattern = '*';
		(pattern.indexOf('*') === -1) ? subClient.subscribe(pattern) : subClient.psubscribe(pattern);
	});
});

function echo(channel, message) {
	channelCount[channel] = channelCount[channel] ? channelCount[channel] + 1 : 1;
	const timeNow = new Date();
	console.log('\n' + getTime() + ' [%s, total:%d] %s', channel, channelCount[channel], message);

	function getTime() {
		return new Date(timeNow.getTime()).toLocaleTimeString('en-US', {
			hour12: false,
		}) + '.' + timeNow.getMilliseconds();
	}
}

/**
 * End script
 */
