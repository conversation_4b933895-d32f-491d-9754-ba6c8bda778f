{"name": "Merchant", "plural": "Merchants", "base": "PersistedModel", "idInjection": true, "strict": false, "mixins": {"MultitenantRemote": true}, "properties": {}, "validations": [], "relations": {}, "acls": [], "indexes": {}, "scopes": {}, "methods": {"getToken": {"http": {"path": "/token", "verb": "get"}, "accepts": [{"arg": "subject", "type": "string", "required": true}, {"arg": "placeId", "type": "any", "required": true}, {"arg": "invalidate", "type": "boolean", "default": false}], "returns": {"type": "object", "root": true}}}}