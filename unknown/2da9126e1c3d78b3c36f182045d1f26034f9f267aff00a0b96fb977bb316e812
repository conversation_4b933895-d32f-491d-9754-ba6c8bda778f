/**
 * Eventbus Pubber
 *
 * Author: <PERSON><PERSON><PERSON>@waveo.com
 */

const redis = require('redis');
const uniqid = require('uniqid');
const argv = require('minimist')(process.argv.slice(2));
const channelCount = {};

const EVENTBUS = argv.h || argv.host || '127.0.0.1';
const pubClient = redis.createClient(6379, EVENTBUS);

/**
 * Setup event
 */
const tenantCode = '',
	domain = '',
	actor = '',
	action = '';

let TOTAL = 1;

const eventData = {
};

// ====================================== main ======================================

const eventName = domain + '.' + actor + '.' + action;

pubClient.on('ready', () => {
	console.log('\n>>> connected to eventbus <<<\n');

	const interval = setInterval(() => {
		publish(tenantCode, domain, eventName, eventData);

		if (!--TOTAL) {
			clearInterval(interval);
			console.log('\n>>> All published <<<\n');
		}
	}, 10); // delay 10ms
});

function publish(tenantCode, domain, eventName, eventData) {
	const channel = tenantCode + '.' + eventName,
		message = JSON.stringify({
			id: uniqid(), // event uuid
			tenantCode,
			domain,
			actor,
			action,
			name: eventName,
			data: eventData,
			published: Date.now(), // the number of milliseconds elapsed since 1 January 1970 00:00:00 UTC
		});
	echo(channel, message);
	pubClient.publish(channel, message);
}

function echo(channel, message) {
	channelCount[channel] = channelCount[channel] ? channelCount[channel] + 1 : 1;
	const timeNow = new Date();
	console.log('\n' + getTime() + ' [%s, total:%d] %s', channel, channelCount[channel], message);

	function getTime() {
		return new Date(timeNow.getTime()).toLocaleTimeString('en-US', {
			hour12: false,
		}) + '.' + timeNow.getMilliseconds();
	}
}

/**
 * End script
 */
