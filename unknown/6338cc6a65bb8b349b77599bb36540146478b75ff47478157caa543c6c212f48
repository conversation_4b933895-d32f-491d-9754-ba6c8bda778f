{"name": "Fulfillment", "plural": "Fulfillments", "base": "PersistedModel", "idInjection": true, "strict": false, "mixins": {"MultitenantRemote": true}, "properties": {"id": {"type": "String", "id": true, "generated": true}}, "validations": [], "relations": {}, "acls": [], "methods": {"findByProviderOrderId": {"description": "Find fulfillment(s) of provider by (external) order id", "http": {"path": "/provider/orderId", "verb": "get"}, "accepts": [{"arg": "provider", "type": "string", "required": true}, {"arg": "orderId", "type": "string", "required": true}], "returns": {"type": ["Fulfillment"], "root": true}}, "findOpenForTables": {"description": "Get Open fulfillments for tables", "http": {"path": "/tables/open", "verb": "post"}, "accepts": [{"arg": "tables", "type": [{"types": "object"}], "required": true, "description": "List of tables (Spot)"}, {"arg": "kitchen", "type": "boolean", "default": false, "description": "true => include kitchen fulfillments"}], "returns": {"type": ["Fulfillment"], "root": true}}, "requested": {"description": "Mark fulfillments of provider as Requested for order", "http": {"path": "/requested", "verb": "post"}, "accepts": [{"arg": "orderId", "type": "string", "required": true}, {"arg": "provider", "type": "string"}, {"arg": "at", "type": "date"}], "returns": {"type": ["Fulfillment"], "root": true}}, "shippingRates": {"description": "Get shipping rates", "http": {"path": "/shipping/rates", "verb": "post"}, "accepts": [{"arg": "origin", "type": "object", "required": true}, {"arg": "destination", "type": "object", "required": true}, {"arg": "items", "type": "array", "required": true}, {"arg": "currency", "type": "string", "required": true}], "returns": {"type": "Object", "root": true}}, "pickupTimes": {"description": "Estimate pickup times for items at a place", "http": {"path": "/pickup/times", "verb": "post"}, "accepts": [{"arg": "placeId", "type": "any", "required": true}, {"arg": "items", "type": "array", "required": true}, {"arg": "options", "type": "object"}], "returns": {"type": "Object", "root": true}}, "status": {"description": "Fulfillment status", "http": {"path": "/status", "verb": "get"}, "accepts": [{"arg": "fulfillmentId", "type": "any", "required": true}], "returns": {"type": "Object", "root": true}}, "prototype.request": {"description": "Send fulfillment request to provider", "http": {"path": "/request", "verb": "post"}, "accepts": [{"arg": "acquired", "type": "object", "description": "Order touchpoint"}], "returns": {"type": "Fulfillment", "root": true}}, "prototype.delivered": {"description": "<PERSON> as Delivered", "http": {"path": "/delivered", "verb": "post"}, "accepts": [{"arg": "at", "type": "date"}], "returns": {"type": "Object", "root": true}}, "prototype.cancel": {"description": "Cancel fulfillment of items", "http": {"path": "/cancel", "verb": "post"}, "accepts": [{"arg": "items", "type": "array", "required": true}], "returns": {"type": "array", "root": true}}}}