{"name": "Gateway", "plural": "Gateways", "base": "PersistedModel", "idInjection": true, "strict": false, "mixins": {"MultitenantRemote": true}, "options": {"validateUpsert": true}, "properties": {"id": {"type": "String", "generated": true, "id": true}, "name": {"type": "string"}}, "validations": [], "relations": {"owner": {"type": "belongsTo", "model": "Business", "foreignKey": "ownerId"}, "bank": {"type": "referencesMany", "model": "Payment", "foreignKey": "bankIds"}, "card": {"type": "referencesMany", "model": "Payment", "foreignKey": "cardIds"}, "linepay": {"type": "referencesMany", "model": "Payment", "foreignKey": "linepayIds"}, "alipay": {"type": "referencesMany", "model": "Payment", "foreignKey": "alipayIds"}, "grabpay": {"type": "referencesMany", "model": "Payment", "foreignKey": "grabpayIds"}, "storedvalue": {"type": "referencesMany", "model": "Payment", "foreignKey": "storedvalueIds"}, "creditcredits": {"type": "referencesMany", "model": "Payment", "foreignKey": "creditcreditsIds"}, "redeemedcredits": {"type": "referencesMany", "model": "Payment", "foreignKey": "redeemedcreditsIds"}, "redeemedcash": {"type": "referencesMany", "model": "Payment", "foreignKey": "redeemedcashIds"}, "settledcredits": {"type": "referencesMany", "model": "Payment", "foreignKey": "settledcreditsIds"}}, "acls": [], "indexes": {}, "scopes": {}, "methods": {"getPayment": {"description": "Get wallet (payment) for type of payment", "http": {"path": "/payment", "verb": "get"}, "accepts": [{"arg": "businessId", "type": "String", "required": true}, {"arg": "type", "type": "String", "enum": ["card", "alipay", "linepay", "bank", "storedvalue"], "required": true}, {"arg": "provider", "type": "String"}], "returns": {"type": "Payment", "root": true}}, "providerFor": {"description": "Get Provider for payment type", "http": {"path": "/provider", "verb": "get"}, "accepts": [{"arg": "businessId", "type": "String", "required": true}, {"arg": "type", "type": "String", "enum": ["card", "alipay", "linepay", "bank", "storedvalue"], "required": true}], "returns": {"type": "string", "root": true}}}}