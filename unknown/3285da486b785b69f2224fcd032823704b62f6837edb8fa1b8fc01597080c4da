{"name": "Pickup", "description": "", "idInjection": false, "strict": false, "options": {"validateUpsert": true}, "properties": {"id": false, "type": {"type": "string", "default": "store", "enum": ["store", "convenience_store", "vending"]}, "countries": [{"type": "string", "description": "ISO 3166-1 alpha-2 country codes in uppercase"}], "pricing": {"type": "FulfillPrice"}, "hours": {"type": "OpeningHour", "default": {}}, "prescheduled": {"type": "boolean", "default": false, "description": "Requires prescheduled pickup according to leadTime, hours & timeSlot"}, "leadTime": {"type": "number", "description": "Minimum time (minutes) required from order to pickup"}, "timeslot": {"type": "number", "default": 30, "description": "Time intervals (duration in minutes)"}, "include": [{"id": {"type": "string", "description": "PlaceId"}, "name": {"type": "string", "description": "Store name"}, "locationCode": {"type": "string", "max": 24, "description": "Used by carrier"}, "hours": {"type": "OpeningHour", "default": {}}, "timeslot": {"type": "number", "default": 30, "description": "Time intervals (duration in minutes)"}}], "excludePlaces": [{"type": "string", "description": "placeIds where pickup not available"}], "availability": {"type": {"check": {"type": "boolean", "description": "???"}}}, "lookup": {"type": "string", "description": "Url for 'autocomplete' service (for 3rd party pickups, eg. 7-11), returns list of 'places'"}, "images": [{"type": "string", "description": "Image url of icons/logos of fulfillment service"}], "form": {"type": "object", "description": "Form definition for data collection (optional)"}}, "validations": []}