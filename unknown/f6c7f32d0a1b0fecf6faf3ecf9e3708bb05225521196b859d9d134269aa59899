/**
 *  @module Mixin:Image
 */
const path = require('node:path'),
	request = require('request'),
	loopback = require('loopback'),
	{ Apis } = require('@crm/types'),
	{ Context } = require('@perkd/multitenant-context'),
	{ TENANT } = Apis.Headers

module.exports = function(Model, options) {

	const modelName = options.model,
		ImageModel = loopback.getModel(modelName),
		ImageRelation = options.relation,
		methodDescription = options.description,
		uploadMethod = ImageRelation + 'Upload',
		upsertMethod = 'upsert' + ImageRelation.charAt(0).toUpperCase() + ImageRelation.slice(1),
		deleteMethod = ImageRelation + 'Delete',
		cloneMethod = ImageRelation + 'Clone'

	// -----  Instance Methods  -----
	// Avoid use this function, use image service api directly, more efficiently
	Model.prototype[uploadMethod] = async function(imageId, req, res) {
		const self = this,
			{ pluralModelName } = ImageModel,
			url = ImageModel.getDataSource().connector.url + '/' + path.join(pluralModelName, imageId, 'upload'),
			options = {
				url,
				headers: {},
				json: true,
				gzip: true, // response body maybe gzipped
			}

		options.headers[TENANT] = Context.tenant

		return new Promise((resolve, reject) => {
			req.pipe(
				request.post(options, (err, response, body) => {
					if (!err) {
						if (response.statusCode === 200) {
							const ctx = {
								Model,
								isNewInstance: false,
								instance: self,
								data: {},
								hookState: {},
							}

							ctx.data[ImageRelation] = [ body ]
							Model.doUpsert ? Model.notifyObserversOf('after doUpsert', ctx) : Model.notifyObserversOf('after save', ctx)
							Model.notifyObserversOf('after imageUploaded', ctx)
							resolve(body)
						}
						else {
							err = new Error(body.error.message)
							err.statusCode = body.error.statusCode
							err.stack = body.error.stack ? body.error.stack : undefined
							reject(err)
						}
					}
					else reject(err)
				})
			)
		})
	}

	Model.prototype[upsertMethod] = async function(imageList, options = {}) {
		const { id } = this,
			images = await new Promise((resolve, reject) => {
				this[ImageRelation]((err, images) => {
					if (err) reject(err)
					else resolve(images)
				})
			}),
			updates = []

		if (images.length) return images

		for (const image of imageList) {
			if (!image.id) continue

			const img = await ImageModel.findById(image.id).catch(() => undefined)

			if (img) {
				const instance = await img.updateAttributes({ ownerId: id }),
					context = {
						Model,
						isNewInstance: false,
						data: { id },
						hookState: {},
					}

				context.data[ImageRelation] = [ instance ]
				Model.notifyObserversOf('after save', context)

				updates.push(instance)
			}
		}

		return updates
	}

	Model.prototype[deleteMethod] = async function(imageId) {
		const ctx = {
			Model,
			isNewInstance: false,
			currentInstance: this,
			instance: this,
			where: {},
			hookState: {},
		}

		await ImageModel.destroyById(imageId).catch(() => null)

		Model.doUpsert ? Model.notifyObserversOf('after doUpsert', ctx) : Model.notifyObserversOf('after save', ctx)
		Model.notifyObserversOf('after imageDeleted', ctx)
	}

	Model.prototype[cloneMethod] = async function(imageId, ownerId) {
		const image = await ImageModel.findById(imageId)
		return image.clone(ownerId)
	}

	// -----  Remote hooks  -----

	ImageModel.on('dataSourceAttached', model => {
		const remotes = model.getDataSource().connector.remotes

		if (remotes) {
			remotes.before('**', (ctx, next, method) => {
				const { req } = ctx

				req.headers = req.headers || {}
				req.headers[TENANT] = Context.tenant // Multitenancy: inject tenantCode
				next()
			})
		}
	})

	// -----  Operation hooks  -----

	Model.observe('before delete', async ({ hookState }) => {
		const before = hookState._before

		if (before) {
			const images = await before[ImageRelation].find(),
				res = []

			for (const image of images) {
				res.push(
					await before[deleteMethod](image.id)
				)
			}

			return res
		}
	})

	// -----  Remote Methods  -----

	Model.remoteMethod('prototype.' + uploadMethod, {
		description: methodDescription,
		http: { path: '/' + ImageRelation + '/:fk/upload', verb: 'post' },
		accepts: [
			{ arg: 'fk', type: 'string', required: true, description: 'Foreign key for ' + ImageRelation },
			{ arg: 'req', type: 'object', http: { source: 'req' } },
			{ arg: 'res', type: 'object', http: { source: 'res' } },
		],
		returns: { type: 'object', root: true },
	})

	Model.remoteMethod('prototype.' + upsertMethod, {
		description: methodDescription,
		http: { path: '/' + upsertMethod, verb: 'post' },
		accepts: [ { arg: 'imageList', type: 'array', required: true }, { arg: 'options', type: 'object' } ],
		returns: { type: 'object', root: true },
	})

	Model.remoteMethod('prototype.' + deleteMethod, {
		description: 'Delete a related item by id for ' + ImageRelation + '.',
		http: { path: '/' + ImageRelation + '/:fk/delete', verb: 'delete' },
		accepts: [ { arg: 'fk', type: 'string', required: true, description: 'Foreign key for ' + ImageRelation } ],
		returns: { type: 'object', root: true },
	})

	Model.remoteMethod('prototype.' + cloneMethod, {
		description: 'Clone a related item by id for ' + ImageRelation + '.',
		http: { path: '/' + ImageRelation + '/:fk/clone', verb: 'post' },
		accepts: [ { arg: 'fk', type: 'string', required: true, description: 'Foreign key for ' + ImageRelation }, { arg: 'ownerId', type: 'string' } ],
		returns: { type: 'object', root: true },
	})
}
