{"name": "Bag", "plural": "Bags", "base": "Model", "idInjection": false, "strict": false, "options": {}, "mixins": {}, "properties": {"name": {"type": "string", "required": true, "description": "Unique name of bag"}, "items": {"type": [{"type": {"variantId": {"type": "string"}, "title": {"type": "string", "max": 80}, "quantity": {"type": "number"}, "unitPrice": {"type": "number"}, "price": {"type": "number"}, "options": {"type": ["object"]}, "images": {"type": ["string"]}, "tags": {"type": ["string"]}, "taxes": {"type": [{"type": {"title": {"type": "string"}, "rate": {"type": "number"}}}]}}}], "default": []}, "fulfillment": {"type": "object"}, "discountsApplied": {"type": ["object"], "default": []}, "totalDiscounts": {"type": "number"}, "subtotalPrice": {"type": "number"}, "totalTax": {"type": "number"}, "totalPrice": {"type": "number"}, "currency": {"type": "number"}, "taxIncluded": {"type": "boolean"}, "taxes": {"type": [{"type": {"title": {"type": "string"}, "rate": {"type": "number"}}}], "default": []}, "startTime": {"type": "date"}, "endTime": {"type": "date"}, "policy": {"type": {"channel": {"type": "string"}, "offers": {"type": ["object"]}, "fulfillments": {"type": "object"}, "payments": {"type": {"enabled": {"type": "boolean"}, "methods": {"type": ["string"], "enum": ["applepay", "googlepay", "alipay"], "description": "Payment methods available for checkout, use CardMaster's if not specified"}}, "default": {}}, "options": {"type": {"itemQuantity": {"type": "boolean"}, "itemConfig": {"type": "boolean"}}}, "excludeOffers": {"type": ["string"], "description": "Offer masterIds to exclude"}, "limit": {"type": {"noRewards": {"type": "boolean"}, "variants": {"type": "object", "description": "{ <id>: { min, max, increment, modify }}"}, "clusters": {"type": [{"type": {"variants": {"type": ["string"], "description": "variantId"}, "max": {"type": "number"}}}]}, "quantity": {"type": {"min": {"type": "number"}, "max": {"type": "number"}}}, "totalPrice": {"type": {"min": {"type": "number"}, "max": {"type": "number"}}}}}, "modify": {"type": {"items": {"type": "boolean"}, "quantities": {"type": "boolean"}, "payment": {"type": "boolean"}}}, "checkout": {"type": "object"}}}}, "validations": [], "acls": [], "scopes": {}, "methods": {}}