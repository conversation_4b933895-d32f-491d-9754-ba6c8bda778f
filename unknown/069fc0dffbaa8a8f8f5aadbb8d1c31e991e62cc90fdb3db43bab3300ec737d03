/**
 *  @module Perkd SDK
 * ENVIRONMENT
 * 	PERKD_SECRET_KEY
 */
const { PerkdWallet } = require('@perkd/sdk')

class PerkdSDKModule {
	/**
	 * @param	{Object} app - emitter
	 * @param	{Object} settings
	 * 			{Boolean} enabled
	 */
	constructor(app, settings) {
		const { enabled } = settings

		this.app = app
		this.settings = settings
		this.service = app.service
		this.enabled = enabled
	}

	async start() {
		const { enabled } = this,
			{ metrics } = appModule('metrics')

		if (enabled) {
			this.Perkd = new PerkdWallet(undefined, undefined, metrics)
		}
	}
}

module.exports = PerkdSDKModule
