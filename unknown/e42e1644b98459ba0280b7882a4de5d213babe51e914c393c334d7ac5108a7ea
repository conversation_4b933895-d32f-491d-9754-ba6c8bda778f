/**
 *  @module Mixin:Email
 */

const { EmbedLib } = appRequire('lib/common/embedLibrary')

module.exports = function(Model, options) {
	const emailLib = new EmbedLib(Model, 'emails', options)
	emailLib.setup()

	Model.prototype._email = function({ address }) {
		return this.emails.value().find(email => email.address === (address && address.toLowerCase())) // email.address is lowercase
	}

	Model.prototype._emailType = function(type) {
		return this.emails.value().find(email => email.type === type)
	}

	Model.prototype.optInEmail = function(address) {
		const email = this._email({ address })
		return email ? email.optIn : false
	}

}
