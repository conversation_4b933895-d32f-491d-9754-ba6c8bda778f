{"name": "Option", "plural": "Options", "base": "Model", "description": "Options for <PERSON><PERSON><PERSON>", "idInjection": false, "strict": true, "mixins": {}, "properties": {"key": {"type": "string", "id": true, "defaultFn": "nanoid"}, "title": {"type": "String", "max": 24, "description": "Display name, eg. size, color"}, "values": {"type": [{"type": "OptionValue"}], "required": true, "default": [], "description": "Setting for component options"}, "required": {"type": "boolean", "default": false, "description": "Is option mandatory?"}, "unique": {"type": "boolean", "description": "Single selection per option"}, "min": {"type": "number", "description": "Minimum quantity to be selected"}, "max": {"type": "number", "description": "Maximum quantity to be selected"}, "type": {"type": "string", "enum": ["checkboxList", "quantityList", "tagList", "switchList", "radioboxList", "carousel", "slider", "input", "bundle"], "description": "UI - component to use"}, "icon": {"type": "String", "description": "UI - picon character"}, "tip": {"type": "String", "description": "UI - hint text"}, "properties": {"type": "object", "description": "UI - used for textInput (e.g: keyboard type)"}, "style": {"type": "object", "description": "UI - overwrite styles defined in Formik/styles.js"}}, "methods": {}}