/**
 *  @module Mixin:Behavior - depends on FindOneAndUpdate mixin
 */

const { Methods, Settings } = require('@perkd/behaviors'),
	{ camelcase } = require('@perkd/utils')

const CANCELLED = 'cancelled'

module.exports = function(Model) {

	/**
	 * @param	{String} id of instance to update
	 * @param	{String} name of behavior
	 * @param	{Object} preset for this model & behavior, from behavior.json in config folder
	 *			{Array} metrics - metrics to generate
	 *			{String} foreignKey - in source model for behaviors, eg. Order, Reward
	 * @param	{Object} event event from eventbus, {name:'xxx', data:{...}}
	 */
	Model.updateBehavior = async function(id, name, preset, event) {
		const instance = await Model.findById(id)
		return instance?.updateBehavior(name, preset, event)
	}

	Model.prototype.updateBehavior = async function(name, preset = {}, event) {
		const { id } = this,
			{ app, name: model } = Model,
			{ Event, models } = app,
			Method = Methods[name]

		if (typeof Method !== 'function') {
			throw new Error(`Behavior '${name}' not supported for model: ${model}`)
		}

		const method = new Method(models, this, preset),
			behavior = await method.behavior(),
			changes = {
				$set: { [`behaviors.${name}`]: behavior }
			},
			updated = await Model.findOneAndUpdate({ id }, changes),
			{ rollbackEvents } = Settings[name]

		if (event) {
			const isRollback = rollbackEvents.includes(event.name),
				evtName = Event[model.toLowerCase()].behavior[isRollback ? CANCELLED : name],
				instance = updated?.toJSON(),
				evtData = {
					type: name,
					model,
					behavior,
					instance,
					sourceId: event.data.id,
					source: event,
				}

			app.emit(evtName, evtData)
		}

		return updated
	}
}
