/**
 *  @module Model:Service
 *
 */
const { lookup } = require('node:dns').promises,
	{ hostname } = require('node:os'),
	{ isEmpty, keys, filter, invert } = require('lodash'),
	debug = require('debug')('waveo:service'),
	{ Settings: SETTINGS } = require('@crm/types'),
	{ Context } = require('@perkd/multitenant-context'),
	{ Tenants, MESSAGE } = require('@perkd/tenants'),
	{ Settings } = require('@perkd/settings'),
	{ cloneDeep, merge } = require('@perkd/utils')

const { DEFAULT } = SETTINGS

module.exports = function(Service) {
// -----  Static Properties  -----

	Service.State = { UNKNOWN: 0, UP: 1, READY: 2, STARTED: 3, PAUSED: 4, RECOVERING: 5, RECOVERED: 6 }

	// -----  Static Methods  -----

	Service.init = async function(name, appConfig) {
		const { app } = Service,
			{ service, modules } = appConfig,
			{ host: tenantsHost, port: tenantsPort, username: tenantsUserName, password: tenantsPassword } = modules.tenants,
			tenantsConfig = {
				redis: { host: tenantsHost, port: tenantsPort, username: tenantsUserName, password: tenantsPassword },
			},
			tenants = service.multitenancy
				? (!isEmpty(service.tenants) ? service.tenants : undefined)
				: { [service.tenantCode]: { code: service.tenantCode } },
			TENANTS = new Tenants(tenantsConfig, tenants),
			ipAddress = await _getIpAddress(),
			instance = await _initInstance(name, appConfig, ipAddress)

		Service.TENANTS = TENANTS

		await _initTenants(app, instance, TENANTS)
		await _initSettings(tenantsConfig)

		debug('service instance %j', instance)
		debug('service tenants %j', TENANTS.codes)

		return instance
	}

	Service.ready = async function(id) {
		return Service._callInstance('ready', id)
	}

	Service.start = async function(id) {
		return Service._callInstance('start', id)
	}

	Service.stop = async function(id) {
		Service.TENANTS.end()
		return Service._callInstance('stop', id)
	}

	Service.pause = async function(id) {
		return Service._callInstance('pause', id)
	}

	Service.resume = async function(id) {
		return Service._callInstance('resume', id)
	}

	Service.ping = async function() {
		return 'OK'
	}

	Service.allTenantCodes = function() {
		return Service.TENANTS.codes
	}

	Service.getSettings = function(key, place) {
		const { tenant } = Context,
			defaultSettings = DEFAULT[key.toUpperCase()]

		return place?.getSettingsByName(key)
			|| merge(defaultSettings, Service.getTenantSettings(tenant, key))
	}

	Service.getTenantSettings = function(tenant, key) {
		return Service.SETTINGS.get(tenant, key)
	}

	Service.status = async function() {
		return 'To be implemented!'		// TODO
	}

	Service.events = async function(tenantCode, eventName) {
		const { app } = Service
		return app.Service.eventbus.getSubscribeStatus(tenantCode, eventName)
	}

	Service._callInstance = async function(method, id) {
		const ID = id || Service.app.service.name,
			instance = await Service.findById(ID)

		return instance.call(method)
	}

	// -----  Instance Methods  -----

	Service.prototype.ready = async function() {
		try {
			await this._call('ready', [ Service.State.UP ], Service.State.READY)
		}
		catch (err) {
			if (this.state.now === Service.State.UP) {
				await this.setState(Service.State.READY)	// force state to READY
			}
			throw err
		}

		if (this.gracefulShutDown === false) {
			appNotify('service_crash_detected', null, 'alert')
			await this.recover()
		}
		await this.updateGracefulShutDown(false)
		return this
	}

	Service.prototype.start = async function() {
		await this._call('start', [ Service.State.READY, Service.State.PAUSED, Service.State.RECOVERING, Service.State.RECOVERED ], Service.State.STARTED)
		return this
	}

	Service.prototype.stop = async function() {
		return this._call('stop', [ Service.State.STARTED, Service.State.PAUSED ], Service.State.READY)
	}

	Service.prototype.pause = async function() {
		return this._call('pause', [ Service.State.STARTED ], Service.State.PAUSED)
	}

	Service.prototype.resume = async function() {
		return this._call('resume', [ Service.State.PAUSED ], Service.State.STARTED)
	}

	Service.prototype.recover = async function() {
		await this.setState(Service.State.RECOVERING)
		await this._call('recover', [ Service.State.READY, Service.State.RECOVERING ], Service.State.RECOVERED)
		debug('Service is recovered')
		return this
	}

	Service.prototype.terminate = async function() {
		const { canTerminate, state } = this
		// to support modules terminate in loopback 4

		if (!canTerminate) {
			throw new Error("Don't allow terminate service")
		}

		try {
			await this._call('terminate', [ state.now ], Service.State.UNKNOWN)
			await this.updateGracefulShutDown(true)

			appEcho('\n*** 👌  Service gracefully shutdown ***\n')
			process.exit(0)
		}
		catch (err) {
			appEcho('\n*** ❌  Service NOT gracefully shutdown !!! ***\n')
			process.exit(0)
		}
	}

	/**
	 * @param {Boolean} gracefulShutDown
	 * @return {Service}
	 */
	Service.prototype.updateGracefulShutDown = async function(gracefulShutDown) {
		return this.updateAttributes({ gracefulShutDown })
	}

	Service.prototype.setState = async function(state) {
		const { app } = Service,
			{ Event } = app,
			newState = {
				now: state,
				text: keys(Service.State)[state],
				since: new Date().toISOString(),
			}

		await this.updateAttributes({ state: newState })
		app.emit(Event.service[newState.text], { service: this })
		return this
	}

	Service.prototype.dependenciesState = async function() {
		const dependencies = keys(this.dependencies),
			filter = {
				where: { name: { inq: dependencies } },
				fields: { name: true, version: true, state: true },
			}

		return Service.find(filter)
	}

	Service.prototype.dependents = async function() {
		const { name } = this,
			all = {
				fields: { name: true, version: true, state: true, config: true, dependencies: true }
			}

		const services = await Service.find(all)
		return filter(services, svc => (keys(svc.dependencies).indexOf(name) !== -1))
	}

	Service.prototype._call = async function(method, allowedStates, newState) {
		debug('_call %j', arguments)

		const { name, state } = this,
			{ app } = Service

		if (allowedStates.indexOf(state.now) !== -1) {
			// check if 'main' module implements the method
			if (typeof app.Service.main[method] === 'function') {
				try {
					await app.Service.main[method]()
					await this.setState(newState)

					appEcho('[%s] %s', name, newState ? invert(Service.State)[newState] : 'TERMINATED')
					return this
				}
				catch (err) {
					appEcho('[%s] call %s error:', name, method, err)
				}
			}
			throw new ReferenceError(method + '() not implemented by [' + name + ']')
		}
		else {
			throw new RangeError('Cannot ' + method.toUpperCase() + ' when service is in ' + state.text + ' state')
		}
	}

	// ---  Private Functions

	async function _getIpAddress() {
		return lookup(hostname()).catch(() => '0.0.0.0')
	}

	// MUST clear old value so that state can be updated by 'register()' later
	async function _initInstance(name, appConfig, ipAddress) {
		const { service, port, restApiRoot: apiRoot, modules, apiRequest } = appConfig,
			{ settings } = service,
			state = {
				now: Service.State.UNKNOWN,
				text: '',
				since: ''
			}

		let svcInstance = await Service.findById(name)

		if (svcInstance) {
			svcInstance.config.host = ipAddress
			svcInstance.config.port = port
			await svcInstance.updateAttributes({
				config: svcInstance.config,
				settings,
				state
			})
		}
		else {
			const newService = cloneDeep(service)

			appEcho('[%s] service instance not found, creating from config.json', name)
			newService.config = { host: ipAddress, port, apiRoot }
			newService.config.modules = cloneDeep(modules)
			newService.config.apiRequest = cloneDeep(apiRequest)
			newService.settings = cloneDeep(settings)
			newService.state = state

			svcInstance = await Service.create(newService)
		}

		Service.app.service = svcInstance
		return svcInstance
	}

	async function _initTenants(app, svcInstance, TENANTS) {
		const tenants = await TENANTS.init()

		TENANTS.on(MESSAGE.tenant.ADDED, e => {
			app.emit(MESSAGE.tenant.ADDED, e)
			Service.SETTINGS.addTenant(e.code)
		})
		TENANTS.on(MESSAGE.tenant.REMOVED, e => {
			app.emit(MESSAGE.tenant.REMOVED, e)
			Service.SETTINGS.deleteTenant(e.code)
		})

		return svcInstance.updateAttributes({ tenants })
	}

	async function _initSettings(tenantsConfig) {
		const SETTINGS = new Settings(Service.allTenantCodes(), tenantsConfig)
		await SETTINGS.init()
		Service.SETTINGS = SETTINGS
	}
}
