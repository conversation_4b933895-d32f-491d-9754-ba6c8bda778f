{"name": "Discount", "plural": "Discounts", "description": "", "base": "Model", "idInjection": false, "forceId": false, "strict": true, "properties": {"id": {"type": "String", "id": true, "defaultFn": "nanoid"}, "offerId": {"type": "string", "description": "optional (used in Order instance discount list)"}, "offerMasterId": {"type": "string", "description": "optional (used in CardMaster instance commerce discount list)"}, "name": {"type": "String", "max": 32, "required": true, "description": "Display name (in app) of discount"}, "kind": {"type": "string", "enum": ["fixed", "percentage"], "description": "Type of discount"}, "value": {"type": "number", "description": "Used with 'fixed', 'percentage'. (fraction when 'percentage', 1 implies 'free')"}, "currency": {"type": "string", "description": "ISO 4217 Currency Code"}, "amount": {"type": "number", "description": "Total discounts applied (positive number)"}, "code": {"type": "string", "max": 32, "description": "The alphanumeric code a user needs to redeem the offer"}, "external": {"type": "object", "description": "External ids of discount applied to Order"}, "use": {"type": "string", "enum": ["always", "alone", "one", "combine", "single"], "description": "If discount can be applied with other discounts"}, "priority": {"type": "number", "description": "Relative priority when applying discounts, higher number applied earlier (default = 5)"}, "excludeOffers": {"type": ["string"], "default": [], "description": "Cannot be applied with these offers onto same item"}, "qualifiers": {"type": {"redeem": {"type": "string"}, "prerequisite": {"type": "string"}, "entitled": {"type": "string"}}, "default": {}, "description": "For in-App checkout"}, "targetType": {"type": "string", "enum": ["item", "shipping"], "default": "item", "description": "item: applies to line items, shipping: applies to shipping lines"}, "targetSelection": {"type": "string", "enum": ["all", "entitled"], "default": "all", "description": "Items selected for discount, default: all"}, "allocation": {"type": {"method": {"type": "string", "enum": ["each", "across"], "description": "Allocation method - each: discount applied to each entitled items, across: a calculated discount amount applied across all entitled items"}, "limit": {"type": "number", "default": 1, "description": "Number of times the discount can be allocated on the order. Null => unlimited"}}, "default": {}}, "entitled": {"type": {"prerequisiteQuantity": {"type": "number"}, "entitledQuantity": {"type": "number"}, "items": {"type": [{"type": {"variantId": {"type": "string"}, "title": {"type": "string"}, "unitPrice": {"type": "number"}, "price": {"type": "number", "description": "Nett price to pay, ie. (unitPrice x quantity) + discounts"}, "images": {"type": [{"type": "string"}], "description": "List of image uri, display first by default"}, "tags": {"type": [{"type": "string"}]}, "options": {"type": [{"type": {"name": {"type": "string"}, "required": {"type": "boolean"}, "multiple": {"type": "number"}, "values": {"type": "array", "description": "[{ variantId: '', name: 'Blue', price: 10 }]"}}}]}, "taxes": {"type": [{"type": {"title": {"type": "string"}, "rate": {"type": "number"}, "price": {"type": "number"}}}]}}}]}}, "description": "For in-App checkout, used in buyxgety", "default": {}}, "immutable": {"type": "boolean", "description": "Discount cannot be changed / removed once applied."}}, "acls": [], "methods": {}}