{"name": "Geometry", "plural": "Geometries", "description": "based on GeoJSON RFC-7946", "base": "Model", "idInjection": false, "strict": true, "options": {}, "mixins": {}, "properties": {"id": false, "type": {"type": "String", "default": "Point", "enum": ["Point", "MultiPoint", "LineString", "MultiLineString", "Polygon", "MultiPolygon", "GeometryCollection"]}, "coordinates": {"type": "array", "description": "[ longitude, latitude ] pair (Point), or array of Points"}}, "validations": [], "relations": {}, "acls": [], "indexes": {}, "scopes": {}, "methods": []}