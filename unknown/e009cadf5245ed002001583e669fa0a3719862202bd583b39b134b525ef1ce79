/**
 *  @module User	middleware
 *  Setup user, role, role-mapping from user of context
 */

const { Context } = require('@perkd/multitenant-context'),
	{ isEmptyObj, ObjectId } = require('@perkd/utils')

const FAKEPASS = 'fake'

module.exports = function() {

	return async function saveUserRelated(req, res, next) {
		const { app } = req

		let user = Context.user || {}

		if (typeof user === 'string') {
			user = JSON.parse(user)
			Context.user = user
		}

		if (!isEmptyObj(user)) {
			user.password = FAKEPASS

			await Promise.all([
				upsertUser(user),
				upsertRole(user.roles || []),
				upsertRoleMapping(user.roleMappings || []),
			])
				.catch(err => console.error('[saveUserRelated]', err))
		}

		next()

		// -----  Private Functions  -----

		function upsertUser(user = {}) {
			const Model = app.models.user,
				{ modelName } = Model,
				Collection = Model.getDataSource().connector.collection(modelName),
				ID = ObjectId(user.id)

			delete user.id
			return Collection.findOneAndUpdate(
				{ _id: ID },
				{ $setOnInsert: user },
				{ upsert: true }
			)
		}

		async function upsertRole(roles = []) {
			const Model = app.models.role,
				{ modelName } = Model,
				Collection = Model.getDataSource().connector.collection(modelName),
				updated = []

			for (const role of roles) {
				const ID = ObjectId(role.id)

				delete role.id

				updated.push(
					await Collection.findOneAndUpdate(
						{ _id: ID },
						{ $setOnInsert: role },
						{ upsert: true }
					)
				)
			}

			return updated
		}

		// @@ TODO   need to fix this
		async function upsertRoleMapping(roleMappings = []) {
			const Model = app.models.roleMapping,
				{ modelName } = Model,
				Collection = Model.getDataSource().connector.collection(modelName),
				updated = []

			for (const roleMap of roleMappings) {
				const ID = ObjectId(roleMap.id)

				delete roleMap.id

				updated.push(
					await Collection.findOneAndUpdate(
						{ _id: ID },
						{ $setOnInsert: roleMap },
						{ upsert: true }
					)
				)
			}

			return updated
		}
	}
}
