{"name": "Permission", "plural": "Permissions", "base": "Model", "idInjection": false, "strict": false, "mixins": {}, "options": {}, "properties": {"channel": {"type": "String", "id": true, "max": 32, "enum": ["serviceTerms", "privacyPolicy", "mobile", "email", "postal", "voice", "push", "location", "locationAlways", "camera", "photos", "contacts", "calendar", "homeKit", "health", "speechRecognition", "bleCentral", "ble<PERSON><PERSON><PERSON><PERSON>", "microphone", "motionFitness"], "description": "Type of permission, includes device feature"}, "status": {"type": "Number", "default": 0, "enum": [-2, "-1", "0", "1"], "description": "0: unknown, 1: opt-in, -1: opt-out, -2: do not disturb"}, "grantedAt": {"type": "Date"}, "revokedAt": {"type": "Date"}}, "validations": [], "relations": {}, "acls": [], "indexes": {}, "scopes": {}, "methods": {}}