{"name": "Facebook", "plural": "Facebook", "base": "PersistedModel", "idInjection": true, "strict": false, "mixins": {"MultitenantRemote": true}, "options": {}, "properties": {"id": {"type": "string", "id": true, "generated": true}, "name": {"type": "string", "required": true}}, "validations": [], "acls": [], "indexes": {}, "methods": {"sendMessage": {"description": "Send message to Facebook user", "http": {"path": "/sendmessage", "verb": "post"}, "accepts": [{"arg": "userId", "type": "string", "required": true}, {"arg": "message", "type": "string", "required": true}], "returns": {"type": "object", "root": true}}}, "scopes": {}}