/**
 * EventbusLib
 */

const redis = require('redis'),
	EventEmitter = require('events'),
	uniqid = require('uniqid');

class eventbus extends EventEmitter {
	constructor(settings = {}) {
		super();
		this.host = settings.host || '127.0.0.1';
		this.tenant = settings.tenant || 'dev';
		this._pub = redis.createClient(6379, this.host);
		this._sub = redis.createClient(6379, this.host);
		this._ready = 0;

		this._pub.on('error', err => this.emit('error', err));
		this._sub.on('error', err => this.emit('error', err));

		this._pub.on('ready', () => {
			if (++this._ready > 1) this.emit('ready');
		});
		this._sub.on('ready', () => {
			this._sub.on('pmessage', (pattern, channel, message) => this.emit('event', message));
			this._sub.on('message', (channel, message) => this.emit('event', message));
			if (++this._ready > 1) this.emit('ready');
		});
	}

	publish(name, data) {
		const parts = name.split('.'),
			[domain = '', actor = '', ...rest] = parts,
			channel = this.tenant + '.' + name,
			id = uniqid(), // event uuid
			tenantCode = this.tenant,
			published = Date.now(),
			action = rest.join('.'),
			message = JSON.stringify({ id, domain, actor, action, name, data, tenantCode, published });

		this._pub.publish(channel, message);
	}

	subscribe(pattern = '*') {
		const pat = this.tenant ? this.tenant + '.' + pattern : pattern;
		(pattern.indexOf('*') === -1) ? this._sub.subscribe(pat) : this._sub.psubscribe(pat);
	}
}

module.exports = exports = eventbus;

/**
 * End script
 */
