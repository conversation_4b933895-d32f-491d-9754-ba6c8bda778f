/**
 *  @module Mixin:Tag
 */

const USER = 'user'

module.exports = function(Model) {

	/**
	 * Add tags to model instance in atomic & sequential manner
	 * @param	{Array} tags
	 * @param	{String} kind user/system/category/keyword/list
	 * @param	{Object} options
	 * @return	{Promise<Array>}
	 */
	Model.prototype.addTag = async function(tags, kind = USER, options) {
		const key = `tags.${kind}`,
			current = (this.tags?.toJSON() || {})[`${kind}`] || [],
			result = cleanse([ ...current ], tags),
			newTags = result.filter(t => !current.includes(t))

		if (current.length !== result.length) {
			await this.updateDocument({
				$addToSet: { [key]: { $each: newTags } }
			}, options)
		}

		return result
	}

	/**
	 * Remove tags from model instance in atomic & sequential manner
	 * @param	{Array} tags
	 * @param	{String} kind user/system/category/keyword/list
	 * @param	{Object} options
	 * @return	{Promise<Array>}
	 */
	Model.prototype.removeTag = async function(tags, kind = USER, options) {
		const key = `tags.${kind}`,
			current = (this.tags?.toJSON() || {})[`${kind}`] || [],
			oldTags = cleanse([], tags),
			result = current.filter(t => !oldTags.includes(t))

		if (current.length !== result.length) {
			await this.updateDocument({
				$pull: { [key]: { $in: oldTags } }
			}, options)
		}

		return result
	}

	/**
	 * Exclude invalids, Trim & De-dup tags
	 * @param	{Array} source
	 * @param	{String} tags to add/remove
	 * @return	{Promise<Array>}
	 */
	function cleanse(source = [], tags = []) {
		for (const tag of tags) {
			if (!tag) continue

			const isString = typeof tag === 'string',
				isObject = typeof tag === 'object',
				cleansed = (isString ? tag : (isObject ? JSON.stringify(tag) : tag.toString())).trim()

			if (cleansed && !source.includes(cleansed)) source.push(cleansed)
		}
		return source
	}

	// ---  Remote & Operation Hooks  ---

	Model.remoteMethod('prototype.addTag', {
		description: 'Add tags to model instance',
		http: { path: '/tags/add', verb: 'post' },
		accepts: [
			{ arg: 'tags', type: 'array', required: true },
			{ arg: 'kind', type: 'string', default: 'user' },
			{ arg: 'options', type: 'object' },
		],
		returns: { type: 'array', root: true },
	})

	Model.remoteMethod('prototype.removeTag', {
		description: 'Remove tags from model instance',
		http: { path: '/tags/remove', verb: 'post' },
		accepts: [
			{ arg: 'tags', type: 'array', required: true },
			{ arg: 'kind', type: 'string', default: 'user' },
			{ arg: 'options', type: 'object' },
		],
		returns: { type: 'array', root: true },
	})
}
