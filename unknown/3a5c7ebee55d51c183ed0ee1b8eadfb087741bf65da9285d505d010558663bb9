/**
 * @module Mixin:Commit
 */

const { pickObj } = require('@perkd/utils')

module.exports = function(Model) {

	// attach 'uncommitted' relations
	Model.referencesMany(Model, {
		as: 'uncommitted',
		property: 'uncommittedId',
	})

	// -----  Instance Methods  -----

	Model.prototype.__create__uncommitted = async function(data) {
		const uncommitted = {
				...pickObj(this.toJSON(), Model.COMMIT_PROPERTIES),
				...data,
				state: 'uncommit',
				visible: false,
			},
			created = await this.uncommitted.create(uncommitted)

		await this.updateAttributes({ committed: false })
		return created
	}

	Model.prototype.commit = async function() {
		const [ uncommittedId ] = this[Model.relations.uncommitted.keyFrom] // current support 1 working copy

		if (!uncommittedId) return this

		const res = await this.applyCommit(uncommittedId)
		await this.uncommitted.destroy(uncommittedId)
		return res || this
	}

	// -----  Remote Methods  -----
	Model.remoteMethod('prototype.commit', {
		http: { path: '/commit', verb: 'post' },
		description: 'Commit changes to an instance.',
		returns: { type: 'object', root: true },
	})

	// ---  Unused Remote Methods  ---
	Model.disableRemoteMethodByName('prototype.__findById__uncommitted')
	Model.disableRemoteMethodByName('prototype.__count__uncommitted')
	Model.disableRemoteMethodByName('prototype.__updateById__uncommitted')
	Model.disableRemoteMethodByName('prototype.__delete__uncommitted')
	Model.disableRemoteMethodByName('prototype.__exists__uncommitted')
	Model.disableRemoteMethodByName('prototype.__link__uncommitted')
	Model.disableRemoteMethodByName('prototype.__unlink__uncommitted')
}
