/**
 * @module Model:Variable
 * <AUTHOR>
 */

const _ = require('lodash');

module.exports = function(Variable) {
	Variable.observe('before save', (ctx, next) => {
		if (ctx.instance) {
			const namespace = ctx.instance.__namespace || Variable.app.service.domain || '';
			const timeNow = new Date();
			ctx.instance.id = namespace ? namespace + '.' + ctx.instance.id : ctx.instance.id;
			ctx.instance.created = ctx.instance.modified = timeNow;
			ctx.instance.__namespace && (delete ctx.instance.__namespace);
		} else {
			ctx.data.modified = new Date();
			ctx.data.__namespace && (delete ctx.data.__namespace);
		}
		next();
	});

	/*
	 * namespace: optional
	 */
	Variable.merge = function(keyName, object, namespace, cb) {
		if ('function' === typeof namespace) {
			cb = namespace;
			namespace = null;
		}
		Variable.get(keyName, namespace, instance => {
			if (instance) {
				_.extend(instance.value || {}, object || {});
				instance.updateAttributes({
					value: instance.value,
				}, (err, instanceUpdated) => {
					cb && cb(err, instanceUpdated);
				});
			} else {
				Variable.set(keyName, object, namespace, cb);
			}
		});
	};

	/*
	 * namespace: optional
	 */
	Variable.set = function(keyName, value, namespace, cb) {
		if ('function' === typeof namespace) {
			cb = namespace;
			namespace = null;
		}
		Variable.create({
			id: keyName,
			value,
			__namespace: namespace,
		}, (err, instance) => {
			cb && cb(err, instance);
		});
	};

	/* Usage:
		1. getValue(keyName, cb);
		2. getValue(keyName, namespace, cb);
	*/
	Variable.getValue = function(keyName, namespace, cb) {
		if ('function' === typeof namespace) {
			cb = namespace;
			namespace = null;
		}
		Variable.get(keyName, namespace, instance => {
			if (instance) {
				cb(instance.value);
			} else {
				cb(null);
			}
		});
	};

	/* Usage:
		1. get(keyName, cb);
		2. get(keyName, domain, cb);
	*/
	Variable.get = function(keyName, namespace, cb) {
		if ('function' === typeof namespace) {
			cb = namespace;
			namespace = null;
		}
		namespace = namespace || Variable.app.service.domain || ''; // default namespace is domain name - ZJ
		keyName = namespace ? namespace + '.' + keyName : keyName;

		Variable.findById(keyName, (err, instance) => {
			if (!err && instance && instance.id) { // shoule be null, but don't know why {}, so use .id to check - ZJ
				cb(instance);
			} else {
				cb(null);
			}
		});
	};
};
