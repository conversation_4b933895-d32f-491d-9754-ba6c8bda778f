/**
 *  @module Mixin:Setting - used by Business & Place
 */
const { EmbedLib } = appRequire('lib/common/embedLibrary')

module.exports = function(Model, options) {

	const settingsLib = new EmbedLib(Model, 'settings', options)
	settingsLib.setup()

	Model.prototype._setting = function(name) {
		const { settingList = [] } = this,
			NAME = name.toLowerCase(),
			{ value } = settingList.find(setting => (setting.name === NAME)) ?? {}

		return value
	}

	Model.prototype.getSettingsByName = function(name) {
		return this._setting(name)
	}

	Model.prototype.updateSetting = async function(name, setting) {
		const NAME = name.toLowerCase()

		await this.removeSetting(NAME)
		const { value } = await this.settings.create(setting)
		return value
	}

	Model.prototype.removeSetting = async function(name) {
		const NAME = name.toLowerCase(),
			settings = this.settings.value().filter(s => s.name === NAME)

		for (const setting of settings) {
			await setting.destroy()
		}
	}

	// -----  Remote Methods  -----

	Model.remoteMethod('prototype.updateSetting', {
		description: 'Update a setting by name',
		http: { path: '/settings/:name', verb: 'post' },
		accepts: [
			{ arg: 'name', type: 'string', http: { source: 'path' }, required: true },
			{ arg: 'setting', type: 'object', http: { source: 'body' }, required: true }
		],
		returns: { type: 'object', root: true },
	})

	Model.remoteMethod('prototype.removeSetting', {
		description: 'Remove a setting by name',
		http: { path: '/settings/:name', verb: 'delete' },
		accepts: [
			{ arg: 'name', type: 'string', http: { source: 'path' }, required: true }
		],
		returns: { type: 'object', root: true },
	})
}
