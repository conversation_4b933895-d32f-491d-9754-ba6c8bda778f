/**
 * @module Mixin:Queue
 * 			reference:	https://www.npmjs.com/package/async-lock
 * 						https://www.npmjs.com/package/redlock
 */

const AsyncLock = require('async-lock'),
	{ default: Redlock } = require('redlock'),
	{ Client } = require('@perkd/redis'),
	{ Context } = require('@perkd/multitenant-context')

// ENVIRONMENT
const {
	REDLOCK_HOST,
	REDLOCK_PORT,
	REDLOCK_USERNAME,
	REDLOCK_PASSWORD
} = process.env

const CONFIG = {
		redis: {
			host: REDLOCK_HOST,
			port: Number(REDLOCK_PORT),
			username: REDLOCK_USERNAME,
			password: REDLOCK_PASSWORD
		},
	},
	SETTINGS = {
		retryCount: 25,
		retryDelay: 300,
		retryJitter: 200,
		automaticExtensionThreshold: 0
	},
	DURATION = 25000,
	OPTIONS = {
		maxOccupationTime: 180000,		// 3 mins, prevent starvation if earlier job don't complete
	}

const redis = new Client(CONFIG),
	redlock = new Redlock([ redis ], SETTINGS),
	Locks = {}

module.exports = function(Model) {

	/**
	 * Model-level lock (tenanted)
	 * @param {String} key - resource to lock: ${Model.name}-${key}
	 * @param {Function} fn - task
	 * @param {Object} [options] - override default options
	 * @param {Function} [sharedLock] - inject (shared, cross-tenants) async-lock instance
	 * @return {Promise}
	 */
	Model.queue = async function(key, fn, options, sharedLock) {
		return doLock(`${Model.name}-${key}`, fn, options, sharedLock)
	}

	Model.queue2 = async function (key, fn) {
		return doRedLock(`${Model.name}-${key}`, fn)
	}

	/**
	 * Model Instance-level lock (tenanted)
	 * @param {String} key - resource to lock: ${Model.name}-${id}-${key}
	 * @param {Function} fn - task
	 * @param {Object} [options] - override default options
	 * @param {Function} [sharedLock] - inject (shared, cross-tenants) async-lock instance
	 * @return {Promise}
	 */
	Model.prototype.queue = async function(key, fn, options, sharedLock) {
		return doLock(`${Model.name}-${this.id}-${key}`, fn, options, sharedLock)
	}

	Model.prototype.queue2 = async function (key, fn) {
		return doRedLock(`${Model.name}-${this.id}-${key}`, fn)
	}

	// -------- Private function --------

	async function doLock(key, fn, options = OPTIONS, sharedLock) {
		const { tenant } = Context,
			{ connectionManager } = Model.app,
			lock = sharedLock || (Locks[tenant] ||= new AsyncLock(options))

		return lock.acquire(key, () => Context.runAsTenant(tenant, fn, connectionManager))
			.catch(err => {
				if (err?.message?.startsWith('Maximum occupation time is exceeded')) {
					throw { statusCode: 504, message: err.message }
				}
				throw err
			})
	}

	async function doRedLock(key, fn) {
		const { tenant } = Context,
			{ connectionManager } = Model.app,
			KEY = `lock:${tenant}:${key}`

		return redlock.using([ KEY ], DURATION, async signal => Context.runAsTenant(tenant, fn, connectionManager))
	}
}
