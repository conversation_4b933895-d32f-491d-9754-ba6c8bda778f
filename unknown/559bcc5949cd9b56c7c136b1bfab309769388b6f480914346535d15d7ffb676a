{"name": "OpeningHour", "plural": "OpeningHours", "description": "based on Google Places API", "base": "Model", "idInjection": false, "strict": true, "mixins": {}, "options": {}, "properties": {"id": false, "specific": [{"date": {"type": {"year": {"type": "Number", "description": "Year of date, 1-9999, or 0 if specifying a date without a year."}, "month": {"type": "Number", "description": "Month of year, 1-12, or 0 if specifying a year without a month and day."}, "day": {"type": "Number", "description": "Day of month, 1-31 and valid for the year and month, or 0 if specifying a year by itself or a year and month where the day is not significant."}}}, "periods": [{"open": {"day": {"type": "Number", "description": "Day of week, 1-7, where 7 represents Sunday"}, "time": {"type": "String", "description": "24hr ISO 8601 basic format (hhmm), 0000-2400, where 2400 represents midnight at the end of the specified day field."}}, "close": {"day": {"type": "Number", "description": "Day of week, 1-7, where 7 represents Sunday"}, "time": {"type": "String", "description": "24hr ISO 8601 basic format (hhmm), 0000-2400, where 2400 represents midnight at the end of the specified day field."}}, "busy": [{"start": {"type": "String", "description": "24hr ISO 8601 basic format (hhmm), 0000-2400, must be between open.time - close.time"}, "end": {"type": "String", "description": "24hr ISO 8601 basic format (hhmm), 0000-2400, must be between open.time - close.time"}}]}]}], "periods": [{"open": {"day": {"type": "Number", "description": "Day of week, 1-7, where 7 represents Sunday"}, "time": {"type": "String", "description": "24hr ISO 8601 basic format (hhmm), 0000-2400, where 2400 represents midnight at the end of the specified day field."}}, "close": {"day": {"type": "Number", "description": "Day of week, 1-7, where 7 represents Sunday"}, "time": {"type": "String", "description": "24hr ISO 8601 basic format (hhmm), 0000-2400, where 2400 represents midnight at the end of the specified day field."}}, "busy": [{"start": {"type": "String", "description": "24hr ISO 8601 basic format (hhmm), 0000-2400, must be between open.time - close.time"}, "end": {"type": "String", "description": "24hr ISO 8601 basic format (hhmm), 0000-2400, must be between open.time - close.time"}}]}]}, "validations": [], "relations": {}, "acls": [], "indexes": {}, "scopes": {}, "methods": []}