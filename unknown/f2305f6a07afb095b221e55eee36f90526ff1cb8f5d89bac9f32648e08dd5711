{"name": "RemoteEvent", "plural": "RemoteEvents", "base": "PersistedModel", "strict": true, "options": {"validateUpsert": true}, "mixins": {"MultitenantRemote": true}, "properties": {"id": {"type": "string", "id": true, "defaultFn": "shortid"}, "name": {"type": "string", "length": 80}, "event": {"type": "string"}, "callback": {"type": {"method": {"type": "String", "enum": ["get", "post"], "description": "Http method name"}, "url": {"type": "string"}, "payload": {"type": "object"}, "rate": {"type": {"limit": {"type": "number", "default": 50}, "interval": {"type": "number", "default": 1000, "description": "in milliseconds"}}}}}, "mandatoryFields": {"type": "array"}, "fieldMap": {"type": "object"}, "fieldMap2": {"type": "object"}, "active": {"type": "boolean", "default": true}, "endPoint": {"type": "string", "description": "Injected & returned post CREATE, not persisted"}, "debug": {"type": "boolean", "default": false}, "createdAt": {"type": "date"}, "modifiedAt": {"type": "date"}}, "hidden": [], "validations": [], "relations": {}, "acls": [], "methods": {"prototype.request": {"description": "Endpoint (id) for remote request", "http": {"path": "/request", "verb": "post"}, "accepts": [{"arg": "param", "type": "object", "http": {"source": "body"}, "required": true}], "returns": {"type": "object", "root": true}}, "prototype.endPoint": {"description": "Returns URL of endpoint", "http": {"path": "/endpoint", "verb": "get"}, "returns": {"type": "object", "root": true}}}}