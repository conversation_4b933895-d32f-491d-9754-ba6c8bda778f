{"name": "Context", "plural": "Contexts", "base": "PersistedModel", "idInjection": true, "strict": true, "options": {"validateUpsert": true}, "mixins": {"Common": true, "Timestamp": true, "DisableAllRemotes": {"create": true, "prototype.patchAttributes": true, "find": true, "findById": true, "findOne": true, "count": true}}, "properties": {"id": {"type": "string", "id": true, "generated": true}, "createdAt": {"type": "date"}, "modifiedAt": {"type": "date"}}, "validations": [], "relations": {"person": {"type": "belongsTo", "model": "Person", "foreignKey": "personId"}}, "acls": [], "indexes": {"personId_type": {"keys": {"personId": 1, "type": 1}}}, "scopes": {}, "methods": {}}