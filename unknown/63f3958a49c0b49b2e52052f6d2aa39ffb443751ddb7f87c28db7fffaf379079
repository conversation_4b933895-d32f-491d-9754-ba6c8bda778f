{"name": "Campaign", "plural": "Campaigns", "base": "PersistedModel", "idInjection": true, "strict": false, "mixins": {"MultitenantRemote": true}, "options": {}, "properties": {"id": {"type": "String", "id": true, "generated": true}}, "relations": {}, "validations": [], "acls": [], "indexes": {}, "scopes": {}, "methods": {"provisionTemplates": {"description": "Create preset templates", "http": {"path": "/provision/templates", "verb": "post"}, "accepts": [{"arg": "templates", "type": []}], "returns": {"type": "object", "root": true}}, "prototype.schedule": {"description": "Set start of campaign", "http": {"path": "/schedule", "verb": "post"}, "accepts": [{"arg": "when", "type": "string"}, {"arg": "param", "type": "object"}], "returns": {"type": "object", "root": true}}, "prototype.start": {"description": "Initiate campaign execution", "http": {"path": "/start", "verb": "post"}, "accepts": [{"arg": "param", "type": "object", "http": {"source": "body"}}], "returns": {"type": "object", "root": true}}, "prototype.pause": {"description": "Pause campaign execution", "http": {"path": "/pause", "verb": "post"}, "accepts": [{"arg": "param", "type": "object", "http": {"source": "body"}}], "returns": {"type": "object", "root": true}}, "prototype.resume": {"description": "Resume campaign execution", "http": {"path": "/resume", "verb": "post"}, "accepts": [{"arg": "param", "type": "object", "http": {"source": "body"}}], "returns": {"type": "object", "root": true}}, "prototype.stop": {"description": "Stop campaign execution", "http": {"path": "/stop", "verb": "post"}, "accepts": [{"arg": "param", "type": "object", "http": {"source": "body"}}], "returns": {"type": "object", "root": true}}, "prototype.restore": {"description": "Restore campaign execution", "http": {"path": "/restore", "verb": "post"}, "accepts": [{"arg": "param", "type": "object", "http": {"source": "body"}}], "returns": {"type": "object", "root": true}}, "prototype.reset": {"description": "Reset campaign for reuse", "http": {"path": "/reset", "verb": "post"}, "accepts": [{"arg": "param", "type": "object", "http": {"source": "body"}}], "returns": {"type": "object", "root": true}}}}