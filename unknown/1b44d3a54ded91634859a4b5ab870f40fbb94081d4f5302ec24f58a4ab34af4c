/**
 *  @module Mixin:Group
 */

module.exports = function(Model, options) {
	const IDMODEL = '_group'

	// -----  Static Methods  -----

	Model.addGroup = function(groupId, gIdList) {
		const filter = {
			where: { _gId: { inq: gIdList } },
			fields: { _gId: true, groupIds: true, id: true },
		}

		return Model.find(filter).then(instances => {
			const updates = instances.reduce((res, instance) => {
				const { groupIds } = instance

				if (groupIds.indexOf(groupId) < 0) {
					groupIds.push(groupId)
					res.push(
						instance.updateAttributes({ groupIds })
					)
				}
				return res
			}, [])

			return Promise.all(updates)
		})
	}

	Model.removeGroup = function(groupId, gIdList) {
		const filter = {
			where: { _gId: { inq: gIdList } },
			fields: { _gId: true, groupIds: true, id: true },
		}

		return Model.find(filter).then(instances => {
			const updates = instances.reduce((res, instance) => {
				const { groupIds } = instance,
					pos = groupIds.indexOf(groupId)

				if (pos > -1) {
					groupIds.splice(pos, 1)
					res.push(
						instance.updateAttributes({ groupIds })
					)
				}
				return res
			}, [])

			return Promise.all(updates)
		})
	}

	// -----  Instance Methods  -----

	Model.prototype.addToGroup = function(groupId) {
		const self = this

		return self.groups.findById(groupId).then(group =>
			group ? self : self.groups.add(groupId).then(grp => self))
	}

	Model.prototype.removeFromGroup = function(groupId) {
		const self = this

		return self.groups.findById(groupId).then(group =>
			group ? self.groups.remove(groupId).then(grp => self) : self)
	}

	Model.prototype.removeFromAllGroups = function() {
		const self = this

		return self.groups().then(groups => {
			const updates = groups.map(group => group.removeId([ self._gId ]))
			return Promise.all(updates)
		})
	}

	// --- Private functions ---

	function getId(Model, cb) {
		getCollection(Model, (err, collection) => {
			if (!err && collection) {
				collection.findOneAndUpdate(
					{ _id: Model.modelName },
					{ $inc: { next: 1 } },
					{ new: true },
					(err, res) => {
						if (!err) cb(null, res.value.next)
						else cb(err)
					}
				)
			}
			else cb(err, -1)	// flag error in gId
		})
	}

	function getCollection(Model, cb) {
		let groupID = Model.app.models[IDMODEL]
		const ds = Model.getDataSource()			// use same ds as host Model
		if (groupID) {				// model exists
			cb(null, ds.connector.collection(IDMODEL))
		}
		else {			// create model on-the-fly, everytime service restarts
			groupID = ds.define(IDMODEL, {
				id: { type: String, id: true },
				next: { type: Number },
			})

			groupID.findById(Model.modelName, (err, found) => {
				if (found) cb(null, ds.connector.collection(IDMODEL))
				else {
					groupID.create({ id: Model.modelName, next: 0 }, (err, res) => {	// initialize sequence #
						cb(err, ds.connector.collection(IDMODEL))
					})
				}
			})
		}
	}

	// -----  Operation hooks  -----

	Model.observe('persist', ({ isNewInstance, data }, next) => {
		if (isNewInstance) {
			getId(Model, (err, gId) => {
				data._gId = gId
				next()
			})
		}
		else next()
	})

	Model.observe('before delete', ({ instance, where }, next) => {
		if (!instance) {
			Model.findOne({ where }, (err, found) => {
				if (found && found.groupIds && found.groupIds.length > 0) {
					console.log('remove _gId (%s) from: ', found._gId, found.groupIds)
					found.removeFromAllGroups(next)
				}
				else next()
			})
		}
		else {
			console.log('remove _gId (%s) from: ', instance._gId, instance.groupIds)
			next()
		}
	})

	// -----  Remote Methods  -----

	Model.remoteMethod('addGroup', {
		description: 'Add group to list of objects (gId)',
		http: { path: '/addGroup', verb: 'post' },
		accepts: [
			{ arg: 'groupId', type: 'string', required: true },
			{ arg: 'gIdList', type: 'array', required: true },
		],
		returns: { type: 'object', root: true },
	})

	Model.remoteMethod('removeGroup', {
		description: 'Remove group from list of objects (gId)',
		http: { path: '/removeGroup', verb: 'post' },
		accepts: [
			{ arg: 'groupId', type: 'string', required: true },
			{ arg: 'gIdList', type: 'array', required: true },
		],
		returns: { type: 'object', root: true },
	})
}

/**
 * End of script
 */
