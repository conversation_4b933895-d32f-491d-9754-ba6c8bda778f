/**
 * @module Mixin:Address
 */

const { Contacts } = require('@crm/types'),
	{ EmbedLib } = appRequire('lib/common/embedLibrary')

const { WORK, HOME } = Contacts.Type

module.exports = function(Model, options) {

	Model.TYPE = [ WOR<PERSON>, HOME ]

	const addrLib = new EmbedLib(Model, 'addresses', options)
	addrLib.setup()

	Model.prototype._address = function(address) {
		return this.addresses.value().find(addr =>
			(address.id)
				? (addr.id === address.id)
				: (Model.TYPE.indexOf(address.type) >= 0 && addr.type === address.type))
	}

	Model.prototype._addressType = function(type) {
		return this.addresses.value().find(address => address.type === type)
	}

	Model.prototype.optInAddress = function(type) {
		const address = this._addressType(type)
		return address ? address.optIn : false
	}
}
