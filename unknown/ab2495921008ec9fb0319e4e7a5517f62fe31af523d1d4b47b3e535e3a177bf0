{"name": "Messaging", "plural": "Messaging", "description": "CRM", "base": "PersistedModel", "idInjection": true, "strict": true, "options": {}, "mixins": {"MultitenantRemote": true}, "properties": {"id": {"type": "String", "id": true, "generated": true}}, "validations": [], "relations": {}, "acls": [], "scopes": {}, "indexes": {}, "methods": {"sendSMS": {"description": "Send a SMS", "http": {"path": "/sms/send", "verb": "post"}, "accepts": [{"arg": "text", "type": "string", "required": true}, {"arg": "to", "type": "object", "required": true, "description": "Recipient mobile number: { countryCode: '', number: '' }"}, {"arg": "options", "type": "object"}], "returns": {"type": "object", "root": true}}, "sendEmail": {"description": "Send an Email", "http": {"path": "/email/send", "verb": "post"}, "accepts": [{"arg": "subject", "type": "string", "required": true}, {"arg": "body", "type": "object", "required": true, "description": "{ html: '', text: '' }"}, {"arg": "to", "type": "object", "required": true, "description": "{ email: '', name: '' }"}, {"arg": "from", "type": "object", "required": true, "description": "{ email: '', name: '' }"}, {"arg": "options", "type": "object"}], "returns": {"type": "object", "root": true}}, "sendPush": {"description": "Send a Push Notification", "http": {"path": "/push/send", "verb": "post"}, "accepts": [{"arg": "content", "type": "object", "required": true, "description": "Notification content"}, {"arg": "installation", "type": "object", "required": true}, {"arg": "options", "type": "object"}], "returns": {"type": "object", "root": true}}, "sendData": {"description": "Send data (silent push)", "http": {"path": "/data/send", "verb": "post"}, "accepts": [{"arg": "data", "type": "object", "required": true, "description": "Data payload"}, {"arg": "installation", "type": "object", "required": true}, {"arg": "options", "type": "object"}], "returns": {"type": "object", "root": true}}, "sendRich": {"description": "Send a Perkd Message", "http": {"path": "/rich/send", "verb": "post"}, "accepts": [{"arg": "content", "type": "object", "required": true, "description": "Message Template Id & customized notification"}, {"arg": "target", "type": "object", "required": true, "description": "{ cardId, personalize: {} }"}, {"arg": "options", "type": "object"}], "returns": {"type": "object", "root": true}}, "sendNotify": {"description": "Send a Notify (CRM)", "http": {"path": "/notify/send", "verb": "post"}, "accepts": [{"arg": "content", "type": "object", "required": true, "description": "{en: { title, body, image }, 'zh-Hant-TW': { title, body, image }}"}, {"arg": "target", "type": "object", "required": true, "description": "{to:'cardId', objects?:{offer:{},reward:{}}}"}, {"arg": "options", "type": "object"}], "returns": {"type": "object", "root": true}}, "callVoice": {"description": "Make an outbound voice call & speak text", "http": {"path": "/voice/call", "verb": "post"}, "accepts": [{"arg": "text", "type": "string", "required": true}, {"arg": "to", "type": "object", "required": true, "description": "Recipient phone number: { countryCode: '', number: '' }"}, {"arg": "language", "type": "string", "description": "ISO 639-1 + ISO 15924, eg. zh-Hans"}, {"arg": "options", "type": "object"}], "returns": {"type": "object", "root": true}}, "sendPushToCard": {"description": "Send Notification to a card", "http": {"path": "/push/send/card/:cardId", "verb": "post"}, "accepts": [{"arg": "cardId", "type": "any", "http": {"source": "path"}, "required": true}, {"arg": "content", "type": "object", "required": true, "description": "with translations  (eg. : { en: { body, image }, 'zh-Hant-TW': {} })"}, {"arg": "options", "type": "object"}]}, "spamCheckEmail": {"description": "Spam check score (1-10 most likely is spam)", "http": {"path": "/email/spam", "verb": "post"}, "accepts": [{"arg": "subject", "type": "string", "required": true}, {"arg": "body", "type": "object", "required": true, "description": "{ html: '', text: '' }"}, {"arg": "to", "type": "object", "required": true, "description": "{ email: '', name: '' }"}, {"arg": "from", "type": "object", "required": true, "description": "{ email: '', name: '' }"}, {"arg": "options", "type": "object"}], "returns": {"type": "object", "root": true}}, "sendWhatsapp": {"description": "Send a Whatsapp message (BETA sandbox!)", "http": {"path": "/whatsapp/send", "verb": "post"}, "accepts": [{"arg": "content", "type": "object", "required": true}, {"arg": "to", "type": "object", "required": true, "description": "Recipient mobile number"}, {"arg": "options", "type": "object"}], "returns": {"type": "object", "root": true}}, "prototype.add": {"description": "Add targets to be dispatched", "http": {"path": "/add", "verb": "post"}, "accepts": [{"arg": "targets", "type": "array", "required": true, "description": "[ { to: {}, personalize: {}, track: {} } ]"}, {"arg": "options", "type": "object", "required": false, "description": "{ language: '' }"}], "returns": {"type": "object", "root": true}}, "prototype.start": {"description": "Start dispatching", "http": {"path": "/start", "verb": "post"}, "accepts": [], "returns": {"type": "object", "root": true}}, "prototype.pause": {"description": "Pause sending dispatch", "http": {"path": "/pause", "verb": "post"}, "accepts": [], "returns": {"type": "object", "root": true}}, "prototype.resume": {"description": "Resume paused dispatch", "http": {"path": "/resume", "verb": "post"}, "accepts": [], "returns": {"type": "object", "root": true}}, "prototype.stop": {"description": "Stop paused dispatch", "http": {"path": "/stop", "verb": "post"}, "accepts": [], "returns": {"type": "object", "root": true}}, "sendSMSBulk": {"description": "Send a non-personalized message to list of recipients", "http": {"path": "/sms/send/bulk", "verb": "post"}, "accepts": [{"arg": "targets", "type": "array", "required": true, "description": "list of { to: { countryCode, number } }"}, {"arg": "text", "type": "string", "required": true, "description": "Non-personalized message"}, {"arg": "options", "type": "object"}], "returns": {"type": "object", "root": true}}, "sendEmailBulk": {"description": "Send a non-personalized email to list of recipients", "http": {"path": "/email/send/bulk", "verb": "post"}, "accepts": [{"arg": "targets", "type": "array", "required": true, "description": "list of { to: { email, name } }"}, {"arg": "content", "type": "object", "required": true, "description": "{ subject: '', html: '', text: '' }"}, {"arg": "options", "type": "object", "description": "{ from: { email, name }, replyTo: { email, name }}"}], "returns": {"type": "object", "root": true}}, "sendPushBulk": {"description": "Send notification to list of recipients", "http": {"path": "/push/send/bulk", "verb": "post"}, "accepts": [{"arg": "targets", "type": "array", "required": true}, {"arg": "content", "type": "object", "required": true, "description": "Notification content"}, {"arg": "options", "type": "object"}], "returns": {"type": "object", "root": true}}, "sendRichBulk": {"description": "Send Perkd Message to recipients", "http": {"path": "/rich/send/bulk", "verb": "post"}, "accepts": [{"arg": "content", "type": "object", "required": true, "description": "Message Template Id & customized notification"}, {"arg": "targets", "type": "array", "required": true, "description": "[ { cardId, personalize: {} } ]"}, {"arg": "options", "type": "object"}], "returns": {"type": "object", "root": true}}}}