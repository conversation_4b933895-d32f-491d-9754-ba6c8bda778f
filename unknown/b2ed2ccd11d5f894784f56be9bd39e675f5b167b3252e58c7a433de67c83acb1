/**
 * @module Mixin:DisableAllRemotes
 * Disables ALL built-in remote methods on a model EXCEPT those explicitly enabled in options
 */

const methodNames = [
	'create',
	'upsert',
	'updateAll',
	'prototype.patchAttributes',
	'find',
	'findById',
	'findOne',
	'findOrCreate',
	'createChangeStream',
	'changeStream',
	'deleteById',
	'confirm',
	'count',
	'exists',
	'replaceById',
	'replaceOrCreate',
	'upsertWithWhere',
]

/**
 * Disables all remote methods except those explicitly enabled in options
 * @param {Object} Model - The model to disable remote methods on
 * @param {Object} options - Configuration options
 * @param {Object.<string, boolean>} options.methods - Map of method names to enable/disable
 *                                                    true = keep enabled, false/undefined = disable
 */
module.exports = function(Model, options = {}) {
	const methods = { ...options }  // Simple spread is sufficient for shallow object
	const relations = Object.keys(Model.definition.settings.relations || {})

	// Build list of all remote methods
	const remoteMethodsList = [
		...methodNames,
		...relations.flatMap(relation => [
			`prototype.__findById__${relation}`,
			`prototype.__destroyById__${relation}`,
			`prototype.__updateById__${relation}`,
			`prototype.__exists__${relation}`,
			`prototype.__link__${relation}`,
			`prototype.__get__${relation}`,
			`prototype.__create__${relation}`,
			`prototype.__update__${relation}`,
			`prototype.__destroy__${relation}`,
			`prototype.__unlink__${relation}`,
			`prototype.__count__${relation}`,
			`prototype.__delete__${relation}`
		])
	]

	// Disable methods unless explicitly enabled in options
	remoteMethodsList.forEach(methodName => {
		try {
			if (!methods[methodName]) {
				Model.disableRemoteMethodByName(methodName)
			}
		}
		catch (err) {
			console.error(`Failed to disable remote method ${methodName}:`, err)
		}
	})

	// Disable any additional methods specified in options
	Object.keys(methods).forEach(methodName => {
		if (!remoteMethodsList.includes(methodName) && !methods[methodName]) {
			try {
				Model.disableRemoteMethodByName(methodName)
			}
			catch (err) {
				console.error(`Failed to disable remote method ${methodName}:`, err)
			}
		}
	})
}
