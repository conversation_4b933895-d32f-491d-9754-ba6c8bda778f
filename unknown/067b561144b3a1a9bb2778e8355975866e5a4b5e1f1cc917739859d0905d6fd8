{"name": "Level", "plural": "Levels", "base": "Model", "idInjection": false, "strict": true, "properties": {"level": {"type": "number"}, "description": {"type": "string", "max": 500, "description": "Quick details about the reward"}, "terms": {"type": "string", "description": "Longer details including terms and conditions"}, "startTime": {"type": "date", "description": "The date and time the level becomes valid for use"}, "endTime": {"type": "date", "description": "The date and time the level becomes disabled"}, "imageNdx": {"type": "number"}, "stamps": {"type": [{"type": {"name": {"type": "string", "max": 16}, "stamped": {"type": "boolean", "default": false}, "issuedAt": {"type": "date", "default": null, "description": "Time stamp was issued"}, "bgImageNdx": {"type": "number"}, "stampImageNdx": {"type": "number"}}}], "default": []}, "images": {"type": [{"type": {"url": {"type": "string"}}}], "description": "List of image URLs"}, "issuedAt": {"type": "date", "description": "The date and time the level was issued"}, "completedAt": {"type": "date", "default": null, "description": "Time level was completed"}, "fullyRedeemedAt": {"type": "date", "default": null, "description": "Time of offer was fully redeemed"}, "offerIds": {"type": [{"type": "string"}], "default": [], "description": "Id of Offers issued when level was completed"}}}