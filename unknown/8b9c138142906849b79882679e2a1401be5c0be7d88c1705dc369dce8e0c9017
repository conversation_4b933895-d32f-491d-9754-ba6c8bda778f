/**
 *  @module Mixin:BatchUpload - used by Places & Orders
 */

const { parse } = require('csv-parse'),
	{ Forms, Contents } = require('@crm/types'),
	{ splitIntoChunks } = require('@perkd/utils'),
	{ formData } = require('@perkd/forms')

const { FILE } = Forms.FileType,
	{ CSV } = Contents.MediaType,
	MAX_BULK = 100

module.exports = function(Model) {

	/**
	 * Upload .csv file
	 * @param	{Object} req
	 * @return	{Promise<Object>}
	 */
	Model.upload = async function(req) {
		const { body: fields, files } = await formData(req),
			{ file: stream, mimeType } = files[FILE],
			csvParser = parse(),
			file = []

		if (!stream) return this.rejectErr('file_missing')
		if (mimeType !== CSV) return this.rejectErr('must_be_csv')

		await new Promise((resolve, reject) => {
			stream.pipe(csvParser)
				.on('readable', () => {
					let record
					while ((record = csvParser.read())) file.push(record)
				})
				.on('error', reject)
				.on('end', resolve)
		})

		appNotify('upload', { total: file.length }, 'done')
		return Model.handleUpload({ fields, file })
	}

	// ----- Private Static Methods  -----

	if (!Model.handleUpload) {
		/**
		 * Bulk create instance using batch uploaded data
		 * @param	{Object} parsed parsed fields & csv file
		 {
				fields: { currency: "SGD" },
				file: [
					["orderId","cardNumber","receipt"],
					["ORDR-00001","C6500000001","CUST-00001"]
				]
		}
		* @return	{Promise<Object>} (ie. {"success": [], "fail":  [["orderId","cardNumber","receipt","reason"],["ORDR-00001","CUST-00001","Missing cardNumber"]] )
		*/
		Model.handleUpload = async function({ fields, file }) {
			const [ headers, ...uploads ] = file,
				// TODO: validate header
				chunks = splitIntoChunks(uploads, MAX_BULK),
				res = []

			for (const chunk of chunks) {
				const builds = []

				for (const row of chunk) {
					builds.push(
						Model.build(fields, headers, row)
					)
				}

				const list = await Promise.all(builds),
					instances = await Model.create(list)

				res.push(...instances)
			}

			return res		// FIXME return value correct?
		}
	}

	// -----  Remote Methods  -----

	Model.remoteMethod('upload', {
		description: `Upload ${Model.pluralModelName}.`,
		http: { path: '/upload', verb: 'post' },
		accepts: [
			{ arg: 'req', type: 'object', http: { source: 'req' } },
		],
		returns: { type: 'array', root: true },
	})
}
