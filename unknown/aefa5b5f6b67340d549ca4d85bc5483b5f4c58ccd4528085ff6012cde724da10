/**
 *  @module Mixin:Phone
 */
const { Contacts } = require('@crm/types'),
	{ EmbedLib } = appRequire('lib/common/embedLibrary')

const { MOBILE } = Contacts.Type

module.exports = function(Model, options) {

	const phoneLib = new EmbedLib(Model, 'phones', options)
	phoneLib.setup()

	Model.prototype._phone = function({ fullNumber }) {
		if (fullNumber && typeof fullNumber !== 'string') {
			fullNumber = fullNumber.toString()
		}
		return this.phones.value().find(phone => phone.fullNumber === fullNumber)
	}

	Model.prototype._phoneType = function(type) {
		return this.phones.value().find(phone => phone.type === type)
	}

	Model.prototype.optInPhone = function(fullNumber) {
		const phone = this._phone({ fullNumber })
		return phone ? phone.optIn : false
	}

	Model.prototype.replacePhone = async function(oldNumber, newNumber, options = {}) {
		const fullNumber = typeof newNumber !== 'object' ? { fullNumber: newNumber } : newNumber,
			newPhone = {
				type: MOBILE,
				...fullNumber
			},
			oldPhone = this._phone({ fullNumber: oldNumber }),
			{ through } = options

		await this.deletePhone(oldPhone, through, options)
		return Model.doUpsert({ id: this.id, phoneList: [ newPhone ] }, null, { through })
	}
}
