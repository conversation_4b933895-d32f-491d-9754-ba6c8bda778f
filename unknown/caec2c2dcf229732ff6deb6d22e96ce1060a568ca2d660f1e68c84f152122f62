/**
 *  @module errorResponse
 *
 *  Standard error response
 *
 *  NPM: https://github.com/demakrated/loopback-common-errors/blob/master/loopback-errors.js
 *
 *  List of HTTP status codes:
 *  https://en.wikipedia.org/wiki/List_of_HTTP_status_codes
 */

function ErrorResponse() {
	// empty
}

const ErrorDefinitions = {
	badRequest: { statusCode: 400, msg: 'Bad Request' },
	unauthorized: { statusCode: 401, msg: 'Unauthorized' },
	forbidden: { statusCode: 403, msg: 'Forbidden' },
	notFound: { statusCode: 404, msg: 'Not Found' },
	serverError: { statusCode: 500, msg: 'Internal Server Error' },
}

Object.keys(ErrorDefinitions).forEach(method => {
	ErrorResponse.prototype[method] = (function(msg, code = 0, details) {
		const err = new Error(this.msg)

		err.statusCode = this.statusCode
		err.code = code
		err.occurredAt = new Date()
		msg && (err.reason = msg)
		details && (err.details = details)

		if (method === 'serverError') appNotify('serverError', { err })

		return err
	}).bind(ErrorDefinitions[method])
})

module.exports = new ErrorResponse()	// Default instance
