{"digital": {"orderpaid": "Wohoo~ You've placed a new order 🎉", "orderfulfilled": "not in use", "ordercancelled": "❗️Your order has been cancelled", "fulfillcreated": "not in use", "fulfillcreatedpaid": "not in use", "fulfillrequested": "not in use", "fulfillprepare": "not in use", "fulfillpacked": "not in use"}, "store": {"orderpaid": "not in use", "orderfulfilled": "Your order has been collected, enjoy~", "ordercancelled": "❗️Your order has been cancelled", "fulfillcreated": "We received your order. Please make payment now", "fulfillcreatedpaid": "not in use", "fulfillrequested": "Your order expected to be ready at {{scheduledAt, datetime}}", "fulfillprepare": "Your order shall be ready in {{eta}} mins", "fulfillpacked": "Your order #{{queue}} is ready for collection"}, "deliver": {"orderpaid": "not in use", "orderfulfilled": "Your order #{{queue}} has arrived, enjoy~", "ordercancelled": "❗️Your order has been cancelled", "fulfillcreated": "We received your order. Please make payment now", "fulfillcreatedpaid": "Wohoo~ You've placed a new order 🎉", "fulfillrequested": "Your order scheduled to be delivered at {{scheduledAt, datetime}}", "fulfillprepare": "We expect to deliver your order in {{eta}} mins", "fulfillpacked": "not in use"}, "dinein": {"orderpaid": "not in use", "orderfulfilled": "Your order is completely served, enjoy 😋", "ordercancelled": "❗️Your order has been cancelled", "fulfillcreated": "We received your order. Please make payment now", "fulfillcreatedpaid": "not in use", "fulfillrequested": "We should start serving in {{eta}} mins", "fulfillprepare": "not in use", "fulfillpacked": "not in use"}, "pickup": {"orderpaid": "not in use", "orderfulfilled": "Your order has been collected, enjoy~", "ordercancelled": "❗️Your order has been cancelled", "fulfillcreated": "We received your order. Please make payment now", "fulfillcreatedpaid": "not in use", "fulfillrequested": "Pick up your order at {{scheduledAt, datetime}} from {{storeName}}", "fulfillprepare": "Please collect your order in {{eta}} mins", "fulfillpacked": "Your order #{{queue}} is ready for collection"}}