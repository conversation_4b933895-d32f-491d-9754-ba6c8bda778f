/**
 * @module i18n
 */

//  Module Dependencies
const
	colors = require('colors'),
	i18n = appRequire('lib/common/i18nLib');

const
	I18N = '[i18n]'.magenta;

class I18n {
	constructor(app, settings) {
		this.app = app;
		this._settings = settings;
	}

	init() {
		const self = this,
			{ enabled, namespaces, languages, fallbacks, filepath } = self._settings;

		if (enabled) {
			try {
				this._SDK = i18n.init({ namespaces, languages, fallbacks, filepath });
				console.log(I18N + ` namespaces: ${namespaces.length === 0 ? 'NONE'.red : namespaces.join(', ')}`.green);
			} catch (err) {
				console.log(I18N + ` error: ${err}`.red);
			}
		}
	}
}

module.exports = exports = I18n;

/**
 * End of script
 */
