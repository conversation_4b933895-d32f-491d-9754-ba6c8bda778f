{"name": "Action", "base": "Model", "description": "Shell for accessing Action lambda", "strict": true, "options": {}, "mixins": {"MultitenantRemote": true}, "properties": {"id": {"type": "String", "id": true, "generated": true}, "minAppVersion": {"type": "String"}, "key": {"type": "String"}, "kind": {"type": "String", "enum": ["local", "remote", "template", "multi", "intent"]}, "description": {"type": "String"}, "startTime": {"type": "Date"}, "endTime": {"type": "Date"}, "object": {"type": "String"}, "action": {"type": "String"}, "data": {"type": "Object"}, "callback": {"type": "Callback"}, "enabled": {"type": "Boolean", "default": false}, "once": {"type": "Boolean", "default": false}, "published": {"type": "Boolean", "default": false}, "transient": {"type": "Boolean", "default": false}, "ttl": {"type": "Number"}, "tenant": {"type": {"code": {"type": "String"}}}, "personId": {"type": "String"}, "deviceId": {"type": "String"}, "triggeredAt": {"type": "Date"}, "purgeTime": {"type": "Date"}, "createdAt": {"type": "Date"}, "modifiedAt": {"type": "Date"}, "deletedAt": {"type": "Date"}}, "relations": {}, "acls": [], "indexes": {}, "methods": {"create": {"description": "Create action", "http": {"path": "/", "verb": "post"}, "accepts": [{"arg": "action", "type": "object", "required": true}, {"arg": "options", "type": "object", "description": "{ ttl (seconds) }"}], "returns": {"type": "Action", "root": true}}, "get": {"description": "Get action by Id", "http": {"path": "/:id", "verb": "get"}, "accepts": [{"arg": "id", "type": "string", "required": true}], "returns": {"type": "Action", "root": true}}, "search": {"description": "Search actions", "http": {"path": "/search", "verb": "post"}, "accepts": [{"arg": "filter", "type": "object", "description": "{ action, object, kind, enabled, tenant, personId, transient }"}], "returns": {"type": ["Action"], "root": true}}, "deleteById": {"description": "Delete action by Id", "http": {"path": "/:id", "verb": "delete"}, "accepts": [{"arg": "id", "type": "string", "required": true}], "returns": {"type": "Action", "root": true}}, "syncFetch": {"description": "Used by Sync to fetch instances", "http": {"path": "/sync/fetch", "verb": "get"}, "accepts": [{"arg": "personId", "type": "string", "required": true}, {"arg": "last", "type": "date"}], "returns": {"type": ["Action"], "root": true}}, "qrCode": {"description": "Get the QR code of an action", "http": {"path": "/:id/qrCode", "verb": "get"}, "accepts": [{"arg": "id", "type": "string", "required": true}, {"arg": "payload", "type": "object"}, {"arg": "options", "type": "object"}], "returns": {"type": "string", "root": true}}}}