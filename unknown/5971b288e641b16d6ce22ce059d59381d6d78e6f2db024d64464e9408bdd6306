{"name": "Group", "plural": "Groups", "base": "PersistedModel", "idInjection": true, "strict": false, "mixins": {"MultitenantRemote": true}, "options": {"validateUpsert": true}, "properties": {"id": {"type": "String", "id": true, "generated": true}}, "hidden": ["keyMap"], "validations": [], "relations": {}, "acls": [], "indexes": {}, "scopes": {}, "methods": {"findByIds": {"description": "Find all instances from the data source.", "accessType": "READ", "http": {"path": "/findByIds", "verb": "get"}, "accepts": [{"arg": "ids", "type": "array", "required": true}, {"arg": "query", "type": "object"}], "returns": {"type": "array", "root": true}}, "prototype.applyFilter": {"description": "Apply filter to Group (default using in-object filter)", "http": {"path": "/filter", "verb": "post"}, "accepts": [{"arg": "filter", "type": "object"}, {"arg": "options", "type": "object"}], "returns": {"type": "object", "root": true}}, "prototype.qualify": {"description": "Qualify (default using in-object filter)", "http": {"path": "/qualify", "verb": "post"}, "accepts": [{"arg": "idOrIdList", "type": "any", "required": true}, {"arg": "options", "type": "object"}], "returns": {"type": "object", "root": true}}, "prototype.pick": {"description": "Shortlist from current Group Data", "http": {"path": "/pick", "verb": "post"}, "accepts": [{"arg": "filter", "type": "any"}, {"arg": "options", "type": "object"}], "returns": {"type": "object", "root": true}}, "prototype.dedup": {"description": "Remove duplicates from current Group Data", "http": {"path": "/dedup", "verb": "post"}, "accepts": [{"arg": "valueFunc", "type": "string"}, {"arg": "options", "type": "object"}], "returns": {"type": "object", "root": true}}, "prototype.reset": {"description": "Reset keyMap from current Group Data", "http": {"path": "/reset", "verb": "post"}, "returns": {"type": "object", "root": true}}, "prototype.transformData": {"description": "Transform data from current Group Data", "http": {"path": "/transformData", "verb": "post"}, "accepts": [{"arg": "transFunc", "type": "string"}, {"arg": "options", "type": "object"}], "returns": {"type": "object", "root": true}}, "prototype.refresh": {"description": "Refresh data of Group", "http": {"path": "/refresh", "verb": "post"}, "accepts": [{"arg": "filter", "type": "object", "required": false}, {"arg": "options", "type": "object", "required": false}], "returns": {"type": "object", "root": true}}, "prototype.compose": {"description": "Compose Group", "http": {"path": "/compose", "verb": "post"}, "accepts": [{"arg": "include", "type": "array"}, {"arg": "addIds", "type": "array"}, {"arg": "exclude", "type": "array"}, {"arg": "removeIds", "type": "array"}], "returns": {"type": "object", "root": true}}, "prototype.suppress": {"description": "Suppress group data", "http": {"path": "/suppress", "verb": "post"}, "accepts": [{"arg": "type", "type": "string", "required": true}, {"arg": "suppress", "type": "object", "required": true}, {"arg": "options", "type": "object"}], "returns": {"type": "object", "root": true}}, "prototype.clone": {"description": "Duplicate Group (but not groupData)", "http": {"path": "/clone", "verb": "post"}, "accepts": [{"arg": "name", "type": "string", "required": true}], "returns": {"type": "object", "root": true}}, "prototype.clear": {"description": "Remove all groupData", "http": {"path": "/clear", "verb": "post"}, "returns": {"type": "object", "root": true}}, "prototype.union": {"description": "Add groups into this Group", "http": {"path": "/union", "verb": "post"}, "accepts": [{"arg": "groups", "type": "array", "required": true}], "returns": {"type": "object", "root": true}}, "prototype.minus": {"description": "Exclude groups from this Group", "http": {"path": "/minus", "verb": "post"}, "accepts": [{"arg": "groups", "type": "array", "required": true}], "returns": {"type": "object", "root": true}}, "prototype.intersect": {"description": "Shortlist from Group that also exist in all groups", "http": {"path": "/intersect", "verb": "post"}, "accepts": [{"arg": "groups", "type": "array", "required": true}], "returns": {"type": "object", "root": true}}, "prototype.addId": {"description": "Add list of objects (gId) to Group", "http": {"path": "/addId", "verb": "post"}, "accepts": [{"arg": "gIds", "type": "array", "required": true}], "returns": {"type": "object", "root": true}}, "prototype.removeId": {"description": "Remove list of objects (gId) from Group", "http": {"path": "/removeId", "verb": "post"}, "accepts": [{"arg": "gIds", "type": "array", "required": true}], "returns": {"type": "object", "root": true}}, "prototype.contains": {"description": "Check whether list of objects (gId) are contained in Group", "http": {"path": "/contains", "verb": "get"}, "accepts": [{"arg": "gIds", "type": "array", "required": true}], "returns": {"type": "array", "root": true}}, "prototype.chunks": {"description": "Get chunks of group", "http": {"path": "/chunks", "verb": "get"}, "returns": {"type": "array", "root": true}}, "prototype.chunksCheck": {"description": "Get total of group chunks", "http": {"path": "/chunks/check", "verb": "get"}, "returns": {"type": "object", "root": true}}, "prototype.findDataByChunkId": {"description": "Get data of group by chunk Id", "http": {"path": "/chunkdata", "verb": "get"}, "accepts": [{"arg": "chunkId", "type": "string", "required": true}], "returns": {"type": "array", "root": true}}, "prototype.updateDataByChunkId": {"description": "Update data of group by chunk Id", "http": {"path": "/chunkdata", "verb": "post"}, "accepts": [{"arg": "chunkId", "type": "string", "required": true}, {"arg": "data", "type": "array", "required": true}], "returns": {"type": "number", "root": true}}, "containersOf": {"description": "Groups (of model type) that object (gId) belongs to", "http": {"path": "/containersOf", "verb": "get"}, "accepts": [{"arg": "model", "type": "string", "required": true}, {"arg": "gId", "type": "string", "required": true}], "returns": {"type": "object", "root": true}}, "contains": {"description": "Check whether list of objects (gId) are contained in Group", "http": {"path": "/contains", "verb": "get"}, "accepts": [{"arg": "groupId", "type": "any", "required": true}, {"arg": "gIds", "type": "array", "required": true}], "returns": {"type": "array", "root": true}}, "prototype.facebookCreateAudience": {"description": "Create Facebook Audience from Person group", "http": {"verb": "post", "path": "/facebook/audience"}, "accepts": [{"arg": "accountId", "type": "any", "required": true}, {"arg": "name", "type": "string", "required": true}, {"arg": "description", "type": "string", "required": true}, {"arg": "type", "type": "string"}, {"arg": "options", "type": "object"}], "returns": {"type": "object", "root": true}}, "prototype.facebookAddAudience": {"description": "Add users to Facebook Audience from Person group", "http": {"verb": "post", "path": "/facebook/audience/:audienceId"}, "accepts": [{"arg": "audienceId", "type": "string", "required": true, "http": {"source": "path"}}], "returns": {"type": "object", "root": true}}}}