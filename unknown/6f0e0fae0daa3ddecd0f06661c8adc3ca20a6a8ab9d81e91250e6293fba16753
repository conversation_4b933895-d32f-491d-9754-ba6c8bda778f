{"name": "CustomData", "plural": "CustomData", "base": "Model", "idInjection": true, "strict": false, "mixins": {}, "options": {}, "properties": {"id": {"type": "String", "id": true}, "name": {"type": "String", "description": "barcode, balance, ..."}, "value": {"type": "any", "description": "the value can be number, string or json, ..."}}, "validations": [], "relations": {}, "acls": [], "indexes": {}, "scopes": {}, "methods": {}}