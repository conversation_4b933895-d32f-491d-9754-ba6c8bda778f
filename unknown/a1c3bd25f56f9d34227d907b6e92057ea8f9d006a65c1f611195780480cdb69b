{"name": "Staff", "plural": "Staff", "base": "PersistedModel", "idInjection": true, "strict": false, "mixins": {"MultitenantRemote": true}, "options": {"validateUpsert": true}, "properties": {"id": {"type": "String", "id": true, "generated": true}}, "validations": [], "relations": {}, "acls": [], "scopes": {}, "methods": {"doUpsert": {"description": "Update or create a staff", "http": {"path": "/doUpsert", "verb": "post"}, "accepts": [{"arg": "data", "type": "object", "required": true}, {"arg": "filter", "type": "object"}, {"arg": "options", "type": "object"}], "returns": {"type": "object", "root": true}}, "getUserDetail": {"description": "Get staff with user details", "http": {"path": "/getUserDetail", "verb": "get"}, "returns": {"type": "object", "root": true}}, "prototype.upsertIdentities": {"description": "Create or update Identities", "http": {"path": "/upsertIdentities", "verb": "post"}, "accepts": [{"arg": "identityList", "type": "array", "required": true}, {"arg": "options", "type": "object"}], "returns": {"type": "array", "root": true}}, "prototype.replaceIdentity": {"description": "Replace an identity of the model", "http": {"path": "/identities/:fk/replace", "verb": "post"}, "accepts": [{"arg": "fk", "type": "string", "required": true}, {"arg": "newIdentity", "type": "any", "required": true}, {"arg": "options", "type": "object"}], "returns": {"type": "object", "root": true}}, "prototype.deleteIdentity": {"description": "Delete a related item by id for identity", "http": {"path": "/identities/:fk", "verb": "DELETE"}, "accepts": [{"arg": "fk", "type": "string", "required": true}, {"arg": "options", "type": "object"}], "returns": {"type": "object", "root": true}}, "prototype.upsertDates": {"description": "Create or update Dates", "http": {"path": "/upsertDates", "verb": "post"}, "accepts": [{"arg": "dateList", "type": "array", "required": true}, {"arg": "options", "type": "object"}], "returns": {"type": "array", "root": true}}, "prototype.deleteDate": {"description": "Delete a date", "http": {"path": "/dates/:fk", "verb": "DELETE"}, "accepts": [{"arg": "fk", "type": "string", "required": true}, {"arg": "options", "type": "object"}], "returns": {"type": "object", "root": true}}, "prototype.getUserDetail": {"description": "Get user details of the staff", "http": {"path": "/account", "verb": "get"}, "returns": {"type": "object", "root": true}}, "prototype.issueCard": {"description": "Issue card to staff", "http": {"path": "/card/issue", "verb": "post"}, "accepts": [{"arg": "programId", "type": "any", "required": true}, {"arg": "tierLevel", "type": "number", "required": true}, {"arg": "options", "type": "object", "required": false}], "returns": {"type": "object", "root": true}}}}