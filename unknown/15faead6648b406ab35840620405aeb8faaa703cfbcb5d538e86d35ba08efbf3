{"name": "Callback", "plural": "Callbacks", "base": "Model", "idInjection": true, "strict": false, "mixins": {}, "options": {"validateUpsert": true}, "properties": {"id": {"type": "String", "id": true, "defaultFn": "nanoid"}, "name": {"type": "String", "max": 32, "required": true}, "method": {"type": "String", "required": true}, "url": {"type": "String", "required": true}, "headers": {"type": "Object"}, "payload": {"type": "Object"}, "rate": {"type": {"limit": {"type": "number", "default": 50}, "interval": {"type": "number", "default": 1000, "description": "in milliseconds"}}}, "accessToken": {"type": "string"}}, "validations": [], "acls": [], "scopes": {}, "methods": {}}