/**
 *  @module Mixin:doUpsert
 */
const { isEmptyObj, pickDeepValue, cloneDeep } = require('@perkd/utils'),
	{ JSONPatch } = require('@perkd/sync'),
	{ compare } = JSONPatch

module.exports = function(Model, options = {}) {

	const { delta: DELTA_PROPS = {}, merge: MERGE_PROPS = {}, arrayDifference: ARR_DIFF_PROPS = {} } = options,
		EmbedsUpsertFuncs = {}		// eg: phoneList: 'upsertPhones'

	// -----  Static Methods  -----

	/**
	 * Create or update a Model instance
	 * @param	{Object} data
	 * @param	{Object} filter
	 * @param	{Object} through
	 * @param	{Object} options
	 * @return	{Promise<Model>}
	 */
	Model.doUpsert = async function(data, filter, through = {}, options = {}) {
		// before hook
		const context = { data, hookState: {} }
		await Model.notifyObserversOf('before doUpsert', context)

		const { app } = Model,
			modelname = Model.modelName.toLowerCase(),
			embedsPropList = extractEmbedsProperties(data),

			// FIXME compat old touchPoint format, remove after all services deployed
			{ through: throughOpt } = options,
			{ through: throughThr } = through,
			finalThrough = throughThr || throughOpt || { ...through, ...options }

		finalThrough.touchedAt = finalThrough.touchedAt || new Date()

		// end of compat

		try {
			const result = await createOrUpdate(data, filter, finalThrough),
				{ isNew, instance, updates } = result,
				delta = []

			options.isNew = isNew	// if isNew, submodels omit update event, only fire model create event - ZJ

			// fire main model update event
			if (!isNew && updates && app.Event?.[modelname]?.updated) {
				const arr = Object.keys(ARR_DIFF_PROPS),
					added = [],
					removed = []

				for (const e of arr) {
					const n = pickDeepValue(updates.after, e) || [],
						o = pickDeepValue(updates.before, e) || [],
						a = n.filter(i => !o.includes(i)),
						r = o.filter(i => !n.includes(i))

					if (a.length) added.push({ op: 'add', path: '/' + e.replace(/\./g, '/'), value: a })
					if (r.length) removed.push({ op: 'remove', path: '/' + e.replace(/\./g, '/'), value: r })

					delete updates.before[e.split('.')[0]]
					delete updates.after[e.split('.')[0]]
				}

				delta.push(...compare(updates.before, updates.after))

				if (added.length > 0 || removed.length > 0) {
					delta.push(...added, ...removed)
				}

				updates.delta = delta
			}

			// upsert embeds
			const res = await upsertEmbedsProperties(instance, embedsPropList, finalThrough, options, result),
				{ delta: embedsDelta } = res || {}

			if (embedsDelta) {
				result.updates ||= {}
				result.updates.delta = result.updates.delta
					? result.updates.delta.concat(embedsDelta)
					: embedsDelta
			}

			// after hook
			const ctx = {
				isNewInstance: isNew,
				instance,
				hookState: {}
			}

			if (Array.isArray(updates?.delta)) {
				updates.delta = updates.delta.filter(({ path }) => {
					const key = path.split('/')[1]
					return DELTA_PROPS[key]
				})
			}
			if (!isNew && updates.delta?.length > 0) {
				ctx.hookState.delta = updates.delta
			}
			else {
				ctx.hookState.delta = null		// null can be filtered on activity
			}
			if (finalThrough) {
				ctx.hookState.through = finalThrough
			}

			const { instance: final } = await Model.notifyObserversOf('after doUpsert', ctx)
			return final
		}
		catch (err) {
			appNotify(`${Model.name}/doUpsert`, { data, finalThrough, err })
		}
	}

	// Model.prototype.doUpsert = function(data) {
	// 	return this.doUpsert({ ...data, id: this.id });
	// };

	/**
	 *
	 * @param {Object} data
	 * @param {Object} filter
	 * @param {Object} through
	 * @return {Object} { isNew, instance, updates: { before, after: {} } }		(updates optional)
	 */
	async function createOrUpdate(data, filter, through) {
		const { id } = data,
			acquired = through ? { acquired: through } : undefined,
			withAcquired = { ...data, ...acquired }

		if (filter) {
			const [ instance, isNew ] = await Model.findOrCreate(filter, withAcquired)
			return isNew ? { isNew, instance } : updateInstance(instance, data)
		}

		if (id) {
			const found = await Model.findById(id)

			if (found) {
				delete data.id		// delete id, no need to update object id
				return updateInstance(found, data)
			}
			// not found, create
			const instance = await Model.create(withAcquired)
			return { isNew: true, instance }
		}

		const instance = await Model.create(withAcquired)
		return { isNew: true, instance }
	}

	/**
	 * @param {Model|Object} instance
	 * @param {Object} updates
	 * @return {Object} { isNew, instance, updates }
	 */
	async function updateInstance(instance, updates) {
		const isNew = false,
			data = instance.toJSON ? instance.toJSON() : instance,
			before = {}

		Object.keys(updates).forEach(key => {
			if (MERGE_PROPS[key]) updates[key] = { ...data[key], ...updates[key] }
			before[key] = data[key]
		})
		if (isEmptyObj(updates)) {
			return { isNew, instance, updates: { before, after: {} } }
		}

		await instance.updateAttributes(cloneDeep(updates), { doUpsert: true })
		return { isNew, instance, updates: { before, after: updates } }
	}

	function extractEmbedsProperties(data) {
		isEmptyObj(EmbedsUpsertFuncs) && initEmbedsFuncs(Model)

		const embedsPropList = []
		for (const property in data) {
			if (EmbedsUpsertFuncs[property]) {
				embedsPropList.push({
					data: data[property],
					property,
					funcName: EmbedsUpsertFuncs[property],
				})
				delete data[property]
			}
		}
		return embedsPropList

		function initEmbedsFuncs(model) {
			Object.keys(model.settings.relations).forEach(relationName => {
				const relation = model.settings.relations[relationName]
				if ((relation.type === 'embedsMany' || relation.type === 'embedsOne') && relation.property) {
					EmbedsUpsertFuncs[relation.property] = 'upsert' + relationName.charAt(0).toUpperCase() + relationName.slice(1)
				}
			})
		}
	}

	/**
	 * @param {Model} instance
	 * @param {Object[]} embedsPropList - { funcName, data }
	 * @param {Object} through
	 * @param {Object} options
	 * @param {Object} result - mutated
	 * @return {Object} result
	 */
	async function upsertEmbedsProperties(instance, embedsPropList, through, options, result = {}) {
		const delta = []

		for (const { funcName, data } of embedsPropList) {
			const update = await instance[funcName](data, through, options),
				updates = Array.isArray(update) ? update : [ update ]

			for (const { _delta } of updates) {
				if (_delta) delta.push(..._delta)
			}
		}

		if (delta.length && result.updates) {
			result.updates.delta = delta
		}
		result.instance = await Model.findById(instance.id)
		return { delta }
	}

	// -----  Remote Methods  -----

	Model.remoteMethod('doUpsert', {
		description: 'Create or update a Model instance',
		http: { path: '/doUpsert', verb: 'post' },
		accepts: [
			{ arg: 'data', type: 'object', required: true },
			{ arg: 'filter', type: 'object' },
			{ arg: 'through', type: 'object', description: 'compat param, to depracate' },
			{ arg: 'options', type: 'object', description: '{ through }' },
		],
		returns: { type: Model.name, root: true },
	})
}
