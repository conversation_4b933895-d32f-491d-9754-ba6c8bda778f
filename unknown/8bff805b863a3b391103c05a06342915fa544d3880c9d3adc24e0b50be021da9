/**
 *  @module Mixin:Date
 */

const { yearsSince, daysSince, daysToAnniversary, daysSinceAnniversary } = require('@perkd/utils'),
	{ EmbedLib } = appRequire('lib/common/embedLibrary')

module.exports = function(Model, options) {
	const dateLib = new EmbedLib(Model, 'dates', options)
	dateLib.setup()

	Model.prototype._date = function({ name }) {
		return this.dates.value().find(date => (date.name === name))
	}

	/**
	 * Number of years since the specified date
	 * @param  {String} name of date
	 * @return {Number|null} number of days (inclusive)
	 */
	Model.prototype.yearsSince = function(name) {
		const { date } = this._date({ name }) || {}

		return date ? yearsSince(date) : null
	}

	/**
	 * Number of days from the specified date
	 * @param  {String} name of date
	 * @return {Number|null} number of days (inclusive)
	 */
	Model.prototype.daysFrom = function(name) {
		const { date } = this._date({ name }) || {}		// FIXME: compare date only

		return date ? daysSince(date) : null
	}

	/**
	 * Number of days to the NEXT anniversary of the specified date
	 * @param  {String} name of date
	 * @return {Number} number of days (inclusive), eg.	0 = today, 1 = tomorrow
	 */
	Model.prototype.daysFromNext = function(name) {
		const { date } = this._date({ name }) || {}

		return date ? daysToAnniversary(date) : null
	}

	/**
	 * Number of days since the LAST anniversary of the specified date
	 * @param  {String} name of date
	 * @return {Number} number of days (inclusive), eg.	0 = today, 1 = yesterday
	 */
	Model.prototype.daysFromLast = function(name) {
		const { date } = this._date({ name }) || {}

		return date ? daysSinceAnniversary(date) : null
	}
}
