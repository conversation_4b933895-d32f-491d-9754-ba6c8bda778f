{"name": "Product", "plural": "Products", "base": "PersistedModel", "idInjection": true, "strict": false, "options": {}, "mixins": {"MultitenantRemote": true, "RemotingTypes": {"ProductImage": true}}, "properties": {"id": {"type": "String", "id": true, "generated": true}}, "validations": [], "relations": {"bundle": {"type": "embeds<PERSON><PERSON>", "model": "OptionValue", "property": "bundleList", "options": {"validate": false, "persistent": true}}, "variations": {"type": "embeds<PERSON><PERSON>", "model": "Variation", "property": "variationList", "options": {"validate": false, "persistent": true}}, "vendor": {"type": "belongsTo", "model": "Business", "foreignKey": "businessId"}, "images": {"type": "hasMany", "model": "ProductImage", "foreignKey": "ownerId"}, "variants": {"type": "hasMany", "model": "<PERSON><PERSON><PERSON>", "foreignKey": "productId"}, "resource": {"type": "belongsTo", "model": "Resource", "foreignKey": "resourceId"}}, "acls": [], "indexes": {}, "scopes": {}, "methods": {"provisionStoredValue": {"description": "Create Product & Variants for Stored Value", "http": {"path": "/provision/storedvalue", "verb": "post"}, "accepts": [{"arg": "type", "type": "string", "enum": ["topup", "preload"]}, {"arg": "currency", "type": "object", "required": true, "description": "{ code, symbol, precision, exchangeRate }"}, {"arg": "denominations", "type": ["number"], "description": "list of amounts"}, {"arg": "variable", "type": "object", "description": "{ min, [max], [increment] }"}], "returns": {"type": "object", "root": true}}, "findByIds": {"description": "Find all instances from the data source.", "accessType": "READ", "http": {"path": "/findByIds", "verb": "get"}, "accepts": [{"arg": "ids", "type": "array", "required": true}, {"arg": "query", "type": "object"}], "returns": {"type": "array", "root": true}}, "doFindOne": {"description": "Find first instance of the model matched by filter from the data source.", "http": {"path": "/findOne", "verb": "post"}, "accepts": {"arg": "filter", "type": "object"}, "returns": {"type": "object", "root": true}}, "gtinLookup": {"description": "Get related product information by GTIN", "http": {"path": "/gtin/lookup/:upc", "verb": "get"}, "accepts": [{"arg": "upc", "type": "string", "http": {"source": "path"}, "required": true}], "returns": {"type": "array", "root": true}}, "applinkAddToBag": {"description": "Create Add-to-Bag product collection promotion page (microsite)", "http": {"path": "/applink/addtobag", "verb": "post"}, "accepts": [{"arg": "data", "type": "object", "http": {"source": "body"}, "required": true}], "returns": {"type": "object", "root": true}}, "applinkAddToBagDeploy": {"description": "Generate Add-to-Bag product collection promotion page", "http": {"path": "/applink/addtobag/:micrositeId/deploy", "verb": "post"}, "accepts": [{"arg": "micrositeId", "type": "string", "http": {"source": "path"}, "required": true}, {"arg": "productIds", "type": "array", "required": true}, {"arg": "fallbackActionId", "type": "string", "required": true}, {"arg": "start", "type": "date"}, {"arg": "end", "type": "date"}, {"arg": "shorten", "type": "boolean", "default": true}, {"arg": "options", "type": "object"}], "returns": {"type": "object", "root": true}}, "applinkAddToBagUndeploy": {"description": "Remove Add-to-Bag product promotion page & its assets on s3", "http": {"path": "/applink/addtobag/:micrositeId/undeploy", "verb": "post"}, "accepts": [{"arg": "micrositeId", "type": "string", "http": {"source": "path"}, "required": true}, {"arg": "slug", "type": "string", "required": true}], "returns": {"type": "object", "root": true}}, "applinkAddToBagDelete": {"description": "Delete Add-to-Bag product promotion page", "http": {"path": "/applink/addtobag/:micrositeId", "verb": "delete"}, "accepts": [{"arg": "micrositeId", "type": "string", "http": {"source": "path"}, "required": true}], "returns": {"type": "object", "root": true}}, "prototype.applinkRefresh": {"description": "Refresh Perkd applinks & related content", "http": {"path": "/applink/refresh", "verb": "post"}, "accepts": [], "returns": {"type": "object", "root": true}}, "prototype.applinkAddToBag": {"description": "Create Add-to-Bag product promotion page (microsite)", "http": {"path": "/applink/addtobag", "verb": "post"}, "accepts": [{"arg": "data", "type": "object", "http": {"source": "body"}, "required": true}], "returns": {"type": "object", "root": true}}, "prototype.applinkAddToBagDeploy": {"description": "Generate Add-to-Bag product promotion page", "http": {"path": "/applink/addtobag/:micrositeId/deploy", "verb": "post"}, "accepts": [{"arg": "micrositeId", "type": "string", "http": {"source": "path"}, "required": true}, {"arg": "fallbackActionId", "type": "string", "required": true}, {"arg": "start", "type": "date"}, {"arg": "end", "type": "date"}, {"arg": "shorten", "type": "boolean", "default": true}, {"arg": "options", "type": "object"}], "returns": {"type": "object", "root": true}}}}