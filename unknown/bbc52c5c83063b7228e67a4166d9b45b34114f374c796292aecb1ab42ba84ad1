/**
 *  @module Model:Email
 */

const { isEmail } = require('@perkd/utils')

module.exports = function(Email) {

	// -----  Validations  -----
	Email.validate('address', emailFormat, { message: 'Invalid email address' })

	function emailFormat(err) {
		const { address, valid } = this

		this.address = address.toLowerCase()
		if (valid === undefined || valid === null) {
			this.valid = isEmail(address)
		}
	}

	// -----  Operation hooks  -----

	Email.observe('before save', async ({ instance, data, currentInstance }) => {
		const updated = instance || data,
			existingOptIn = currentInstance ? currentInstance.optIn : null

		if (existingOptIn === null && updated.optIn === undefined) {
			// user did not provide optIn status, should set default null
			updated.optIn = null
		}
	})
}
