/**
 * @module Mixin:UnsetThrough
 */

/* global */

//  Module Dependencies

module.exports = function(Model, options = {}) {
	// -----  Static Methods  -----

	// -----  Instance Methods  -----

	// ---  Specific methods  ---

	// ---  Remote & Operation Hooks  ---

	Model.observe('before save', async ctx => {
		const updated = ctx.instance || ctx.data;
		ctx.instance ? updated.unsetAttribute('through') : (delete updated.through);
	});
};

/**
 * End of script
*/
