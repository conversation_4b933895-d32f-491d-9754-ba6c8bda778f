/**
* @module Mixin:ListOwner
* Put List model before Model in model-config.json
*/

// Module Dependencies
const
	Promise = require('bluebird');

const
	LIST = 'List';

module.exports = function(Model, options) {
	// -----  Static Properties  -----

	const { ListModel = LIST } = options,
		name = ListModel.charAt(0).toLowerCase() + ListModel.slice(1),
		relation = name + 's',
		foreignKey = name + 'Ids',
		List = Model.modelBuilder.models[ListModel];

	// -----  Static Methods  -----

	// -----  Instance Methods  -----
	Model.prototype[`__link__${relation}`] = function(fk, asDefault = false) { // TODO: asDefault
		const self = this;
		return self.updateDocument({ $addToSet: { [foreignKey]: fk } });
	};

	Model.prototype[`__unlink__${relation}`] = function(fk) {
		const self = this;
		return self.updateDocument({ $pull: { [foreignKey]: fk } });
	};

	Model.prototype[`__default__${relation}`] = function(fk) {
		const self = this,
			ids = self[foreignKey],
			ndx = ids.indexOf(fk);

		if (ndx === -1) return Promise.reject('Foreign key not found');
		if (ndx === 0) return Promise.resolve(self);		// already is default

		ids.splice(ndx, 1);
		ids.unshift(fk);
		return self.updateAttributes({ [foreignKey]: ids }); // use updateDocument $pull & updateDocument $push + $position?
	};

	Model.prototype[`__get__${relation}`] = function(filter) {
		const self = this,
			ids = self[foreignKey];

		return (ids.length > 0) ? self[relation].find(filter) : Promise.resolve([]);
	};

	// -----  Remote Methods  -----

	Model.remoteMethod('prototype.addToList', {
		description: `Add a related List by id for ${relation}`,
		http: { path: `/${relation}/rel/:fk`, verb: 'put' },
		accepts: [
			{ arg: 'fk', type: 'string', http: { source: 'path' }, required: true, description: `Foreign key for ${relation}` },
			{ arg: 'asDefault', type: 'boolean', description: 'Set as 1st element (default)' },
		],
		returns: { type: 'object', root: true },
	});

	Model.remoteMethod('prototype.removeFromList', {
		description: `Remove a related List by id for ${relation}`,
		http: { path: `/${relation}/rel/:fk`, verb: 'delete' },
		accepts: [
			{ arg: 'fk', type: 'string', http: { source: 'path' }, required: true, description: `Foreign key for ${relation}` },
		],
		returns: { type: 'object', root: true },
	});

	Model.remoteMethod('prototype.setAsDefault', {
		description: `Set a related List by id as 1st element for ${relation}`,
		http: { path: `/${relation}/rel/:fk/default`, verb: 'post' },
		accepts: [
			{ arg: 'fk', type: 'string', http: { source: 'path' }, required: true, description: `Foreign key for ${relation}` },
		],
		returns: { type: 'object', root: true },
	});

	Model.remoteMethod('prototype.getList', {
		description: `Queries ${relation} of ${Model.name}`,
		http: { path: `/${relation}`, verb: 'get' },
		accepts: [
			{ arg: 'filter', type: 'object', http: { source: 'query' } },
		],
		returns: { type: 'object', root: true },
	});
};
