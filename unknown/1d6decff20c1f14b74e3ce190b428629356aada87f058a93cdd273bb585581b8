{"name": "PointsTransaction", "plural": "PointsTransactions", "description": "", "base": "PersistedModel", "idInjection": true, "strict": false, "mixins": {"MultitenantRemote": true}, "properties": {"id": {"type": "String", "id": true, "generated": true}, "kind": {"type": "String", "enum": ["collect", "redeem", "bonus", "adjust", "forfeit", "forward", "carry", "expire"], "required": true, "description": "transaction type"}, "amount": {"type": "Number", "default": 0, "description": "Amount of Points, can be negative"}, "balance": {"type": "Number", "default": 0, "description": "Net balance available"}, "external": {"type": {"provider": {"type": "String"}, "customerId": {"type": "string"}, "orderId": {"type": "string"}, "transactionId": {"type": "string", "description": "Id of Points transaction"}}, "default": {}}, "acquired": {"type": "TouchPoint"}, "visible": {"type": "Boolean", "default": true}, "endTime": {"type": "Date", "default": null}, "transactedAt": {"type": "Date", "default": null}, "createdAt": {"type": "Date"}, "modifiedAt": {"type": "Date"}, "deletedAt": {"type": "Date"}}, "hidden": [], "validations": [], "relations": {}, "acls": [], "methods": {"bulkCreate": {"description": "Create multiple instances of PointsTransaction and save to the data source.", "http": {"path": "/bulk", "verb": "post"}, "accepts": {"arg": "data", "type": "any", "description": "Model instance data", "http": {"source": "body"}}, "returns": {"type": "object", "root": true}}, "create": {"description": "Create a new instance of the model and persist it into the data source.", "http": {"path": "/", "verb": "post"}, "accepts": [{"arg": "data", "type": "object", "http": {"source": "body"}}], "returns": {"type": "object", "root": true}}}}