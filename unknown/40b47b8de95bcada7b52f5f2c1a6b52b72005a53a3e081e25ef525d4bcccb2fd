{"name": "Person", "plural": "Persons", "base": "PersistedModel", "idInjection": true, "strict": false, "options": {}, "mixins": {"MultitenantRemote": true}, "properties": {"id": {"type": "String", "id": true, "generated": true}}, "validations": [], "relations": {}, "acls": [], "indexes": {}, "scopes": {}, "methods": {"doFind": {"description": "Find all instances of the model matched by filter from the data source.", "http": {"path": "/find", "verb": "post"}, "accepts": {"arg": "filter", "type": "object"}, "returns": {"type": "array", "root": true}}, "doFindOne": {"description": "Find first instance of the model matched by filter from the data source.", "http": {"path": "/findOne", "verb": "post"}, "accepts": {"arg": "filter", "type": "object"}, "returns": {"type": "object", "root": true}}, "doCount": {"description": "Count instances of the model matched by where from the data source.", "http": {"path": "/count", "verb": "post"}, "accepts": {"arg": "where", "type": "object"}, "returns": {"type": "number", "root": true}}, "match": {"description": "Match existing instances by given profile & options", "http": {"path": "/match", "verb": "get"}, "accepts": [{"arg": "profile", "type": "object", "required": true}, {"arg": "options", "type": "object"}], "returns": {"type": ["Person"], "root": true}}, "matchOrCreate": {"description": "Match or Create person with profile & options", "http": {"path": "/matchOrCreate", "verb": "post"}, "accepts": [{"arg": "profile", "type": "object", "required": true}, {"arg": "options", "type": "object", "description": "through, programId, createUser (boolean)"}], "returns": {"type": "Person", "root": true}}, "search": {"description": "Search Persons with options", "http": {"path": "/search", "verb": "get"}, "accepts": [{"arg": "textString", "type": "string", "required": true}, {"arg": "options", "type": "object"}], "returns": {"type": "any", "root": true}}, "doUpsert": {"description": "Create or update a person", "http": {"path": "/doUpsert", "verb": "post"}, "accepts": [{"arg": "data", "type": "object", "required": true}, {"arg": "filter", "type": "object"}, {"arg": "options", "type": "object"}], "returns": {"type": "Person", "root": true}}, "findByIdentity": {"description": "Get Person with Identity", "http": {"path": "/byIdentity", "verb": "get"}, "accepts": [{"arg": "provider", "type": "string", "required": true}, {"arg": "type", "type": "string"}, {"arg": "externalId", "type": "string", "required": true}], "returns": {"type": "array", "root": true}}, "findOneByIdentity": {"description": "Get most recently created Person with Identity", "http": {"path": "/byIdentity/one", "verb": "get"}, "accepts": [{"arg": "provider", "type": "string", "required": true}, {"arg": "type", "type": "string"}, {"arg": "externalId", "type": "string", "required": true}], "returns": {"type": "object", "root": true}}, "findIdentity": {"description": "Find an Identity for provider", "http": {"path": "/identity", "verb": "get"}, "accepts": [{"arg": "id", "type": "any", "required": true}, {"arg": "provider", "type": "string", "required": true}, {"arg": "type", "type": "string"}, {"arg": "domain", "type": "string"}], "returns": {"type": "string", "root": true}}, "findOneByUserId": {"description": "Find (latest) Person by perkd userId", "http": {"path": "/userId", "verb": "get"}, "accepts": [{"arg": "userId", "type": "string", "required": true}], "returns": {"type": "Person", "root": true}}, "personsOfMobile": {"description": "Persons with mobile", "http": {"path": "/mobile", "verb": "get"}, "accepts": [{"arg": "mobile", "type": "string", "required": true}], "returns": {"type": ["Person"], "root": true}}, "matchPerson": {"description": "Find person (with membership) by given profile & options (Merchant API)", "http": {"path": "/merchant/matchPerson", "verb": "post"}, "accepts": [{"arg": "profile", "type": "object", "required": true}, {"arg": "options", "type": "object"}], "returns": {"type": "array", "root": true}}, "findOrCreateUser": {"description": "Find or Create Person for provider's User", "http": {"path": "/findOrCreate/user", "verb": "post"}, "accepts": [{"arg": "userId", "type": "string", "required": true}, {"arg": "provider", "type": "string", "description": "default: 'perkd'"}], "returns": {"type": "Person", "root": true}}, "upsertIdentity": {"description": "Update or add identity", "http": {"path": "/identity/upsert", "verb": "post"}, "accepts": [{"arg": "id", "type": "string", "required": true, "description": "person id"}, {"arg": "provider", "type": "string", "required": true}, {"arg": "type", "type": "string"}, {"arg": "externalId", "type": "string", "required": true}]}, "prototype.upsertIdentity": {"description": "Update or add identity", "http": {"path": "/identity/upsert", "verb": "post"}, "accepts": [{"arg": "provider", "type": "string", "required": true}, {"arg": "type", "type": "string"}, {"arg": "externalId", "type": "string", "required": true}], "returns": {"type": "object", "root": true}}, "prototype.addTag": {"description": "Add tags to model instance", "http": {"path": "/tags/add", "verb": "post"}, "accepts": [{"arg": "tags", "type": "array", "required": true}, {"arg": "kind", "type": "string", "default": "user"}, {"arg": "options", "type": "object"}], "returns": {"type": "array", "root": true}}, "prototype.removeTag": {"description": "Remove tags from model instance", "http": {"path": "/tags/remove", "verb": "post"}, "accepts": [{"arg": "tags", "type": "array", "required": true}, {"arg": "kind", "type": "string", "default": "user"}, {"arg": "options", "type": "object"}], "returns": {"type": "array", "root": true}}, "prototype.getContact": {"description": "Get contact of Person for collection of channels", "http": {"path": "/getContact", "verb": "get"}, "accepts": [{"arg": "channels", "type": "object", "required": true}], "returns": {"type": "object", "root": true}}, "prototype.upsertActivities": {"description": "Create or update activities", "http": {"path": "/upsertActivities", "verb": "post"}, "accepts": [{"arg": "activityList", "type": "array", "http": {"source": "body"}}], "returns": {"type": "array", "root": true}}, "prototype.upsertIdentities": {"description": "Create or update Identities", "http": {"path": "/upsertIdentities", "verb": "post"}, "accepts": [{"arg": "identityList", "type": "array", "required": true}, {"arg": "options", "type": "object"}], "returns": {"type": "array", "root": true}}, "prototype.replaceIdentity": {"description": "Replace an identity of the model", "http": {"path": "/identities/:fk/replace", "verb": "post"}, "accepts": [{"arg": "fk", "type": "string", "required": true}, {"arg": "newIdentity", "type": "any", "required": true}, {"arg": "options", "type": "object"}], "returns": {"type": "object", "root": true}}, "prototype.deleteIdentity": {"description": "Delete a related item by id for identity", "http": {"path": "/deleteIdentity", "verb": "post"}, "accepts": [{"arg": "identity", "type": "object", "required": true}, {"arg": "options", "type": "object"}], "returns": {"type": "object", "root": true}}, "prototype.upsertPhones": {"description": "Create or update Phones", "http": {"path": "/upsertPhones", "verb": "post"}, "accepts": [{"arg": "phoneList", "type": "array", "required": true}, {"arg": "options", "type": "object"}], "returns": {"type": "array", "root": true}}, "prototype.deletePhone": {"description": "Delete a related item by fullNumber for phone", "http": {"path": "/deletePhone", "verb": "post"}, "accepts": [{"arg": "phone", "type": "object", "required": true}, {"arg": "options", "type": "object"}], "returns": {"type": "object", "root": true}}, "prototype.defaultPhone": {"description": "Set default phone", "http": {"path": "/phones/:fk/default", "verb": "post"}, "accepts": [{"arg": "fk", "type": "string", "required": true}], "returns": {"type": "object", "root": true}}, "prototype.upsertEmails": {"description": "Create or update Emails", "http": {"path": "/upsertEmails", "verb": "post"}, "accepts": [{"arg": "emailList", "type": "array", "required": true}, {"arg": "options", "type": "object"}], "returns": {"type": "array", "root": true}}, "prototype.defaultEmail": {"description": "Set default email by address", "http": {"path": "/emails/:fk/default", "verb": "post"}, "accepts": [{"arg": "fk", "type": "string", "required": true}], "returns": {"type": "object", "root": true}}, "prototype.replaceEmail": {"description": "Replace email of a model instance", "http": {"path": "/emails/:fk/replace", "verb": "post"}, "accepts": [{"arg": "fk", "type": "string", "required": true}, {"arg": "<PERSON><PERSON><PERSON><PERSON>", "type": "string", "required": true}, {"arg": "options", "type": "object"}], "returns": {"type": "object", "root": true}}, "prototype.deleteEmail": {"description": "Delete a related item by address for email", "http": {"path": "/deleteEmail", "verb": "post"}, "accepts": [{"arg": "email", "type": "object", "required": true}, {"arg": "options", "type": "object"}], "returns": {"type": "object", "root": true}}, "prototype.upsertBehaviors": {"description": "Create or update Behaviors", "http": {"path": "/upsertBehaviors", "verb": "post"}, "accepts": [{"arg": "subModel", "type": "string", "required": true}, {"arg": "behaviorList", "type": "array", "required": true}], "returns": {"type": "array", "root": true}}, "prototype.deleteBehavior": {"description": "Delete a behavior", "http": {"path": "/deleteBehavior", "verb": "post"}, "accepts": [{"arg": "subModel", "type": "string", "required": true}, {"arg": "behavior", "type": "object", "required": true}], "returns": {"type": "object", "root": true}}, "prototype.getActivities": {"description": "Get activities with provided options of the person", "http": {"path": "/getActivities", "verb": "get"}, "accepts": [{"arg": "options", "type": "object", "http": {"source": "body"}}], "returns": {"type": "array", "root": true}}, "prototype.upsertPermissions": {"description": "Create or update Permissions", "http": {"path": "/upsertPermissions", "verb": "post"}, "accepts": [{"arg": "permissionList", "type": "array", "required": true}, {"arg": "options", "type": "object"}], "returns": {"type": "array", "root": true}}}}