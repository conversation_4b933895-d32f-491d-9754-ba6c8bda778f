{"name": "Flow", "plural": "Flows", "base": "Model", "description": "Steps for card sign-up", "idInjection": false, "strict": true, "options": {}, "properties": {"at": {"type": "number", "description": "Current step (index)"}, "steps": [{"type": "string", "enum": ["request", "share", "shareoruse", "approval", "kyc", "payment", "register", "done", "update", "expended"], "description": "ordered steps for sign-up flow"}]}, "scopes": {}, "methods": {}}