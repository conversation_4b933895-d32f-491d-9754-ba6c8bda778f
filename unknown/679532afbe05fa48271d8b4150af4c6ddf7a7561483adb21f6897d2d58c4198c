{"name": "Webview", "plural": "Webviews", "base": "Model", "idInjection": false, "strict": true, "options": {}, "mixins": {}, "properties": {"id": false, "html": {"type": "string", "required": true, "description": "Html content by language code"}, "uri": {"type": "string", "description": "S3 url of rendered html"}, "resources": {"type": "array", "default": [], "description": "List of external resources for pre-fetch"}, "param": {"type": "object", "default": {}, "description": "Parameters for webview. { styles: { backgroundColor: \"rgba(0,0,0,0)\" }, props: { bounces: false }, dimension: { width: 0, height: 0 } }"}}, "methods": {}}