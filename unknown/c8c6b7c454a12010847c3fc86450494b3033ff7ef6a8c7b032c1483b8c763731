/**
 *  @module Mixin:UpdateDocument - depends on FindOneAndUpdate mixin
 */

const MONGO_OPERATORS = [ '$currentDate', '$inc', '$max', '$min', '$mul', '$rename', '$setOnInsert',
	'$set', '$unset', '$addToSet', '$pop', '$pullAll', '$pull', '$pushAll', '$push', '$bit' ]

module.exports = function(Model) {

	Model.collection = function() {
		return Model.getDataSource().connector.collection(Model.name)
	}

	/**
	 * Modifies an existing document in a collection.
	 * @param	{Object} updateData
	 * @param	{Object} opt
	 * 			{Object} hookState
	 * @return	{Promise<Model>} instance
	 * Supports 4 hooks:
	 * 		before save
	 * 		after save
	 * 		persist
	 *		loaded
	 */
	Model.prototype.updateDocument = async function(updateData = {}, opt = {}) {
		const { id } = this,
			context = {
				Model,
				where: { id: id.toString() },		// TODO: byIdQuery, getIdValue,
				data: updateData,
				currentInstance: this,
				isNewInstance: false,
				hookState: opt.hookState || {},
				options: opt,
			},
			// TODO: _sanitizeData
			// TODO: Make sure id(s) cannot be changed
			beforeSave = await Model.notifyObserversOf('before save', context),
			// TODO: applyStrictCheck
			// TODO: inst.setAttributes
			// TODO: inst.isValid
			// TODO: convertSubsetOfPropertiesByType
			// TODO: _sanitizeData

			ctx = await Model.notifyObserversOf('persist', beforeSave),
			collection = Model.collection(),
			{ where } = ctx

		// TODO: idName
		setMongoId(collection, where)

		// add data to $set
		ctx.data.$set = ctx.data.$set || {}

		const keys = Object.keys(ctx.data)

		for (let i = 0; i < keys.length; i++) {
			const key = keys[i]

			if (MONGO_OPERATORS.includes(key)) continue

			ctx.data.$set[key] = ctx.data[key]
			delete ctx.data[key]
		}
		// TODO: toDatabase

		ctx.options.returnOriginal = false

		const updated = await collection.findOneAndUpdate(where, ctx.data, ctx.options)
		ctx.data = (updated && updated.value) || null

		if (!ctx.data) throw errorIdNotFoundForUpdate(ctx.Model.name, where._id)

		const loaded = await Model.notifyObserversOf('loaded', ctx),
			{ Model: modelName, isNewInstance, hookState, options, data } = loaded,
			instance = buildInstance(data),
			contextFinal = { Model: modelName, isNewInstance, hookState, options, instance },
			afterSave = await Model.notifyObserversOf('after save', contextFinal),
			{ instance: result } = afterSave

		return result
	}

	// -----  Private functions  -----

	function buildInstance(data) {
		const options = {
			applySetters: false,
			persisted: true,
		}

		if (Model.settings.applyDefaultsOnReads === false) {
			options.applyDefaultValues = false
		}

		setModelId(data)
		return new Model(data, options)
	}

	function setMongoId(collection, filter) {
		const idName = Model.definition.idName() || 'id',
			idValue = filter[idName],
			ObjectId = collection.s.pkFactory

		if (idValue) {
			filter._id = ObjectId(idValue)
			if (idName !== '_id') delete filter[idName]
		}
	}

	function setModelId(data) {
		const idName = Model.definition.idName() || 'id',
			idValue = data._id

		if (idValue) {
			data[idName] = idValue.toString()
			if (idName !== '_id') {
				delete data._id
			}
		}
	}

	function errorIdNotFoundForUpdate(modelvalue, idValue) {
		const msg = 'No ' + modelvalue + ' found for id ' + idValue,
			error = new Error(msg)

		error.statusCode = error.status = 404
		return error
	}
}
