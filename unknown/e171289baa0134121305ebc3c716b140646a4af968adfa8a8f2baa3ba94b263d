/**
 *  @module Mixin:MainSetting - for Main Business
 */
const { Context } = require('@perkd/multitenant-context')

module.exports = function(Business) {

	Business.redisSyncSettings = async function() {
		const { Service } = Business.app.models,
			business = await Business.getMain(),
			code = Context.tenant

		for (const setting of business.settings()) {
			await Service.SETTINGS.add(code, setting)
		}
	}

	Business.addSetting = async function(setting) {
		const { Service } = Business.app.models,
			business = await Business.getMain(),
			code = Context.tenant,
			result = await business.settings.create(setting)

		await Service.SETTINGS.add(code, result.toJSON())
		return result
	}

	Business.getSettingsByName = async function(name) {
		const business = await Business.getMain()

		return business.getSettingsByName(name)
	}

	Business.removeSetting = async function(name) {
		const { Service } = Business.app.models,
			NAME = name.toLowerCase(),
			code = Context.tenant,
			business = await Business.getMain()

		await business.removeSetting(NAME)
		await Service.SETTINGS.remove(code, NAME)
	}

	Business.updateSetting = async function(name, setting) {
		try {
			const { app } = Business,
				NAME = name.toLowerCase(),
				handler = Business[`settings${name.toUpperCase()}updated`],
				hasHandler = typeof handler === 'function',
				ctx = {}

			if (hasHandler) {
				ctx.before = app.getSettings(NAME)
			}

			await Business.removeSetting(NAME)
			const { value } = await Business.addSetting(setting)

			if (hasHandler) {
				ctx.after = value
				handler(ctx)
			}
			return value
		}
		catch (err) {
			appNotify('[updateSetting]', { err, name })
		}
	}

	// -----  Remote Methods  -----

	Business.remoteMethod('redisSyncSettings', {
		description: 'Add the whole settings list of the main business to redis',
		http: { path: '/settings/redis/sync', verb: 'post' },
		returns: { type: 'object', root: true },
	})

	Business.remoteMethod('addSetting', {
		description: 'Add a setting to the main business (sync to redis)',
		http: { path: '/settings', verb: 'post' },
		accepts: {
			arg: 'setting',
			type: { name: String, value: Object },
			http: { source: 'body' },
		},
		returns: { type: 'object', root: true },
	})

	Business.remoteMethod('getSettingsByName', {
		description: 'Get settings of Main business by name',
		http: { path: '/settings/:name', verb: 'get' },
		accepts: [
			{ arg: 'name', type: 'string', http: { source: 'path' }, required: true }
		],
		returns: { type: 'object', root: true },
	})

	Business.remoteMethod('removeSetting', {
		description: 'Remove a setting of Main business (sync to redis) by name',
		http: { path: '/settings/:name', verb: 'delete' },
		accepts: [
			{ arg: 'name', type: 'string', http: { source: 'path' }, required: true }
		],
		returns: { type: 'object', root: true },
	})

	Business.remoteMethod('updateSetting', {
		description: 'Update a setting of Main business (sync to redis) by name',
		http: { path: '/settings/:name', verb: 'post' },
		accepts: [
			{ arg: 'name', type: 'string', http: { source: 'path' }, required: true },
			{ arg: 'setting', type: 'object', http: { source: 'body' }, required: true }
		],
		returns: { type: 'object', root: true },
	})
}
