/**
 *  @module Mixin:TouchPoint
 */
const { isEmptyObj } = require('@perkd/utils'),
	{ EmbedLib } = appRequire('lib/common/embedLibrary')

module.exports = function(Model, options = {}) {

	const RELATION = options.relation || 'touchPoint',
		touchLib = new EmbedLib(Model, RELATION, options, null)

	touchLib.setup()

	Model.prototype._touchPoint = function() {
		const touchPoint = this[`${RELATION}`] && this[`${RELATION}`].value()

		return touchPoint && !isEmptyObj(touchPoint) ? touchPoint : false
	}
}
