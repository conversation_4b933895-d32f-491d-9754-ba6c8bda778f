/**
*  @module Model:List
*/

const
	MAX_IDS = 5000;

module.exports = function(List) {
	// -----  Instance Methods  -----

	List.prototype.add = function(instanceId, tag) {
		const self = this,
			List = this.constructor,
			ids = self.ids.toArray();

		if (ids.length > MAX_IDS) return Promise.reject(`Maximum of ${MAX_IDS} ids exceeded`);	// TODO: watchdog!

		const updates = { $addToSet: { ids: instanceId } };
		if (tag) updates.$addToSet['tags.user'] = tag;

		return List.findOneAndUpdate({ id: self.id }, updates);
	};

	List.prototype.pull = function(instanceId) {
		const self = this,
			List = this.constructor;

		const updates = { $pull: { ids: instanceId } };

		return List.findOneAndUpdate({ id: self.id }, updates);
	};

	// -----  Validation  -----
	List.validatesUniquenessOf('name'); // List Name must be unique

	// ---  Unused Remote Methods  ---

	// List.disableRemoteMethodByName('create');
	// List.disableRemoteMethodByName('upsert');
	List.disableRemoteMethodByName('updateAll');
	List.disableRemoteMethodByName('patchOrCreate');
	List.disableRemoteMethodByName('findOrCreate');
	// List.disableRemoteMethodByName('prototype.patchAttributes');

	List.disableRemoteMethodByName('createChangeStream');

	List.disableRemoteMethodByName('confirm');
	List.disableRemoteMethodByName('exists');

	List.disableRemoteMethodByName('replaceById');
	List.disableRemoteMethodByName('replaceOrCreate');

	List.disableRemoteMethodByName('upsertWithWhere');
};
