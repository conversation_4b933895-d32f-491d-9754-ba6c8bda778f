/**
 *  @module Mixin:ApiTracker
 */

const { Context } = require('@perkd/multitenant-context'),
	{ bm } = require('@perkd/utils')

module.exports = function(Model, options) {
	// -----  Remote & Operation hooks  -----
	//	doc:  https://loopback.io/doc/en/lb3/Remote-hooks.html
	const { methods } = { methods: [], ...options }

	methods.forEach(pattern => {
		Model.beforeRemote(pattern, async ctx => {
			ctx._benchmark = { start: new Date(), ts: bm.mark() }
		})

		Model.afterRemote(pattern, async ctx => {
			const { methodString, req, args, result, _benchmark } = ctx || {}, { headers, originalUrl } = req || {},
				{ start, ts } = _benchmark || {},
				isAppApi = originalUrl.includes('/app/'),
				{ personId, id: installId } = isAppApi ? Context.installation : {},
				latency = ts && bm.diff(ts),
				end = new Date()

			appLog(`afterRemote:${methodString}`, { latency, personId, installId, start, end, originalUrl, headers, args, result })

			// if (!isProduction()) {
			// 	appEcho(`[ApiTracker] ✅ ${Context.tenant}:${originalUrl} %j`.green, { latency, personId, installId, start, end, originalUrl, methodString, headers, args, result });
			// 	if (isAppApi && originalUrl !== '/api/Cache/app/sync') {
			// 		appNotify(`${originalUrl} - ${latency} ms`, { personId, installId, start, end, args, result }, 'done', '-appapi');
			// 	}
			// }
		})

		Model.afterRemoteError(pattern, async ctx => {
			const { methodString, req, args, result, _benchmark, error } = ctx || {}, { headers, originalUrl } = req || {},
				{ start, ts } = _benchmark || {},
				isAppApi = originalUrl.includes('/app/'),
				{ personId, id: installId } = isAppApi ? Context.installation : {},
				latency = ts && bm.diff(ts),
				end = new Date()

			appLog(`afterRemote:${methodString}`, { error, latency, personId, installId, start, end, originalUrl, headers, args, result }, 'error')

			// if (!isProduction()) {
			// 	appEcho(`[ApiTracker] ❌ ${Context.tenant}:${originalUrl} %j`.red, { latency, personId, installId, start, end, originalUrl, methodString, headers, args, result, error });
			// 	if (isAppApi && originalUrl !== '/api/Cache/app/sync') {
			// 		appNotify(`${originalUrl} - ${latency} ms`, { personId, installId, start, end, args, result, err: error }, 'error', '-appapi');
			// 	}
			// }
		})
	})
}

/**
 * End of script
 */
