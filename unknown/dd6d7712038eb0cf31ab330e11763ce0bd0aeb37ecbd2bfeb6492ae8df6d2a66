/**
 *  @module Mixin:Share - CRM
 */
const { pickObj, ObjectId } = require('@perkd/utils'),
	{ Wallet } = require('@crm/types'),
	{ SHARE: SHARE_ERR } = require('@perkd/errors/dist/service')

const { Share, Notify } = Wallet,
	{ INVITE, CLONE, TRANSFER, SEND } = Share.Mode,
	{ TRANSFERRING } = Share.State,				// state of origin after sharing by 'transfer'
	{ SHARE } = Notify.Action,
	{ SHARE_MODE_NOT_CONFIG, SHARE_MAXIMUM_LIMIT, SHARE_MEMBERSHIP_NOT_FOUND } = SHARE_ERR,
	DEFAULT_TTL = 30 * 24 * 60 * 60 * 1000		// 30 days (in ms)

module.exports = function(Model) {

	Model.SHARE = { INVITE, CLONE, TRANSFER, SEND }

	// -----  Instance Methods  -----

	/**
	 * Create shared object 	(called by .requestOffer())
	 * @param	{Membership} membership
	 * @param	{Object} share - perkd share
	 *			{String} mode
     *			{String} cardId - of shared's card
     *			{String} sharerId - personId (Perkd) of the sharer
	 *			{String} originId - offerId (Perkd) of the originating offer (used for finding Sharing instance to update later)
	 *          {String} sharingId - perkd sharing
     *			{Boolean} queue - true => queue request if cardId not found, eg. when sharing offers (default: TRUE)
	 * @param	{Object} origin CRM instance  (when called by issuePending())
	 * @param	{Array} total recipient's objects already hv
	 * @return	{Object} shared instance, null if queued
	 */
	Model.prototype.createShared = async function(membership, share, origin, total = 0) {
		const { id, sharePolicies } = this,
			{ Pending } = Model.app.models,
			{ mode, cardId, sharerId, sharerName, sharerImageUrl, originId, sharingId, queue = true } = share,
			policy = sharePolicies.find(({ mode: policyMode }) => policyMode === mode),
			{ max } = policy,
			through = {
			// TODO:	need to add perkdOriginId here, so that Perkd can link to sharing
			},
			notification = { action: SHARE }

		if (!policy) {
			return this.rejectErr(SHARE_MODE_NOT_CONFIG, { membership, share, origin, total }, true)
		}
		if (membership) {
			const { personId, memberId, id: membershipId } = membership,
				sharer = {
					personId: origin.personId,
					originId: origin.id.toString(),
					sharingId: ObjectId().toString(),
					generation: origin.sharer ? origin.sharer.generation + 1 : 1,
				},
				generation = sharer.generation,
				shareModes = (max?.generation <= generation) ? [] : origin.shareModes,
				perkdSharer = {
					mode, originId, sharingId, generation,
					personId: sharerId, name: sharerName, imageUrl: sharerImageUrl
				}

			if (max.perPerson !== null && total >= max.perPerson) {
				return this.rejectErr(SHARE_MAXIMUM_LIMIT, { membership, share, origin, total, policy }, true)
			}

			switch (mode) {
			case INVITE:
				return this.issue(membership, 1, { through, notification })
					.then(shared => shared[0].updateAttributes({ sharer }))

			case SEND:
			case TRANSFER: {
				const newData = Model.cloneToShare(origin, { sharer, shareModes, personId, memberId, membershipId }),
					changes = { state: TRANSFERRING, 'when.transferred': new Date() }

				origin.updateAttributes(changes)
				return Model.issueCloned(membership, newData, { perkdSharer, notification })
			}

			case CLONE: {
				const data = Model.cloneToShare(origin, { sharer, shareModes, personId, memberId, membershipId })
				return Model.issueCloned(membership, data, { perkdSharer, notification })
			}

			default:
				console.error(`Share mode '${mode}' not supported`)
				return Promise.reject(`Share mode '${mode}' not supported`)
			}
		}

		if (!queue) {
			return this.rejectErr(SHARE_MEMBERSHIP_NOT_FOUND, { membership, share, origin, total }, true)
		}

		// no membership & person yet, card pre-issued in X
		const modelName = Model.name,
			masterId = id.toString(),
			purgeTime = new Date(new Date().getTime() + (policy.ttl || DEFAULT_TTL))

		await Pending.create({ mode, modelName, masterId, cardId, sharerId, sharerName,
			sharerImageUrl, originId, sharingId, through, purgeTime })
	}

	/**
     * Cancel offer share
	 * @param	{String} mode - share mode
	 * @param	{String} originId - of sharer's offer (Perkd)
	 * @param	{String} sharingId - of sharing (Perkd)
	 * @param	{String} sharedId - of shared's offer (Perkd)
	 * @param	{Object} opts
	 * @return	{Object} ignore - origin or null
     */
	Model.prototype.cancelShared = async function(mode, originId, sharingId, sharedId, opts = {}) {
		const { models } = Model.app,
			{ Pending, Sharing } = models,
			NOW = new Date()

		if (sharedId) {
			const shared = await this.findInstanceByDigitalId(sharedId),
				filter = {
					where: { sharedId: String(shared.id) }
				}

			const [ sharing ] = await Promise.all([
					Sharing.findOne(filter),
					shared.cancel()
				]),
				updates = { when: { ...sharing.when, cancelled: NOW } }

			sharing.updateAttributes(updates)
				.catch( err => {
					console.log('Error [cancelShared]', { err, sharingId: sharing.id })
				})

			if (mode === TRANSFER || mode === SEND) {
				const { sourceType, sourceId } = sharing,
					origin = await models[`${sourceType}`].findById(sourceId)

				return origin.recall(opts)
			}

			Promise.resolve(null)
		}

		if (sharingId) {
			const filter = {
					where: { sharingId }
				},
				pending = await Pending.findOne(filter)

			Pending.deleteById(pending.id)
			purgeExpiredPendings()		// housekeeping
		}

		if (mode === TRANSFER || mode === SEND) {
			const origin = await this.findInstanceByDigitalId(originId)
			return origin.recall({ ...opts, sharingId })
		}
	}

	// -----  Private Methods  -----

	/**
	 * Issue pending shared instance  (called by eventHandler receiving 'registerCard' events with 'sharer')
	 * Must NOT throw
	 * @param	{String} cardId - of shared card that just been registered
	 * @return	{Object} shared instance, null if queued
	 */
	Model.issuePending = async function(cardId) {
		const { app, name: modelName } = Model,
			{ Pending, Membership } = app.models,
			filter = {
				where: {
					modelName,
					cardId,
					purgeTime: { gte: new Date() },
				},
			},
			[ membership, pendings ] = await Promise.all([
				Membership.findActiveByCardId(cardId),
				Pending.find(filter),
			])

		if (!membership) {
			appNotify('[issuePending] membership_not_found', { cardId })
			return
		}

		const { personId } = membership,
			instances = []

		for (const pending of pendings) {
			const { mode, masterId, sharerId, sharerName, sharerImageUrl, originId, sharingId, through } = pending,
				share = { mode, cardId, sharerId, sharerName, sharerImageUrl, originId, sharingId, through },
				person = { id: personId },	// only id is required for recipient
				master = await Model.getInstance(masterId),
				origin = await master.findInstanceByDigitalId(originId),
				shared = await master.createShared(membership, share, origin)

			instances.push(shared)
			await Model.createSharing(origin, mode, shared, person, shared.sharer)

			Pending.deleteById(pending.id)
			purgeExpiredPendings()	// housekeeping
		}

		return instances
	}

	/**
	 * Cancel pending things
	 * @param	{String} cardId - of shared card
	 * @param	{String} opts
	 * @return	{Promise}
	 */
	Model.cancelPending = async function(cardId, opts) {
		const { models } = Model.app,
			{ Pending } = models,
			filter = {
				where: {
					modelName: Model.name,
					cardId,
					purgeTime: { gte: new Date() },
				},
			},
			pendings = await Pending.find(filter)

		for (const pending of pendings) {
			const { modelName, masterId, mode, originId, sharingId } = pending,
				master = await models[`${modelName}`].findById(masterId)

			await master.cancelShared(mode, originId, sharingId, null, opts)
		}
	}

	Model.deletePending = async function(cardId) {
		const { Pending } = Model.app.models,
			where = {
				modelName: Model.name,
				cardId: cardId || null,
			}

		await Pending.deleteAll(where)
		purgeExpiredPendings()		// housekeeping
	}

	Model.cloneToShare = function(original, updates) {
		return { ...pickObj(original, Model.CLONED_PROPERTIES), ...updates }
	}

	/**
	 * Create sharing transaction
	 * @param	{Object} origin instance
	 * @param	{String} mode of sharing
	 * @param	{Object} shared instance - 'null' when sharing non-card & pending (ie. queued in CRM)
	 * @param	{Person} person recipient
	 * @param	{Sharer} sharer
	 * @param	{String} channel
	 * @return	{Object} shared instance - null if pending
	 */
	Model.createSharing = async function(origin, mode, shared, person, sharer, channel) {
		const { id: personId } = person,
			{ id: sharedId } = shared || {},
			{ sharingId, originId, generation } = sharer,
			sharing = {
				id: sharingId,
				mode,
				recipient: {
					personId,
					// mobile: person.phoneList[0].fullNumber,	// not necessary in CRM
					// name: person.fullName.slice(0, 80),
				},
				originId,
				sharedId,
				generation,
				channel,
				when: { received: new Date() },
				// purgeTime:  not applicable for CRM
			}

		return origin.sharings.create(sharing)
	}

	// -----  Private functions  -----

	function purgeExpiredPendings() {
		return Model.app.models.Pending.deleteAll({ purgeTime: { lt: new Date() } })
			.then(({ count }) => appEcho(`Housekeeping: ${count} pendings removed\t${new Date()}`))
	}

	// -----  Remote Methods  -----

	Model.remoteMethod('prototype.share', {
		description: 'Share (app API)',
		http: { path: '/app/share', verb: 'post' },
		accepts: [
			{ arg: 'recipient', type: 'object', required: true, description: '{ personId, mobile }' },
			{ arg: 'mode', type: 'string', required: true, enum: [ 'invite', 'clone', 'transfer' ] },
			{ arg: 'options', type: 'object', description: '{ channel }' },
		],
		returns: { type: 'object', root: true },
	})
}
