/**
 *  @module GroupData
 */

module.exports = function(Group) {
	/**
	 * Fetch group data chunks & apply fn on each chunk serially
	 * @param	{Function} fn - async handler function
	 * @return	{Promise<void>}
	 */
	Group.prototype.eachChunk = async function(fn) {
		const { id: groupId } = this,
			{ GroupData } = Group.app.models,
			filter = {
				where: { groupId },
				fields: { id: true },	// retrieve minimal fields for speed
				order: 'chunk ASC'
			},
			chunks = await GroupData.find(filter)

		for (const { id } of chunks) {
			const chunk = await GroupData.findById(id)

			await fn(chunk).catch(err => console.error('[eachChunk]', err))
		}
	}
}
