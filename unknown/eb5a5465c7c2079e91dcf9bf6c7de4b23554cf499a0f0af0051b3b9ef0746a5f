# CRM & Perkd Platform Microservices

## Table of Contents
1. [Overview](#overview)
2. [Core Technologies](#core-technologies)
3. [Multi-tenancy](#multi-tenancy)
4. [Event System](#event-system)
5. [API Layer](#api-layer)
6. [Configuration Management](#configuration-management)
7. [User Management](#user-management)
8. [Internationalization](#internationalization)
9. [Monitoring and Observability](#monitoring-and-observability)
10. [Key Dependencies](#key-dependencies)
11. [Deployment](#deployment)

## Overview
All CRM & Perkd Platform microservices are Node.js-based multi-tenant services built using the LoopBack framework supporting both REST API and event-driven architecture.

```mermaid
graph TD
	subgraph Clients
		C[Web, Mobile & API Clients]
	end

	subgraph Gateway Layer
		G[API Gateway]
	end

	subgraph AL[Application Layer]

    	subgraph Config[Configuration Management]
			FM[Providers Config]
			TC[Tenant Config]
			DC[Dynamic Settings]
		end

		subgraph Auth[Identity & Auth]
			AA[Authentication]
			AZ[Authorization]
			SM[Session Mgmt]
		end

        subgraph API[API Services]
			BL[Business Logic]
			TI[Tenant Isolation]
			DM[Data Models]
		end
	end

	subgraph Event System
		ES[Event Bus]
	end

	subgraph Data Layer
		SM1[Secrets Manager]
		Redis[(Redis)]
		MongoDB[(MongoDB)]
	end

	C --> G
	G --> API
	G --> Auth
	G --> Config

	AL <--> MongoDB
	AL <--> Redis
	Auth --> SM1

	AL <--> ES

	classDef client fill:#1a365d,stroke:#333,stroke-width:2px,color:#fff
	classDef gateway fill:#b7791f,stroke:#333,stroke-width:2px,color:#fff
	classDef service fill:#1e40af,stroke:#333,stroke-width:2px,color:#fff
	classDef subservice fill:#3b82f6,stroke:#333,stroke-width:2px,color:#fff
	classDef data fill:#166534,stroke:#333,stroke-width:2px,color:#fff
	classDef events fill:#9f1239,stroke:#333,stroke-width:2px,color:#fff

	class C client
	class G gateway
	class API,Auth,Config service
	class BL,TI,DM,AA,AZ,SM,FM,TC,DC subservice
	class MongoDB,Redis,SM1 data
	class ES events
```

Key architectural components:
- **Gateway Layer**: API Gateway with built-in WAF and security features
- **Application Layer**: 
  - API Services: Core business logic and data operations with tenant isolation
  - Identity & Auth: User authentication, authorization, and session management
  - Config Service: Dynamic configuration and feature management per tenant
- **Data Layer**: MongoDB for tenant data, Redis for caching and events
- **Event System**: Redis Streams-based event bus for service communication


## Core Technologies
- Node.js (>=20)
- LoopBack 3.x with @crm/loopback extensions
- MongoDB 5.x (via loopback-connector-mongodb)
- Redis 7.x (for Event Bus and distributed locking)
- Redlock for distributed synchronization/locking
- Node-Cache for local caching
- Docker


## Multi-tenancy
The platform implements complete tenant isolation through database-level separation. Each tenant operates in its own dedicated MongoDB database, with tenant identification handled through JWT tokens and request context. The tenant context is automatically propagated across service boundaries, ensuring consistent tenant isolation throughout the entire request lifecycle.

### Core Components
1. **Tenant Context Management**
   - `multitenant` middleware for tenant identification and context setup
   - Token-based authentication with LRU cache (15-minute TTL)
   - Direct tenant code support via headers or query parameters
   - Automatic context propagation via Loopback Context

2. **Connection Management**
   - `multitenant-ds` middleware for proactive connection establishment
   - Connection manager initialization with 30-second timeout and 3 retries
   - Dynamic pool creation with configurable size limits
   - Health monitoring with ping validation
   - Automatic cleanup of idle connections

3. **Model Integration**
   - `Multitenant` mixin for MongoDB connection handling
   - Automatic transaction management
   - Session pooling with cleanup
   - Connection validation and health checks
   - Helper methods for transaction and connection management

### Connection Manager Initialization
The Connection Manager is initialized when the first model with the Multitenant mixin is attached to the app:

1. **Initialization Process**
   - Triggered in the `attached` event of the Multitenant mixin
   - Creates a singleton Connection Manager instance at the app level
   - Configures connection factory with tenant-specific settings
   - Sets up model tracking for tenant datasource attachment

2. **Model Registration**
   ```mermaid
   sequenceDiagram
       participant Model
       participant Mix as Multitenant Mixin
       participant CM as Connection Manager
       participant DS as Datasources
       
       Note over Model,DS: Model Attachment Phase
       
       Model->>Mix: Apply Mixin
       Mix->>Mix: Model.on('attached')
       
       alt First Tenant Model
           Mix->>Mix: Track Model
           
           Mix->>CM: Create & Configure
           CM->>CM: Initialize Factory
           
           activate CM
           Note over CM: Async Initialization (30s timeout, 3 retries)
           CM-->>Mix: Manager Ready
           deactivate CM
       else Subsequent Models
           Mix->>Mix: Track Model
       end
       
       Note over Mix,DS: Connection Creation
       
       Mix->>CM: Request Connection
       CM->>DS: Create Tenant DS
       DS->>DS: Configure & Attach Models
   ```

3. **Connection Factory**
   - Creates new datasource for each tenant
   - Inherits base settings from trap datasource
   - Configures tenant-specific database URL
   - Applies tenant-specific pool size limits
   - Attaches all registered tenant models

4. **Considerations**
   - Single Connection Manager instance per app
   - Models must be registered before tenant connections
   - Initialization is asynchronous with timeout/retry
   - Models share the same connection pool per tenant
   - Connection validation and cleanup handlers

### Authentication Flow
```mermaid
sequenceDiagram
    participant C as Client
    participant M as Multitenant Middleware
    participant Cache as Token Cache
    participant CM as Connection Manager
    participant DB as MongoDB
    
    C->>M: Request with JWT/Tenant Code
    
    alt JWT Token
        M->>Cache: Check Token Cache
        alt Cache Hit
            Cache-->>M: Return Tenant Info
        else Cache Miss
            M->>M: Validate JWT
            M->>Cache: Store Token Info (15m TTL)
        end
    else Direct Tenant Code
        M->>M: Extract from Headers/Query
    end
    
    M->>CM: Ensure Connection
    CM->>DB: Connect to Tenant DB
    DB-->>C: Response
```

### Connection Architecture
```mermaid
graph TD
    subgraph Request_Pipeline
        M1[Multitenant Middleware]
        M2[Multitenant-DS Middleware]
        Mix[Multitenant Mixin]
    end

    subgraph Connection_Management
        CM[Connection Manager]
        PM[Pool Manager]
        SM[Session Manager]
    end

    subgraph Resource_Control
        Val[Connection Validation]
        Clean[Connection Cleanup]
        Health[Health Checks]
        Trans[Transaction Control]
    end

    subgraph Tenant_Data
        DS[Tenant Datasource]
        Pool[Connection Pool]
        DB[(MongoDB)]
    end

    M1 -->|Set Context| M2
    M2 -->|Ensure Connection| CM
    Mix -->|Get Connection| CM
    
    CM -->|Manage| PM
    CM -->|Control| SM
    
    PM -->|Create/Get| Pool
    PM -->|Monitor| Health
    PM -->|Cleanup| Clean
    
    SM -->|Handle| Trans
    
    Pool -->|Connect| DB
    Pool -->|Validate| Val
    DS -->|Use| Pool
```

### System Configuration
```typescript
interface ConnectionConfig {
	minPoolSize: number        // Default: 2
	maxPoolSize: number        // Default: 10
	maxIdleTime: number        // 5 minutes
	acquireTimeout: number     // 30 seconds
	cleanupInterval: number    // 60 seconds
	monitorInterval: number    // 30 seconds
	initTimeout: number        // 30 seconds
	maxRetries: number         // 3
	retryDelay: number         // 1 second
}

interface SessionConfig {
	validationInterval: number   // 30 seconds
	maxTransactionRetries: number // 3
	transactionTimeout: number   // 30 seconds
	mode: 'auto' | 'enabled' | 'disabled'
}

interface ValidationConfig {
	healthCheckInterval: number  // 30 seconds
	maxErrorThreshold: number    // 5
	maxActiveRequests: number    // 20
}

interface CleanupConfig {
	idleTimeout: number        // 5 minutes
	sessionTimeout: number     // 10 minutes
	maxSessionAge: number      // 1 hour
}
```

### Key Features
1. **Tenant Isolation**
   - Database-level separation
   - Connection pool isolation
   - Transaction isolation
   - Session management

2. **Resource Management**
   - Dynamic pool sizing
   - Connection health monitoring
   - Automatic cleanup
   - Usage tracking

3. **Error Handling**
   - Connection validation
   - Transaction rollback
   - Session cleanup
   - Tenant isolation errors

4. **Performance Optimization**
   - Token caching (15-minute TTL)
   - Connection pooling
   - Session reuse
   - Health monitoring

### Request Pipeline
```mermaid
sequenceDiagram
    participant C as Client
    participant M1 as Multitenant Middleware
    participant Cache as Token Cache
    participant M2 as Multitenant-DS Middleware
    participant CM as Connection Manager
    participant DS as Tenant Datasource
    participant DB as MongoDB
    
    C->>M1: Request with JWT/Tenant Code
    
    alt JWT Token
        M1->>Cache: Check Token Cache
        alt Cache Hit
            Cache-->>M1: Return Tenant Info
        else Cache Miss
            M1->>M1: Validate JWT
            M1->>Cache: Store Token Info (15m TTL)
        end
    else Direct Tenant Code
        M1->>M1: Extract from Headers/Query
    end
    M1->>M1: Set Tenant Context
    
    alt Invalid Context
        M1-->>C: Error: Invalid Tenant Context
    else Valid Context
        M1->>M2: Forward Request
        
        M2->>CM: Check Connection
        alt Not Initialized
            M2->>CM: Wait for Init (30s timeout)
            alt Success
                CM-->>M2: Manager Ready
            else Timeout
                M2-->>C: Error: Init Failed
            end
        end

        M2->>CM: Get/Create Connection
        alt Success
            CM->>DS: Get/Create Pool
            DS->>DB: Execute Query
            DB-->>C: Response
        else Error
            CM-->>M2: Connection Error
            M2-->>C: Error: Connection Failed
        end
    end
```

1. **Authentication & Context**
   - JWT token or tenant code validation
   - Tenant context extraction and validation
   - User context propagation
   - `multitenant` middleware

2. **Connection Management**
   - Proactive connection establishment in middleware
   - Connection manager initialization check
   - Pool health monitoring and validation
   - Automatic resource cleanup
   - `multitenant-ds` middleware & `Multitenant` mixin

### Connection Architecture

```mermaid
graph TD
    subgraph Connection_Management
        CM[Connection Manager]
        PM[Pool Manager]
        SM[Session Manager]
        VS[Validation Service]
        CS[Cleanup Service]
    end

    subgraph System_Control
        SL[System Load]
        MP[Memory Pressure]
        CP[CPU Usage]
        Cache[Token Cache]
    end

    subgraph Connection_Control
        PS[Pool Management]
        subgraph Health
            HC[Health Checks]
            MR[Metrics & Recovery]
        end
        subgraph Isolation
            CI[Connection Isolation]
            SI[Session Isolation]
            TI[Transaction Isolation]
        end
    end

    subgraph Database
        MC[(MongoDB Connection)]
    end

    CM -->|Manages| PM
    CM -->|Uses| SM
    CM -->|Validates| VS
    CM -->|Schedules| CS
    PM -->|Reports Status| CM
    SM -->|Reports Status| CM
    VS -->|Reports Status| CM

    SL & MP & CP -->|Influences| PS
    Cache -->|Optimizes| CM
    PS -->|Configures| PM
    PS -->|Configures| SM

    PM -->|Manages| MC
    SM -->|Uses| MC
```

The multi-tenancy implementation is built on a robust connection management architecture that ensures proper tenant isolation, efficient resource utilization, and reliable database operations.

It is implemented through:
- `multitenant` middleware for tenant context management
- `multitenant-ds` middleware for proactive connection establishment
- `Multitenant` mixin with integrated connection management
- Centralized `ConnectionManager` for efficient resource handling

Key components:
1. **ConnectionManager**
   - Central coordinator for all tenant connections
   - Manages initialization and lifecycle
   - Coordinates with specialized services

2. **PoolManager**
   - Handles connection pool lifecycle
   - Creates and maintains tenant-specific pools
   - Implements pool validation and cleanup

3. **SessionManager**
   - Manages database sessions
   - Handles transaction management
   - Implements session cleanup

4. **ValidationService**
   - Ensures tenant isolation
   - Validates connections
   - Maintains validation cache

5. **CleanupService**
   - Performs periodic cleanup
   - Manages cleanup tasks
   - Tracks cleanup metrics

### Data Management

1. **Storage**
   - Database-level tenant isolation
   - Tenant-specific encryption
   - Automatic connection management
   - Health monitoring and recovery

2. **Caching**
   - Redis-based distributed caching
   - Tenant-aware cache invalidation
   - Automatic cache cleanup

3. **Event Handling**
   - Redis Streams for event distribution
   - Tenant context propagation
   - Automatic event cleanup

### Security

1. **Access Control**
   - Tenant context validation
   - Session ownership verification
   - Resource isolation enforcement

2. **Monitoring**
   - Resource usage tracking
   - Health status monitoring
   - Error detection and reporting

3. **Resource Protection**
   - Memory pressure monitoring
   - Connection pool management
   - Automatic resource cleanup


## Event System

```mermaid
graph TD
    subgraph CRM
        S1[Service 1]
        S2[Service 2]
    end
    
    subgraph Perkd Platform
        S3[Service 1]
        S4[Service 2]
    end
    
    EB1[Event Bus CRM]
    EB2[Event Bus Perkd]
    
    Bridge[Event Bridge]
    
    S1 -->|Publish| EB1
    S2 -->|Publish| EB1
    S3 -->|Publish| EB2
    S4 -->|Publish| EB2
    
    EB1 -->|Subscribe| S1
    EB1 -->|Subscribe| S2
    EB2 -->|Subscribe| S3
    EB2 -->|Subscribe| S4
    
    EB1 <-->|Cross-Platform Events| Bridge
    EB2 <-->|Cross-Platform Events| Bridge
    
    classDef crmService fill:#1a365d,stroke:#333,stroke-width:2px,color:#fff
    classDef perkdService fill:#b7791f,stroke:#333,stroke-width:2px,color:#fff
    classDef stream fill:#1e40af,stroke:#333,stroke-width:2px,color:#fff
    classDef bridge fill:#dc2626,stroke:#333,stroke-width:2px,color:#fff
    
    class S1,S2 crmService
    class S3,S4 perkdService
    class EB1,EB2 stream
    class Bridge bridge
```

### Event Bus
- Uses Redis Streams for tenanted event handling (`@perkd/eventbus`)
- Configurations in `config/eventbus.json`:
  - Event subscriptions and publications (tenanted)
  - Event mapping and transformation
  - Service-specific event size limits and retention policies (MAXLEN: 1000, LIMIT: 100)


## API Layer
- REST API with comprehensive model definitions
- API versioning support
- Compression middleware
- Security
	- CORS protection
	- nocache middleware
	- Rate limiting (per tenant)
	- Request validation with tenant context
	- Automatic tenant database routing
- Development
    - API Explorer
    - OpenAPI/Swagger documentation

### API Structure
```mermaid
graph TD
    subgraph API[Model Endpoints]
        B[Endpoint 1]
        S[Endpoint 2]
        O[Endpoint 3]
        M[Endpoint 4]
    end
    
    subgraph Auth[Authentication]
        JWT[JWT Token]
        TC[Tenant Context]
    end
    
    subgraph Security[Security Layer]
        CORS[CORS Protection]
        Rate[Rate Limiting]
        Val[Request Validation]
        TI[Tenant Isolation]
    end
    
    Auth --> Security
    Security --> API
    
    classDef api fill:#1a365d,stroke:#333,stroke-width:2px,color:#fff
    classDef auth fill:#b7791f,stroke:#333,stroke-width:2px,color:#fff
    classDef sec fill:#1e40af,stroke:#333,stroke-width:2px,color:#fff
    
    class B,S,O,M,P api
    class JWT,TC auth
    class CORS,Rate,Val,TI sec
```

### Authentication
- JWT-based authentication with tenant context
- Methods:
    1. Bearer token in the Authorization header
    2. X_ACCESS_TOKEN in headers
    3. `tenant-code` in headers or query parameters
- Context propagation via Loopback Context
- Tenant isolation through middleware

```mermaid
sequenceDiagram
    participant C as Client
    participant M as Multitenant Middleware
    participant L as Loopback Context
    participant S as Service
    
    C->>M: Request with JWT/Tenant Code
    M->>M: Extract from Headers/Query
    M->>L: Set Tenant Context
    M->>L: Set User Context
    L-->>S: Propagate Context
    S-->>C: API Response

    Note over M,L: JWT validation & context extraction<br/>happens in middleware
```


## Configuration Management

```mermaid
graph TD
    subgraph Config Types
        T[Tenant Config]
        S[Settings]
        P[Provider Config]
    end
    
    subgraph Storage
        R[(Redis)]
        M[(MongoDB)]
        SM[Secrets Manager]
    end
    
    T -->|Platform-wide| R
    S -->|Global & Tenant| R
    P -->|Shared & Tenanted| R
    P -->|Instance-specific| M
    P -.->|Credentials| SM
    
    classDef config fill:#1a365d,stroke:#333,stroke-width:2px,color:#fff
    classDef storage fill:#b7791f,stroke:#333,stroke-width:2px,color:#fff
    
    class T,S,P config
    class R,M,SM storage
```

### Tenant Management
- Platform-wide tenant-specific configurations, stored in Redis
- Dynamic tenant provisioning and management
  - Automatic database creation and initialization
  - Tenant database connection management
- Tenant-level feature toggles and customizations
- Updates synchronized across all service instances via Redis pub/sub
- Access via `@perkd/tenant`

### Settings Management
- Global and tenant-specific settings, stored in Redis
- Configuration versioning
- Dynamic configuration updates
- Environment-specific configurations
- Updates synchronized across all service instances via Redis pub/sub
- Access via `@perkd/settings`

### Provider Management
- Provider configuration and credentials
- Configurable provider enablement per business entity
- Types of providers:
  1. Shared - platform-wide, shared among tenants (eg. AWS, Google)
  2. Tenanted - tenant-specific, separately configured for each tenant (eg. Shopify)
  3. Instance - data model instance-specific (eg. payment wallet for each membership)
- Shared and Tenanted configuration stored in Redis
- Instance configuration stored in tenant's dedicated MongoDB database
- Credentials are stored in secrets manager
- Updates synchronized across all service instances via Redis pub/sub
- Access via `@perkd/providers`


## User Management
- User authentication and authorization
- User profile management
- Session handling
- Access Control
	- Role-based access control (RBAC)
	- Permission management
	- Dynamic policy evaluation


## Internationalization
  - Multi-language support
    - English
    - Chinese (Traditional & Simplified)
    - Korean
    - Japanese
    - Malay
    - Indonesian
  - Locale-specific formatting
  - Timezone management


## Monitoring and Observability

```mermaid
graph TD
    subgraph Services
        App[Application]
        Health[Health Check]
        Metrics[Metrics Collector]
    end

    subgraph Logging
        Winston[Winston Logger]
        Format[Log Formatter]
    end

    subgraph Mission Control
        Prom[Prometheus]
        Graf[Grafana]
    end

    subgraph AWS
        CW[CloudWatch]
        Logs[Log Streams]
        Alarms[Alarms]
        Dashboard[Dashboards]
    end

    App -->|Health Status| Health
    App -->|Metrics| Metrics
    App -->|Logs| Winston
    Winston -->|Format| Format
    Health -->|Report| CW
    Metrics -->|Push| Prom
    Prom -->|Visualize| Graf
    Format -->|Stream| Logs
    CW -->|Trigger| Alarms
    CW -->|Visualize| Dashboard

    classDef service fill:#1a365d,stroke:#333,stroke-width:2px,color:#fff
    classDef logging fill:#b7791f,stroke:#333,stroke-width:2px,color:#fff
    classDef mission fill:#1e40af,stroke:#333,stroke-width:2px,color:#fff
    classDef aws fill:#166534,stroke:#333,stroke-width:2px,color:#fff

    class App,Health,Metrics service
    class Winston,Format logging
    class Prom,Graf mission
    class CW,Logs,Alarms,Dashboard aws
```

- Metrics collection and visualization
	- @perkd/metrics for collection
	- Prometheus for metrics storage
	- Grafana for metrics visualization
- Logging
	- Winston with CloudWatch integration
	- Tenant-specific log streams
	- Structured logging format
- Health monitoring
	- Health check endpoints (/health)
	- Service status reporting to CloudWatch
	- Dependency health checks


## Key Dependencies
- Core Framework
	- @crm/loopback: Extended LoopBack framework
	- @crm/types: TypeScript type definitions
- Infrastructure
	- @perkd/eventbus: Event handling
	- @perkd/errors: Error standardization
	- @perkd/metrics: Metrics collection
	- @perkd/settings: Configuration management
- Business Logic
	- @perkd/commerce: Commerce operations
	- @perkd/fulfillments: Order fulfillment
	- @perkd/provisions: Service provisioning
	- @perkd/sync: Data synchronization
- Utilities
	- @perkd/utils: Common utilities
	- @perkd/activity-registry-crm: Activity tracking
	- @perkd/event-registry-crm: Event definitions


## Deployment

### Architecture
```mermaid
graph TD
    subgraph Clients
        C[Web, Mobile & API Clients]
    end

    subgraph Gateway
        G[API Gateway & WAF]
    end

    subgraph Core Services
        CS[CRM & Perkd]
        IS[Identity & Config]
    end

    subgraph Data & Events
        DB[(Databases)]
        ES[Event System]
    end

    C --> G
    G --> CS
    G --> IS
    CS <--> DB
    IS <--> DB
    CS <--> ES
    IS <--> ES

    classDef client fill:#1a365d,stroke:#333,stroke-width:2px,color:#fff
    classDef gateway fill:#b7791f,stroke:#333,stroke-width:2px,color:#fff
    classDef service fill:#1e40af,stroke:#333,stroke-width:2px,color:#fff
    classDef data fill:#166534,stroke:#333,stroke-width:2px,color:#fff

    class C client
    class G gateway
    class CS,IS service
    class DB,ES data
```

### Deployment Pipeline

```mermaid
graph TD
    subgraph GitHub
        Code[Source Code]
        Action[GitHub Actions]
        Tests[Tests]
    end

    subgraph AWS
        subgraph ECS
            Service[ECS Service]
            Task[Task Definition]
            Container[Container]
        end

        subgraph Network
            ALB[Load Balancer]
            TG[Target Group]
            VPC[VPC]
        end

        subgraph Storage
            ECR[Container Registry]
            S3[S3 Artifacts]
        end
    end

    Code -->|Trigger| Action
    Action -->|Run| Tests
    Tests -->|Push| ECR
    ECR -->|Pull| Container
    Container -->|Run in| Task
    Task -->|Managed by| Service
    Service -->|Register with| TG
    TG -->|Route via| ALB
    VPC -->|Contains| Service

    classDef github fill:#1a365d,stroke:#333,stroke-width:2px,color:#fff
    classDef ecs fill:#b7791f,stroke:#333,stroke-width:2px,color:#fff
    classDef network fill:#1e40af,stroke:#333,stroke-width:2px,color:#fff
    classDef storage fill:#166534,stroke:#333,stroke-width:2px,color:#fff

    class Code,Action,Tests github
    class Service,Task,Container ecs
    class ALB,TG,VPC network
    class ECR,S3 storage
```

### Infrastructure
- AWS ECS deployment with Docker
  - Task definitions with resource limits
  - Auto-scaling based on CPU/Memory

- Load balancing (ALB)
  - Health checks and recovery
  - SSL termination and path routing
  - Multi-AZ deployment

### Configuration
- Environment-based configuration
- AWS Secrets Manager for sensitive data
- Tenant-specific configurations in Redis
- Resource limits and thresholds

### CI/CD
- GitHub Actions pipeline
  - Multi-stage Docker builds
  - Container scanning
  - Rolling updates with zero-downtime
  - Automated rollbacks

### Monitoring
- CloudWatch integration
  - Container insights
  - Tenant-specific log streams
- Health check endpoints (/health)

### Security & Recovery
- VPC isolation with security groups
- IAM roles and WAF integration
- Automated backups
- Cross-region replication


## Environment Variables

### Required Variables
- `NODE_ENV`: Environment (development/production)
- `TENANT_CODE`: Default tenant code
- `MONGODB_URL`: MongoDB connection URL
- `REDIS_URL`: Redis connection URL
- `AWS_REGION`: AWS region for services
- `AWS_ACCESS_KEY_ID`: AWS access key
- `AWS_SECRET_ACCESS_KEY`: AWS secret key

### Optional Variables
- `LOG_LEVEL`: Winston log level (default: info)
- `PORT`: Server port (default: 3000)
- `API_VERSION`: API version (default: v1)
- `CORS_ORIGINS`: Allowed CORS origins
- `MAX_POOL_SIZE`: MongoDB connection pool size
- `REDIS_PREFIX`: Redis key prefix
