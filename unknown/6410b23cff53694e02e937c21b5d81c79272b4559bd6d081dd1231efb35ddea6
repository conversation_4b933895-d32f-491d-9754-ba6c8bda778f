/**
 *  @module Mixin:Recommend
 */

//  Module Dependencies

module.exports = function(Model, options) {
	Model.recommendFetchData = function(languages, recipients = [], recommend) {
		// TODO: this is fake data
		const personIds = recipients.map(m => m.personId),
			recommended = personIds.reduce((res, id) => {
				res[id] = languages.reduce((obj, L) => {
					obj[L] = { variant: { name: 'sample - ' + id } };
					return obj;
				}, {});
				return res;
			}, {});

		return Promise.resolve(recommended);
	};
};
