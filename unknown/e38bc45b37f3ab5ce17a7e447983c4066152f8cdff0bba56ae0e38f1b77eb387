/**
 *  @module Mixin:MemWatch
 */

//  Module Dependencies
const
	memwatch = require('node-memwatch');

let HEAPDIFF;

module.exports = function(Model, options) {
	Model.heapStart = function() {
		HEAPDIFF = new memwatch.HeapDiff();
		return Promise.resolve();
	};

	Model.heapDiff = function() {
		if (HEAPDIFF) {
			const hd = HEAPDIFF.end();
			// console.log('\n------ Heap Diff: \n', hd);
			return Promise.resolve(hd);
		} return Promise.resolve();
	};

	// -----  Remote Methods  -----
	Model.remoteMethod('heapStart', {
		description: 'Start Heap Snapshot',
		http: { path: '/heap/start', verb: 'get' },
		returns: { type: 'object', root: true },
	});

	Model.remoteMethod('heapDiff', {
		description: 'Get Heap Diff',
		http: { path: '/heap/diff', verb: 'get' },
		returns: { type: 'object', root: true },
	});
};
