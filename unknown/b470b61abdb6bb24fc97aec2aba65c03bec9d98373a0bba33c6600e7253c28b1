{"name": "grab", "plural": "grab", "base": "PersistedModel", "strict": true, "mixins": {"MultitenantRemote": true}, "properties": {"id": {"type": "String", "id": true, "generated": true}}, "acls": [], "scopes": {}, "methods": {"menuRefresh": {"description": "Notify Grab to refresh menu (Grab callback API)", "http": {"path": "/refresh/menu", "verb": "post"}, "accepts": [{"arg": "provider", "type": "string", "required": true, "description": "provider name: grabfood or grabmart"}, {"arg": "shop", "type": "string", "description": "shop name"}]}}}