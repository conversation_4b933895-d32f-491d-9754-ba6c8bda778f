/**
 *  @module Model:Geometry
 *
 *  references:
 *   	https://www.npmjs.com/package/geojson
 */

const geolib = require('geolib')

module.exports = function(Geometry) {
	Geometry.prototype.toPoint = function() {
		return { lat: this.coordinates[1], lng: this.coordinates[0] }
	}

	Geometry.prototype.distance = function(geo) {
		const from = this.toPoint(),
			to = geo.toPoint()

		return geolib.getDistance(
			{ latitude: from.lat, longitude: from.lng },
			{ latitude: to.lat, longitude: to.lng }
		)	// in meters
	}
}
