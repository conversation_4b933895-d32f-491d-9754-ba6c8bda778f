{"name": "Beacon", "plural": "Beacons", "base": "PersistedModel", "idInjection": true, "strict": false, "mixins": {}, "options": {}, "properties": {"type": {"type": "Number"}, "name": {"type": "String"}, "uuid": {"type": "String", "length": 36}, "major": {"type": "Number", "description": "maximum value: 65536"}, "minor": {"type": "Number", "description": "maximum value: 65536"}, "namespace": {"type": "String", "length": 12}, "instance": {"type": "String", "length": 12}, "url": {"type": "String"}, "active": {"type": "Boolean"}, "geometry": {"type": "Geometry"}}, "validations": [], "relations": {}, "acls": [], "indexes": {"uuid_index": {"keys": {"uuid": 1}}, "namespace_index": {"keys": {"namespace": 1}}}, "scopes": {}, "methods": {}}