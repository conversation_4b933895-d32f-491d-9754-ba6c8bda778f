/**
 * @module Mixin:Expire
 */

const { startOf, endOf, bm } = require('@perkd/utils')

const ACTIVE = 'active',
	EXPIRED = 'expired'

module.exports = function(Model) {

	Model.handleExpiry = async function(daysAgo = 1) {
		const startDay = startOf('day', -daysAgo),
			endOfYesterday = endOf('day', -1),
			start = bm.mark(),
			where = {
				endTime: { $gte: startDay, $lte: endOfYesterday },
				state: ACTIVE,
			},
			update = { $set: { state: EXPIRED } }

		try {
			const { result } = await Model.collection().updateMany(where, update),
				latency = bm.diff(start)

			appLog('handleExpiry', { latency, where, result })

			if (latency > 1000 || result.nModified > 0 || !result.ok) {
				appNotify(`handleExpiry - ${result.nModified}/${latency}ms`, { where, result })
			}
		}
		catch (err) {
			appNotify('handleExpiry', { where, err })
		}
	}

	// ---  Remote & Operation Hooks  ---

	Model.observe('loaded', async ({ data }) => {
		const { state, endTime } = data

		if (state === ACTIVE && endTime && new Date(endTime) < new Date()) {
			data.state = EXPIRED
		}
	})

	Model.remoteMethod('handleExpiry', {
		description: `Do qualify when ${Model.name} goes expired`,
		http: { path: '/handleExpiry', verb: 'post' },
		accepts: [],
		returns: { type: 'object', root: true },
	})
}
