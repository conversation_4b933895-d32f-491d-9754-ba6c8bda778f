/**
 *  @module Mixin:Activity
 */

//  Module Dependencies

module.exports = function(Model) {
	Model.createActivity = function(activity = {}) {
		const { actorId = null, data } = activity,
			{ details } = data?.result?.error || {}

		if (details instanceof Object) { // stringify error details to void improper command (e.g. {"where":{"digitalCard.id": "xxxxxx"}})
			data.result.error.details = JSON.stringify(details)
		}

		return (Model.app.models[`${Model.name}Activity`] || Model.app.models.Activity).create({ ...data, ownerId: actorId })
	}
}
