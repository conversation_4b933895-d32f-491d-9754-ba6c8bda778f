======== Person ========
{
  "familyName": "Schultz",
  "givenName": "Fatima",
  "gender": 1,
  "ethnicity": 0,
  "visible": true,
  "createdAt": "2017-11-08T14:15:10.166Z",
  "modifiedAt": "2017-11-08T14:15:10.166Z",
  "deletedAt": null,
  "id": "5a03116ee2b7034eed23d6f0",
  "identityList": [],
  "locationList": [],
  "permissionList": [{
    "channel": "mobile",
    "status": 0
  }, {
    "channel": "email",
    "status": 0
  }, {
    "channel": "postal",
    "status": 0
  }, {
    "channel": "push",
    "status": 0
  }, {
    "channel": "voice",
    "status": 0
  }],
  "tagList": [],
  "companyList": [],
  "socialList": [],
  "dateList": [],
  "emailList": [],
  "phoneList": [],
  "addressList": [],
  "purchaseBehaviorList": [],
  "globalizeList": [],
  "noteList": [],
  "groupIds": [],
  "deviceIds": [],
  "latestDeviceId": [],
  "productIds": [],
  "tenantCode": "tblmy",
  "_gId": 9,
  "timestamp": "2017-11-08T14:15:10.181Z"
}

======== Member ========
{
  "createdAt": "2017-11-08T14:15:10.195Z",
  "modifiedAt": null,
  "id": "5a03116e0ed4ed50495385a1",
  "activeProgramIds": [],
  "identityList": [],
  "purchaseBehaviorList": [],
  "activeMembershipIds": [],
  "personId": "5a03116ee2b7034eed23d6f0",
  "tenantCode": "tblmy",
  "timestamp": "2017-11-08T14:15:10.197Z"
}

======== Membership ========
{
  "tierLevel": 1,
  "qualifier": "join",
  "startTime": "2017-11-08T16:17:18.687Z",
  "endTime": "2018-11-08T15:59:59.000Z",
  "status": 1,
  "programPriority": 2,
  "cardNumber": "",
  "cardRegisteredAt": null,
  "digitalCard": null,
  "type": null,
  "createdAt": "2017-11-08T16:17:18.689Z",
  "modifiedAt": "2017-11-08T16:17:18.689Z",
  "id": "5a032e0e0ed4ed50495385af",
  "programId": "5906ef245a3b56b4f797b647",
  "memberId": "5a032e0d0ed4ed50495385ab",
  "previousId": [],
  "nextId": [],
  "acquiredThrough": null,
  "purchaseBehaviorList": [],
  "rewardBehaviorList": [],
  "personId": "5a032e0ce2b7034eed23d6f7",
  "tenantCode": "tblmy"
}


