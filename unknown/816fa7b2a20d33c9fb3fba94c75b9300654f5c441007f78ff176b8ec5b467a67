/**
 *  @module Watchdog
 */
const os = require('node:os'),
	{ Providers } = require('@crm/types'),
	{ Context, TRAP, SERVICE } = require('@perkd/multitenant-context'),
	{ shortId, camelcase, pickObj } = require('@perkd/utils'),
	{ WebClient } = require('@slack/web-api')

/**
 * Environment
 */
const {
	NODE_ENV,
	BOT_TOKEN = ''		// Slack Bot User OAuth Token with chat:write scope
} = process.env

const { PERKD } = Providers.PROVIDER
const slack = (!NODE_ENV && BOT_TOKEN) ? new WebClient(BOT_TOKEN) /* dev */ : undefined

if (!('toJSON' in Error.prototype)) {
	Object.defineProperty(Error.prototype, 'toJSON', {
		value() {
			const alt = {}

			Object.getOwnPropertyNames(this).forEach(function(key) {
				alt[`${key}`] = this[`${key}`]
			}, this)

			return alt
		},
		configurable: true,
		writable: true,
	})
}

const DEFAULT_CHANNEL = '-service',
	DEPLOYMENT = {
		production: 'live',
		test: 'test',
		staging: 'staging',
		development: 'dev',
	},
	ICONS = {
		X: '💛',
		ok: '👌',
		go: '🚀',
		default: '👉🏻',
		scheduled: '✅',
		cancelled: '❎',
		start: '🚀',
		started: '🚀',
		pause: '⏸️',
		paused: '⏸️',
		resume: '🚀',
		resumed: '🚀',
		stop: '☑️',
		stopped: '☑️',
		reset: '♻️',
		resetted: '♻️',
		done: '✅',
		end: '☑️',
		ended: '☑️',
		good: '👍',
		bad: '👎',
		warn: '⚠️',
		error: '❌',
		failed: '❌',
		alert: '❗️',
		debug: '🕸',
		email: '📧',
	},
	ALERT = { warn: 1, error: 1, alert: 1, failed: 1, bad: 1 },
	ERROR_PROPS = [ 'name', 'message', 'code', 'related', 'details', 'timings', 'statusCode' ]

const Watchdog = function(app, settings) {
	if (!(this instanceof Watchdog)) {
		return new Watchdog(app, settings)
	}

	this.app = app
	this.env = app.get('env')

	const { env } = this

	this.deployment = DEPLOYMENT[env] || env
	this.hostname = os.hostname()

	this.settings = settings
	this._service = app.service

	this.domain = camelcase(this._service.domain)
	this.multitenancy = this._service.multitenancy
	this.tenantCode = this.multitenancy ? '' : this._service.tenantCode.trim()
}

module.exports = Watchdog

// ================ Private functions ================ //

Watchdog.prototype.init = function(settings) {
}

/**
 * log to cloudwatch
 * appLog('memory', usage);
 * appLog('shopify_product_not_found', { receipt, item }, 'error');
 * appLog('payment_fail', new Err({ code: 'payment_amount_too_small', statusCode: 500, details: { minAmount: 0.5, amount: 0.2 } }), 'error');
 * @param {string} message
 * @param {object} details
 * @param {string} level - info / error
 * @param {string} logId
 * @return {string} logId
 */
Watchdog.prototype.log = function(message, details, level = 'info', logId = shortId()) {
	const { settings } = this

	if (!settings.enabled) return
	console.error(message, details)
}

/**
 * Emit WatchDog event - will be picked up by slack-updates (lambda) and sent to Slack
 * @param {string} message
 * @param {object} details
 * @param {string} type - default info, see const ICONS
 * @param {string} to - channel name
 * @param {object/boolean} stack - trace stack
 */
Watchdog.prototype.notify = async function (message, details = {}, type, to = DEFAULT_CHANNEL, stack) {
	const { app, settings, deployment, domain, multitenancy } = this,
		{ watchdog: EVENT } = app.Event,
		tenantCode = multitenancy ? Context.tenant : this.tenantCode

	if (!settings.enabled || type === 'debug' && isProduction()) return

	let dt = details, pretext

	if (details instanceof Error) {
		type = type || 'error'
		stack = dt.code ? false : (dt.stack || true)
		pretext = dt.code || dt.message || ''
		pretext += dt.logId ? ` (logId:${dt.logId})` : ''
		dt = pickObj(dt, ERROR_PROPS)
	}
	else if (dt && dt.err) {
		const { err } = dt
		type = type || 'error'
		if (typeof err === 'string') dt.err = { message: err }
		stack = dt.err.code ? false : (dt.err.stack || true)
		pretext = dt.err.code || dt.err.message || ''
		pretext += dt.logId ? ` (logId:${dt.logId})` : ''
		dt.err = pickObj(dt.err, ERROR_PROPS)
	}

	const icon = type && ICONS[`${type}`] || ICONS.default
	message = `${icon} ${message}`
	pretext = pretext ? `🔘 ${pretext}` : undefined

	const dashed = to.startsWith('-'),
		suffix = type && (to === DEFAULT_CHANNEL) && ALERT[`${type}`] && '-alert' || '',
		channel = dashed ? `${deployment}${to}${suffix}` : to,
		from = tenantCode ? `[${tenantCode}] ${domain}` : `${domain}`,
		text = (dt === null || dt === undefined) ? undefined : JSON.stringify(dt),
		username = undefined,
		attachments = text ? [ { pretext, text } ] : []

	if (stack) {
		attachments.push({
			pretext: '👇',
			text: stack === true ? new Error().stack.replace('Error', '') : stack
		})
	}

	try {
		const tenant = tenantCode === SERVICE ? PERKD : (tenantCode || TRAP)
		if (slack) {	// dev
			slack.chat.postMessage({
				text: message,
				channel: channel || to,
				username: from || username,
				attachments
			})
		}
		else {
			app.emit(EVENT.notify.slack, { channel, from, message, attachments, username }, tenant)
		}
	}
	catch (err) {
		appEcho('❌ watchdogNotifyError %j', { channel, from, message, attachments, username, err })
	}
}
