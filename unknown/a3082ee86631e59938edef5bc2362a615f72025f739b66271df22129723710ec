/**
 *  @module Model:Address
 */

module.exports = function(Address) {

	// -----  Remote & Operation hooks  -----

	Address.observe('before save', async ({ instance, data, currentInstance }) => {
		const updated = instance || data,
			existingOptIn = currentInstance ? currentInstance.optIn : null

		if (existingOptIn === null && (updated.optIn === undefined)) {
			// user did not provide optIn status, should set default null
			updated.optIn = null
		}
	})
}
