/**
* @module Mixin:List
*
*/
const LIST = 'List'

module.exports = function(Model, options = {}) {

	const { ListModel = LIST } = options,
		List = Model.modelBuilder.models[ListModel],
		mixedMode = !!List.definition.properties.modelName		// list is multi-models (mixed) by default

	// inject 'lists' relations
	Model.referencesMany(List, {
		as: 'lists',
		options: { persist: true },
	})

	// -----  Private functions  -----

	/**
	 * @param	{String[]} listTags
	 * @return	{Promise}
	 */
	Model.prototype.upsertLists = async function(listTags = []) {
		const { id } = this,
			{ name: modelName, app } = Model,
			List = app.models[ListModel]

		for (const tag of listTags) {

			Model.queue(tag, async () => {	// for concurrent upsertLists
				// findOrCreateList
				const filter = mixedMode
						? { where: { modelName, 'tags.user': tag } }
						: { where: { 'tags.user': tag } },
					lists = await List.find(filter)

				if (!lists.length) await this.createDefaultList(tag)

				// updateList & references
				for (const list of lists) {
					const updatedList = await list.add(id)

					if (updatedList) {
						const exists = await this.lists.exists(updatedList.id)

						if (!exists) {
							await this.lists.add(updatedList.id)
						}
					}
				}
			})
		}
	}

	/**
	 * @param	{String[]} listTags
	 * @return	{Promise}
	 */
	Model.prototype.deLists = async function(listTags) {
		const { id } = this,
			List = Model.app.models[ListModel],
			filter = {
				where: { 'tags.user': { inq: listTags } }
			},
			lists = await List.find(filter),
			res = []

		for (const list of lists) {
			const updatedList = await list.pull(id)

			if (updatedList) {
				res.push(
					await this.lists.remove(updatedList.id)
				)
			}
		}

		return res
	}

	// allow overriding by model
	if (!Model.prototype.createDefaultList) {
		Model.prototype.createDefaultList = async function(tag) {
			const { id, lists } = this,
				data = {
					name: tag + ' list',
					ids: [ id ],
					tags: { user: [ tag ] },
				}

			if (mixedMode) data.modelName = Model.name
			return lists.create(data).catch(err => console.log('Ohoh', err))
		}
	}

	// -----  Remote & Operation hooks  -----

	Model.observe('before save', async ({ currentInstance, instance, data, isNewInstance, hookState }) => {
		const updates = instance || data,
			listTags = updates.tags && updates.tags.list

		if (listTags) {
			hookState.tags = listTags
			if (!isNewInstance && currentInstance) {
				hookState.currentTags = currentInstance.tags.list
			}
		}
	})

	Model.observe('after save', async ({ instance, hookState }) => {
		// upsertLists & deLists notify "after save" hook with data.tags.list = undefined
		// Model may need to await update list, options for async update?
		const listTags = instance.tags.list || []

		if (instance.deletedAt && listTags.length > 0) {
			return instance.deLists(listTags)
		}

		if (hookState.tags && hookState.tags.length > 0) {
			await instance.upsertLists(hookState.tags)
		}

		if (hookState.currentTags) {
			const removedTags = hookState.currentTags.filter(l => !listTags.includes(l))
			if (removedTags.length > 0) return instance.deLists(removedTags)
		}
	})
}
