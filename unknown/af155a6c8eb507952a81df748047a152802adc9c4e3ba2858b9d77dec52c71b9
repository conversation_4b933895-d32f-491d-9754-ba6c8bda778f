{"name": "Date", "plural": "Dates", "base": "Model", "idInjection": true, "strict": false, "mixins": {}, "options": {}, "properties": {"id": {"type": "String", "id": true, "defaultFn": "nanoid"}, "name": {"type": "String", "required": true, "enum": ["birth", "graduate", "married", "baptised"]}, "date": {"type": "Date"}, "year": {"type": "Number"}, "month": {"type": "Number"}, "day": {"type": "Number"}}, "validations": [], "methods": []}