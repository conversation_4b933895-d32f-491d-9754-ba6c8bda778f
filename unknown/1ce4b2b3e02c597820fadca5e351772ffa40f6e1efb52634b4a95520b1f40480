{"name": "Sharer", "plural": "Sharers", "base": "Model", "idInjection": false, "strict": true, "properties": {"id": false, "personId": {"type": "string", "description": "Identifier of the user who shared the object"}, "name": {"type": "string", "max": 80, "description": "Fullname of sharer (for app)"}, "imageUrl": {"type": "string", "description": "URL of sharer ProfileImage (for app)"}, "mode": {"type": "string", "required": true, "description": "Sharing mode", "enum": ["clone", "invite", "send", "transfer", "subobject"]}, "originId": {"type": "string", "description": "Id of instance from which share originated"}, "sharingId": {"type": "string", "description": "Related sharing Id under the shared object"}, "generation": {"type": "number", "default": 1, "description": "nth level of sharing"}, "noNotify": {"type": "boolean", "default": false}}, "scopes": {}, "methods": {}}