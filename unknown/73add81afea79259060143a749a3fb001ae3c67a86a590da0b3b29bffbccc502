/**
 * fake contact/member creation
 * <AUTHOR>
 */

const argv = require('minimist')(process.argv.slice(2));

const Faker = require('Faker'),
	rn = require('random-number'),
	rp = require('request-promise'),
	async = require('async'),
	bm = require('../benchmark'),
	_ = require('lodash');

if (argv._.length === 0 || argv.help) {
	console.log('\n Usage:');
	console.log('	1) node fake-contact.js tenantCode // default create 1 contact, concurrency 1');
	console.log('	2) node fake-contact.js tenantCode -s 100 -c 10 -d 1000 // create 100 contacts, concurrency 10, each create delay 1s');
	console.log('	3) node fake-contact.js tenantCode -h staging.waveo.com // change api to staging, default is localhost');
	// console.log('	1) node fake-contact.js tenantCode -m 91000000 // start mobile no. from 91000000');
	console.log('\n');
	return;
}

// parse argv
const PARAM = {
	tenantCode: argv._[0],
	size: argv.s || 1,
	concurrency: argv.c || 1,
	host: argv.h || '127.0.0.1',
	delay: argv.d || 0,
	programId: '5906ef245a3b56b4f797b647',
	tierLevel: 1,
};

// var startMobile = 91000000;
const totalCreated = 1;

if (!validate()) return;

const API = {
	Person: 'http://' + PARAM.host + ':3101/api',
	Order: 'http://' + PARAM.host + ':3102/api',
	Member: 'http://' + PARAM.host + ':3103/api',
};

function validate() {
	if (isNaN(PARAM.size)) {
		console.log('\n *** Error: -s should be a number\n');
		return false;
	}
	if (isNaN(PARAM.concurrency)) {
		console.log('\n *** Error: -c should be a number\n');
		return false;
	}
	if (isNaN(PARAM.delay)) {
		console.log('\n *** Error: -d should be a number\n');
		return false;
	}
	return true;
}

function fakeContacts(total, concurrency, callback) {
	const queue = async.queue((person, cb) => {
	    createPerson(person)
			.delay(1000)
			.then(person => createMember(person))
			.delay(1000)
			.then(member => joinMembership(member))
			.delay(1000)
			.then(membership => createOrder(membership))
			.delay(1000)
			.finally(cb);
	}, concurrency);

	queue.pause();

	queue.drain = function() {
		callback(totalCreated);
	};

	for (let i = 1; i <= total; i++) {
		const gender = rn.generator({
			min: 1,
			max: 2,
			integer: true,
		});
		const person = {
			gender: gender(),
		};
		if (1 == person.gender) {
			person.givenName = Faker.Name.firstNameMale();
		} else {
			person.givenName = Faker.Name.firstNameFemale();
		}
		person.familyName = Faker.Name.lastName();
		// person.email = Faker.Internet.email();
		// person.mobileCountryCode = 'TW';
		// person.mobileCountryPrefix = 886;
		// person.mobileNo = startMobile++;
		//
		queue.push(person);
	}

	queue.resume();
}

function getRandomDate(from, to) {
	from = from.getTime();
	to = to.getTime();
	return new Date(from + Math.random() * (to - from));
}

function createPerson(person) {
	const options = {
		url: API.Person + '/Persons/doUpsert',
		json: { data: person },
	};
	return callAPI(options);
}

function createMember(person) {
	const options = {
		url: API.Member + '/Members',
		json: { personId: person.id },
	};
	return callAPI(options);
}

function joinMembership(member) {
	const options = {
		url: API.Member + '/Programs/' + PARAM.programId + '/joinMembership',
		json: { memberId: member.id, tierLevel: PARAM.tierLevel },
	};
	return callAPI(options);
}

function createOrder(membership) {											// TODO - ZJ
	const options = {
		url: API.Order + '/Orders/doUpsert',
		json: {
			data: {
				cardNumber: membership.cardNumber,
				receiptNumber: 'Test' + new Date().getTime(),
				purchaseAt: new Date(),
				channel: 'store',
				channelId: '5906ce5444d871347eef7001',
				quantity: 2,
				currency: 'SGD',
				totalAmount: _.random(20, 100),
				status: 1,
				personId: membership.personId,
				memberId: membership.memberId,
				membershipid: membership.id,
			},
		},
	};
	return callAPI(options);
}

function callAPI(opt) {
	const options = Object.assign({
		method: 'POST',
		headers: { 'tenant-code': PARAM.tenantCode },
	}, opt);
	return rp(options).then(result => {
		console.log('result %j', result);
		return result;
	});
}

bm.mark('create.start');
fakeContacts(PARAM.size, PARAM.concurrency, created => {
	bm.mark('create.end');
	const lapsed = bm.elapsedTime('create') / 1000;
	console.log('Total %s contacts created, lapsed %s (s)', created, lapsed);
});

/**
 * End of script
 */
