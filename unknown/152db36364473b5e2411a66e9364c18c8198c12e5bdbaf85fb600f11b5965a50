{"name": "Offer", "plural": "Offers", "description": "CRM", "base": "PersistedModel", "idInjection": true, "strict": false, "mixins": {"MultitenantRemote": true}, "properties": {"id": {"type": "String", "id": true, "generated": true}}, "validations": [], "relations": {}, "acls": [], "methods": {"findByDigitalIds": {"description": "Find by digitalIds", "http": {"path": "/digitalIds", "verb": "get"}, "accepts": [{"arg": "ids", "type": "array", "required": true}], "returns": {"type": ["Offer"], "root": true}}, "qualifyRedeem": {"description": "Qualify Offers redemption", "http": {"path": "/qualify/redeem", "verb": "get"}, "accepts": [{"arg": "ids", "type": "array", "required": true}], "returns": {"type": "array", "root": true}}, "redeem": {"description": "<PERSON><PERSON><PERSON>", "http": {"path": "/redeem", "verb": "post"}, "accepts": [{"arg": "ids", "type": "array", "required": true}, {"arg": "at", "type": "date"}, {"arg": "options", "type": "object", "description": "{ through, qualify }"}], "returns": {"type": "array", "root": true}}, "revert": {"description": "<PERSON><PERSON>", "http": {"path": "/revert", "verb": "post"}, "accepts": [{"arg": "ids", "type": "array", "required": true}, {"arg": "options", "type": "object", "description": "{ through }"}], "returns": {"type": "array", "root": true}}, "cancel": {"description": "Cancel Offers", "http": {"path": "/cancel", "verb": "post"}, "accepts": [{"arg": "ids", "type": "array", "required": true}, {"arg": "at", "type": "date"}, {"arg": "reason", "type": "string"}, {"arg": "options", "type": "object", "description": "{ through }"}], "returns": {"type": "array", "root": true}}, "redeemByCodes": {"description": "Redeem offers by codes of channel for person", "http": {"path": "/redeem/byCodes", "verb": "post"}, "accepts": [{"arg": "codes", "type": ["string"], "required": true}, {"arg": "channel", "type": "string", "enum": ["store", "online", "perkd"], "required": true}, {"arg": "personId", "type": "string", "required": true}, {"arg": "at", "type": "date"}], "returns": {"type": ["Offer"], "root": true}}, "authorize": {"description": "Authorize a voucher for a specified amount (PerkdPay)", "http": {"path": "/authorize", "verb": "post"}, "accepts": [{"arg": "digitalOfferId", "type": "string", "required": true, "description": "The digital offer ID to authorize"}, {"arg": "amount", "type": "number", "required": true, "description": "The amount to authorize (must be <= voucher's discount value)"}], "returns": {"type": "Object", "root": true}}, "capture": {"description": "Capture (redeem) an authorized voucher (PerkdPay)", "http": {"path": "/capture", "verb": "post"}, "accepts": [{"arg": "digitalOfferId", "type": "string", "required": true, "description": "The digital offer ID to capture"}, {"arg": "amount", "type": "number", "required": true, "description": "The amount to capture (must be <= voucher's discount value)"}], "returns": {"type": "Object", "root": true}}, "refund": {"description": "Refund a captured voucher (PerkdPay)", "http": {"path": "/refund", "verb": "post"}, "accepts": [{"arg": "digitalOfferId", "type": "string", "required": true, "description": "The digital offer ID to refund"}], "returns": {"type": "Object", "root": true}}, "cancelAuthorized": {"description": "Cancel an authorized voucher (PerkdPay)", "http": {"path": "/authorize", "verb": "delete"}, "accepts": [{"arg": "digitalOfferId", "type": "string", "required": true, "description": "The digital offer ID to cancel"}], "returns": {"type": "Object", "root": true}}, "prototype.redeem": {"description": "Redeem offer", "http": {"path": "/redeem", "verb": "post"}, "accepts": [{"arg": "at", "type": "date"}, {"arg": "options", "type": "object", "description": "{ through, qualify }"}], "returns": {"type": "Offer", "root": true}}, "prototype.authorize": {"description": "Authorize a normal offer for async payment", "http": {"path": "/authorize", "verb": "post"}, "accepts": [{"arg": "at", "type": "date"}], "returns": {"type": "Offer", "root": true}}, "prototype.revert": {"description": "Reverse redeemed offer", "http": {"path": "/revert", "verb": "post"}, "accepts": [{"arg": "quantity", "type": "number", "description": "Reset all redemptions if not specified"}, {"arg": "options", "type": "object", "description": "{ through }"}], "returns": {"type": "Offer", "root": true}}, "prototype.cancel": {"description": "Cancel offer", "http": {"path": "/cancel", "verb": "post"}, "accepts": [{"arg": "at", "type": "date"}, {"arg": "reason", "type": "string"}, {"arg": "options", "type": "object", "description": "{ through, force }"}], "returns": {"type": "Offer", "root": true}}, "prototype.recover": {"description": "Recover cancelled offer", "http": {"path": "/recover", "verb": "post"}, "accepts": [{"arg": "options", "type": "object", "description": "{ through }"}], "returns": {"type": "Offer", "root": true}}, "prototype.extend": {"description": "Extend validity with option to link to new membership. options: { through, membershipId }", "http": {"path": "/extend", "verb": "post"}, "accepts": [{"arg": "endTime", "type": "string", "required": true, "description": "Specific date or duration like 'P30D' (base on endTime)"}, {"arg": "options", "type": "object", "description": "{ through, membershipId }"}], "returns": {"type": "Offer", "root": true}}}}