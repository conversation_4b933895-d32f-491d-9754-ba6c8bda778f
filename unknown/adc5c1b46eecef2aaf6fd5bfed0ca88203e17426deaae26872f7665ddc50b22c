{"name": "Place", "plural": "Places", "base": "PersistedModel", "idInjection": true, "strict": false, "options": {}, "mixins": {"MultitenantRemote": true, "Setting": true}, "properties": {"id": {"type": "String", "id": true, "generated": true}}, "validations": [], "relations": {"phones": {"type": "embeds<PERSON><PERSON>", "model": "Phone", "property": "phoneList", "options": {"validate": true, "forceId": false, "persistent": true, "methods": ["default"]}}, "addresses": {"type": "embeds<PERSON><PERSON>", "model": "Address", "property": "addressList", "options": {"validate": true, "forceId": false, "persistent": true, "methods": ["default"]}}, "settings": {"type": "embeds<PERSON><PERSON>", "model": "Setting", "property": "settingList", "options": {"validate": false, "forceId": false}}, "beacons": {"type": "referencesMany", "model": "Beacon", "foreignKey": "beaconIds"}, "owner": {"type": "belongsTo", "model": "Business", "foreignKey": "ownerId"}, "queueNumbers": {"type": "hasOne", "model": "Numbering", "foreignKey": "ownerId"}, "photos": {"type": "hasMany", "model": "PhotoImage", "foreignKey": "ownerId"}, "visits": {"type": "hasMany", "model": "Visit", "foreignKey": "placeId"}, "staff": {"type": "hasMany", "model": "Staff", "foreignKey": "placeId", "through": "Assignment"}, "printers": {"type": "hasMany", "model": "Printer", "foreignKey": "placeId"}, "kiosks": {"type": "hasMany", "model": "Kiosk", "foreignKey": "placeId"}, "vendings": {"type": "hasMany", "model": "Vending", "foreignKey": "placeId"}}, "acls": [], "indexes": {}, "scopes": {}, "methods": {"provisionPrinter": {"description": "Provision Printer for place (provision only)", "http": {"path": "/printers/provision", "verb": "post"}, "accepts": [{"arg": "placeId", "type": "string", "required": true}, {"arg": "name", "type": "string", "description": "Name of printer"}, {"arg": "purpose", "type": "string", "enum": ["kitchen", "receipt", "delivery"], "default": "kitchen"}, {"arg": "credentials", "type": "string", "required": true, "description": "scan qr-code on printer"}], "returns": {"type": "object", "root": true}}, "provisionStatus": {"description": "Provision status", "http": {"path": "/provision/status", "verb": "get"}, "accepts": [{"arg": "businessId", "type": "string"}], "returns": {"type": "object", "root": true}}, "doUpsert": {"description": "Create or update a place", "http": {"path": "/doUpsert", "verb": "post"}, "accepts": [{"arg": "data", "type": "object", "required": true}, {"arg": "filter", "type": "object"}, {"arg": "options", "type": "object"}], "returns": {"type": "object", "root": true}}, "addListTagByBusinessId": {"description": "Add tags 'list' by businessId", "http": {"path": "/tags/list/add", "verb": "post"}, "accepts": [{"arg": "businessId", "type": "any", "required": true}, {"arg": "keys", "type": "array", "required": true}], "returns": {"type": "object", "root": true}}, "findOneByProviderStoreId": {"description": "Find Place by Provider storeId", "http": {"path": "/findOne/provider/storeId", "verb": "get"}, "accepts": [{"arg": "provider", "type": "string", "required": true, "description": "Name of provider"}, {"arg": "storeId", "type": "any", "required": true, "description": "Provider storeId"}], "returns": {"type": "Place", "root": true}}, "findOrCreateProviderStore": {"description": "Find or Create Store of Provider", "http": {"path": "/findOrCreate/provider", "verb": "post"}, "accepts": [{"arg": "provider", "type": "string", "required": true, "description": "Name of provider"}, {"arg": "store", "type": "object", "required": true, "description": "Store properties, must include 'id'"}, {"arg": "listed", "type": "boolean", "description": "To list store in Wallet app", "default": false}], "returns": {"type": "Place", "root": true}}, "prototype.queueNumber": {"description": "Get next Queue Number for place", "http": {"path": "/queue", "verb": "get"}, "accepts": [], "returns": {"type": "string", "root": true}}, "prototype.attachPrinter": {"description": "Attach Printer", "http": {"path": "/printers/:fk/attach", "verb": "post"}, "accepts": [{"arg": "fk", "type": "string", "http": {"source": "path"}, "required": true, "description": "printerId"}], "returns": {"type": ["object"], "root": true}}, "prototype.detachPrinter": {"description": "Detach Printer", "http": {"path": "/printers/:fk/detach", "verb": "post"}, "accepts": [{"arg": "fk", "type": "string", "http": {"source": "path"}, "required": true, "description": "printerId"}], "returns": {"type": ["object"], "root": true}}, "prototype.selectPrinter": {"description": "Select printer by purpose, type, name & tag", "http": {"path": "/printers/select", "verb": "get"}, "accepts": [{"arg": "purpose", "type": "string", "enum": ["kitchen", "receipt", "delivery"], "description": "Purpose of printer"}, {"arg": "type", "type": "string", "enum": ["label", "receipt"], "description": "Type of printer"}, {"arg": "name", "type": "string", "description": "Name of printer"}, {"arg": "tag", "type": "string", "description": "eg. kitchen station"}], "returns": {"type": ["Printer"], "root": true}}, "prototype.notifyStaff": {"description": "Send Push Notification to ALL staff of place", "http": {"path": "/staff/notify", "verb": "post"}, "accepts": [{"arg": "body", "type": "string", "required": true}, {"arg": "personalize", "type": "object"}, {"arg": "options", "type": "object", "description": "For push notification"}], "returns": {"type": "object", "root": true}}}}