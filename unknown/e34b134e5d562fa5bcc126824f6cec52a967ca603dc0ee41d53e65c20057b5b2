{"name": "<PERSON><PERSON><PERSON>", "plural": "Variants", "base": "PersistedModel", "idInjection": true, "strict": false, "mixins": {"MultitenantRemote": true, "RemotingTypes": {"ProductImage": true}}, "properties": {"id": {"type": "string", "id": true, "generated": true}}, "validations": [], "relations": {"images": {"type": "hasMany", "model": "ProductImage", "foreignKey": "ownerId"}}, "acls": [], "indexes": {}, "scopes": {}, "methods": {"order": {"description": "Order only (no payment)", "http": {"path": "/order", "verb": "post"}, "accepts": [{"arg": "userId", "type": "string", "description": "Perkd personId, omit for anonymous (web) orders"}, {"arg": "order", "type": "object", "required": true, "description": "see @perkd/commerce"}, {"arg": "pricings", "type": ["object"], "required": true, "description": "see @perkd/commerce"}, {"arg": "options", "type": "object", "description": "{ reservation, through, cardId, description, metadata, notification, noNotify }"}], "returns": {"type": "object", "root": true, "description": "{ orderId, fulfilled }"}}, "cancelDigitalFulfill": {"description": "Cancel fulfilled digital products - Stored Value, Voucher/Tickets, Gift Cards", "http": {"path": "/fulfillments/digital/cancel", "verb": "post"}, "accepts": [{"arg": "fulfilled", "type": "array", "required": true, "description": "Fulfilled items to cancel"}], "returns": {"type": "array", "root": true}}, "applinkAddToBagUndeploy": {"description": "Remove Add-to-Bag product promotion page & its assets on s3", "http": {"path": "/applink/addtobag/:micrositeId/undeploy", "verb": "post"}, "accepts": [{"arg": "micrositeId", "type": "string", "http": {"source": "path"}, "required": true}, {"arg": "slug", "type": "string", "required": true}], "returns": {"type": "object", "root": true}}, "applinkAddToBagDelete": {"description": "Delete Add-to-Bag product promotion page", "http": {"path": "/applink/addtobag/:micrositeId", "verb": "delete"}, "accepts": [{"arg": "micrositeId", "type": "string", "http": {"source": "path"}, "required": true}], "returns": {"type": "object", "root": true}}, "transformExternalOrder": {"description": "Transform external Order items - map external variantId/sku/gtin to CRM item, product & variant properties", "http": {"path": "/order/external/transform", "verb": "post"}, "accepts": [{"arg": "order", "type": "object", "required": true, "description": "Order with EXTERNAL variantId/sku/gtin in items"}, {"arg": "provider", "type": "string"}], "returns": {"type": "object", "root": true}}, "completeItems": {"description": "Complete items with item, variant & product properties", "http": {"path": "/order/items/complete", "verb": "post"}, "accepts": [{"arg": "items", "type": "array", "required": true, "description": "List of (skeleton) items with product & variant ids"}], "returns": {"type": "array", "root": true}}, "orderCommit": {"description": "Commit (pending) Order", "http": {"path": "/order/commit", "verb": "post"}, "accepts": [{"arg": "transaction", "type": "object", "required": true}, {"arg": "options", "type": "object"}], "returns": {"type": "object", "root": true}}, "prototype.applinkRefresh": {"description": "Refresh Perkd applinks & related content", "http": {"path": "/applink/refresh", "verb": "post"}, "accepts": [], "returns": {"type": "object", "root": true}}, "prototype.applinkAddToBag": {"description": "Create Add-to-Bag product promotion page (microsite)", "http": {"path": "/applink/addtobag", "verb": "post"}, "accepts": [{"arg": "data", "type": "object", "http": {"source": "body"}, "required": true}], "returns": {"type": "object", "root": true}}, "prototype.applinkAddToBagDeploy": {"description": "Generate Add-to-Bag product promotion page", "http": {"path": "/applink/addtobag/:micrositeId/deploy", "verb": "post"}, "accepts": [{"arg": "micrositeId", "type": "string", "http": {"source": "path"}, "required": true}, {"arg": "fallbackActionId", "type": "string", "required": true}, {"arg": "start", "type": "date"}, {"arg": "end", "type": "date"}, {"arg": "shorten", "type": "boolean", "default": true}, {"arg": "options", "type": "object"}], "returns": {"type": "object", "root": true}}}}