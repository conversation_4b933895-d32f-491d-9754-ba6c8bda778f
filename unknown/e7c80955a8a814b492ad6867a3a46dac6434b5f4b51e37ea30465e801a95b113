module.exports = function(server) {
	// Install a `/` route that returns server status
	const router = server.loopback.Router()
	router.get('/', server.loopback.status())

	// Install health check route
	router.get('/health', (req, res) => {
		const { service, models } = server,
			{ READY, STARTED, PAUSED, RECOVERING, RECOVERED } = models.Service.State

		switch (service.state.now) {
		case READY:
		case STARTED:
		case PAUSED:
		case RECOVERING:
		case RECOVERED:
			res.status(200).end()
			break
		default:
			res.status(404).end()
		}
	})
	server.use(router)
}
