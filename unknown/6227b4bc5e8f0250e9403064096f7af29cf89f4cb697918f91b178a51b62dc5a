/**
 *  @module Mixin:Errors
 */

const { Err } = require('@perkd/errors')

module.exports = function(Model) {

	Model.rejectErr = function(code, details, notify = false, topic = 'error', statusCode = 400) {
		const related = { model: Model.name }
		return rejectErr.call(related, code, details, notify, topic, statusCode)
	}

	Model.serverErr = function(code, message, details) {
		return this.rejectErr(code, message, details, 500)
	}

	Model.prototype.rejectErr = function(code, details, notify = false, topic = 'error', statusCode = 400) {
		const related = { model: Model.name, instanceId: this.id, instanceName: this.name }
		return rejectErr.call(related, code, details, notify, topic, statusCode)
	}

	Model.prototype.serverErr = function(code, message, details) {
		return this.rejectErr(code, details, undefined, undefined, 500)
	}
}

function rejectErr(code, details, level, topic = 'error', statusCode = 400) {
	const related = this,
		isErr = true,
		err = code instanceof Error ? new Err(code) : new Err({ code, details, statusCode }) // strong-error-handler will stringify details, breaks error response

	err.related = related
	err.isErr = isErr

	// if (level === 'alert') appLog(`rejectErr: ${code}`, err, 'error');
	if (level) appLog(code, err, 'error')
	throw err
}
