{"name": "RewardTransaction", "plural": "RewardTransactions", "base": "Model", "idInjection": false, "strict": true, "mixins": {}, "properties": {"id": {"type": "string"}, "type": {"type": "string", "enum": ["issue", "deduct", "carryover"], "required": true}, "quantity": {"type": "number", "required": true, "description": "Number of stamps"}, "level": {"type": "number", "description": "Number of last impacted level"}, "notifiedAt": {"type": "date", "default": null, "description": "Time of in-app notification"}, "createdAt": {"type": "date", "description": "Time of transaction"}, "acquired": {"type": "TouchPoint"}}}