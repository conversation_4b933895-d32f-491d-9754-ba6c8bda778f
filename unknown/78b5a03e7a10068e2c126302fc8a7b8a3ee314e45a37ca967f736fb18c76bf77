{"name": "StoredValue", "plural": "StoredValues", "idInjection": false, "strict": true, "options": {"validateUpsert": false}, "properties": {"id": false, "balance": {"type": "number", "default": 0, "description": "Balance stored value (integer)"}, "currency": {"type": {"code": {"type": "string", "max": 8, "description": "ISO 4217 currency code, 'CREDIT' or 'POINT'"}, "precision": {"type": "number", "default": 0, "description": "Decimal precision, ie. 2 => amount of 1000 = $10.00"}}, "required": true}}, "scopes": {}, "methods": {}}