/**
 *  @module ConfigLoader
 */
const path = require('path'),
	fs = require('fs'),
	{ cloneDeep } = require('@perkd/utils'),
	debug = require('debug')('waveo:lib:config-loader')

const ConfigLoader = exports

ConfigLoader.loadAppConfig = function(rootDir, env) {
	return loadNamed(rootDir, env, 'config', mergeAppSettings)
}

ConfigLoader.loadAppSettings = function(rootDir, name, env) {
	return loadNamed(rootDir, env, name, mergeAppSettings)
}

ConfigLoader.loadAppDefaultConfig = function(rootDir, env) {
	return loadNamed(rootDir + '/lib/common/config', env, 'config', mergeAppDefaultConfig)
}

/* -- Implementation -- */

function loadNamed(rootDir, env, name, mergeFn) {
	debug('loadNamed %j', arguments)
	env = env || process.env.NODE_ENV || 'development'
	const files = findConfigFiles(rootDir, env, name)
	if (files.length) {
		debug('found %s %s files', env, name)
		files.forEach(f => {
			debug('  %s', f)
		})
	}
	const configs = loadConfigFiles(files)
	const merged = mergeConfigurations(configs, mergeFn)

	debug('merged %s %s configuration %j', env, name, merged)

	return merged
}

function findConfigFiles(appRootDir, env, name) {
	debug('findConfigFiles %j', arguments)
	const master = ifExists(name + '.json')
	if (!master && (ifExistsWithAnyExt(name + '.local')
				|| ifExistsWithAnyExt(name + '.' + env))) {
		console.log('WARNING: Main {{config}} file "%s.json" is missing', name)
	}
	if (!master) return []

	const candidates = [
		master,
		ifExistsWithAnyExt(name + '.local'),
		ifExistsWithAnyExt(name + '.' + env),
	]

	return candidates.filter(c => c !== undefined)

	function ifExists(fileName) {
		const filepath = path.resolve(appRootDir, fileName)
		return fileExistsSync(filepath) ? filepath : undefined
	}

	function ifExistsWithAnyExt(fileName) {
		return ifExists(fileName + '.js') || ifExists(fileName + '.json')
	}
}

function loadConfigFiles(files) {
	debug('loadConfigFiles %j', arguments)
	return files.map(f => {
		const config = cloneDeep(require(f))
		Object.defineProperty(config, '_filename', {
			enumerable: false,
			value: f,
		})
		debug('loaded config file %s: %j', f, config)
		return config
	})
}

function mergeConfigurations(configObjects, mergeFn) {
	const result = configObjects.shift() || {}
	while (configObjects.length) {
		const next = configObjects.shift()
		mergeFn(result, next, next._filename)
	}
	return result
}

function mergeAppSettings(target, config, fileName) {
	const err = mergeObjects(target, config)
	if (err) {
		err.fileName = fileName
		throw err
	}
}

function mergeAppDefaultConfig(target, config, fileName) {
	const err = mergeObjects(target, config)
	if (err) {
		err.fileName = fileName
		throw err
	}
}

function mergeObjects(target, config, keyPrefix) {
	for (const key in config) {
		const fullKey = keyPrefix ? keyPrefix + '.' + key : key
		const err = mergeSingleItemOrProperty(target, config, key, fullKey)
		if (err) return err
	}
	return null // no error
}

function mergeSingleItemOrProperty(target, config, key, fullKey) {
	const origValue = target[key]
	const newValue = config[key]

	if (!hasCompatibleType(origValue, newValue)) {
		return 'Cannot merge values of incompatible types for the option `'
						+ fullKey + '`.'
	}

	if (Array.isArray(origValue)) {
		return mergeArrays(origValue, newValue, fullKey)
	}

	if (newValue !== null && typeof origValue === 'object') {
		return mergeObjects(origValue, newValue, fullKey)
	}

	target[key] = newValue
	return null // no error
}

function mergeArrays(target, config, keyPrefix) {
	if (target.length !== config.length) {
		return 'Cannot merge array values of different length'
						+ ' for the option `' + keyPrefix + '`.'
	}

	// Use for(;;) to iterate over undefined items, for(in) would skip them.
	for (let ix = 0; ix < target.length; ix++) {
		const fullKey = keyPrefix + '[' + ix + ']'
		const err = mergeSingleItemOrProperty(target, config, ix, fullKey)
		if (err) return err
	}

	return null // no error
}

function hasCompatibleType(origValue, newValue) {
	if (origValue === null || origValue === undefined) {
		return true
	}

	if (Array.isArray(origValue)) {
		return Array.isArray(newValue)
	}

	if (typeof origValue === 'object') {
		return typeof newValue === 'object'
	}

	// Note: typeof Array() is 'object' too,
	// we don't need to explicitly check array types
	return typeof newValue !== 'object'
}

/**
 * Check synchronously if a filepath points to an existing file.
 * Replaces calls to fs.existsSync, which is deprecated (see:
 * https://github.com/nodejs/node/pull/166).
 *
 * @param   {String} filepath The absolute path to check
 * @return {Boolean}  True if the file exists
 */
function fileExistsSync(filepath) {
	debug('fileExistsSync %j', arguments)
	try {
		fs.statSync(filepath)
		return true
	}
	catch (e) {
		return false
	}
}
