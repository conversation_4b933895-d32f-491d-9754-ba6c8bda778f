{"name": "Machine", "plural": "Machines", "base": "PersistedModel", "idInjection": true, "strict": true, "mixins": {"MultitenantRemote": true}, "properties": {"id": {"type": "string", "id": true, "generated": true}, "provider": {"type": "String", "required": true, "description": "Name (key) of provider", "enum": ["zeal", "cloudretail"]}, "machineId": {"type": "String", "required": true, "description": "from provider (external)"}, "name": {"type": "String", "max": 32, "required": true, "description": "of machine (for human)"}, "type": {"type": "String", "required": true, "enum": ["label", "receipt"]}, "purposes": {"type": ["string"], "required": true}, "enabled": {"type": "boolean", "default": true}, "model": {"type": "String", "max": 16, "description": "of machine (for human)"}, "config": {"type": "object", "description": "provider-specific, printer eg. key, width, height, etc"}}, "validations": [], "relations": {"place": {"type": "belongsTo", "model": "Place", "foreignKey": "placeId"}}, "acls": [], "methods": {"prototype.isOnline": {"http": {"verb": "get", "path": "/online"}, "accepts": [], "description": "Get online status of machine", "returns": {"type": "boolean", "root": true}}}}