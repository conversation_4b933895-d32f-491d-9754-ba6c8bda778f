{"name": "Member", "plural": "Members", "base": "PersistedModel", "idInjection": true, "strict": false, "mixins": {"MultitenantRemote": true}, "properties": {"id": {"type": "string", "id": true, "generated": true}, "since": {"type": "date"}, "behaviors": {"type": "object", "default": {}}, "createdAt": {"type": "date"}, "modifiedAt": {"type": "date"}}, "validations": [], "acls": [], "indexes": {}, "scopes": {}, "methods": {"doFind": {"description": "Find all instances of the model matched by filter from the data source.", "http": {"path": "/find", "verb": "post"}, "accepts": {"arg": "filter", "type": "object"}, "returns": {"type": "array", "root": true}}, "doFindOne": {"description": "Find first instance of the model matched by filter from the data source.", "http": {"path": "/findOne", "verb": "post"}, "accepts": {"arg": "filter", "type": "object"}, "returns": {"type": "object", "root": true}}, "doCount": {"description": "Count instances of the model matched by where from the data source.", "http": {"path": "/count", "verb": "post"}, "accepts": {"arg": "where", "type": "object"}, "returns": {"type": "number", "root": true}}, "activeMembershipsOf": {"description": "Get active memberships of Person (highest priority first), optionally for tierLevel of programId", "http": {"path": "/activeMembershipsOf", "verb": "get"}, "accepts": [{"arg": "personId", "type": "any", "required": true}, {"arg": "programId", "type": "any"}, {"arg": "tierLevel", "type": "number"}], "returns": {"type": "object", "root": true}}, "staffCheckin": {"description": "Staff to be assigned to a store  (Store checkin)", "http": {"path": "/staff/checkin", "verb": "post"}, "accepts": [{"arg": "spot", "type": "object", "required": true}, {"arg": "options", "description": "{ source, staff, location, installation, forceUpdate }", "type": "object", "required": true}], "returns": {"type": "object", "root": true}}, "staffCheckout": {"description": "Staff to be de-assigned from current store  (Store checkout)", "http": {"path": "/staff/checkout", "verb": "post"}, "accepts": [{"arg": "spot", "type": "object", "required": true}, {"arg": "options", "description": "{ source, staff, location, installation, forceUpdate }", "type": "object", "required": true}], "returns": {"type": "object", "root": true}}, "staffCardIssue": {"description": "Issue staff card", "http": {"path": "/staff/issueCard", "verb": "post"}, "accepts": [{"arg": "programId", "type": "string", "required": true}, {"arg": "tierLevel", "type": "number", "required": true}, {"arg": "personId", "type": "string"}, {"arg": "profile", "type": "object"}, {"arg": "through", "type": "object"}, {"arg": "options", "type": "object"}], "returns": {"type": "object", "root": true}}, "staffCardTerminate": {"description": "Terminate a staff card", "http": {"path": "/staff/terminate", "verb": "post"}, "accepts": [{"arg": "membershipId", "type": "string", "required": true}, {"arg": "reason", "type": "string"}, {"arg": "through", "type": "object"}], "returns": {"type": "object", "root": true}}, "createWithPerson": {"description": "Create Member with associated Person", "http": {"path": "/createWith<PERSON>erson", "verb": "post"}, "accepts": [{"arg": "profile", "type": "object"}, {"arg": "through", "type": "object"}], "returns": {"type": "object", "root": true}}}}