{"name": "Assignment", "plural": "Assignments", "base": "PersistedModel", "idInjection": true, "strict": false, "mixins": {"MultitenantRemote": true}, "options": {"validateUpsert": true}, "properties": {"id": {"type": "String", "id": true, "generated": true}}, "validations": [], "relations": {"staff": {"type": "belongsTo", "model": "Staff", "foreignKey": "staffId"}, "place": {"type": "belongsTo", "model": "Place", "foreignKey": "placeId"}}, "acls": [], "indexes": {}, "scopes": {}, "methods": {}}