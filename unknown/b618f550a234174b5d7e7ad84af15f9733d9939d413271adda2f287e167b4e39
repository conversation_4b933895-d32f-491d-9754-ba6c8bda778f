{"name": "Spot", "plural": "Spots", "base": "Addr", "idInjection": true, "forceId": false, "strict": false, "mixins": {}, "options": {}, "properties": {"type": {"type": "String", "description": "Place Type, eg. store, vending"}, "name": {"type": "String"}, "placeId": {"type": "String"}, "resourceId": {"type": "String"}, "position": [{"type": {"key": {"type": "string", "description": "Unique name for position identifier, eg. table, hall, seat, vending (globalized)"}, "value": {"type": "string", "description": "Value position key, eg. 8 (for table), 3H (for seat), <machineId> for vending"}}}]}, "validations": [], "relations": {}, "acls": [], "indexes": {}, "scopes": {}, "methods": []}