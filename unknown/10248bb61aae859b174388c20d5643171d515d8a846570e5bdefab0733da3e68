{"name": "Aggregate", "base": "PersistedModel", "idInjection": true, "options": {"validateUpsert": true}, "mixins": {"Mongo": true, "MongoCollection": true, "Multitenant": true, "Aggregate": true}, "properties": {"id": {"id": true, "type": "string"}, "key": {"type": "String", "required": true}, "dimensions": {"type": "Object"}, "dValues": {"type": "Object"}, "campaignId": {"type": "String", "description": "only use in campaign, other service no need to set this value"}, "startTS": {"type": "Number", "description": "timestamp of scale start"}, "refreshTS": {"type": "Number", "description": "timestamp of refresh this document"}, "value": {"type": "Number"}}, "validations": [], "relations": {}, "acls": [], "methods": {}}