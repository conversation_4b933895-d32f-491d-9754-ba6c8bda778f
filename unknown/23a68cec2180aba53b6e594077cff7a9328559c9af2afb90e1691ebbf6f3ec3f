{"name": "WidgetConfig", "plural": "WidgetConfig", "idInjection": false, "strict": true, "options": {"validateUpsert": false}, "properties": {"id": false, "key": {"type": "string", "max": 32, "required": true, "description": "Unique key of widget"}, "kind": {"type": "string", "required": true, "description": "Type of widget", "enum": ["uri", "applet", "place", "wifi", "share", "offer", "message", "reward", "ticket", "pay"]}, "param": {"type": "object", "description": "Custom parameters, overrides corresponding in Card Master"}, "startTime": {"type": "date"}, "endTime": {"type": "date"}, "visible": {"type": "boolean"}}, "scopes": {}, "methods": {}}