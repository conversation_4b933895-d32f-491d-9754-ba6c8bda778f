{"name": "Sharing", "plural": "Sharings", "base": "PersistedModel", "idInjection": true, "forceId": false, "strict": false, "options": {"validateUpsert": true}, "mixins": {"Common": true, "Timestamp": true, "FindOneAndUpdate": true}, "properties": {"mode": {"type": "String", "enum": ["clone", "invite", "send", "transfer", "subobject"]}, "recipient": {"type": {"personId": {"type": "String", "description": "Person id of recipient"}, "mobile": {"type": "String", "max": 14, "description": "Mobile number of recipient (full number without '+ 'prefix)"}, "name": {"type": "String", "max": 80, "description": "Fullname of recipient"}, "imageUrl": {"type": "string", "description": "URL of recipient ProfileImage (for app)"}}}, "originId": {"type": "string", "description": "Id of instance from which share originated"}, "sharedId": {"type": "string", "description": "Shared (new) object Id"}, "generation": {"type": "Number", "default": 1, "description": "nth level of sharing"}, "channel": {"type": "String", "description": "ie. sms, wechat, whatsapp, messenger, ..."}, "purgeTime": {"type": "date", "description": "Time when instance will be auto-deleted"}, "when": {"type": {"received": {"type": "Date", "default": null}, "accepted": {"type": "Date", "default": null}, "declined": {"type": "Date", "default": null}, "registered": {"type": "Date", "default": null}, "activated": {"type": "Date", "default": null}, "cancelled": {"type": "Date", "default": null}}, "default": {}}, "createdAt": {"type": "Date"}, "modifiedAt": {"type": "Date"}}, "validations": [], "relations": {"source": {"type": "belongsTo", "polymorphic": true}}, "acls": [], "indexes": {"sourceId_index": {"keys": {"sourceId": 1}}, "recipientId_index": {"keys": {"recipientId": 1}}}, "scopes": {}, "methods": {}}