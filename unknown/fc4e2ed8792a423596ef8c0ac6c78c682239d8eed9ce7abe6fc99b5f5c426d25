/**
 * @module Mixin:DisableRemotes
 * Disables specified remote methods on a model
 *
 * Usage in model.json:
 * "mixins": {
 *   "DisableRemotes": {
 *     "createChangeStream": true,
 *     "changeStream": true
 *   }
 * }
 */

const methodNames = [
	'create',
	'upsert',
	'updateAll',
	'prototype.patchAttributes',
	'find',
	'findById',
	'findOne',
	'findOrCreate',
	'createChangeStream',	// post
	'changeStream',			// get
	'deleteById',
	'confirm',
	'count',
	'exists',
	'replaceById',
	'replaceOrCreate',
	'upsertWithWhere',
]

/**
 * Disables only the remote methods specified in options
 * @param {Object} Model - The model to disable remote methods on
 * @param {Object} options - Object with method names as keys and true values to disable
 */
module.exports = function(Model, options = {}) {
	const relations = Object.keys(Model.definition.settings.relations || {})

	// Process each method to disable
	Object.keys(options).forEach(methodName => {
		// Skip if the option is not set to true
		if (options[methodName] !== true) return

		try {
			// Handle relation methods (e.g. "__findById__users")
			if (methodName.includes('__')) {
				const [ prefix, relation ] = methodName.split('__')
				if (relations.includes(relation)) {
					Model.disableRemoteMethodByName(`${prefix}__${relation}`)
				}
			}
			// Handle standard methods
			else if (methodNames.includes(methodName)) {
				Model.disableRemoteMethodByName(methodName)
			}
			// Handle full relation method names (e.g. "prototype.__findById__users")
			else {
				Model.disableRemoteMethodByName(methodName)
			}
		}
		catch (err) {
			console.error(`Failed to disable remote method ${methodName}:`, err)
		}
	})
}
