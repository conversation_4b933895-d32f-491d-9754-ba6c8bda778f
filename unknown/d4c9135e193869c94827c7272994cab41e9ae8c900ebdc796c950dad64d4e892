/**
 * @module Mixin:Embeds
 */

module.exports = function(Model) {
	Object.values(getEmbeds(Model.relations)).forEach(setupMethod)

	function setupMethod(relation) {
		const { name: relationName, modelTo, keyFrom, keyTo, options = {} } = relation,
			{ name: modelName } = modelTo,
			{ methods = [] } = options

		if (methods.includes('default')) {
			const defaultMethod = `default${modelName}`

			Model.prototype[defaultMethod] = function(id) {
				return _default(this, keyFrom, keyTo, id)
			}

			Model.remoteMethod(`prototype.${defaultMethod}`, {
				description: 'Set default ' + modelName.toLowerCase(),
				http: { path: '/' + relationName + '/:fk/default', verb: 'post' },
				accepts: [ { arg: 'fk', type: 'string', required: true } ],
				returns: { type: 'object', root: true },
			})
		}
	}
}

/**
 * Move embedded item to first in list
 * @param {*} instance
 * @param {*} keyFrom
 * @param {*} keyTo
 * @param {*} id
 * @return	{Promise<Object>} embedded item
 */
async function _default(instance, keyFrom, keyTo, id) {
	const list = instance[keyFrom],
		index = list.findIndex(row => row[keyTo].toString() === id)

	if (index < 0) return Promise.resolve(null)
	if (index === 0) return Promise.resolve(list[0])

	const moved = list.splice(index, 1)[0]
	list.unshift(moved)

	await instance.updateAttributes({ [keyFrom]: list })
	return moved
}

/**
 * Shortlist embed relations from Model.relations
 * @return	{Object} embed relations
 */
function getEmbeds(relations) {
	return Object.values(relations).reduce((embeds, relation) => {
		if (relation.embed) embeds[relation.name] = relation
		return embeds
	}, {})
}
