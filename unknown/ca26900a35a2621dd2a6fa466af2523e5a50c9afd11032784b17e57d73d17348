{"personalize": {"person": {"model": "Person", "properties": ["<PERSON><PERSON><PERSON>"]}, "membership": {}, "points": []}, "dependencies": {"user": {"custom": {"heading": {"type": "text", "max": 20}}}, "model": {"business": {"brand": "business.name", "logo": ""}, "campaign": {"id": "campaign.id"}, "program": {"name": "program.name", "tier": {"cardImage": "", "name": "program.tier.name"}}}}, "recommend": {}, "system": {"perkd": {"logo": "", "downloadUrl": "https://perkd.me/app"}, "email": {"unsubscribe": ""}, "facebook": {"pixelId": ""}}, "alias": {"custom": {"heading": "dependencies.user.custom.heading", "desc": "dependencies.user.custom.description", "offers": "dependencies.user.custom.offers", "terms": "dependencies.user.custom.terms"}, "style": {"bg": "dependencies.user.style.background", "text": "dependencies.user.style.text", "accent": "dependencies.user.style.accent", "list": "dependencies.user.style.list"}, "asset": {"img": "dependencies.user.asset.image"}, "content": {"url": "dependencies.model.content.url"}, "business": {"brand": "dependencies.model.business.brand", "logo": "dependencies.model.business.logo"}, "program": {"name": "dependencies.model.program.name", "tier": "dependencies.model.program.tier.name", "card": "dependencies.model.program.tier.cardImage"}, "campaign": {"id": "dependencies.model.campaign.id"}, "fb": {"pixel": "system.facebook.pixelId"}}}