/**
 *  @module personalizeLib
 */

module.exports = exports = {
	injectImages,
	fetchImages,
	howPersonalized,
	globalize,
	// properties accessors
	person: { get: getPerson },
	membership: { get: getMembership },
}

/**
 * Fetch Person data required by personalize
 * @param	{Person} Person
 * @param	{String[]} languages
 * @param	{Array} recipients
 * @param	{String} key
 * @param	{Array} properties
 * @param	{Array} images
 * @return	{Object} 'globalized' person data
 */
async function getPerson(Person, languages, recipients = [], key, properties, images) {
	const personIds = recipients.map(m => m.personId),
		filter = {
			where: { id: { inq: personIds } },
			fields: properties.concat([ 'id', 'globalize' ]),
		},
		instances = await Person.find(filter),
		data = { [key]: listToIdCollection(key, instances, properties) }

	return globalize(data, languages)
}

/**
 * Fetch Membership data required by personalize
 * @param	{Membership} Membership
 * @param	{String[]} languages
 * @param	{Array} recipients
 * @param	{String} key
 * @param	{Array} properties
 * @param	{Object} options
 * @return	{Object} 'globalized' membership data
 */
async function getMembership(Membership, languages, recipients = [], key, properties, options) {
	const { programId } = options,
		personIds = recipients.map(m => m.personId),
		filter = {
			where: {
				programId,
				personId: { inq: personIds },
				state: 'active',
			},
			fields: properties.concat([ 'programId', 'personId', 'state' ]),
		},
		instances = await Membership.find(filter),
		data = { [key]: listToIdCollection(key, instances, properties, 'personId') }

	return globalize(data, languages)
}

/**
 * Convert related images of instances into image properties { url, width, height } and inject into instances
 * @param	{Model} model
 * @param	{Object} images
 * @param	{Object} instances
 * @return	{Array} of instances
 */
async function injectImages(model, images, instances) {
	if (!images) return instances

	const fetches = []

	for (const instance of instances) {
		fetches.push(
			fetchImages(model, instance, images).then(img => Object.assign(instance, img))
		)
	}

	return Promise.all(fetches)
}

/**
 * Get properties (original & versions) of images (instance) related (relations) to model
 * @param	{Model} model
 * @param	{Object} instance
 * @param	{Array} relations
 * @return	{Object} { <relation>: { original: { url, width, height }, <version>: { url, width, height } } }
 */
async function fetchImages(model, instance, relations = []) {
	const res = {}

	for (const relation of relations) {
		if (!model.relations[relation]) continue

		const images = await instance[relation]().catch(err => console.error('fetchImages', err))

		for (const image of images) {
			const { original, versionList } = image,
				{ url, width, height } = original

			res[relation] = { original: { url, width, height } }

			for (const { type, url, width, height } of versionList) {
				res[relation][type] = { url, width, height }
			}
		}
	}

	return res
}

/**
 * Detect 'personal' & 'non-personal' data required in 'personalize' property
 * @param	{Object} personalize
 * @return	{Object} { hasPersonal, hasNonPersonal }
 */
function howPersonalized(personalize) {
	const res = { hasPersonal: false, hasNonPersonal: false }

	for (const [ key, value ] of Object.entries(personalize)) {
		if (value.ids.length === 0) res.hasPersonal = true
		else res.hasNonPersonal = true
	}
	return res
}

/**
 * Convert collection into globalize structure
 * @param	{Object} collection
 * @param	{Array} languages
 * @param	{Locale} locale
 * @return	{Object} provider (API)
 */
function globalize(collection, languages, locale) {
	const res = {
		[languages[0]]: collection,		// default language		// FIXME: @alvin, default wrong?
	}

	for (let i = 1; i < languages.length; i++) {
		res[languages[i]] = collection
	}
	return res
}

// ----  Private functions  ----

/**
 * Convert list of instances to id collection
 * @param	{String} key
 * @param	{Array} instances
 * @param	{Array} properties
 * @param	{String} idName name of id property
 * @return	{Object} { id: { ...properties }}
 */
function listToIdCollection(key, instances, properties, idName = 'id') {
	return instances.reduce((res, instance) => {
		// const { id, personId, ...props } = instance.toJSON();
		res[instance[idName]] = { [key]: pick(instance.toJSON(), properties) }		// TODO: can optimize pick?
		return res
	}, {})
}

// TODO: move to objHelper.js (common)
function pick(obj, properties) {
	const res = {}
	for (const key in obj) {
		if (properties.includes(key)) res[key] = obj[key]
	}
	return res
}
