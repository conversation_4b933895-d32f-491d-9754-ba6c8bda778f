{"name": "TimeseriesValue", "plural": "TimeseriesValues", "base": "PersistedModel", "idInjection": true, "strict": false, "mixins": {"Multitenant": true, "Timeseries": true, "Mongo": true, "MongoCollection": true}, "options": {"mongodb": {"collection": "_m_ts_value"}}, "properties": {"key": {"type": "string"}, "dimensions": {"type": "array"}, "dValues": {"type": "array"}, "timestamp": {"type": "number"}, "value": {"type": "any"}, "createdAt": {"type": "date"}, "modifiedAt": {"type": "date"}}, "validations": [], "relations": {}, "acls": [], "indexes": {}, "scopes": {}, "methods": {}}