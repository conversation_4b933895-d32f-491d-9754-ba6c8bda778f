{"name": "Share", "plural": "Share", "base": "Model", "idInjection": false, "strict": true, "options": {}, "properties": {"mode": {"type": "string", "required": true, "description": "Sharing mode. Note: `subobject` is mode for shared object (offer, reward, ...), which is a copy of shared object controller's sharePolicies", "enum": ["clone", "invite", "send", "transfer", "subobject"]}, "toCardMasterIds": {"type": [{"type": "string"}], "default": [], "description": "Shared object to be attached to a card of one of these cardmasters, default use first one when issue card"}, "limit": {"type": "number", "default": null, "description": "Number of times object can be shared, null => unlimited"}, "remain": {"type": "number", "default": null, "description": "Remaining number of time object can be shared"}, "count": {"type": "number", "default": 0, "description": "Number of times object has been shared"}, "max": {"type": {"perPerson": {"type": "number", "default": null, "description": "Times object can be shared per person"}, "generation": {"type": "number", "default": null, "description": "Number of generations object can be shared, null => unlimited"}}, "default": {}}, "flows": {"type": {"sharer": {"type": "Flow", "default": null, "description": "Steps for sharer after share (if omitted, will continue sign-up flow)"}, "recipient": {"type": "Flow", "default": null, "description": "Sign-up flow for recipient (if omitted, will use cardMaster's sign-up flow)"}}, "default": {}}, "ttl": {"type": "number", "description": "Time-to-Live of queued shared object (milliseconds)"}, "message": {"type": [{"type": {"method": {"type": "number", "description": "1: from the view of user, 2: from backend system", "default": 1}, "content": {"type": "String"}}}], "default": []}}, "scopes": {}, "methods": {}}