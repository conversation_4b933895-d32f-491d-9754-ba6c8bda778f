/**
 *  @module Model:Action
 */
const { Context } = require('@perkd/multitenant-context')

module.exports = function(Action) {

	Action.create = async function(action, options = {}) {
		const token = getToken(),
			{ ttl } = options,
			data = { action, ttl },
			[ created ] = await Action.createRest(data, token)

		return created
	}

	Action.get = async function(id) {
		if (!id) return undefined

		const token = getToken(),
			[ action ] = await Action.getRest(id, token)

		return action
	}

	Action.search = async function(filter) {
		const token = getToken(),
			[ actions ] = await Action.searchRest(filter, token),
			instances = actions.map(a => new Action(a))

		return instances
	}

	Action.deleteById = async function(id) {
		const token = getToken(),
			[ result ] = await Action.deleteByIdRest(id, token)

		return result
	}

	Action.syncFetch = async function(personId, last) {
		const token = getToken(),
			lastSync = last && last.toISOString(),
			[ actions ] = await Action.syncFetchRest(personId, lastSync, token),
			instances = actions.map(a => new Action(a))

		return instances
	}

	Action.qrCode = async function(id, payload, options) {
		const token = getToken(),
			payloadStr = formatParamSrting(payload),
			optionsStr = formatParamSrting(options),
			[ qrCode ] = await Action.qrCodeRest(id, payloadStr, optionsStr, token)

		return qrCode
	}

	function getToken() {
		return Context.accessToken || Context.generateAccessToken()
	}

	function formatParamSrting(obj = {}) {
		return encodeURIComponent(JSON.stringify(obj))
	}
}
