/**
  *  @module tracker middleware
  *
  */

const { bm } = require('@perkd/utils'),
	{ Context } = require('@perkd/multitenant-context')

module.exports = function() {
	return function tracker(req, res, next) {
		const url = req.url, start = bm.mark()
		res.once('finish', () => {
			console.log('\n[%s] 👉 %s - %s ms\n', Context.tenant, url, bm.diff(start))
		})
		next()
	}
}

/**
 * End of script
 */
