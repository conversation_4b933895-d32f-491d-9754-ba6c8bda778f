/**
 *  @module Mixin:Globalize
 */

const { pickObj, isEmptyObj } = require('@perkd/utils')

const ENGLISH = 'en'

module.exports = function(Model, options = {}) {

	Model.toGlobalize = function(translations) {
	}

	// -----  Instance Methods  -----

	/**
	 * Generate translations for properties
	 * @param	{String[]} properties (first level only)
	 * @param	{String} [language] all available languages if omitted
	 * @return	{Object}
	 */
	Model.prototype.translations = function(properties, language) {
		const data = this.toJSON(),
			{ globalize = {} } = data,
			languages = language ? [ language ] : Object.keys(globalize),
			translations = {}

		if (language) {
			const obj = globalize[language] || data
			return properties ? pickObj(obj, properties) : obj
		}

		// all languages
		for (const lng of languages) {
			const obj = globalize[lng] || data

			translations[lng] = properties
				? pickObj(obj, properties)
				: obj
		}
		return translations
	}

	/**
	 * Generate default globalize or format globalize to update
	 */
	Model.prototype.buildGlobalize = async function(data) {
		const globalize = this.globalize ?? { t: {}, default: '' }

		if (!this.globalize || isEmptyObj()) {
			const { Business } = Model.app.models,
				filter = {
					where: { isMain: true },
					fields: [ 'locale' ]
				},
				business = await Business.findOne(filter),
				{ locale = {} } = business ?? {},
				{ languages = [] } = locale,
				[ lang = ENGLISH ] = languages

			globalize.t = globalize.t || {}
			globalize.t[lang] = {}
			globalize.default = lang
		}

		data = data || this
		for (const prop of options.properties) {
			if (data[prop]) {
				globalize.t[globalize.default][prop] = data[prop]
			}
		}

		return globalize
	}

	// -----  Remote & Operation hooks  -----

	Model.observe('before save', async ({ data, instance, currentInstance, isNewInstance }) => {
		if (isNewInstance) {
			if (!instance.globalize) {
				const globalize = await instance.buildGlobalize()
				if (globalize) instance.globalize = globalize
			}
		}
		else if (!data?.globalize) {
			if (Object.keys(data || []).find(k => options.properties.includes(k))) {
				const globalize = await currentInstance.buildGlobalize(data)
				if (globalize) data.globalize = globalize
			}
		}
	})
}
