/**
 *  @module Mixin:Audit
 */

const { getDate } = require('@perkd/utils')

module.exports = function(Model) {

	Model.checkDigitalIdMissing = async function(start = { duration: 'P-2D', startOf: 'day' }, end) {
		const startTime = getDate(start),
			endTime = getDate(end),
			where = {
				or: [
					{ 'digital.id': null },
					{ 'digital.id': { exists: false } },
				],
				createdAt: { gte: startTime, lte: endTime },
			},
			total = await Model.count(where)

		if (total) {
			appNotify('digital_id_missing', { total, createdAt: [ startTime, endTime ] }, 'alert')
		}
		return total
	}

	// -----  Remote Methods  -----

	Model.remoteMethod('checkDigitalIdMissing', {
		description: 'Count digital id missing',
		http: { path: '/check/digitalIdMissing', verb: 'post' },
		accepts: [
			{ arg: 'start', type: 'object', description: 'start time rule' },
			{ arg: 'end', type: 'object', description: 'end time rule' },
		],
		returns: { type: 'Object', root: true },
	})
}
