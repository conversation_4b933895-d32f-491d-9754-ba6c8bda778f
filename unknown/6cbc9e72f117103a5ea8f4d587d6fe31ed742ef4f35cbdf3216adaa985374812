/**
 *  @module Model:Activity
 */

module.exports = function(Activity) {
	const UNDO_HANDLERS = {
		redeemOffer,
	};

	Activity.UNDO_HANDLERS = UNDO_HANDLERS;

	// -----  Instance Methods  -----

	Activity.prototype.executeUndo = function(options = {}) {
		const self = this;

		if (self.undo && self.undo.enabled) {
			const undoFn = UNDO_HANDLERS[self.name];
			return undoFn.call(self);
		} return Promise.reject(new Error('The activity `' + self.id + '` has no undo action'));
	};

	// -----  Operation hooks  -----

	Activity.observe('before save', (ctx, next) => {
		const updated = ctx.instance || ctx.data;

		if (ctx.isNewInstance) {
			updated.revokedAt = updated.revokedAt ? new Date(updated.revokedAt) : null;
			updated.deletedAt = updated.deletedAt ? new Date(updated.deletedAt) : null;
		}
		next();
	});

	// -----  Private Methods  -----

	function redeemOffer() {
		return Promise.reject(new Error('Not implemented!'));
	}
};
