/**
 *  @module Model:Validity
 *  <AUTHOR>
 *
 * standard: https://en.wikipedia.org/wiki/ISO_8601
 * duration: http://momentjs.com/docs/#/durations/
 * duration UI: https://github.com/urish/angular2-moment
 */

// Module Dependencies
const ISO8601 = /^([\+-]?\d{4}(?!\d{2}\b))((-?)((0[1-9]|1[0-2])(\3([12]\d|0[1-9]|3[01]))?|W([0-4]\d|5[0-2])(-?[1-7])?|(00[1-9]|0[1-9]\d|[12]\d{2}|3([0-5]\d|6[1-6])))([T\s]((([01]\d|2[0-3])((:?)[0-5]\d)?|24\:?00)([\.,]\d+(?!:))?)?(\17[0-5]\d([\.,]\d+)?)?([zZ]|([\+-])([01]\d|2[0-3]):?([0-5]\d)?)?)?)?$/;

/**
 *
 * @param {Model} Validity
 * @description assert the validity of the offer period,
 * 				There will only be four possibility (from Jack)
 * @example :
 * [dropdown selection index], [dropdown selection index] : start [value], end [value], duration [value]
 * 1, 1 : start 0, end 0, duration null
 * 1, 3 : start 0, end 0, duration "P[number]D"
 * 3, 1 : start "P[number]D", end 0, duration null
 * 3, 3 : start "P[number]D", end "P[number]D", duration null
 */
module.exports = function(Validity) {
	const
		ALLOWED_STARTS = ['membershipStartTime', 'rewardStartTime'],
		ALLOWED_ENDS = ['membershipEndTime', 'rewardEndTime'];

	// -----  Validations  -----
	Validity.validate('start', startFormat, { message: 'Invalid start' });
	Validity.validate('end', endFormat, { message: 'Invalid end' });
	Validity.validate('duration', durationFormat, { message: 'Invalid duration' });

	function startFormat(err) {
		if (this.start) {
			const isIn = ALLOWED_STARTS.indexOf(this.start) > -1;
			if (0 !== this.start && typeof this.start === 'string' &&
				!this.start.startsWith('P') && !ISO8601.test(this.start) && !isIn) { err(); }
		}
	}

	function endFormat(err) {
		if (this.end) {
			const isIn = ALLOWED_ENDS.indexOf(this.end) > -1;
			if (0 !== this.end && !ISO8601.test(this.end) && !isIn) { err(); }
		}
	}

	function durationFormat(err) {
		if (this.duration) {
			const valid = this.duration.startsWith('P');
			if (!valid || typeof this.duration !== 'string') { err(); }
		}
	}
};
