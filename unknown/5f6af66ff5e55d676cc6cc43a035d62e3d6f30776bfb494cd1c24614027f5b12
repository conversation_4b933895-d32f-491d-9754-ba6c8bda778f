{"name": "List", "plural": "Lists", "base": "PersistedModel", "idInjection": true, "strict": true, "options": {"validateUpsert": true}, "mixins": {"Multitenant": true, "Timestamp": true, "Queue": true, "Mongo": true, "FindOneAndUpdate": true, "Tag": true}, "properties": {"name": {"type": "String", "max": 64, "required": true, "unique": true}, "modelName": {"type": "string", "max": 32, "required": true}, "ids": {"type": [], "default": [], "description": "List of ids"}, "tags": {"type": "Tag", "default": {"user": [], "list": []}}, "createdAt": {"type": "Date"}, "modifiedAt": {"type": "Date"}}, "validations": [], "relations": {}, "acls": [], "indexes": {"modelName_index": {"keys": {"modelName": 1}}}, "scopes": {}, "methods": {}}