{"name": "Locale", "idInjection": false, "strict": false, "options": {}, "properties": {"id": false, "languages": {"type": [{"type": "string"}], "default": [], "description": "Apple Language ID: ISO 639-1 + ISO 15924"}, "currency": {"type": "String", "max": 3, "description": "ISO 4217"}, "country": {"type": "string", "length": 2, "description": "ISO 3166-1 alpha-2"}, "timeZone": {"type": "string", "description": "IANA time zone name, eg. Asia/Singapore"}, "regions": {"type": [{"type": "string", "max": 8}], "default": [], "description": "List of region codes: UN/LOCODE with optional city extensions, eg. MY-JHB"}, "numberFormat": {"type": {"decimalSeparator": {"type": "string", "default": "."}, "groupingSeparator": {"type": "string", "default": ","}}}, "useMetric": {"type": "Boolean", "default": true}, "temperature": {"type": "string", "default": "celsius", "enum": ["celsius", "fahrenheit"]}, "calendar": {"type": "string"}}, "validations": [], "scopes": {}, "methods": {}}