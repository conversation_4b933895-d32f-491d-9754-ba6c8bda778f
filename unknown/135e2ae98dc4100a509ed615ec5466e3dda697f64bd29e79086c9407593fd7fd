/**
 *  @module Model:Date
 */

const { parseTime } = require('@perkd/utils')

module.exports = function(Model) {
	// -----  Validations  -----

	// -----  Operation hooks  -----

	// NOTICE: Cannot use "Date", cause Date is a JS keyword
	Model.observe('before save', (ctx, next) => {
		const updated = ctx.instance || ctx.data,
			{ year, month, day, date } = updated

		// 1. get new value from 'date' or Y,M,D
		updated.date = (year && month && day) ? new Date(year, month - 1, day) : date

		if (updated.date !== 'Invalid Date') {
			// 2. update (Y,M,D) values
			if (typeof updated.date !== 'object') {
				updated.date = parseTime(updated.date)
			}

			updated.year = updated.date.getFullYear()
			updated.month = updated.date.getMonth() + 1 // 1 = Jan, 12 = Dec
			updated.day = updated.date.getDate()

			next()
		}
		else next(new Error('Invalid Date'))
	})
}
