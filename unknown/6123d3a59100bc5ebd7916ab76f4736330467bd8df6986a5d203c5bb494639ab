/**
 * @module Model:Phone
 */
const { parsePhoneNumber } = require('@perkd/utils')

//  References:
//  	https://github.com/googlei18n/libphonenumber
//  	https://www.npmjs.com/package/country-data
//		https://rawgit.com/googlei18n/libphonenumber/master/javascript/i18n/phonenumbers/demo-compiled.html

module.exports = function(Phone) {

	// -----  Validations  -----
	// Phone.validatesUniquenessOf('fullNumber', {message: 'fullNumber is not unique'});

	// -----  Operation hooks  -----

	Phone.observe('before save', async ctx => {
		const updated = ctx.instance || ctx.data,
			existingOptIn = ctx.currentInstance ? ctx.currentInstance.optIn : null,
			{ fullNumber: fullNbr, countryCode: ctryCode, number: nbr } = updated,
			phoneNumber = parsePhoneNumber(fullNbr || `${ctryCode}${nbr}`),
			{ regionCode, countryCode, nationalNumber, fullNumber, lineType, possible } = phoneNumber

		updated.regionCode = regionCode
		updated.countryCode = countryCode
		updated.areaCode = ''
		updated.number = nationalNumber
		updated.fullNumber = fullNumber
		updated.lineType = lineType
		updated.valid = possible

		if (existingOptIn === null && updated.optIn === undefined) {
			// user did not provide optIn status, should set default null
			updated.optIn = null
		}
	})
}
