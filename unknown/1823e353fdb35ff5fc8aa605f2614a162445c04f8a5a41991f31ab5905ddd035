{"name": "Style", "description": "Font & color palettes (dark & light theme) in hex", "idInjection": false, "strict": false, "options": {}, "properties": {"id": false, "font": {"type": "String", "max": 32, "description": "Official font name"}, "light": {"type": {"primary": {"type": "string", "max": 10, "description": "Hex color with optional alpha, eg. #112233ff"}, "text": {"type": "string", "max": 10}, "accent": {"type": "string", "max": 10}, "secondary": {"type": "string", "max": 10}, "tertiary": {"type": "string", "max": 10}, "background": {"type": "string", "max": 10}}, "default": {}, "description": "Light theme color palette"}, "dark": {"type": {"primary": {"type": "string", "max": 10}, "text": {"type": "string", "max": 10}, "accent": {"type": "string", "max": 10}, "secondary": {"type": "string", "max": 10}, "tertiary": {"type": "string", "max": 10}, "background": {"type": "string", "max": 10}}, "default": {}, "description": "Dark theme color palette"}}, "validations": [], "scopes": {}, "methods": {}}