/**
  *  @module stats middleware
  *
  */

const { bm } = require('@perkd/utils')

module.exports = function(options) {

	return function stats(req, res, next) {
		const url = req.url,
			metric = options[`${url}`] || options['*'], // FIXME: httpMethod
			start = bm.mark()

		if (metric) {
			appMetric(metric)
			appMetric(`${metric}.total`)

			res.once('finish', () => {
				appMetric(`${metric}.latency`, bm.diff(start))

				if (res.statusCode >= 500 || res.statusCode < 200) {
					appMetric(`${metric}.error`)
				}
			})
		}

		next()
	}
}
