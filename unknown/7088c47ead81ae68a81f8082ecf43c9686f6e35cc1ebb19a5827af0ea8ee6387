{"name": "PresetCode", "plural": "PresetCodes", "description": "", "base": "PersistedModel", "idInjection": true, "strict": true, "mixins": {"Multitenant": true, "MongoCollection": true, "Errors": true}, "options": {"mongodb": {"allowExtendedOperators": true}}, "properties": {"ownerId": {"type": "string"}, "code": {"type": "string"}, "usedAt": {"type": "date", "default": null}, "createdAt": {"type": "date", "default": null}}, "validations": [], "relations": {}, "acls": [], "indexes": {"ownerId": {"keys": {"ownerId": 1}}}, "methods": {}}