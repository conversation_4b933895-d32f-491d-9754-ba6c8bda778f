/**
 *  @module Mixin:Provider
 */
const { Settings } = require('@crm/types'),
	{ PROVIDER: PROVIDER_ERR } = require('@perkd/errors/dist/service')

const { PROVIDER } = Settings.Name,
	{ EVENT_NOT_HANDLED } = PROVIDER_ERR

module.exports = function(Model) {

	const { tenanted = {} } = appSettings(PROVIDER),
		TENANTED_PROVIDERS = Object.keys(tenanted)

	// -----  Service-level providers (eg. push, sms, email)  ----

	/**
	 * Returns first provider that supports the service (name), managed by Provider (service) module
	 * @param	{String} service name
	 * @param	{String} [module] name
	 * @param	{String} [shop] name - select shop-specific tenanted provider (multimerchant)
	 * @return	{Object|void} provider api
	 */
	Model.getProviderByService = async function(service, module, shop) {
		try {
			return await appModule(PROVIDER)?.getByService(service, module, shop)
		}
		catch (error) {
			console.error(`[${Model.name}]getProviderByService - '${service}'${module ? ` (${module}) ${shop ? `[shop: ${shop}]` : ''}` : ''}`, error)
		}
	}

	/**
	 * Load and initialize provider SDK with tenant's credentials & sdk options
	 *
	 * IMPORTANT:
	 * 		when 'credentials' is set as 'hidden' in model definition for provider.json (Common)
	 * 		credentials is still returned in getByName() BUT .toJSON() will enforce and hide it!
	 * @param	{string} name of provider
	 * @param	{string} [module] name
	 * @param	{String} [shop] name - select shop-specific tenanted provider (multimerchant)
	 * @return	{Promise<Object|void>} provider api
	 */
	Model.getProvider = async function(name, module, shop) {
		try {
			return await appModule(PROVIDER)?.getByName(name, module, shop)
		}
		catch (error) {
			console.error(`[${Model.name}]getProvider - '${name}'${module ? ` (${module})` : ''}${shop ? ` [shop: ${shop}]` : ''}`, error.message)
		}
	}

	/**
	 * Reset in-memory Provider APIs (to force refresh of credentials & config)
	 * @param	{String} service name, optional. if omitted, reset all providers
	 * @return	{null}
	 */
	Model.refreshProvider = async function(service) {
		appModule(PROVIDER)?.refreshProvider(service)
	}

	// -----  Tenanted (model-level) providers (eg. shopify)  ----

	/**
	 * Centralized handler for all provider events (Internal, ie. CRM, and External, ie. Provider)
	 * @param	{Object} evt
	 */
	Model.handleProviderEvents = async function(evt) {
		const { name, domain, actor, action, data } = evt,
			shortName = actor + '.' + action

		if (TENANTED_PROVIDERS.includes(domain)) {
			try {
				await Model.providerExternalEvents(shortName, data)
			}
			catch (err) {
				appNotify('providerExternalEvents', { err, domain, name, data })
			}
		}
		else {
			try {
				await Model.providerInternalEvents(name, data)
			}
			catch (err) {
				appNotify('providerInternalEvents', { err, domain, name, data })
			}
		}
	}

	if (!Model.providerExternalEvents) {
		Model.providerExternalEvents = async function(name, data) {
			appNotify(EVENT_NOT_HANDLED, { name, data }, 'error')
		}
	}

	if (!Model.providerInternalEvents) {
		Model.providerInternalEvents = async function(name, data) {
			appNotify(EVENT_NOT_HANDLED, { name, data }, 'error')
		}
	}

	// used by payment provision
	Model.loadSdk = function(provider) {
		return appModule(PROVIDER).loadSdk(provider)
	}

	// used by payment provision
	Model.getAPI = async function(config, module, service) {
		return appModule(PROVIDER).getAPI(config, module, service)
	}

	// -----  Instance-level providers (eg. payment)  ----

	/**
	 * Return provider associated with this instance
	 * @return {Promise<Object|void>} provider api
	 */
	Model.prototype.getProvider = async function() {
		return appModule(PROVIDER)?.getAPI(this.toJSON())
	}
}
