/**
* @module Mixin:ListOwnerRemote - only use on remote model definitions (in Common)
*
*/

const
	LIST = 'List';

module.exports = function(Model, options) {
	// -----  Static Properties  -----

	const { ListModel = LIST } = options,
		name = ListModel.charAt(0).toLowerCase() + ListModel.slice(1),
		relation = name + 's';

	// -----  Remote Methods  -----

	Model.remoteMethod('prototype.addToList', {
		description: `Add a related List by id for ${relation}`,
		http: { path: `/${relation}/rel/:fk`, verb: 'put' },
		accepts: [
			{ arg: 'fk', type: 'string', http: { source: 'path' }, required: true, description: `Foreign key for ${relation}` },
			{ arg: 'asDefault', type: 'boolean', description: 'Set as 1st element (default)' },
		],
		returns: { type: 'object', root: true },
	});

	Model.remoteMethod('prototype.removeFromList', {
		description: `Remove a related List by id for ${relation}`,
		http: { path: `/${relation}/rel/:fk`, verb: 'delete' },
		accepts: [
			{ arg: 'fk', type: 'string', http: { source: 'path' }, required: true, description: `Foreign key for ${relation}` },
		],
		returns: { type: 'object', root: true },
	});

	Model.remoteMethod('prototype.setAsDefault', {
		description: `Set a related List by id as 1st element for ${relation}`,
		http: { path: `/${relation}/rel/:fk/default`, verb: 'post' },
		accepts: [
			{ arg: 'fk', type: 'string', http: { source: 'path' }, required: true, description: `Foreign key for ${relation}` },
		],
		returns: { type: 'object', root: true },
	});

	Model.remoteMethod('prototype.getList', {
		description: `Queries ${relation} of ${Model.name}`,
		http: { path: `/${relation}`, verb: 'get' },
		accepts: [
			{ arg: 'filter', type: 'object', http: { source: 'query' } },
		],
		returns: { type: 'object', root: true },
	});
};
