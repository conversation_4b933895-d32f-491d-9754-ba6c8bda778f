/**
 *  @module Model:OpeningHour
 * 		References:   https://developers.google.com/maps/documentation/javascript/places
 */
/**

   openingHours = {
		specific: [ { dateRange: { start: <date>, end: <date> }, periods: [] } ],
		periods: [ {
			open: { day: <number>, time: <string> },
			close: { day, time },
			busy: [ { start, end } ]
		} ]
 	}

	[open] contains a pair of 'day' and 'time' objects describing when the place opens:
		day 	a number from 0–6, corresponding to the days of the week, starting on Sunday.
				For example, 2 means Tuesday.
		time	may contain a time of day in 24-hour 'hhmm' format (values are in the range 0000–2359).
				The time will be reported in the place’s timezone.

	[close] may contain a pair of 'day' and 'time' objects describing when the place closes.

	[busy] array of { 'start' and 'end' } objects describing when the place is busy.

	Stored sorted:  'specific' stored sorted by 'start' date, 'periods' sorted by 'day'

	Note: If a place is always open, the close section will be missing.
	ie. always-open being represented as an open period containing day with value 0 and time with value 0000, and no close.
 */

module.exports = function(OpeningHour) {

	OpeningHour.prototype.valid = function(openingHour) {
		openingHour = openingHour || this

		const validSpecific = !openingHour.specific || this.validSpecifics(openingHour.specific),
			validPeriod = !openingHour.periods || this.validPeriods(openingHour.periods)

		return validSpecific && validPeriod
	}

	OpeningHour.prototype.validSpecifics = function(specifics) {
		specifics = specifics || this.specific

		let lastEnd = null,
			valid = true

		if (!Array.isArray(specifics)) return false

		for (const spec of specifics) {
			const { dateRange: range } = spec

			range.start = (range.start instanceof Date) ? range.start : new Date(range.start)
			range.end = (range.end instanceof Date) ? range.end : new Date(range.end)

			if (range && range.start && range.end && (range.end >= range.start)) {
				if ((lastEnd && range.start <= lastEnd) || (!this.validPeriods(spec.periods))) {
					valid = false
				}
				else {
					lastEnd = range.end
				}

				// FIXME: '2019-03-30T16:00:00:000Z' getDay 6 on server
				// ensure periods define 'day' for every day-of-week within dateRange
				// const days = daysOfWeek(range.start, range.end);
				// days.forEach((day) => {
				// 	if (self.periodsFor(spec.periods, day).length === 0) {
				// 		valid = false;
				// 		// console.log('validSpecifics():  no period for ', day);		// TODO   use watchDog.debug
				// 	}
				// });
			}
			else {
				valid = false
			}
		}

		return valid
	}

	OpeningHour.prototype.periodsFor = function(periods = [], day) {
		const selected = []

		for (const period of periods) {
			if (period.open?.day === day || period.close?.day === day) {
				selected.push(period)
			}
		}

		return selected
	}

	OpeningHour.prototype.validPeriods = function(periods = []) {
		periods = periods || this.periods

		if (!Array.isArray(periods)) return false

		let lastEnd = null,
			valid = true

		for (const period of periods) {
			if (this.validPeriod(period)) {
				if (lastEnd && period.start <= lastEnd) valid = false
				else lastEnd = period.end
			}
			else {
				valid = false
			}
		}

		return valid
	}

	OpeningHour.prototype.validPeriod = function(period = {}) {
		return period.open && validDayTime(period.open) && (period.close ? validDayTime(period.close) : true)

		function validDayTime(dayTime) {
			if (!isNaN(dayTime.day) && typeof dayTime.day !== 'number') {
				dayTime.day = Number(dayTime.day)
			}
			const
				dayInPeriod = !isNaN(dayTime.day) && (dayTime.day >= 0) && (dayTime.day <= 6),
				validDay = (isNaN(dayTime.day) || dayTime.day === undefined) || dayInPeriod	// 0 = Sunday, 6 = Saturday

			const time = parseInt(dayTime.time)

			return (validDay === true) && (time >= 0 && time <= 2400)
		}
	}

	// -----  Validations  -----

	OpeningHour.validate('periods', validatePeriods, { message: 'Error in time periods' })

	function validatePeriods(err) {
		if (this.periods && !this.validPeriods(this.periods)) err()
	}

	OpeningHour.validate('specific', validateSpecific, { message: 'Error in specific period' })

	function validateSpecific(err) {
		if (this.specific && !this.validSpecifics(this.specific)) err()
	}
}
