{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
    
        {
            "type": "node",
            "request": "launch",
            "name": "Launch Program",
            "program": "${workspaceFolder}/server/server.js"
        },
        {
            "type": "node",
            "request": "launch",
            "name": "Launch File",
            "program": "${file}"
        },
        {
            "type": "node",
            "request": "launch",
            "name": "Launch Test",
            "runtimeExecutable": "mocha",
            "runtimeArgs": [
                "--colors",
                "--timeout",
                "0",
            ],
            "program": "${file}"
        }
    ]
}