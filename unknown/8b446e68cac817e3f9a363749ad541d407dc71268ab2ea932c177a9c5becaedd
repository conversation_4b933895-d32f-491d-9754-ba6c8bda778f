const _ = require('lodash'),
	{ diff } = require('deep-diff'),
	{ isEmptyObj, cloneDeep, getDescendantProp } = require('@perkd/utils')

function inherits(ctor, superCtor) {
	ctor.super_ = superCtor
	ctor.prototype = Object.create(superCtor.prototype, {
		constructor: {
			value: ctor,
			enumerable: false,
			writable: true,
			configurable: true,
		},
	})
}

function Diff(kind, path) {
	Object.defineProperty(this, 'kind', {
		value: kind,
		enumerable: true,
	})
	if (path && path.length) {
		Object.defineProperty(this, 'path', {
			value: path,
			enumerable: true,
		})
	}
}

function DiffEdit(path, origin, value) {
	DiffEdit.super_.call(this, 'E', path)
	Object.defineProperty(this, 'lhs', {
		value: origin,
		enumerable: true,
	})
	Object.defineProperty(this, 'rhs', {
		value,
		enumerable: true,
	})
}
inherits(DiffEdit, Diff)
exports.DiffEdit = DiffEdit

function DiffNew(path, value) {
	DiffNew.super_.call(this, 'N', path)
	Object.defineProperty(this, 'rhs', {
		value,
		enumerable: true,
	})
}
inherits(DiffNew, Diff)
exports.DiffNew = DiffNew

function DiffDeleted(path, value) {
	DiffDeleted.super_.call(this, 'D', path)
	Object.defineProperty(this, 'lhs', {
		value,
		enumerable: true,
	})
}
inherits(DiffDeleted, Diff)
exports.DiffDeleted = DiffDeleted

function objectKeyEqual(lhs, rhs, uniqueProperty) {
	if (Array.isArray(uniqueProperty)) {
		for (let i = 0; i < uniqueProperty.length; i++) {
			const lid = getDescendantProp(lhs, uniqueProperty[i]),
				rid = getDescendantProp(rhs, uniqueProperty[i])
			if (lid && rid && lid === rid) {
				return true
			}
		}
		return false
	}
	const lid = getDescendantProp(lhs, uniqueProperty),
		rid = getDescendantProp(rhs, uniqueProperty)
	return (lid === rid)
}

function getArrayDelta(origin, update, uniqueProperty, listPropertyName) {
	const delta = [],
		lhs = cloneDeep(origin[listPropertyName]),
		rhs = cloneDeep(update[listPropertyName]),
		rhs2 = cloneDeep(rhs)

	for (let i = 0; i < lhs.length; i++) {
		// find subroutine
		let found = null, rhsLen = rhs2.length
		// do not add empty array as a diff
		// if (rhs.length <= 0) {
		// 	delta.push(new DiffDeleted([listPropertyName], lhs[i]));
		// }

		// in order to find we must make one assumption:
		// all element in this array is unique
		for (let j = 0; j < rhsLen; j++) {
			if ((uniqueProperty && objectKeyEqual(lhs[i], rhs2[j], uniqueProperty))
			|| (!uniqueProperty && lhs[i] === rhs2[j])) {
				found = rhs2[j]
				rhs2.splice(j, 1)
				rhsLen--
				break
			}
		}
		if (found) {
			if (!_.isEqual(lhs[i], found)) { // edited
				delta.push(new DiffEdit([ listPropertyName ], lhs[i], found))
			}// else is same object
		}
		else { // deleted
			delta.push(new DiffDeleted([ listPropertyName ], lhs[i]))
		}
	}
	if (rhs2.length > 0) {
		rhs2.forEach(e => {
			delta.push(new DiffNew([ listPropertyName ], e))
		})
	}
	return delta
}

function getModelDelta(origin, update, uniqueProperty, listPropertyName) {
	let lhs, rhs
	try {
		lhs = cloneDeep(origin)
		rhs = cloneDeep(update)
	}
	catch (err) {
		lhs = JSON.parse(JSON.stringify(origin))
		rhs = JSON.parse(JSON.stringify(update))
	}
	if (uniqueProperty) {
		if (lhs && lhs[uniqueProperty]) {
			return (rhs[uniqueProperty]) ? new DiffEdit(listPropertyName, lhs, rhs) : new DiffDeleted(listPropertyName, lhs)
		}
	}
	else if (!isEmptyObj(lhs)) {
		return isEmptyObj(rhs) ? new DiffDeleted(listPropertyName, lhs) : new DiffEdit(listPropertyName, lhs, rhs)
	}
	return new DiffNew(listPropertyName, rhs)
}

/**
 * @description For detail usage see trials/testNewDeepDiff.js example
 * @param {Array/string} listPropertyNames
 * @param {Array/string} uniqueProperties
 */
function diffArray(listPropertyNames, uniqueProperties) {
	/**
	 *
	 * @param {Object} rhs New instance
	 */

	return function(before, after) {
		listPropertyNames = Array.isArray(listPropertyNames) ? listPropertyNames : [ listPropertyNames ]
		uniqueProperties = Array.isArray(uniqueProperties) ? uniqueProperties : [ uniqueProperties ]

		if ((before && before[listPropertyNames[0]]) || (after && after[listPropertyNames[0]])) {
			let delta = diff(before, after, (path, key) =>
				 listPropertyNames.includes(key) || (key.indexOf('List') !== -1 && key.charAt(0) !== '_')
				// ignore underscore property and ignore property list
			)
			listPropertyNames.forEach((listPropertyName, i) => {
				const modelDelta = getArrayDelta(before, after, uniqueProperties[i], listPropertyName)
				delta = (modelDelta && delta)
					? delta.concat(modelDelta) : [ modelDelta ]
			})

			return delta
		} return [ getModelDelta(before, after, uniqueProperties[0], listPropertyNames[0]) ]
	}
}
module.exports.diffArray = diffArray
