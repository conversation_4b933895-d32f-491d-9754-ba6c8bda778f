{"name": "Staff", "plural": "Staff", "base": "PersistedModel", "idInjection": true, "strict": false, "mixins": {"MultitenantRemote": true}, "properties": {"id": {"type": "string", "id": true, "generated": true}}, "validations": [], "relations": {"dates": {"type": "embeds<PERSON><PERSON>", "model": "Date", "property": "dateList", "options": {"validate": false, "unique": ["date", "name"], "persistent": true, "forceId": false}}, "identities": {"type": "embeds<PERSON><PERSON>", "model": "Identity", "property": "identityList", "options": {"validate": false, "unique": ["provider", "type", "externalId"], "persistent": true, "forceId": false}}, "activities": {"type": "hasMany", "model": "StaffActivity", "foreignKey": "ownerId"}, "business": {"type": "belongsTo", "model": "Business", "foreignKey": "businessId"}, "places": {"type": "hasMany", "model": "Place", "foreignKey": "staffId", "through": "Assignment"}, "person": {"type": "belongsTo", "model": "Person", "foreignKey": "personId"}, "user": {"type": "hasOne", "model": "AuthUser", "foreignKey": "staffId"}, "membership": {"type": "hasOne", "model": "Membership", "foreignKey": "staffId"}}, "acls": [], "scopes": {}, "methods": {"getCardId": {"description": "Get cardId of Staff", "http": {"path": "/card", "verb": "get"}, "accepts": [{"arg": "staffId", "type": "string", "required": true}], "returns": {"type": "string", "root": true}}, "prototype.issueCard": {"description": "Issue card to staff", "http": {"path": "/card/issue", "verb": "post"}, "accepts": [{"arg": "programId", "type": "any", "required": true}, {"arg": "tierLevel", "type": "number", "required": true}, {"arg": "options", "type": "object", "required": false}], "returns": {"type": "object", "root": true}}, "findOrCreateProviderStaff": {"description": "Find or create crm staff for a provider", "http": {"path": "/by<PERSON><PERSON><PERSON>", "verb": "post"}, "accepts": [{"arg": "provider", "type": "string", "required": true}, {"arg": "staffId", "type": "string", "required": true}], "returns": {"type": "object", "root": true}}, "prototype.notify": {"description": "Send push notification to staff", "http": {"path": "/notify", "verb": "post"}, "accepts": [{"arg": "body", "type": "string", "required": true, "description": "Text content or Template name"}, {"arg": "personalize", "type": "object"}, {"arg": "options", "type": "object"}], "returns": {"type": "object", "root": true}}}}