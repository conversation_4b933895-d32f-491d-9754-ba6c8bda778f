/**
  *  @module Inspect	middleware
  *  <AUTHOR>
  *
  *  Logs request in any middleware once enabled
  */

//  Module Dependencies
/*  global appRequire  */
const
	onHeaders = require('on-headers'),
	{ bm } = require('@perkd/utils')

module.exports = function(options) {
	const blacklist = options && options.blacklist || []

	return function inspectLog(req, res, next) {
		const _start = bm.mark()

		if (!isUrlBlackedListed(req.originalUrl)) {
			req.clientIp = getClientIp(req)

			if (options.details) {
				console.log('> req: ', req)
			}
			else {
				console.log('> %s %s', req.method, req.originalUrl) // GET /api/...
			}

			onHeaders(res, () => {
				res._bm = bm.diff(_start, bm.mark())
				res.location = res.get('location')

				if (options.details) {
					console.log('< res: ', res); console.log('\n')
				}
				else {
					console.log('< %s  (%sms)', res.statusCode, res._bm)
					console.log('\n')
				}
			})
		}
		next()
	}

	function getClientIp(req) {
		const xff = req.get('x-forwarded-for')
		if (xff) {
			const ips = xff.split(',').map(ip => ip.trim())
			const ip = ips[ips.length - 1]
			if (ip) {
				return ip
			}
		}
		return req.ip
	}

	function isUrlBlackedListed(url) {
		return blacklist.some(blackListUrl => url.indexOf(blackListUrl) === 0)
	}
}
