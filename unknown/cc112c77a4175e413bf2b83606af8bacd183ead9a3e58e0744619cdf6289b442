/**
 *  @module Mixin:Personalize
 */

const { person, membership, injectImages, globalize } = appRequire('lib/common/personalizeLib')

const // Supported model (names) for personalized data
	PERSON = 'Person',
	MEMBERSHIP = 'Membership',
	POINT = 'Point'

module.exports = function(Model) {

	Model.personalizeFetchPersonal = async function(languages, recipients = [], personalize = {}, hasPersonal) {
		if (!hasPersonal) return {}

		const { models } = Model.app,
			{ Person, Membership } = models,
			keys = Object.keys(personalize),
			fetches = []

		for (const key of keys) {
			const { model, ids, properties, images, options } = personalize[key] ?? {},
				MODEL = models[model]

			if (MODEL && ids.length === 0) {
				switch (model) {
				case PERSON:
					fetches.push(
						person.get(Person, languages, recipients, key, properties, images)
					)
					break

				case MEMBERSHIP:
					fetches.push(
						membership.get(Membership, languages, recipients, key, properties, options)
					)
					break
				}
			}
		}

		const result = await Promise.all(fetches),
			personal = recipients.reduce((data, recipient) => {
				const { personId } = recipient

				data[personId] = result.reduce((res, row) => {
					if (row) {
						for (const L of languages) {
							res[L] ||= {}
							for (const [ key, list ] of Object.entries(row[L])) {
								res[L][key] = list[personId]?.[key] || {}
							}
						}
					}
					return res
				}, {})

				return data
			}, {})

		return personal
	}

	Model.personalizeFetchNonPersonal = async function(languages, personalize = {}, hasNonPersonal) {
		if (!hasNonPersonal) return {}

		const { models } = Model.app,
			keys = Object.keys(personalize),
			fetches = [],
			res = {}

		for (const key of keys) {
			const { model, ids, properties, images } = personalize[key],
				fields = properties.concat([ 'id', 'globalize' ]),
				filter = {
					where: { id: { inq: ids } },
					fields
				},
				MODEL = models[model]

			if (MODEL && ids.length > 0) {
				fetches.push(
					MODEL.find(filter)
						.then(list => injectImages(MODEL, images, list).then(instances => res[key] = instances))
						.catch(err => console.error('personalizeFetchNonPersonal', err))
				)
			}
		}

		await Promise.all(fetches)
		return globalize(res, languages)
	}
}
