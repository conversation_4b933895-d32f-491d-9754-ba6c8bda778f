/**
 *  @module Mixin:Timestamp
 *  <AUTHOR>
 */

module.exports = function(Model, options) {
	Model.observe('before save', async ctx => {
		const updated = ctx.instance || ctx.data,
			NOW = new Date()

		options = Object.assign({ createdAt: true, modifiedAt: true, deletedAt: true }, options)

		if (ctx.isNewInstance) {
			options.createdAt && (updated.createdAt = updated.createdAt ? new Date(updated.createdAt) : NOW)
			options.modifiedAt && (updated.modifiedAt = updated.modifiedAt ? new Date(updated.modifiedAt) : null)
			options.deletedAt && (updated.deletedAt = updated.deletedAt ? new Date(updated.deletedAt) : null)
		}
		else {
			options.modifiedAt && (updated.modifiedAt = updated.modifiedAt ? new Date(updated.modifiedAt) : NOW)
		}
	})
}

/*
The before save hook provides the ctx.isNewInstance property when ctx.instance is set, with the following values:

True: for all CREATE operations.
False: for all UPDATE and REPLACE operations.
Undefined: for updateOr<PERSON><PERSON>, upsertWithWhere, replaceOrCreate, prototype.save,  prototype.updateAttributes, and updateAll operations.

https://loopback.io/doc/en/lb3/Operation-hooks.html

so if you are using 3rd type of operations, please patch the createdAt in your data
*/

/**
 * End of script
 */
