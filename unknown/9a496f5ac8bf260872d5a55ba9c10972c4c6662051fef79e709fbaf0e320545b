/**
 *  @module Service (base)
 */

const EventEmitter = require('node:events')

class Service extends EventEmitter {
	constructor(app, service = {}) {
		super()

		this.app = app
		this.service = service
		this.setMaxListeners(Infinity)

		const { name, config } = service,
			{ host } = config

		appEcho(`[${name}] initialized (IP: ${host.address}  port: ${config.port})`)
	}

	ready() {
		return main.call(this, 'ready')
	}

	start() {
		return main.call(this, 'start')
	}

	stop() {
		return main.call(this, 'stop')
	}

	pause() {
		return main.call(this, 'pause')
	}

	resume() {
		return main.call(this, 'resume')
	}

	terminate() {
		return main.call(this, 'terminate')
	}

	recover() {
		return main.call(this, 'recover')
	}
}

async function main(method) {
	const _method = '_' + method
	return typeof this[_method] === 'function' ? this[_method]() : true
}

module.exports = Service
