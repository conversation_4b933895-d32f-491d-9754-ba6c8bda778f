const config = {
	connection: {
		minPoolSize: 0,
		maxPoolSize: 10,
		maxIdleTime: 300000,        // 5 minutes
		acquireTimeout: 30000,       // 30 seconds
		cleanupInterval: 60000,      // 60 seconds
		monitorInterval: 60000,      // 60 seconds
		initTimeout: 30000,         // 30 seconds
		maxRetries: 3,
		retryDelay: 1000,           // 1 second
	},

	session: {
		validationInterval: 30000,   // 30 seconds
		maxTransactionRetries: 3,
		transactionTimeout: 30000,   // 30 seconds
		mode: 'disabled'             // 'auto', 'enabled', or 'disabled' // turn off before we upgrade mongodb to 8.0
	},

	validation: {
		healthCheckInterval: 30000,  // 30 seconds
		maxErrorThreshold: 5,
		maxActiveRequests: 20
	},

	cleanup: {
		idleTimeout: 300000,        // 5 minutes
		sessionTimeout: 600000,      // 10 minutes
		maxSessionAge: 3600000      // 1 hour
	}
}

module.exports = config