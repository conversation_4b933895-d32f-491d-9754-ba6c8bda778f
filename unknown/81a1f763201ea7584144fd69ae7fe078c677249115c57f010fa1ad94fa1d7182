/**
 *  @module Mixin:Permission
 */

const { EmbedLib } = appRequire('lib/common/embedLibrary')

const CHANNELS = {
		SERVICETERMS: 'serviceTerms',
		PRIVACYPOLICY: 'privacyPolicy',
		// -- used by CRM --
		MOBILE: 'mobile',
		EMAIL: 'email',
		POST: 'postal',
		VOICE: 'voice',
		// -- used by Perkd --
		PUSH: 'push',
		LOCATION: 'location',
		LOCATION_ALWAYS: 'locationAlways',
		CAMERA: 'camera',
		PHOTOS: 'photos',
		CONTACTS: 'contacts',
		CALENDAR: 'calendar',
		HOMEKIT: 'homeKit',
		HEALTH: 'health',
		SPEECH_RECOGNITION: 'speechRecognition',
		BLE_CENTRAL: 'bleCentral',
		BLE_PERIPHERAL: 'blePeripheral',
		MICROPHONE: 'microphone',
		MOTION_FITNESS: 'motionFitness',
	},
	STATUS = {
		DONOTDISTURB: -2,
		OPTOUT: -1,
		UNKNOWN: 0,
		OPTIN: 1,
	},
	SUBMODELS = {
		mobile: 'Phone',
		email: 'Email',
		postal: 'Address',
	},
	EVENT = 'permissionChanged'

module.exports = function(Model, options = {}) {
	const permLib = new EmbedLib(Model, 'permissions', Object.assign({}, options, { event: EVENT }), 'channel')
	permLib.setup()

	// -----  Static Properties  -----
	Model.CHANNELS = options.channels || Object.keys(CHANNELS).map(key => CHANNELS[`${key}`])

	// -----  Instance Methods  -----

	Model.prototype._permission = function({ channel }) {
		return this.permissions.value().find(perm => perm.channel === channel)
	}

	// -----  Event Listeners  -----

	Model.CHANNELS.forEach(channel => {
		if (SUBMODELS[`${channel}`]) {
			Model.on(SUBMODELS[`${channel}`] + '.created', permissionUpdate.bind(channel))
			Model.on(SUBMODELS[`${channel}`] + '.changed', permissionUpdate.bind(channel))
		}
	})

	function permissionUpdate(evt) {
		const channel = this.toString(),
			instance = evt.instance,
			data = evt.data,
			options = Object.assign({}, evt.options),
			permission = instance._permission({ channel })

		delete options.before // evt is from phone/email/... update, should remove before key!

		if (permission.status === STATUS.DONOTDISTURB) return // DND skip
		if (channel === CHANNELS.MOBILE && data.lineType !== 'mobile') return // not mobile

		const status = data.optIn === true ? STATUS.OPTIN : (data.optIn === false ? STATUS.OPTOUT : STATUS.UNKNOWN)
		if (status === permission.status || status === STATUS.UNKNOWN) return // no change

		instance.upsertPermissions({ channel, status }, options)
	}
}
