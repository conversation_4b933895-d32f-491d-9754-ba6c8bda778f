{"name": "OfferMaster", "plural": "OfferMasters", "description": "CRM", "base": "PersistedModel", "idInjection": true, "strict": false, "mixins": {"MultitenantRemote": true}, "properties": {"id": {"type": "String", "id": true, "generated": true}}, "relations": {}, "acls": [], "methods": {"issue": {"description": "Issue offer", "http": {"path": "/issue", "verb": "post"}, "accepts": [{"arg": "id", "type": "any", "required": true, "description": "OfferMaster Id"}, {"arg": "membershipId", "type": "any", "required": true, "description": "membership id"}, {"arg": "options", "type": "object"}], "returns": {"type": "array", "root": true}}, "issueBulk": {"description": "Issue offers to list of recipients", "http": {"path": "/issue/bulk", "verb": "post"}, "accepts": [{"arg": "id", "type": "any", "required": true, "description": "OfferMaster Id"}, {"arg": "recipients", "type": "array", "required": true, "description": "{ membershipId, personalize: { quantity, startTime, endTime }}"}, {"arg": "options", "type": "object"}], "returns": {"type": "array", "root": true}}, "codeFormats": {"description": "Get supported code formats", "http": {"path": "/codeFormats", "verb": "get"}, "returns": {"type": "array", "root": true}}, "requestOffer": {"description": "Request to issue offer by card (Callback API)", "http": {"path": "/perkd/offer/request", "verb": "post"}, "accepts": [{"arg": "masterId", "type": "any", "required": true, "description": "OfferMaster id"}, {"arg": "cardId", "type": "any", "required": true}, {"arg": "through", "type": "object"}, {"arg": "quantity", "type": "number"}, {"arg": "share", "type": "object", "description": "{ mode, cardId, originId, queue }"}, {"arg": "options", "type": "object", "description": "unused"}], "returns": {"type": "Offer", "root": true}}, "findWelcomeOffers": {"description": "Find active Welcome offers that have discount", "http": {"path": "/welcome", "verb": "get"}, "returns": {"type": "array", "root": true}}, "prototype.issue": {"description": "Issue offer to Membership", "http": {"path": "/issue", "verb": "post"}, "accepts": [{"arg": "membership", "type": "object", "required": true}, {"arg": "quantity", "type": "number"}, {"arg": "options", "type": "object"}], "returns": {"type": "array", "root": true}}, "prototype.uploadPresetCodes": {"description": "Upload Preset Codes file", "http": {"path": "/presetCodes/upload", "verb": "post"}, "accepts": [{"arg": "req", "type": "object", "http": {"source": "req"}}, {"arg": "res", "type": "object", "http": {"source": "res"}}], "returns": {"type": "object", "root": true}}, "prototype.generatePresetCodes": {"description": "Generate Preset Codes", "http": {"path": "/presetCodes/generate", "verb": "post"}, "accepts": [{"arg": "quantity", "type": "number", "required": true}], "returns": {"type": "object", "root": true}}, "prototype.nextCode": {"description": "Get the next tracking code of the offer master", "http": {"path": "/nextCode", "verb": "get"}, "returns": {"type": "string", "root": true}}}}