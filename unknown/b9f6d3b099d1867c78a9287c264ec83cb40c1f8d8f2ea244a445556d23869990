/**
 *  @module i18nLib
 */

const i18next = require('i18next'),
	i18n = require('i18n-js'),
	_ = require('lodash')

const
	settings = {}

function init({ namespaces, languages, fallbacks, filepath }) {
	// i18next
	i18next.init({
		fallbackLng: fallbacks,
	})
	loadResources(filepath, languages, namespaces)
	settings.namespaces = namespaces
	settings.languages = languages

	// i18n-js
	i18n.fallbacks = true

	return i18next
}

/**
 * Translate (key) text or instance (globalize)
 * @param	{String} or {String[]} - translate key
 *			{Object} instance - translate instance
 * @param	{String} [property] translate a property, if absent, returns translated instance
 * @param	{String} [opt]
 * @return	{String} or {Object}
 */
function t(...args) {
	// translate key
	if (typeof args[0] === 'string' || Array.isArray(args[0])) {
		return i18next.t(...args)
	}

	// translate instance / property
	const [ instance, prop, opt ] = args,
		// obj = instance.toJSON ? instance.toJSON() : instance,
		obj = instance,
		{ t } = obj.globalize

	let options = opt || {}
	let property = prop
	if (!opt && typeof prop === 'object') {
		options = prop
		property = undefined
	}
	const locale = options.lng || obj.globalize.default

	// use i18n-js
	i18n.translations = t
	i18n.missingTranslation = () => property ? _.get(t[locale], property) : t[locale]
	const translation = property ? i18n.t(property, { locale }) : t[locale]

	return property ? translation : Object.assign(obj, { ...translation })
}

function allTranslations(...args) {
	const trans = {}

	// translate key
	if (typeof args[0] === 'string' || Array.isArray(args[0])) {
		const [ key, opt = {} ] = args,
			{ lngs = settings.languages, ...options } = opt

		for (const k in options) {
			if (options[k] instanceof Date && !isNaN(options[k])) { // make sure it's a valid date
				const today = new Intl.DateTimeFormat('en', { year: 'numeric', month: 'long', day: 'numeric', timeZone: 'Asia/Singapore' }).format(options[k]),
					param = new Intl.DateTimeFormat('en', { year: 'numeric', month: 'long', day: 'numeric', timeZone: 'Asia/Singapore' }).format()

				options.formatParams = {
					[k]: today === param
						? { minute: 'numeric', hour: 'numeric', timeZone: 'Asia/Singapore' }
						: { minute: 'numeric', hour: 'numeric', weekday: 'long', timeZone: 'Asia/Singapore' }
				}
			}
		}

		for (const lng of lngs) {
			options.lng = lng
			trans[lng] = i18next.t(key, options)
		}
		return trans
	}

	// translate instance / property
	const [ instance, property, lngs ] = args,
		{ t = {} } = instance?.globalize ?? {},
		obj = {}

	if (!instance?.globalize) {
		console.log('❌ i18nLib allTranslations instance.globalize missing %j', instance)
	}

	// use i18n-js
	i18n.translations = t
	i18n.missingTranslation = () => _.get(t[instance.globalize.default], property, _.get(instance, property))

	for (const locale of lngs || settings.languages) {
		obj[locale] = i18n.t(property, { locale })
	}
	return obj
}

/**
 * Get all translations
 * @param	{Object} keys
 * @param	{Object} globalize
 * @param	{Object} data
 * @param	{Object} personalize
 * @return	{Object} translations
 * sample:
{
	"en": {
	"body": "Your order is on the way & expected to arrive in 6 days 😉"
	},
	"zh-Hans": {
	"body": "您的订单已在派送中，预计6天后抵达 😉"
	},
	"zh-Hant": {
	"body": "您的訂單已在派送中，預計6天后抵達 😉"
	}
}
 */
function translate(keys = {}, globalize = {}, data = {}, personalize = {}) {
	const translated = {}

	for (const g in globalize) {
		translated[g] = allTranslations(data, globalize[g])
	}
	for (const k in keys) {
		translated[k] = allTranslations(keys[k], { ...data, ...personalize })
	}
	return merge(translated)
}

/**
 * Merge multiple named translations { subject: { en, kr, ja }, title: { en, kr, ja }} into one => { en: { subject, title }, kr: {} }
 * @param	{Object} properties
 * @return {Object}
 */
function merge(properties) {
	const trans = {}

	for (const p in properties) {
		for (const lng in properties[p]) {
			trans[lng] = trans[lng] || {}
			trans[lng][p] = properties[p][lng]
		}
	}
	return trans
}

/**
 * Add a named translations { title: { en, kr, ja }} to a translations
 * @param	{Object} translations - { en: { subject, image }, kr: {} }
 * @param	{Object} properties - eg. { title: { en, kr, ja }}
 * @return {Object}
 */
function add(translations, properties) {
	for (const p in properties) {
		for (const lng in translations) {
			translations[lng][p] = translations[lng][p] || properties[p][lng] || properties[p].en
		}
	}
	return translations
}

function globalize(instance, property, translations, defaultLanguage) {
	const g = instance.globalize || { t: {} }

	for (const lng in translations) {
		g.t[lng] = g.t[lng] || {}
		_.set(g.t[lng], property, translations[lng])
	}
	if (defaultLanguage) g.default = defaultLanguage
	return g
}

// ---- Private functions  ----

function loadResources(filepath, languages, namespaces) {
	for (const language of languages) {
		for (const ns of namespaces) {
			try {
				const rsrc = require(filepath + ns + '/' + language + '.json')
				i18next.addResourceBundle(language, ns, rsrc)
			}
			catch (err) {
				console.log(`[i18n]  resources missing for ${language} of ${ns}`)
			}
		}
	}
	i18next.setDefaultNamespace(namespaces[0])
}

module.exports = exports = {
	settings,
	init,
	t,
	translate,
	translations: allTranslations,
	merge,
	add,
	g: globalize,
	language: () => i18next.language,
	languages: () => i18next.languages,
	changeLanguage: language => i18next.changeLanguage(language),
}
