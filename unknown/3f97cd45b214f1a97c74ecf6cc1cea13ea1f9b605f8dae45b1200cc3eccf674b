/**
 *  @module Model:Permission
 */
const { values } = require('lodash'),
	{ isEmptyObj } = require('@perkd/utils')

const UNKNOWN = 0	// unknown permission status

module.exports = function(Permission) {

	Permission.CHANNELS = {}		// initialized in Permission mixin

	Permission.default = function() {
		const permissionList = []
		Permission.CHANNELS = this.app.models.Person.CHANNELS
		for (const key in Permission.CHANNELS) {
			permissionList.push({ channel: Permission.CHANNELS[key], status: UNKNOWN })
		}
		return permissionList
	}

	// -----  Validations  -----
	Permission.validate('channel', channelType, { message: 'Invalid channel type' })

	function channelType(err) {
		const app = this
		if (isEmptyObj(Permission.CHANNELS)) {
			Permission.CHANNELS = Permission.app.models.Person.CHANNELS
		}
		// Permission.CHANNELS should be initialized before validation is needed
		if (values(Permission.CHANNELS).indexOf(this.channel) < 0) err()
	}

	// -----  Operation hooks  -----

	Permission.observe('before save', async ({ instance, data, currentInstance }) => {
		const updated = instance || data,
			existingStatus = currentInstance ? currentInstance.status : UNKNOWN

		if (updated.status && updated.status !== existingStatus) {
			const
				existingGrant = currentInstance ? currentInstance.grantedAt : null,
				existingRevoke = currentInstance ? currentInstance.revokedAt : null

			if (existingStatus <= 0 && updated.status > 0) {
				updated.grantedAt = updated.grantedAt ? new Date(updated.grantedAt) : new Date()
				updated.revokedAt = existingRevoke || null
			}
			if (existingStatus >= 0 && updated.status < 0) {
				updated.revokedAt = updated.revokedAt ? new Date(updated.revokedAt) : new Date()
				updated.grantedAt = existingGrant || null
			}
		}
	})
}
