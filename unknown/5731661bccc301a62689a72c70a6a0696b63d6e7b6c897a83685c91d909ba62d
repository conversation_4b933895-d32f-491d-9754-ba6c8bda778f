{"name": "Address", "plural": "Addresses", "base": "Addr", "idInjection": true, "strict": false, "mixins": {}, "options": {}, "properties": {"id": {"type": "String", "id": true, "defaultFn": "nanoid"}, "type": {"type": "String", "required": true, "max": 32, "description": "work, home, others"}, "formatted": {"type": "String"}, "short": {"type": "String"}, "optIn": {"type": "Boolean"}, "valid": {"type": "Boolean"}}, "validations": [], "relations": {}, "acls": [], "indexes": {}, "scopes": {}, "methods": []}