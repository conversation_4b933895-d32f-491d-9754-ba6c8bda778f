/**
 *  @module Timeseries
 */

const { inherits } = require('util'),
	EventEmitter = require('events'),
	Cache = require('node-cache'),
	_ = require('lodash'),
	{ isEmptyObj, pickDeep, satisfyCondition, TIMEZONE, dayjs } = require('@perkd/utils'),
	debug = require('debug')('waveo:metrics:timeseries')

const INIT = {
	second: { 0: 0, 1: 0, 2: 0, 3: 0, 4: 0, 5: 0, 6: 0, 7: 0, 8: 0, 9: 0, 10: 0, 11: 0, 12: 0, 13: 0, 14: 0,
		15: 0, 16: 0, 17: 0, 18: 0, 19: 0, 20: 0, 21: 0, 22: 0, 23: 0, 24: 0, 25: 0, 26: 0, 27: 0, 28: 0, 29: 0,
		30: 0, 31: 0, 32: 0, 33: 0, 34: 0, 35: 0, 36: 0, 37: 0, 38: 0, 39: 0, 40: 0, 41: 0, 42: 0, 43: 0, 44: 0,
		45: 0, 46: 0, 47: 0, 48: 0, 49: 0, 50: 0, 51: 0, 52: 0, 53: 0, 54: 0, 55: 0, 56: 0, 57: 0, 58: 0, 59: 0,
	},
	minute: { 0: 0, 1: 0, 2: 0, 3: 0, 4: 0, 5: 0, 6: 0, 7: 0, 8: 0, 9: 0, 10: 0, 11: 0, 12: 0, 13: 0, 14: 0,
		15: 0, 16: 0, 17: 0, 18: 0, 19: 0, 20: 0, 21: 0, 22: 0, 23: 0, 24: 0, 25: 0, 26: 0, 27: 0, 28: 0, 29: 0,
		30: 0, 31: 0, 32: 0, 33: 0, 34: 0, 35: 0, 36: 0, 37: 0, 38: 0, 39: 0, 40: 0, 41: 0, 42: 0, 43: 0, 44: 0,
		45: 0, 46: 0, 47: 0, 48: 0, 49: 0, 50: 0, 51: 0, 52: 0, 53: 0, 54: 0, 55: 0, 56: 0, 57: 0, 58: 0, 59: 0,
	},
	hour: { 0: 0, 1: 0, 2: 0, 3: 0, 4: 0, 5: 0, 6: 0, 7: 0, 8: 0, 9: 0, 10: 0, 11: 0, 12: 0, 13: 0, 14: 0,
		15: 0, 16: 0, 17: 0, 18: 0, 19: 0, 20: 0, 21: 0, 22: 0, 23: 0, 24: 0, 25: 0, 26: 0, 27: 0, 28: 0, 29: 0,
		30: 0, 31: 0, 32: 0, 33: 0, 34: 0, 35: 0, 36: 0, 37: 0, 38: 0, 39: 0, 40: 0, 41: 0, 42: 0, 43: 0, 44: 0,
		45: 0, 46: 0, 47: 0, 48: 0, 49: 0, 50: 0, 51: 0, 52: 0, 53: 0, 54: 0, 55: 0, 56: 0, 57: 0, 58: 0, 59: 0,
	},
	day: { 0: 0, 1: 0, 2: 0, 3: 0, 4: 0, 5: 0, 6: 0, 7: 0, 8: 0, 9: 0, 10: 0, 11: 0,
		12: 0, 13: 0, 14: 0, 15: 0, 16: 0, 17: 0, 18: 0, 19: 0, 20: 0, 21: 0, 22: 0, 23: 0,
	},
	month: { 0: 0, 1: 0, 2: 0, 3: 0, 4: 0, 5: 0, 6: 0, 7: 0, 8: 0, 9: 0, 10: 0, 11: 0, 12: 0, 13: 0, 14: 0, 15: 0,
		16: 0, 17: 0, 18: 0, 19: 0, 20: 0, 21: 0, 22: 0, 23: 0, 24: 0, 25: 0, 26: 0, 27: 0, 28: 0, 29: 0, 30: 0,
	},
	year: { 0: 0, 1: 0, 2: 0, 3: 0, 4: 0, 5: 0, 6: 0, 7: 0, 8: 0, 9: 0, 10: 0, 11: 0 },
}

const SECOND = 'second', MINUTE = 'minute', HOUR = 'hour', DAY = 'day', MONTH = 'month', YEAR = 'year'
const SCALE = [ SECOND, MINUTE, HOUR, DAY, MONTH, YEAR ]
const IN_MS = {
	second: 1000, 			// 1000
	minute: 60000,			// 60 * SECOND
	hour: 3600000,			// 60 * MINUTE
	day: 86400000,			// 24 * HOUR
	month: 2678400000,		// 31 * DAY    to subtract later on use, based on actual month
	year: 31536000000,		// 365 * DAY
}

const TIMER = '__timer'
const TTL = {
	minute: 60,					// seconds
	hour: 3600,
	day: 86400,
	month: 86400,
	year: 86400,
}
const DEFAULTS = {
	ttl: TTL,
	persist: true,
	persistInterval: 5,		// seconds
}
const EVENT_PROPERTIES = []		// properties (from event data) to be stored with aggregate

/* ----------  In-memory cache:

mCache = {
	'sms.sent.count': <Cache>,		// a node-cache
	'email.sent.count': <Cache>,	// a node-cache
	...
}
Cache = {
	'day.14475658000': <Series>,	// { values: { 0: 22, 1: 37, 2: 0, 3: 98 ...}, changes: { 1: 37, 3: 98 } }
	'hour.14475658100': <Series>,
	'hour.14475658200': <Series>,
	'minute.14475658200': <Series>,
	'minute.14475658210': <Series>,
	'minute.14475658220': <Series>,
	...
}
where:
	mCache = collection of Cache with metric name as keys
	seriesKey = scale + '.'+ timestamp
	scale = year, month, day, hour, minute, second
	timestamp = time at start of the scale

Events
	'added'			new key added, new memory cache setup		{ key, scales }
	'removed'		key removed and memory cache emptied		{ key }
	'expired'		series expired & removed from memory cache 	{ key, series, changes }
	'closed'		all keys and their series saved/flushed and cache emptied
------------- */

class Series {

	constructor(scale, timestamp, values, dimensions, dValues) {
		debug('Series.constructor %j', { scale, timestamp, values, dimensions, dValues })

		this.scale = scale
		this.timestamp = timestamp
		this.values = { ...(values || INIT[this.scale]) }
		this.dimensions = dimensions || []
		this.dValues = dValues || []
		this.changes = {}
	}

	setValue(ndx, value, multiples) {
		debug('Series.setValue %j', { ndx, value })

		this.values[ndx] = this.calculate(multiples, this.values[ndx], value)		// compute new value
		this.changes[ndx] = this.values[ndx]
	}

	get value() {
		return this.calculate(null, this.values)
	}
}

function TimeSeries(options) {
	this.mCache = {}
	this.settings = {}			// settings of each key

	this.options = _.merge({}, DEFAULTS, options)
	this.setMaxListeners(Infinity)

	debug('options %j', options)
}

inherits(TimeSeries, EventEmitter)
module.exports = exports = TimeSeries
module.exports.Series = Series
module.exports.timeslots = timeslots
module.exports.seriesKey = TimeSeries.seriesKey
module.exports.key2ScaleName = TimeSeries.key2ScaleName
module.exports.getSeriesData = getSeriesData
module.exports.initSeriesData = initSeriesData

// --- constants ---
module.exports.SCALE = SCALE
module.exports.INIT = INIT

module.exports.SECOND = SECOND
module.exports.MINUTE = MINUTE
module.exports.HOUR = HOUR
module.exports.DAY = DAY
module.exports.MONTH = MONTH
module.exports.YEAR = YEAR

// --- Accumulator functions ---
module.exports.sum = sum
module.exports.count = count
module.exports.max = max
module.exports.min = min
module.exports.mean = mean
module.exports.distinct = distinct

// ---  Static methods  --------

TimeSeries.seriesKey = function(scale, timestamp) {
	return scale + '.' + timestamp
}

TimeSeries.key2ScaleName = function(seriesKey) {
	return seriesKey.split('.')[0]
}

// ---  Instance methods  --------

TimeSeries.prototype.add = async function(key, scales, computeFn, property, filter, ttl) {
	debug('add %j', { key, scales, computeFn, property, filter, ttl })

	//  validate scales
	const diff = _.difference(scales, SCALE)

	if (this.mCache[key]) {
		throw new Error('Key already exist')
	}
	else if (scales.indexOf(SECOND) !== -1) {
		throw new Error('Scale of SECOND not supported. Finest scale is MINUTE')
	}
	else if (diff.length > 0) {
		throw new Error('Scale not supported: ' + diff.toString())
	}

	//  setup
	this.mCache[key] = new Cache({ useClones: false, checkperiod: 3 })
	this.setExpiryHandler(key)

	this.settings[key] = {
		scales,
		fn: computeFn,
		property,
		filter,
		ttl,
	}

	this.emit('added', { key, scales })
}

TimeSeries.prototype.remove = async function(key) {
	debug('remove %s', key)

	const { mCache } = this,
		cache = mCache[key]

	if (cache) {
		await this.persist(key).catch(() => null)

		cache.close()
		cache.flushAll()
		delete mCache[key]

		this.emit('removed', { key })
	}
}

TimeSeries.prototype.setValue = function(key, timestamp, data, filter, scales, computeFn, property, ttl) {
	debug('setValue %j', { key, timestamp, data, filter, scales, computeFn, property, ttl })

	const { mCache, options } = this,
		{ evtData, dimensions, dValues, multiples } = data

	if (!filter || satisfyCondition(evtData, filter)) {
		debug('✅  satisfiedCondition %j', { evtData, filter })

		if (mCache[key] === undefined) {
			this.add(key, scales, computeFn, property, filter, ttl)
		}
		this.setPersistTimer(key, options.persistInterval)

		const slots = timeslots(timestamp)

		scales.forEach(scale => {
			const ts = slots[scale].ts,
				sKey = TimeSeries.seriesKey(scale, ts)

			const series = this.initValue(key, sKey, scale, ts, data)
			series.dimensions = dimensions
			series.dValues = dValues

			// additional event properties to store
			EVENT_PROPERTIES.forEach(prop => {
				series[prop] = pickDeep(evtData, prop)
			})

			// use evtData as value, or extract value of property from evtData
			const newValue = (property === undefined) ? evtData : pickDeep(evtData, property)
			series.setValue(slots[scale].ndx, newValue, multiples)

			debug('seriesAfterSet %j', series)
		})
	}
	else {
		debug('❌  not satisfyCondition %j', { evtData, filter })
	}
}

TimeSeries.prototype.initValue = function(key, seriesKey, scale, ts, data) {
	debug('initValue %j', { key, seriesKey, scale, ts, data })

	const { mCache, options } = this,
		cache = mCache[key],
		settings = this.settings[key],
		{ fn, ttl } = settings

	let series = cache.get(seriesKey)

	if (series === undefined) {
		// return an initial (zero) values
		series = new Series(scale, ts)
		series.calculate = fn
		cache.set(seriesKey, series, ttl || options.ttl[scale])

		process.nextTick(() => {
		// async fetch previous stored values (if any) and apply to cached asynchronously
			this.fetch(key, seriesKey, scale, ts, data).then(stored => {
				if (stored) {
					series.calculate(null, series, stored, true)
					series.changed = true
				}
			})
		})
	}
	else {
		cache.ttl(seriesKey, ttl || options.ttl[scale])
	}

	debug('seriesKey %s, series: %j', seriesKey, series)

	return series
}

TimeSeries.prototype.persist = async function(key, seriesKey, series) {
	debug('persist %j', { key, seriesKey, series })

	const { mCache, options } = this,
		cache = mCache[key]

	if (!options.persist) return
	if (seriesKey) return saveChanges(this, key, seriesKey, series)

	const saves = []

	for (const sKey of this.keys(key)) {
		saves.push(
			saveChanges(this, key, sKey, cache.get(sKey))
		)
	}

	const res = await Promise.all(saves)
	return res

	async function saveChanges(self, key, seriesKey, series) {
		if (series && !isEmptyObj(series.changes)) {
			const P = self.save(key, seriesKey, series)
			series.changes = {} // must after save
			return P
		}
	}
}

TimeSeries.prototype.getAllSeries = function(key, timestamp) {
	debug('getAllSeries %j', { key, timestamp })

	const { mCache } = this,
		cache = mCache[key],
		settings = this.settings[key],
		{ scales } = settings,
		allSeries = {}

	if (!cache) return {}

	const slots = timeslots(timestamp)

	for (const scale of scales) {
		const cached = cache.get(TimeSeries.seriesKey(scale, slots[scale].ts))

		if (cached) {
			cached.ndx = slots[scale].ndx
			allSeries[scale] = cached
		}
	}

	return allSeries
}

TimeSeries.prototype.close = async function() {
	const { mCache } = this,
		keys = Object.keys(mCache)

	for (const key of keys) {
		mCache[key].del(TIMER)
		await this.remove(key)
	}

	this.emit('closed')
}

TimeSeries.prototype.setPersistTimer = function(key, interval) {
	debug('setPersistTimer %j', { key, interval })

	const { mCache } = this,
		cache = mCache[key]

	if (interval === 0) cache.del(TIMER)		// 'cancel' timer, no timed persistance of cache
	else if (cache.get(TIMER) === undefined) {
		cache.set(TIMER, true, interval)
	}
}

TimeSeries.prototype.setExpiryHandler = function(key) {
	const { mCache } = this,
		cache = mCache[key]

	cache.on('expired', (seriesKey, series) => {
		if (seriesKey === TIMER) {
			this.persist(key)
		}
		else if (!isEmptyObj(series.changes)) {
			cache.set(seriesKey, series, DEFAULTS.ttl.minute) // has changes not save, put it back to cache for TIMER persist in next round.
		}
		else {
			this.emit('expired', { key, series: seriesKey })
		}
	})
}

TimeSeries.prototype.keys = function(key) {
	const { mCache } = this,
		cache = key ? mCache[key] : mCache

	return key
		? cache.keys().filter(e => e !== TIMER)
		: Object.keys(cache)
}

TimeSeries.prototype.getStats = function(key) {
	const { mCache } = this

	let stats = { hits: 0, misses: 0, keys: 0, ksize: 0, vsize: 0 }

	if (key) {
		stats = mCache[key] && mCache[key].getStats() || stats
	}
	else {
		for (const key in mCache) {
			if ({}.hasOwnProperty.call(mCache, key)) {
				const s = mCache[key].getStats()
				stats.hits += s.hits
				stats.misses += s.misses
				stats.keys += s.keys
				stats.ksize += s.ksize
				stats.vsize += s.vsize
			}
		}
	}
	return stats
}

//  Caller to implement these methods where necessary

TimeSeries.prototype.fetch = function(key, seriesKey, scale, timestamp, data) {
	// return TimeSeries.Series object in callback
	throw new Error('Must implement fetch()')
}

TimeSeries.prototype.save = function(key, seriesKey, changes, series) {
	// return stored object in OPTIONAL callback
	throw new Error('Must implement save()')
}

// ----  Accumulators (exported)

function sum(multiples, oldVal, newVal, restored) {
	if (restored) {
		for (const i in oldVal.values) {
			if ({}.hasOwnProperty.call(oldVal.values, i)) {
				oldVal.values[i] += (newVal.values[i] || 0)
			}
		}
	}
	else if (typeof oldVal === 'object' && newVal === undefined) {
		// handle sum of 'values'
		let total = 0
		for (const i in oldVal) {
			if ({}.hasOwnProperty.call(oldVal, i)) {
				total += oldVal[i]
			}
		}
		return total
	}
	else {
		newVal = Number(newVal)
		if (isNaN(newVal)) newVal = 1
		return oldVal + (multiples ? newVal * multiples : newVal)
	}
}

/**
 * Increment oldVal by 1 (OR) sum of values when oldVal is an object (Series.values)
 * @param  {Number} multiples   multiples * newValue
 * @param  {Number} oldVal   	existing value. HOWEVER when 'restored' == true, type is <Series> object
 * @param  {Number} newVal   	new value (ignored). when 'restored' == true, type is <Series> Object
 * @param  {Boolean} restored 	if true, oldVal is stored values (Series) and newVal is existing values (Series)
 * @return {Number}          	computed result. However, when 'restored' == true, no result is returned
 */
function count(multiples, oldVal, newVal, restored) {
	if (restored) {
		// 1. handle restore from storage
		for (const i in oldVal.values) {
			if ({}.hasOwnProperty.call(oldVal.values, i)) {
				oldVal.values[i] += (newVal.values[i] || 0)
			}
		}
	}
	else if (typeof oldVal === 'object' && newVal === undefined) {
		// 2. handle sum of 'values'
		let total = 0
		for (const i in oldVal) {
			if ({}.hasOwnProperty.call(oldVal, i)) {
				total += oldVal[i]
			}
		}
		return total
	}
	else {
		// 3. normal computation
		return oldVal + (multiples || 1)
	}
}

function min(multiples, oldVal, newVal, restored) {
	throw new Error('not implemented')
}

function max(multiples, oldVal, newVal, restored) {
	throw new Error('not implemented')
}

function mean(multiples, oldVal, newVal, restored) {
	// TODO:  need past values
	throw new Error('not implemented')
}

function distinct(multiples, oldVal, newVal, restored) {
	// TODO:  need past values/keys
	throw new Error('not implemented')
}

// ----  Private methods

function timeslots(timestamp, timezone) {
	timestamp = Number(timestamp)
	timezone = timezone || TIMEZONE

	const time = dayjs.tz(timestamp, timezone),
		offset = time.utcOffset() * IN_MS.minute, // ms
		Y = time.format('YYYY'),
		M = time.format('M') - 1,
		D = time.format('D')

	// console.log('timestamp: %d, Y: %s, M: %s, D: %s, offset %d', timestamp, Y, M, D, offset);

	const slots = {
		year: { ts: Date.UTC(Y, 0, 1) + offset, ndx: M, idx: 0 },
		month: { ts: Date.UTC(Y, M, 1) + offset, ndx: D - 1, idx: M },
		day: { ts: Date.UTC(Y, M, D) + offset },
		hour: { ts: Math.floor(timestamp / IN_MS[HOUR]) * IN_MS[HOUR] },
		minute: { ts: Math.floor(timestamp / IN_MS[MINUTE]) * IN_MS[MINUTE] },
		second: { ts: Math.floor(timestamp / IN_MS[SECOND]) * IN_MS[SECOND], ndx: 0, idx: 0 },
	}
	slots.day.ndx = Math.floor((timestamp - slots.day.ts) / IN_MS[HOUR])
	slots.hour.ndx = Math.floor((timestamp - slots.hour.ts) / IN_MS[MINUTE])
	slots.minute.ndx = Math.floor((timestamp - slots.minute.ts) / IN_MS[SECOND])
	slots.day.idx = slots.month.ndx
	slots.hour.idx = slots.day.ndx
	slots.minute.idx = slots.hour.ndx

	// console.log('slots %j', slots);
	return slots
}

function initSeriesData(scale, granularity, timestamp, timezone) {
	debug('getSeriesData %j', { scale, granularity, timestamp, timezone })

	timezone = timezone || TIMEZONE
	const allSeries = {}

	for (const scale in INIT) {
		if (scale === 'month') {
			allSeries[scale] = { scale, values: {} }
			const days = dayjs.tz(timestamp, timezone).daysInMonth()
			for (let i = 0; i < days; i++) allSeries[scale].values[i] = 0
		}
		else {
			allSeries[scale] = { scale, values: INIT[scale] }
		}
	}

	const seriesData = getSeriesData(allSeries, _.pick(granularity, scale), false)

	let timeseries = {}
	timeseries[scale] = 0
	timeseries = { ...seriesData[scale].values, ...timeseries }

	return timeseries
}

function getSeriesData(allSeries, include, changes) {
	debug('getSeriesData %j', { allSeries, include, changes })

	const data = {}

	for (const scale in allSeries) {
		if (include[scale] && allSeries[scale]) {
			data[scale] = {
				timestamp: allSeries[scale].timestamp,
				scale: SCALE.indexOf(scale),
				dimensions: allSeries[scale].dimensions,
				dValues: allSeries[scale].dValues,
				values: {},
			}
			data[scale].values[scale] = allSeries[scale].value

			for (let n = SCALE.indexOf(scale) - 1; n >= 0; n--) {
				const granularity = SCALE[n]

				if (include[scale].indexOf(granularity) !== -1) {
					const change = subscale(scale, allSeries, granularity, changes)
					if (change) {
						data[scale].values[granularity] = {}
						Object.assign(data[scale].values[granularity], change)
					}
				}
			}
		}
	}

	debug('SeriesData %j', data)
	return data

	function subscale(upper, allSeries, granularity, changes) {
		if (upper && allSeries[upper]) {
			const current = SCALE[SCALE.indexOf(upper) - 1],
				values = changes ? allSeries[upper].changes : allSeries[upper].values

			let data = {}
			if (current === granularity) {
				data = values
			}
			else if (!changes) {
				for (const k in values) {
					if ({}.hasOwnProperty.call(values, k)) {
						const change = subscale(current, allSeries, granularity, changes)
						if (change) data[k] = change
					}
				}
			}
			else if (values[allSeries[upper].ndx]) {
				const change = subscale(current, allSeries, granularity, changes)
				if (change) {
					data[allSeries[upper].ndx] = change
				}
			}
			return isEmptyObj(data) ? null : data
		} return null
	}
}
