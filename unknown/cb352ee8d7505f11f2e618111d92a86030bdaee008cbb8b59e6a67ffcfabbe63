{"name": "Payment", "plural": "Payments", "base": "PersistedModel", "idInjection": true, "strict": true, "mixins": {"MultitenantRemote": true}, "options": {"validateUpsert": true}, "properties": {"id": {"type": "String", "id": true, "generated": true}, "type": {"type": "string", "required": true, "max": 16, "description": "Payment type", "enum": ["merchant", "bank", "card", "alipay", "linepay", "storedvalue"]}, "name": {"type": "string", "max": 32, "required": true}, "provider": {"type": "string", "max": 32, "required": true, "enum": ["stripe", "mypay", "perkdpay", "razer", "flexm", "gkash"], "description": "Service provider name (key)"}, "supports": {"type": [], "default": [], "description": "Supported payment types"}, "currency": {"type": {"code": {"type": "string", "max": 8, "description": "ISO 4217 currency code, 'CREDIT' or 'POINT'"}, "precision": {"type": "number", "default": 0, "description": "Decimal precision, ie. 2 => amount of 1000 = $10.00"}}, "required": true}, "credentials": {"type": "object", "default": {}}, "options": {"type": "Object", "default": {}, "description": "provider specific custom data"}, "liveMode": {"type": "boolean"}, "demo": {"type": "boolean", "description": "Enable use of test credentials in production env, & vice versa"}, "createdAt": {"type": "Date"}, "modifiedAt": {"type": "Date"}}, "hidden": [], "validations": [], "relations": {"transactions": {"type": "hasMany", "model": "Transaction", "foreignKey": "paymentId"}, "source": {"type": "belongsTo", "polymorphic": true}}, "acls": [], "indexes": {}, "scopes": {}, "methods": {"provision": {"description": "Provision (skeleton) Gateway for business", "http": {"path": "/provision", "verb": "post"}, "accepts": [{"arg": "name", "type": "string", "required": true, "description": "Name for gateway"}, {"arg": "ownerId", "type": "string", "required": true, "description": "Business id"}], "returns": {"type": "object", "root": true}}, "getProvision": {"description": "Get Methods provisioned for business", "http": {"path": "/provision", "verb": "get"}, "accepts": [{"arg": "businessId", "type": "string", "required": true}], "returns": {"type": "object", "root": true}}, "provisionStatus": {"description": "Provision status for business", "http": {"path": "/provision/status", "verb": "get"}, "accepts": [{"arg": "businessId", "type": "string", "required": true}], "returns": {"type": "object", "root": true}}, "provisionProvider": {"description": "Provision Provider for business", "http": {"path": "/provision/provider", "verb": "post"}, "accepts": [{"arg": "businessId", "type": "string", "required": true}, {"arg": "config", "type": "object", "required": true, "description": "{ provider, currency, credentials, options }"}, {"arg": "restrict", "type": ["string"], "description": "limit to these payment types"}], "returns": {"type": "object", "root": true}}, "deprovisionProvider": {"description": "de-provision Provider for business", "http": {"path": "/provision/provider", "verb": "delete"}, "accepts": [{"arg": "businessId", "type": "string", "required": true}, {"arg": "provider", "type": "string", "required": true, "description": "key of provider"}, {"arg": "restrict", "type": ["string"], "description": "limit to these payment types"}], "returns": {"type": ["string"], "root": true}}, "storedValuesMetricsSummary": {"description": "Stored Value metrics - summary", "http": {"path": "/metrics/storedvalues/summary", "verb": "get"}, "accepts": [], "returns": {"type": "object", "root": true}}, "storedValuesMetricsTransactions": {"description": "Stored Value metrics - transactions", "http": {"path": "/metrics/storedvalues/summary/transactions", "verb": "get"}, "accepts": [{"arg": "from", "type": "date", "required": true}, {"arg": "to", "type": "date", "required": true}], "returns": {"type": "object", "root": true}}, "storedValuesTransactions": {"description": "Stored Value - transactions history", "http": {"path": "/storedvalues/transactions", "verb": "get"}, "accepts": [{"arg": "from", "type": "date", "required": true}, {"arg": "to", "type": "date", "required": true}], "returns": {"type": "object", "root": true}}, "prototype.createIntent": {"description": "Create a Payment Intent", "http": {"path": "/intents", "verb": "post"}, "accepts": [{"arg": "type", "type": "string", "required": true}, {"arg": "request", "type": "Object", "required": true}, {"arg": "details", "type": "Object", "required": true}, {"arg": "options", "type": "Object"}], "returns": {"type": "object", "root": true}}, "prototype.cancelIntent": {"description": "Cancel a PaymentIntent", "http": {"path": "/intents", "verb": "delete"}, "accepts": [{"arg": "intentId", "type": "string", "required": true}, {"arg": "options", "type": "object", "description": "{ reason }"}, {"arg": "method", "type": "string", "description": "method of payment"}], "returns": {"type": "object", "root": true}}, "prototype.createPayout": {"description": "Create a Payout to Reci<PERSON>nt", "http": {"path": "/payouts", "verb": "post"}, "accepts": [{"arg": "amount", "type": "number", "required": true}, {"arg": "currency", "type": "string", "required": true}, {"arg": "recipientId", "type": "any", "required": true, "description": "Payment (wallet) id of recipient"}, {"arg": "details", "type": "object"}, {"arg": "options", "type": "Object"}], "returns": {"type": "object", "root": true}}, "prototype.getPayout": {"description": "Retrieve a Payout", "http": {"path": "/payouts", "verb": "get"}, "accepts": [{"arg": "payoutId", "type": "String", "required": true}], "returns": {"type": "object", "root": true}}, "prototype.capture": {"description": "Capture an authorised Charge (credit card)", "http": {"path": "/capture", "verb": "post"}, "accepts": [{"arg": "request", "type": "object", "required": true, "description": "{ type: intent|charge, method, referenceId }"}], "returns": {"type": "object", "root": true}}, "prototype.createRefund": {"description": "Refund on a Charge", "http": {"path": "/refunds", "verb": "post"}, "accepts": [{"arg": "amount", "type": "Number"}, {"arg": "currency", "type": "String"}, {"arg": "details", "type": "Object", "description": "{ intent, charge, metadata }", "required": true}], "returns": {"type": "object", "root": true}}, "prototype.balance": {"description": "Get Balance of Account or Transaction", "http": {"path": "/balance", "verb": "get"}, "accepts": [{"arg": "transactionId", "type": "any"}], "returns": {"type": "object", "root": true}}, "prototype.extend": {"description": "Extend balance to given expiresAt (so far only support storedValue)", "http": {"path": "/extend", "verb": "post"}, "accepts": [{"arg": "expiresAt", "type": "date"}], "returns": {"type": "object", "root": true}}, "prototype.rates": {"description": "Get fees and rates for Account", "http": {"path": "/rates", "verb": "get"}, "accepts": [], "returns": {"type": "object", "root": true}}, "prototype.linkBank": {"description": "Link to a Bank account", "http": {"path": "/bank", "verb": "post"}, "accepts": [{"arg": "number", "type": "Number", "required": true}, {"arg": "bank", "type": "String", "required": true, "max": 16}, {"arg": "name", "type": "String", "max": 64}, {"arg": "purpose", "type": "String", "max": 80}], "returns": {"type": "object", "root": true}}, "prototype.listBanks": {"description": "List linked bank accounts", "http": {"path": "/banks", "verb": "get"}, "accepts": [], "returns": {"type": "object", "root": true}}, "prototype.withdraw": {"description": "Withdraw amount to another Bank account", "http": {"path": "/bank/withdraw", "verb": "post"}, "accepts": [{"arg": "amount", "type": "Number", "required": true}, {"arg": "express", "type": "Boolean", "default": false}, {"arg": "recipientToken", "type": "String", "required": true}, {"arg": "payoutInvoiceId", "type": "String", "required": true}], "returns": {"type": "object", "root": true}}, "prototype.createCustomer": {"description": "Create Customer account", "http": {"path": "/customers", "verb": "post"}, "accepts": [{"arg": "profile", "type": "object", "required": true, "description": "{ name, email, mobile }"}, {"arg": "source", "type": "object"}, {"arg": "type", "type": "string", "description": "Payment method if required, eg. 'card'"}], "returns": {"type": "object", "root": true}}, "prototype.verifyCustomer": {"description": "Verify Customer account via OTP", "http": {"path": "/customers/verify", "verb": "post"}, "accepts": [{"arg": "mobileNumber", "type": "String", "required": true}, {"arg": "otp", "type": "string", "required": true}], "returns": {"type": "object", "root": true}}, "prototype.getCustomer": {"description": "Get Customer profile", "http": {"path": "/customers", "verb": "get"}, "accepts": [], "returns": {"type": "object", "root": true}}, "prototype.updateCustomer": {"description": "Update Customer profile", "http": {"path": "/customers", "verb": "patch"}, "accepts": [{"arg": "profile", "type": "object", "required": true}], "returns": {"type": "object", "root": true}}, "prototype.createWallet": {"description": "Create Wallet", "http": {"path": "/customers/wallet", "verb": "post"}, "accepts": [{"arg": "profile", "type": "object", "required": true, "description": "{ name, email, mobile }"}, {"arg": "type", "type": "string", "description": "Payment method (default: 'storedvalue')"}], "returns": {"type": "Payment", "root": true}}, "prototype.addPaymentMethod": {"description": "Create a PaymentMethod for Customer", "http": {"path": "/payment/method", "verb": "post"}, "accepts": [{"arg": "type", "type": "string", "required": true, "description": "of payment, eg. 'card'"}, {"arg": "customerId", "type": "string", "required": true}, {"arg": "options", "type": "object", "description": "{ metadata, description }"}], "returns": {"type": "object", "root": true}}, "prototype.removePaymentMethod": {"description": "Detach a PaymentMethod for Customer", "http": {"path": "/payment/method", "verb": "delete"}, "accepts": [{"arg": "type", "type": "string", "description": "of payment, eg. 'card'", "required": true}, {"arg": "paymentMethodId", "type": "string", "required": true}], "returns": {"type": "object", "root": true}}}}