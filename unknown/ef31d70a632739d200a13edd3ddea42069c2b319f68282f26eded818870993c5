{"name": "Lookup", "plural": "Lookups", "description": "", "base": "PersistedModel", "idInjection": false, "strictObjectIDCoercion": true, "strict": false, "mixins": {"Multitenant": true, "Common": true, "Timestamp": true, "Globalize": true}, "properties": {"name": {"type": "string", "required": true, "id": true}, "createdAt": {"type": "date"}, "modifiedAt": {"type": "date"}}, "hidden": [], "validations": [], "relations": {"globalize": {"type": "embeds<PERSON><PERSON>", "model": "Globalize", "property": "globalizeList", "options": {"validate": false, "forceId": false}}}, "acls": [], "scopes": {}, "indexes": {}, "methods": [], "http": {}}