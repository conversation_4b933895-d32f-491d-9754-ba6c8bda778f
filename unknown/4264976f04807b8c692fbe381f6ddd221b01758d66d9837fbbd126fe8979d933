{"name": "Microsite", "plural": "Microsites", "base": "PersistedModel", "idInjection": true, "strict": false, "mixins": {"MultitenantRemote": true}, "properties": {"id": {"type": "string", "id": true, "generated": true}}, "relations": {}, "acls": [], "indexes": {}, "scopes": {}, "methods": {"start": {"description": "Take microsite online", "http": {"path": "/start", "verb": "post"}, "accepts": [{"arg": "name", "type": "string", "required": true}], "returns": {"type": "object", "root": true}}, "stop": {"description": "Take microsite offline", "http": {"path": "/stop", "verb": "post"}, "accepts": [{"arg": "name", "type": "string", "required": true}], "returns": {"type": "object", "root": true}}, "undeploy": {"description": "Delete microsite & related assets on s3", "http": {"path": "/undeploy", "verb": "post"}, "accepts": [{"arg": "name", "type": "string", "required": true}], "returns": {"type": "object", "root": true}}, "prototype.deploy": {"description": "Deploy microsite to S3", "http": {"path": "/deploy", "verb": "post"}, "accepts": [{"arg": "data", "type": "object"}, {"arg": "expiresAt", "type": "date"}, {"arg": "options", "type": "object"}], "returns": {"type": "object", "root": true}}, "prototype.undeploy": {"description": "Delete microsite & related assets on s3", "http": {"path": "/undeploy", "verb": "post"}, "accepts": [], "returns": {"type": "object", "root": true}}, "prototype.clone": {"description": "Clone microsite", "http": {"path": "/clone", "verb": "post"}, "accepts": [], "returns": {"type": "object", "root": true}}, "prototype.build": {"description": "Generate html & personalizations for microsite", "http": {"path": "/build", "verb": "post"}, "accepts": [{"arg": "templates", "type": "object", "required": true, "description": "Multi-lingual templates { en: <html>, zh-Hant: '' }"}], "returns": {"type": "object", "root": true}}, "prototype.preview": {"description": "Preview link", "http": {"path": "/preview", "verb": "get"}, "accepts": [{"arg": "language", "type": "string", "required": true}], "returns": {"type": "object", "root": true}}, "prototype.setupCampaign": {"description": "Setup acquisition campaign (to deprecate)", "http": {"path": "/campaign/setup", "verb": "post"}, "accepts": [{"arg": "requestId", "type": "string"}], "returns": {"type": "object", "root": true}}}}