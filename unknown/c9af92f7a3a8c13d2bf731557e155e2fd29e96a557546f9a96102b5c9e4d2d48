/**
 *  @module error-handler	middleware
 *
 *  Customized error handler, handle errors before loopback default error handler
 *
 */

// statusCode: [code]
const FILTER_LIST = {
		404: [ 'MODEL_NOT_FOUND' ]
	},
	NOT_FOUND = 'convertNullToNotFoundError'

module.exports = function() {
	return function errorHandler(err, req, res, next) {
		const { statusCode, code, stack } = err,
			{ originalUrl, headers, body, params, query } = req

		if (FILTER_LIST[statusCode] && FILTER_LIST[statusCode].includes(code)) {
			if (stack.includes(NOT_FOUND)) {
				res.status(200)
				res.type('application/json')
				return res.send('null')
			}
		}
		err.originalUrl = originalUrl
		next(err)
	}
}
