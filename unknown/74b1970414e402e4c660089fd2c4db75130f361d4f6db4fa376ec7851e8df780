/**
 *  @module Messaging
 */
const { Messagings } = require('@crm/types')
const { Service } = Messagings

class Messaging {

	constructor(app) {
		this.TYPE = Service

		this.app = app
		this.models = app.models
		this.messagings = {}
		this.messagingIds = {}
	}

	get(name) {
		return this.messagings[name]
	}

	add(name, param, messagingId) {
		this.messagings[name] = param
		this.messagingIds[name] = messagingId
	}

	async create(name) {
		this.messagingIds[name] = null

		const messaging = await this.models.Messaging.create(this.messagings[name]),
			{ id } = messaging

		this.messagingIds[name] = id
		return messaging
	}

	async addTargets(name, targets, options) {
		const messaging = await this._find(name)
		return await messaging.add(targets, options)
	}

	sendSMS(text, to, options) {
		return this.models.Messaging.sendSMS(text, to, options)
	}

	sendWhatsapp(content, to, options) {
		return this.models.Messaging.sendWhatsapp(content, to, options)
	}

	sendEmail(subject, body, to, from, options) {
		return this.models.Messaging.sendEmail(subject, body, to, from, options)
	}

	sendRich(content, target, options) {
		return this.models.Messaging.sendRich(content, target, options)
	}

	sendNotify(content, target, options) {
		return this.models.Messaging.sendNotify(content, target, options)
	}

	async start(name) {
		return this._call(name, 'start')
	}

	async pause(name) {
		return this._call(name, 'pause')
	}

	async resume(name) {
		return this._call(name, 'resume')
	}

	async stop(name) {
		return this._call(name, 'stop')
	}

	async _call(name, method) {
		if (!this.messagingIds[name]) return

		const messaging = await this._find(name)
		return messaging[method]()
	}

	_find(name) {
		return this.models.Messaging.findById(this.messagingIds[name])
	}
}

module.exports = exports = Messaging
