{"name": "LogoImage", "plural": "LogoImages", "base": "Image", "idInjection": true, "strict": false, "options": {}, "properties": {"name": {"type": "String", "max": 16, "description": "unique identifier, eg. profile, photo1"}, "bgcolor": {"type": "String"}}, "validations": [], "acls": [], "indexes": {}, "scopes": {}, "methods": {"prototype.destroy": {"http": {"verb": "delete", "path": "/"}, "accepts": [{"arg": "fk", "type": "any", "description": "Foreign key for LogoImage", "required": true, "http": {"source": "path"}}], "description": "Delete a related item by id for LogoImage.", "returns": []}}}