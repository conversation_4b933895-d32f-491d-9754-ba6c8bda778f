/**
 *  @module Stats - https://www.npmjs.com/package/hot-shots
 */

const { Metrics } = require('@perkd/metrics')

const Event = {
	stats: 'stats',
}

function Stats(app, settings) {
	if (!(this instanceof Stats)) {
		return new Stats(app, settings)
	}

	const { prefix } = settings

	this.app = app
	this.metrics = new Metrics(prefix)
	this._settings = settings
	this._service = app.service
	this._metric = { prefix }		// extend Metrics according to 'stats.metrics' in App's config
	this._hbFlag = true

	const { registry } = this.metrics // global list of metrics
	this.app.Stats = registry
}

module.exports = exports = Stats
exports.Event = Event

Stats.prototype.init = function() {
	const { app, _settings } = this

	if (_settings.enabled) {
		app.on(Event.stats, evt => {
			this.metrics.send(evt)
		})
	}

	this.heartbeat()
}

Stats.prototype.heartbeat = function() {
	const { _settings, metrics, _hbFlag } = this,
		{ registry } = metrics,
		{ heartbeat } = _settings

	if (heartbeat) {
		this.metrics.send({
			metric: registry.heartbeat.alive,
			value: (_hbFlag) ? 1 : 0,
		})

		this._hbFlag = !_hbFlag

		_.delay(this.heartbeat.bind(this), heartbeat ? (heartbeat.interval || 1000) : 1000) // schedule next register attempt
	}
}
