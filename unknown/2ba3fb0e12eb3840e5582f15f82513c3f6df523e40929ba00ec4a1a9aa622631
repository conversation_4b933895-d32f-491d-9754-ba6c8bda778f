{"name": "Timeseries", "base": "PersistedModel", "idInjection": true, "options": {"validateUpsert": true}, "mixins": {"Multitenant": true, "Timeseries": true, "Mongo": true, "MongoCollection": true}, "properties": {"id": {"id": true, "type": "string"}, "key": {"type": "String", "required": true}, "dimensions": {"type": "Object"}, "dValues": {"type": "Object"}, "scale": {"type": "Number", "description": "definition => 0: second, 1: minute, 2: hour, 3: day, 4: month, 5: year"}, "startTS": {"type": "Number", "description": "timestamp of scale start"}, "refreshTS": {"type": "Number", "description": "timestamp of refresh this document"}, "timeseries": {"type": "Object"}}, "validations": [], "relations": {}, "acls": [], "methods": {}}