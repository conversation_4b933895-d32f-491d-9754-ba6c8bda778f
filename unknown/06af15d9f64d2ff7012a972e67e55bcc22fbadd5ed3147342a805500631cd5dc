/**
 *  @module Mixin:ComputeStartEndTime
 */

const { getStartTime, getEndTime } = require('@perkd/utils')

module.exports = function(Model, options) {

	Model.prototype.computeStartTime = function(startTime, rule, reference) {
		const { activePeriod } = this
		return getStartTime(startTime ? { base: startTime } : rule, activePeriod, reference)
	}

	Model.prototype.computeEndTime = function(endTime, startTime, rule, reference) {
		const { activePeriod } = this
		return getEndTime(endTime ? { base: endTime } : rule && { base: startTime, ...rule }, activePeriod, reference)
	}
}
