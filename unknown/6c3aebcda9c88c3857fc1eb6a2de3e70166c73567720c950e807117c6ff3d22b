/**
 * @module Model:TimeseriesValue
 */

const { pickDeep, cloneDeep, satisfyCondition, ObjectId } = require('@perkd/utils'),
	TimeSeries = appRequire('lib/common/modules/ometrics/timeseries')

module.exports = function(TimeseriesValue) {

	// -----  Static Methods  -----

	TimeseriesValue.saveMetricValues = function(key, timestamp, data, filter, scales, computeFn, property, ttl) {
		if (!filter || satisfyCondition(data.evtData, filter)) {
			// use evtData as value, or extract value of property from evtData
			const newValue = (property === undefined) ? data.evtData : pickDeep(data.evtData, property)

			TimeseriesValue.create({
				key,
				dimensions: data.dimensions,
				dValues: data.dValues,
				timestamp,
				value: newValue,
			})
		}
	}

	TimeseriesValue.getMetricValues = async function(key, dimensions, dValues, startTs, endTs, scale, granularity, timezone) {
		const startTimeslots = TimeSeries.timeslots(startTs, timezone),
			endTimeslots = TimeSeries.timeslots(endTs, timezone),
			dbScale = TimeseriesValue.getDbScale(scale, granularity) || scale,
			initedValues = TimeseriesValue.initMetricValues(startTs, endTs, scale, granularity, timezone),
			allData = {},
			dVal = dValues ? dValues.map(d => (/^[0-9a-f]{24}$/.test(d) ? ObjectId(d) : d)) : undefined

		return TimeseriesValue.collection()
			.find({
				key: { $regex: key },
				dimensions: { $eq: dimensions }, // must use $eq operator
				dValues: dVal,
				timestamp: { $gte: startTs, $lte: endTs },
			}, { key: true, dValues: true, timestamp: true, value: true })
			.toArray()
			.then(instances => {
				for (let i = 0; i < instances.length; i++) {
					const instance = instances[i], slot = TimeSeries.timeslots(instance.timestamp, timezone)[dbScale], startTS = slot.ts / 1000
					allData[instance.key] = allData[instance.key] || {
						dValues: instance.dValues,
						values: cloneDeep(initedValues),
						value: new Set(),
					}
					allData[instance.key].values[startTS][slot.ndx] = allData[instance.key].values[startTS][slot.ndx] || new Set()
					allData[instance.key].values[startTS][slot.ndx].add(instance.value)
					allData[instance.key].value.add(instance.value)
				}
				const R = {}
				Object.keys(allData).forEach(key => {
					Object.keys(allData[key].values).forEach(ts => {
						for (let i = 0; i < allData[key].values[ts].length; i++) {
							const value = allData[key].values[ts][i]
							allData[key].values[ts][i] = (value instanceof Set) ? value.size : 0
						}
					})
					allData[key].value = allData[key].value.size

					allData[key].values[Math.floor(endTimeslots[dbScale].ts / 1000)]
						.splice(endTimeslots[scale].idx + 1) // MUST DO first
					allData[key].values[Math.floor(startTimeslots[dbScale].ts / 1000)]
						.splice(0, startTimeslots[scale].idx)

					R[key] = R[key] || { dValues: allData[key].dValues, values: [], value: allData[key].value }
					Object.keys(allData[key].values).sort().forEach(TS => {
						R[key].values = R[key].values.concat(allData[key].values[TS])
					})
				})

				return R
			})
	}
}
