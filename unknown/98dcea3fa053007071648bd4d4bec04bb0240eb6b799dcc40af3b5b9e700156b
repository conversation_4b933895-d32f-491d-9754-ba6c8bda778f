{"name": "OptionValue", "plural": "OptionValues", "base": "Model", "description": "Variation/Option value", "idInjection": false, "strict": true, "mixins": {}, "options": {}, "properties": {"id": {"type": "string", "required": true, "description": "unique among all option values of an option, eg. 1, 2, 3"}, "variantId": {"type": "string"}, "title": {"type": "String", "max": 24, "description": "Display name, eg. L, M, Blue"}, "price": {"type": "number", "default": 0, "description": "Overrides Variant price"}, "image": {"type": "String", "description": "Url for image"}, "quantity": {"type": "number", "description": "Used in Order Item only: quantity selected"}, "selected": {"type": "boolean", "description": "Used in Product bundleList only: is this value selected?"}}, "methods": {}}