{"name": "RewardMaster", "plural": "RewardMasters", "description": "CRM", "base": "PersistedModel", "idInjection": true, "strict": false, "mixins": {"MultitenantRemote": true}, "properties": {"id": {"type": "String", "id": true, "generated": true}}, "relations": {}, "acls": [], "methods": {"issue": {"description": "Issue reward set to membership", "http": {"path": "/:id/set/issue", "verb": "post"}, "accepts": [{"arg": "id", "type": "any", "http": {"source": "path"}, "required": true}, {"arg": "membershipId", "type": "any", "required": true}, {"arg": "options", "type": "object"}], "returns": {"type": "object", "root": true}}, "issueStamps": {"description": "Issue reward stamps to membership", "http": {"path": "/:id/stamps/issue", "verb": "post"}, "accepts": [{"arg": "id", "type": "any", "http": {"source": "path"}, "required": true}, {"arg": "membershipId", "type": "any", "required": true}, {"arg": "quantity", "type": "number", "required": true}, {"arg": "options", "type": "object"}], "returns": {"type": "object", "root": true}}, "deductStamps": {"description": "Deduct reward stamp to membership", "http": {"path": "/:id/stamps/deduct", "verb": "post"}, "accepts": [{"arg": "id", "type": "any", "http": {"source": "path"}, "required": true}, {"arg": "membershipId", "type": "any", "required": true}, {"arg": "quantity", "type": "number", "required": true}, {"arg": "options", "type": "object"}], "returns": {"type": "object", "root": true}}, "doQualify": {"description": "Qualify and Apply actions for both Reward Sets and Stamps", "http": {"path": "/qualifyAndApply", "verb": "post"}, "accepts": [{"arg": "memberId", "type": "any", "required": true}, {"arg": "behavior", "type": "object", "required": true}, {"arg": "options", "type": "object"}], "returns": {"type": "object", "root": true}}, "prototype.createRewardImage": {"http": {"path": "/levels/:fk/rewardImages", "verb": "post"}, "accepts": [{"arg": "fk", "type": "string", "http": {"source": "path"}, "required": true}, {"arg": "data", "type": "object", "http": {"source": "body"}}], "returns": {"type": "RewardImage", "root": true}}, "prototype.uploadRewardImage": {"http": {"path": "/levels/:fk/rewardImages/:imagefk/upload", "verb": "post"}, "accepts": [{"arg": "fk", "type": "string", "http": {"source": "path"}, "required": true}, {"arg": "imagefk", "type": "string", "required": true}, {"arg": "req", "type": "object", "http": {"source": "req"}}, {"arg": "res", "type": "object", "http": {"source": "res"}}], "returns": {"type": "RewardImage", "root": true}}, "prototype.deleteRewardImage": {"http": {"path": "/levels/:fk/rewardImages/:imagefk/delete", "verb": "delete"}, "accepts": [{"arg": "fk", "type": "string", "http": {"source": "path"}, "required": true}, {"arg": "imagefk", "type": "string", "required": true}], "returns": {"type": "object", "root": true}}}}