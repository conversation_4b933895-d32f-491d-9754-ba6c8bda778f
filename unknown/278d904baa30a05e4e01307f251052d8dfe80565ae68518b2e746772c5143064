{"name": "ContentMessage", "plural": "ContentMessages", "description": "Perkd", "base": "PersistedModel", "idInjection": true, "strict": false, "options": {}, "mixins": {"MultitenantRemote": true}, "properties": {"id": {"type": "String", "id": true, "generated": true}}, "acls": [], "scopes": {}, "methods": {"prototype.createMessageTemplate": {"description": "Create Perkd Message Template", "http": {"path": "/template/perkd", "verb": "post"}, "accepts": [{"arg": "subject", "type": "object", "required": true, "description": "Subject with translations { en: \"\", zh-Hant: \"\" }"}, {"arg": "options", "type": "object"}], "returns": {"type": "object", "root": true}}}}