{"name": "MicrositeTemplate", "plural": "MicrositeTemplates", "base": "PersistedModel", "idInjection": true, "strict": false, "mixins": {"MultitenantRemote": true}, "properties": {"id": {"type": "string", "id": true, "generated": true}}, "relations": {}, "acls": [], "indexes": {}, "scopes": {}, "methods": {"createMicrosite": {"description": "Create a Microsite", "http": {"path": "/microsites", "verb": "post"}, "accepts": [{"arg": "kind", "type": "string", "required": true}, {"arg": "data", "type": "object", "required": true}], "returns": {"type": "object", "root": true}}}}