{"name": "Transaction", "plural": "Transactions", "base": "PersistedModel", "idInjection": true, "strict": false, "mixins": {"MultitenantRemote": true}, "options": {"validateUpsert": true}, "properties": {"id": {"type": "String", "id": true, "generated": true}}, "validations": [], "relations": {"payment": {"type": "belongsTo", "model": "Payment", "foreignKey": "paymentId"}}, "acls": [], "indexes": {}, "scopes": {}, "methods": {"findByReferenceId": {"description": "Find a transaction by type & referenceId", "http": {"path": "/referenceId", "verb": "get"}, "accepts": [{"arg": "type", "type": "string", "required": true}, {"arg": "referenceId", "type": "string", "required": true}], "returns": {"type": "Transaction", "root": true}}}}