{"name": "Email", "plural": "Emails", "base": "Model", "idInjection": false, "strict": true, "mixins": {}, "options": {"validateUpsert": true}, "properties": {"id": {"type": "String", "id": true, "defaultFn": "nanoid"}, "address": {"type": "string", "required": true, "length": 254}, "type": {"type": "String", "required": true, "max": 32, "description": "work, home, others"}, "optIn": {"type": "Boolean"}, "valid": {"type": "boolean"}}, "validations": [], "relations": {}, "acls": [], "indexes": {}, "scopes": {}, "methods": []}