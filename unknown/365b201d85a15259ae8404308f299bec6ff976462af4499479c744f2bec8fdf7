/**
 *  @module Codes (lookup)
 */

const { findKey, values } = require('lodash')

// -----  Static Properties  -----
const db = { groups: {} },
	fn = {},
	allCodes = [ 'gender', 'contact', 'salutation', 'ethnicity', 'religion', 'countrycode' ]

function Codes(codeList) {
	codeList = codeList || allCodes
	codeList.forEach(name => {
		try {
			fn[name]()
		}
		catch (e) {
			console.log('codeLookup: function to load `%s` missing', name)
		}
	})
}

module.exports = exports = Codes

// -----  Instance Methods  -----

Codes.prototype.getList = function(type, language) {
	const list = {}
	let obj

	for (const key in db[type]) {
		obj = db[type][key]
		list[key] = obj[language] ? obj[language] : obj[Object.keys(obj)[0]]
	}
	return list
}

Codes.prototype.lookup = function(type, key, language) {
	type = type.toLowerCase()
	const obj = db[type] ? db[type][key] : null
	return obj ? (obj[language] ? obj[language] : obj[Object.keys(obj)[0]]) : null
}

Codes.prototype.reverseLookup = function(type, value) {
	type = type.toLowerCase()
	return findKey(db[type], o => values(o, value).length > 0)
}

Codes.prototype.groups = function(type, language) {
	const groups = {}
	type = type.toLowerCase()
	for (const gp in db.groups[type]) {
		const obj = db.groups[type][gp]
		groups[gp] = obj[language] ? obj[language] : obj[Object.keys(obj)[0]]
	}
	return groups
}

// -----  Private functions  -----

fn.gender = function() {
	db.gender = {
		0: { en: 'unknown', 'zh-hans': '不知' },
		1: { en: 'male', 'zh-hans': '男' },
		2: { en: 'female', 'zh-hans': '女' },
	}
}

fn.contact = function() {
	db.contact = {
		email: { en: 'email' },
		voice: { en: 'voice' },
		sms: { en: 'sms' },
		fbMessage: { en: 'fbMessage' },
		twitter: { en: 'twitter' },
	}
}

fn.salutation = function() {
	db.salutation = {
		1: { en: 'Mr', 'zh-hans': '先生' },
		2: { en: 'Mrs', 'zh-hans': '女士' },
		3: { en: 'Ms', 'zh-hans': '小姐' },
		4: { en: 'Mdm', 'zh-hans': '女士' },
	}
}

fn.ethnicity = function() {
	db.ethnicity = {
		100: { en: 'Asian', 'zh-hans': '亚裔' },
		101: { en: 'Chinese' },
		102: { en: 'Indian' },
		103: { en: 'Indonesian' },
		104: { en: 'Japanese' },
		105: { en: 'Korean' },
		106: { en: 'Taiwanese' },
		107: { en: 'Filipino' },
		108: { en: 'Malay' },
		109: { en: 'Thai' },
		110: { en: 'Vietnamese' },
		111: { en: 'Laotian' },
		112: { en: 'Cambodian' },
		113: { en: 'Pakistani' },
		114: { en: 'Burmese' },
		115: { en: 'Bangladeshi' },
		116: { en: 'Bhutanese' },
		117: { en: 'Hindi' },
		118: { en: 'Sri Lankan' },
		119: { en: 'West Indian' },
		120: { en: 'Afghanistani' },
		121: { en: 'Maldivian' },
		122: { en: 'Nepalese' },
		123: { en: 'Mongolian' },
		200: { en: 'European' },
		201: { en: 'Caucasian' },
		202: { en: 'English' },
		203: { en: 'French' },
		204: { en: 'German' },
		205: { en: 'Italian' },
		206: { en: 'Irish' },
		207: { en: 'Nordic' },
		208: { en: 'Greek' },
		209: { en: 'Danish' },
		210: { en: 'Celtic/Gaelic' },
		211: { en: 'Slavic' },
		212: { en: 'Russian' },
		213: { en: 'Portuguese' },
		214: { en: 'Spaniard' },
		215: { en: 'Polish' },
		216: { en: 'Scottish' },
		217: { en: 'Bosnian' },
		218: { en: 'Dutch' },
		219: { en: 'Swedish' },
		300: { en: 'American' },
		301: { en: 'Brazillian' },
		302: { en: 'Central American' },
		303: { en: 'Central American Indian' },
		304: { en: 'African American' },
		305: { en: 'South American' },
		306: { en: 'Latin American' },
		307: { en: 'Hawaiian' },
		308: { en: 'Latino' },
		309: { en: 'Mexican' },
		310: { en: 'Nicaraguan' },
		311: { en: 'Panamanian' },
		312: { en: 'Puerto Rican' },
		313: { en: 'Argentinean' },
		314: { en: 'Chilean' },
		315: { en: 'Colombian' },
		316: { en: 'Peruvian' },
		317: { en: 'Bolivian' },
		318: { en: 'Ecuadorian' },
		319: { en: 'Paraguayan' },
		320: { en: 'Tobagoan' },
		321: { en: 'Trinidadian' },
		322: { en: 'Uruguayan' },
		323: { en: 'Venezuelan' },
		324: { en: 'Salvadoran' },
		325: { en: 'Costa Rican' },
		326: { en: 'Guatemalan' },
		327: { en: 'Honduran' },
		328: { en: 'Bahamian' },
		329: { en: 'Cuban' },
		330: { en: 'Jamaican' },
		331: { en: 'Dominican' },
		332: { en: 'Dominica Islander' },
		333: { en: 'Haitian' },
		334: { en: 'Barbadian' },
		400: { en: 'African' },
		401: { en: 'Black' },
		402: { en: 'North African' },
		403: { en: 'Madagascar' },
		404: { en: 'Nigerian' },
		405: { en: 'Botswanan' },
		406: { en: 'Ethiopian' },
		407: { en: 'Liberian' },
		408: { en: 'Namibian' },
		500: { en: 'Middle Eastern' },
		501: { en: 'Arab' },
		502: { en: 'Jewish' },
		503: { en: 'Iraqi' },
		504: { en: 'Iranian' },
		505: { en: 'Israeili' },
		506: { en: 'Egyptian' },
		507: { en: 'Lebanese' },
		508: { en: 'Palestinian' },
		509: { en: 'Syrian' },
		510: { en: 'Turkish' },
		601: { en: 'Fijian' },
		602: { en: 'Tahitian' },
		603: { en: 'Melanesian' },
		604: { en: 'Micronesian' },
		605: { en: 'Kiribati' },
		606: { en: 'New Hebrides' },
		607: { en: 'Palauan' },
		608: { en: 'Papau New Guinean' },
		609: { en: 'Chuukese' },
		610: { en: 'Marshallese' },
		611: { en: 'Kosraean' },
		612: { en: 'Pohnpeian' },
		613: { en: 'Tongan' },
		614: { en: 'Solomon Islander' },
		615: { en: 'Polynesian' },
		616: { en: 'Samoan' },
		617: { en: 'Tokelauan' },
		1000: { en: 'Others' },
	}

	// extract groups
	const total = Object.keys(db.ethnicity).length
	db.groups.ethnicity = {}
	for (let i = 1; i < total; i++) {
		if (db.ethnicity[i * 100]) db.groups.ethnicity[i * 100] = db.ethnicity[i * 100]
	}
}

fn.religion = function() {
	db.religion = {
		100: { en: 'Non-religious' },
		101: { en: 'Free Thinker' },
		102: { en: 'Agnostic' },
		103: { en: 'Atheist' },
		200: { en: 'Buddhist' },
		201: { en: 'Buddhist: Mahayana' },
		202: { en: 'Buddhist: Tantrayana' },
		203: { en: 'Buddhist: Theravada' },
		204: { en: 'Buddhist: Other' },
		300: { en: 'Christian' },
		301: { en: 'Christian: African Methodist Episcopal' },
		302: { en: 'Christian: African Methodist Episcopal Zion' },
		303: { en: 'Christian: American Baptist Church' },
		304: { en: 'Christian: Anglican' },
		305: { en: 'Christian: Assembly of God' },
		306: { en: 'Christian: Baptist' },
		307: { en: 'Christian: Christian Missionary Alliance' },
		308: { en: 'Christian: Christian Reformed' },
		309: { en: 'Christian: Christian Science' },
		310: { en: 'Christian: Church of Christ' },
		311: { en: 'Christian: Church of God' },
		312: { en: 'Christian: Church of God in Christ' },
		313: { en: 'Christian: Church of the Nazarene' },
		314: { en: 'Christian: Community' },
		315: { en: 'Christian: Congregational' },
		316: { en: 'Christian: Eastern Orthodox' },
		317: { en: 'Christian: Episcopalian' },
		318: { en: 'Christian: Evangelical Church' },
		319: { en: 'Christian: Free Will Baptist' },
		320: { en: 'Christian: Friends' },
		321: { en: 'Christian: Greek Orthodox' },
		322: { en: "Christian: Jehovah's Witness" },
		323: { en: 'Christian: Latter-day Saints' },
		324: { en: 'Christian: Lutheran' },
		325: { en: 'Christian: Lutheran Missouri Synod' },
		326: { en: 'Christian: Mennonite' },
		327: { en: 'Christian: Methodist' },
		328: { en: 'Christian: Orthodox' },
		329: { en: 'Christian: Pentecostal' },
		330: { en: 'Christian: Presbyterian' },
		331: { en: 'Christian: Protestant' },
		332: { en: 'Christian: Reformed Church' },
		333: { en: 'Christian: Reorganized Church of Jesus Christ-LDS' },
		334: { en: 'Christian: Roman Catholic' },
		335: { en: 'Christian: Salvation Army' },
		336: { en: 'Christian: Seventh Day Adventist' },
		337: { en: 'Christian: Southern Baptist' },
		338: { en: 'Christian: Unitarian' },
		339: { en: 'Christian: Unitarian Universalist' },
		340: { en: 'Christian: United Church of Christ' },
		341: { en: 'Christian: United Methodist' },
		342: { en: 'Christian: Wesleyan' },
		343: { en: 'Christian: Wesleyan Methodist' },
		344: { en: 'Christian: Other' },
		400: { en: 'Hindu' },
		401: { en: 'Hindu: Shaivites' },
		402: { en: 'Hindu: Vaishnavites' },
		403: { en: 'Hindu: Other' },
		500: { en: 'Jewish' },
		501: { en: 'Jewish: Conservative' },
		502: { en: 'Jewish: Orthodox' },
		503: { en: 'Jewish: Reconstructionist' },
		504: { en: 'Jewish: Reform' },
		505: { en: 'Jewish: Renewal' },
		506: { en: 'Jewish: Other' },
		600: { en: 'Muslim' },
		601: { en: 'Muslim: Shiite' },
		602: { en: 'Muslim: Sunni' },
		603: { en: 'Muslim: Other' },
		700: { en: 'Others' },
		701: { en: 'Taoist' },
		702: { en: 'Confucian' },
		703: { en: 'Ethnic Religionist' },
		704: { en: 'Jain' },
		705: { en: 'Sikh' },
		706: { en: 'Shintoist' },
		707: { en: 'Spiritist' },
		708: { en: 'Native American' },
		709: { en: 'New Religionist' },
		710: { en: "Baha'i" },
	}

	// extract groups
	const total = Object.keys(db.religion).length
	db.groups.religion = {}
	for (let i = 1; i < total; i++) {
		if (db.religion[i * 100]) db.groups.religion[i * 100] = db.religion[i * 100]
	}
}

fn.countrycode = function() {
	db.countrycode = {
		SG: { en: 'Singapore', 'zh-hans': '新加坡' },
		MY: { en: 'Malaysia', 'zh-hans': '马来西亚' },
	}
}

/**
 * End of script
 */
