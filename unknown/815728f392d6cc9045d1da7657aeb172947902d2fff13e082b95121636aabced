/**
 * fake order creation
 * <AUTHOR>
 */

const argv = require('minimist')(process.argv.slice(2));

const Faker = require('Faker'),
	rn = require('random-number'),
	rp = require('request-promise'),
	async = require('async'),
	bm = require('../benchmark'),
	_ = require('lodash');

if (argv._.length === 0 || argv.help) {
	console.log('\n Usage:');
	console.log('	1) node fake-sale.js tenantCode // default create 1 sale, concurrency 1');
	console.log('	2) node fake-sale.js tenantCode -s 100 -c 10 -d 1000 // create 100 orders, concurrency 10, each create delay 1s');
	console.log('	3) node fake-sale.js tenantCode -h staging.waveo.com // change api to staging, default is localhost');
	// console.log('	1) node fake-contact.js tenantCode -m 91000000 // start mobile no. from 91000000');
	console.log('\n');
	return;
}

// parse argv
const PARAM = {
	tenantCode: argv._[0],
	size: argv.s || 1,
	concurrency: argv.c || 1,
	host: argv.h || '127.0.0.1',
	delay: argv.d || 0,
};

// var startMobile = 91000000;
const totalCreated = 1;

if (!validate()) return;

const API = {
	Order: 'http://' + PARAM.host + ':3102/api',
};

function validate() {
	if (isNaN(PARAM.size)) {
		console.log('\n *** Error: -s should be a number\n');
		return false;
	}
	if (isNaN(PARAM.concurrency)) {
		console.log('\n *** Error: -c should be a number\n');
		return false;
	}
	if (isNaN(PARAM.delay)) {
		console.log('\n *** Error: -d should be a number\n');
		return false;
	}
	return true;
}

function fakeOrders(total, concurrency, callback) {
	const queue = async.queue((order, cb) => {
	    createOrder(order).delay(1000).finally(cb);
	}, concurrency);

	queue.pause();

	queue.drain = function() {
		callback(totalCreated);
	};

	for (let i = 1; i <= total; i++) {
		const order = {
			cardNumber: 'TEST0000000' + i,
			receiptNumber: 'Test' + new Date().getTime(),
			purchaseAt: new Date(),
			channel: 'store',
			channelId: '5906ce5444d871347eef7001',
			quantity: 2,
			currency: 'SGD',
			totalAmount: _.random(20, 100),
			status: 1,
			personId: '5a032e0ce2b7034eed23d6f7',
			memberId: '5a032e0d0ed4ed50495385ab',
			membershipid: '5a032e0e0ed4ed50495385af',
		};
		queue.push(order);
	}

	queue.resume();
}

function createOrder(order) {											// TODO - ZJ
	const options = {
		url: API.Order + '/Orders/doUpsert',
		json: { data: order },
	};
	return callAPI(options);
}

function callAPI(opt) {
	const options = Object.assign({
		method: 'POST',
		headers: { 'tenant-code': PARAM.tenantCode },
	}, opt);
	return rp(options).then(result => {
		console.log('result %j', result);
		return result;
	});
}

bm.mark('create.start');
fakeOrders(PARAM.size, PARAM.concurrency, created => {
	bm.mark('create.end');
	const lapsed = bm.elapsedTime('create') / 1000;
	console.log('Total %s contacts created, lapsed %s (s)', created, lapsed);
});

/**
 * End of script
 */
