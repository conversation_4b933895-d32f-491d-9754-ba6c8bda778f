/**
 *  @module Mixin:SoftDelete
 */

const { touchpoint } = require('@perkd/touchpoints')

module.exports = function(Model) {

	Model.prototype.softDelete = function (through = touchpoint()) {
		return this.updateAttributes({ deletedAt: new Date(), through })
	}

	Model.remoteMethod('prototype.softDelete', {
		description: `Mark ${Model.name} as deleted.`,
		http: { path: '/softDelete', verb: 'delete' },
		accepts: [
			{ arg: 'through', type: 'object' },
		],
		returns: { type: `${Model.name}`, root: true },
	})
}
