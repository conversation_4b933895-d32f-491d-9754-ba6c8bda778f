/**
 *  @module Mixin:State
 *	Must be the last mixin to modify validated properties!
 */
const Ajv = require('ajv'),
	ajv = new Ajv({ allErrors: true })

ajv.addKeyword('hasImage', {
	validate: (expected, instance) => instance.hasImage(),
	async: true,
	errors: false,
})

/**
 * @param {Object} options
 * example (CardMaster):
 {
	"schema": {
		"$async": true,
		"properties": {},
		"required": [
			"name",
			"issuerId",
			"brand"
		],
		"hasImage": true
	}
}
*/
module.exports = function(Model, options) {
	const { STATE } = Model,
		{ INCOMPLETE, READY } = STATE,
		SCHEMA = options.schema,
		validate = ajv.compile(SCHEMA),
		getState = updatedInstance => validate(updatedInstance).then(() => READY).catch(() => INCOMPLETE)

	// -----  Remote & Operation hooks  -----

	Model.observe('before save', async ctx => {
		const { instance, currentInstance = {}, data = {} } = ctx,
			updatedInstance = getUpdatedInstance(ctx),
			updates = instance || data,
			canRefreshState = !data.state,
			stateIncomplete = (instance && instance.state === INCOMPLETE) || currentInstance.state === INCOMPLETE,
			stateReady = currentInstance.state === READY,
			needValidate = canRefreshState && (stateIncomplete || stateReady)

		if (needValidate) updates.state = await getState(updatedInstance)
	})

	Model.observe('after imageUploaded', async ({ instance }) => {
		const needValidate = instance.state === INCOMPLETE
		if (needValidate) return instance.updateAttributes({ state: await getState(instance) })
	})

	Model.observe('after imageDeleted', async ({ instance }) => {
		const needValidate = instance.state === READY
		if (needValidate) return instance.updateAttributes({ state: await getState(instance) })
	})

	// -----  Private Functions  -----

	function getUpdatedInstance({ instance, currentInstance, data }) {
		if (instance) return instance
		const updated = new Model(currentInstance, { applyDefaultValues: false })
		updated.setAttributes(data)
		return updated
	}
}
