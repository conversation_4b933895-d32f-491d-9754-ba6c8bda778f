/**
 *  @module Mixin:Buy<PERSON><PERSON> (callbacks for Card<PERSON>)  - depends on Buy mixin
 */

const { Payments } = require('@crm/types')

const { PAID } = Payments.Status,
	{ INTENT, SOURCE } = Payments.TransactionType

module.exports = function(Model) {

	/**
	 * Commit (pending) Order (by app) - eg. <PERSON>, LinePay
	 * @param	{String} userId - not used
	 * @param	{Object} payment
	 *			{String} method
	 *			{String} provider
	 *			{Object} intent|source: { id, key }
	 * @return {Object} { payment, payments: [], fulfilled: [] }
	 */
	Model.appOrderCommit = function(userId, payment) {
		const { method, provider, intent, source } = payment,
			type = intent ? INTENT : SOURCE,
			status = PAID,
			details = intent || source || {},
			{ id: referenceId } = details,
			transaction = { type, method, referenceId, status, provider, details }

		return Model.orderCommit(transaction)
	}

	/**
	 * Cancel Order associated with payment (by app)
	 * @param	{String} userId - not used
	 * @param	{Object} payment
	 *			{String} method
	 *			{String} provider
	 *			{Object} intent|source: { id }
	 * @param	{Object} options - { reason, cancelledAt, through }
	 * @return	{Promise<Order>}
	 */
	Model.appOrderCancel = function(userId, payment, options) {
		const { method, provider, intent, source } = payment,
			type = intent ? INTENT : SOURCE,
			details = intent || source || {},
			{ id: referenceId } = details,
			transaction = { type, method, referenceId, provider, details }

		return Model.orderCancel(transaction, options)
	}

	// -----  Remote Methods  -----

	Model.remoteMethod('order', {
		description: 'Order without payment (callback API)',
		http: { path: '/perkd/order', verb: 'post' },
		accepts: [
			{ arg: 'userId', type: 'string', required: true },
			{ arg: 'order', type: 'object', required: true },
			{ arg: 'pricings', type: 'array' },
			{ arg: 'options', type: 'object' },
		],
		returns: { type: 'object', root: true },
	})

	Model.remoteMethod('orderPay', {
		description: 'Order with Payment (callback API)',
		http: { path: '/perkd/order/pay', verb: 'post' },
		accepts: [
			{ arg: 'userId', type: 'string' },
			{ arg: 'payments', type: 'array', required: true },
			{ arg: 'order', type: 'object', required: true },
			{ arg: 'pricings', type: 'array' },
			{ arg: 'options', type: 'object' },
		],
		returns: { type: 'object', root: true, description: '{ payment, payments: [], fulfilled: [] }' },
	})

	Model.remoteMethod('appOrderCommit', {
		description: 'Commit Payment (pending) for order (callback API)',
		http: { path: '/perkd/order/commit', verb: 'post' },
		accepts: [
			{ arg: 'userId', type: 'string', required: true },
			{ arg: 'payment', type: 'object', required: true },
		],
		returns: { type: 'object', root: true, description: '{ payment, payments: [], fulfilled: [] }' },
	})

	Model.remoteMethod('appOrderCancel', {
		description: 'Cancel Order associated with Payment (callback API)',
		http: { path: '/perkd/order/cancel', verb: 'post' },
		accepts: [
			{ arg: 'userId', type: 'string', required: true },
			{ arg: 'payment', type: 'object', required: true },
			{ arg: 'options', type: 'object' },
		],
		returns: { type: 'object', root: true, description: 'order (with status = cancelled)' },
	})
}
