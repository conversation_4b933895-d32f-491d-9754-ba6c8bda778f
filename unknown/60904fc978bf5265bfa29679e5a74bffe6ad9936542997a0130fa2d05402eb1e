/**
 *  @module Model:PresetCode
 */
const { readFile, mkdir, rm } = require('node:fs/promises'),
	{ Context } = require('@perkd/multitenant-context'),
	{ randomString } = require('@perkd/utils'),
	{ getTenantPath, parseFormData } = require('@perkd/local-storage')

const THRESHOLD = 1000		// codes remaining

module.exports = function(PresetCode) {

	/**
	 * Generate Preset Codes
	 * @param	{String} ownerId
	 * @param	{Number} quantity - number of codes to generate
	 * @param 	{Number} [codeLength] - excluding any prefix/suffix
	 * @param 	{String} [prefix]
	 * @param 	{String} [suffix]
	 * @return	{Array}
	 */
	PresetCode.generate = async function(ownerId, quantity, codeLength = 5, prefix = '', suffix = '') {
		const codes = Array(quantity)

		for (let i = 0; i < quantity; i++) {
			codes[i] = {
				ownerId,
				code: `${prefix}${randomString(codeLength).toUpperCase()}${suffix}`,
			}
		}

		return PresetCode.create(codes)
	}

	/**
	 * Upload Preset Codes
	 * @param	{Object} req
	 * @param	{Object} res
	 * @param 	{Object} options
	 * @return	{Number} total of code uploaded
	 */
	PresetCode.upload = async function(req, res, options = {}) {
		const { offerMasterId: ownerId } = options,
			{ allowedContentSize, allowedContentTypes } = appSettings(),
			container = Context.tenant,
			opt = {
				maxFileSize: allowedContentSize,
				allowedContentTypes,
			},
			records = []

		try {
			const filePath = PresetCode.filePath()
			await mkdir(filePath, { recursive: true })

			// Parse the form data using our utility
			const data = await parseFormData(container, req, res, opt),
				{ files = {} } = data,
				[ first = {} ] = files.file ?? [],
				{ name } = first

			if (!name) {
				throw new Error('Missing file')
			}

			const content = await readFile(filePath + name)		// Buffer
			await rm(filePath, { recursive: true })

			if (!content) throw new Error(`Empty file: ${filePath + name}`)

			content.toString()
				.split('\r\n').join(',')	// rows
				.split(',')					// columns
				.forEach(code => !!code && records.push({ code, ownerId }))

			await PresetCode.create(records)
			return records.length
		}
		catch (err) {
			appNotify('upload', { options, err })
			throw err
		}
	}

	/**
	 * Get next available code
	 * @param	{String} ownerId
	 * @param	{Number} quantity - number of codes to generate
	 * @param 	{Number} codeLength
	 * @return	{String} code
	 */
	PresetCode.nextNumber = async function(ownerId) {
		const { modelName } = this,
			where = {
				ownerId,
				usedAt: null,
			},
			changes = { $currentDate: { usedAt: true } },
			{ value } = await this.collection(modelName).findOneAndUpdate(where, changes)

		if (!value) {
			return this.rejectErr('ran_out_of_available_preset_codes', { ownerId, tenantCode: Context.tenant }, true)
		}

		this.count(where).then(remaining => {
			if (remaining < THRESHOLD) {
				appNotify('presetCodesRunningOut', { ownerId, remaining, THRESHOLD }, 'alert')
			}
		})

		return value.code
	}

	// -----  Private functions  -----

	// TODO Need to remove the tenant folder if the tenant is deleted
	PresetCode.filePath = function(folderName, noSeparator) {
		return getTenantPath(Context.tenant, folderName, noSeparator)
	}
}
