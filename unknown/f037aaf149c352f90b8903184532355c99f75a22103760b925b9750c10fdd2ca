{"name": "<PERSON><PERSON>", "plural": "Triggers", "base": "PersistedModel", "idInjection": true, "strict": false, "mixins": {"MultitenantRemote": true}, "options": {"validateUpsert": true}, "properties": {"id": {"type": "string", "id": true, "generated": true}, "name": {"type": "String", "required": true, "length": 256}, "type": {"type": "String"}, "priority": {"type": "Number", "max": 20}, "callback": {"method": {"type": "String", "required": true}, "url": {"type": "String", "required": true}, "payload": {}}, "start": {"type": "Date"}, "end": {"type": "Date"}, "persistAfterEnd": {"type": "Boolean", "default": false}, "events": ["String"], "condition": {"type": "String"}, "repeatInterval": {"type": "String"}, "repeat": {"type": "Number"}, "repeatRemaining": {"type": "Number"}, "state": {"type": "String"}, "active": {"type": "Boolean", "default": true}, "nextRunAt": {"type": "Date"}, "lastRunAt": {"type": "Date"}, "lastFinishedAt": {"type": "Date"}, "createdAt": {"type": "date"}, "modifiedAt": {"type": "date"}}, "validations": [], "relations": {}, "acls": [], "scopes": {}, "indexes": {}, "methods": {"prototype.schedule": {"http": {"path": "/schedule", "verb": "post"}, "accepts": [{"arg": "when", "type": "any"}], "returns": {"type": "object", "root": true}}, "prototype.listen": {"http": {"path": "/listen", "verb": "post"}, "accepts": [{"arg": "events", "type": "array", "required": true}], "returns": {"type": "object", "root": true}}, "prototype.handle": {"http": {"path": "/handle", "verb": "post"}, "returns": {"type": "object", "root": true}}, "prototype.pause": {"http": {"path": "/pause", "verb": "get"}, "accepts": [{"arg": "when", "type": "string"}], "returns": {"type": "object", "root": true}}, "prototype.resume": {"http": {"path": "/resume", "verb": "get"}, "accepts": [{"arg": "when", "type": "string"}], "returns": {"type": "object", "root": true}}, "prototype.stop": {"http": {"path": "/stop", "verb": "get"}, "accepts": [{"arg": "when", "type": "string"}], "returns": {"type": "object", "root": true}}}}