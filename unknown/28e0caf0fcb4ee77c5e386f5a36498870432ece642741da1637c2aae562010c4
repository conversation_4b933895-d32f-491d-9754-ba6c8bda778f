{"name": "Venue", "plural": "Venues", "base": "Model", "idInjection": false, "strict": false, "mixins": {}, "options": {}, "properties": {"name": {"type": "string", "required": true}, "formattedAddress": {"type": "string"}, "shortAddress": {"type": "string"}, "geo": {"type": "Geometry"}, "position": [{"type": {"key": {"type": "string", "description": "Unique name for position identifier, eg. table, hall, seat (globalized)"}, "value": {"type": "string", "description": "Value position key, eg. 8 (for table), 3H (for seat)"}}}], "placeId": {"type": "String"}}, "validations": [], "relations": {}, "acls": [], "indexes": {}, "scopes": {}, "methods": []}