{"name": "Program", "plural": "Programs", "description": "", "base": "PersistedModel", "idInjection": true, "strict": false, "mixins": {"MultitenantRemote": true, "RemotingTypes": {"Tier": true}}, "properties": {"id": {"type": "string", "id": true, "generated": true}}, "validations": [], "relations": {"tiers": {"type": "embeds<PERSON><PERSON>", "model": "Tier", "property": "tierList", "options": {"validate": true, "forceId": false}}, "numbering": {"type": "hasMany", "model": "Numbering", "foreignKey": "programId"}}, "acls": [], "indexes": {}, "scopes": {}, "methods": {"provision": {"description": "Provision Program (provision only)", "http": {"path": "/provision", "verb": "post"}, "accepts": [{"arg": "type", "type": "string", "required": true}, {"arg": "program", "type": "object", "required": true}, {"arg": "tiers", "type": ["object"], "required": true, "description": "tier + numbering"}], "returns": {"type": "Program", "root": true}}, "provisionStatus": {"description": "Provision status", "http": {"path": "/provision/status", "verb": "get"}, "accepts": [{"arg": "businessId", "type": "string"}], "returns": {"type": "object", "root": true}}, "prototype.deprovision": {"description": "De-provision Program (provision only)", "http": {"path": "/provision", "verb": "delete"}, "accepts": [{"arg": "forced", "type": "boolean", "default": false}], "returns": {"type": "object", "root": true}}, "qualify": {"description": "Qualify for memberships across all Programs", "http": {"path": "/qualify", "verb": "post"}, "accepts": [{"arg": "memberId", "type": "any", "required": true}, {"arg": "qualifiers", "type": "array", "required": true}, {"arg": "behavior", "type": "object"}, {"arg": "options", "type": "object"}], "returns": {"type": "object", "root": true}}, "qualifyAll": {"description": "Qualify for memberships across all Programs (apply qualified)", "http": {"path": "/qualify/all", "verb": "post"}, "accepts": [{"arg": "memberId", "type": "any", "required": true}, {"arg": "behavior", "type": "object"}, {"arg": "options", "type": "object"}], "returns": {"type": "object", "root": true}}, "qualifyProfile": {"description": "Get Qualify Profile for member", "http": {"path": "/qualify/profile", "verb": "post"}, "accepts": [{"arg": "memberId", "type": "any", "required": true}, {"arg": "include", "type": "object"}, {"arg": "behavior", "type": "object"}, {"arg": "options", "type": "object"}], "returns": {"type": "object", "root": true}}, "matchAndJoin": {"description": "Match person and join membership", "http": {"path": "/membership/matchAndJoin", "verb": "post"}, "accepts": [{"arg": "cardMasterId", "type": "any", "required": true}, {"arg": "profile", "type": "object", "required": true}, {"arg": "options", "type": "object"}], "returns": {"type": "object", "root": true}}, "invite": {"description": "Invite to join using profile (Used by OrderProvider, Campaign)", "http": {"path": "/invite", "verb": "post"}, "accepts": [{"arg": "id", "type": "any", "required": true}, {"arg": "profile", "type": "object", "required": true, "description": "{ familyName, givenName, mobile, email, birth }"}, {"arg": "tierLevel", "type": "number", "required": true}, {"arg": "options", "type": "object", "description": "{ through, identity }"}], "returns": {"type": "object", "root": true}}, "joinMembership": {"description": "Join Membership for Member", "http": {"path": "/membership/join", "verb": "post"}, "accepts": [{"arg": "id", "type": "any", "required": true, "description": "Program Id"}, {"arg": "memberId", "type": "any", "required": true}, {"arg": "tierLevel", "type": "number", "required": true}, {"arg": "options", "type": "object"}], "returns": {"type": "Membership", "root": true}}, "renewMembership": {"description": "Renew Membership for Member", "http": {"path": "/membership/renew", "verb": "post"}, "accepts": [{"arg": "id", "type": "any", "required": true, "description": "Program Id"}, {"arg": "memberId", "type": "any", "required": true}, {"arg": "tierLevel", "type": "number", "required": true}, {"arg": "options", "type": "object"}], "returns": {"type": "Membership", "root": true}}, "upgradeMembership": {"description": "Upgrade Membership for Member", "http": {"path": "/membership/upgrade", "verb": "post"}, "accepts": [{"arg": "id", "type": "any", "required": true, "description": "Program Id"}, {"arg": "memberId", "type": "any", "required": true}, {"arg": "tierLevel", "type": "number", "required": true}, {"arg": "options", "type": "object"}], "returns": {"type": "Membership", "root": true}}, "downgradeMembership": {"description": "Downgrade Membership for Member", "http": {"path": "/membership/downgrade", "verb": "post"}, "accepts": [{"arg": "id", "type": "any", "required": true, "description": "Program Id"}, {"arg": "memberId", "type": "any", "required": true}, {"arg": "tierLevel", "type": "number", "required": true}, {"arg": "options", "type": "object"}], "returns": {"type": "object", "root": true}}, "changeMembershipTier": {"description": "Change Membership Tier for Member", "http": {"path": "/membership/changetier", "verb": "post"}, "accepts": [{"arg": "id", "type": "any", "required": true, "description": "Program Id"}, {"arg": "memberId", "type": "any", "required": true}, {"arg": "tierLevel", "type": "number", "required": true}, {"arg": "options", "type": "object"}], "returns": {"type": "Membership", "root": true}}, "getActives": {"description": " Get Active Programs", "http": {"path": "/actives", "verb": "get"}, "accepts": [{"arg": "options", "type": "object", "description": "{ programs: string[] }  restrict to certain keys or programId"}], "returns": {"type": ["Program"], "root": true}}, "findOneActiveFreeOrPaid": {"description": "Find an active lowest/entry Free or Paid program", "http": {"path": "/active/freeOrPaid", "verb": "get"}, "accepts": [], "returns": {"type": "Program", "root": true}}, "findProgramTierByMembership": {"description": "Find Program Tier for Membership", "http": {"path": "/membership/:id/tier", "verb": "get"}, "accepts": [{"arg": "id", "type": "any", "http": {"source": "path"}, "required": true, "description": "Membership id"}], "returns": {"type": "Tier", "root": true}}, "findProgramTierByCardMaster": {"description": "Find Program & Tier using CardMaster Id", "http": {"path": "/cardmaster/:id", "verb": "get"}, "accepts": [{"arg": "id", "type": "any", "http": {"source": "path"}, "required": true, "description": "CardMaster id"}], "returns": {"type": "Object", "root": true}}, "order": {"description": "Order without payment (callback API)", "http": {"path": "/perkd/order", "verb": "post"}, "accepts": [{"arg": "userId", "type": "any", "required": true}, {"arg": "order", "type": "object", "required": true}, {"arg": "pricings", "type": "array"}, {"arg": "options", "type": "object"}], "returns": {"type": "Object", "root": true}}, "prototype.invite": {"description": "Invite to join using profile (Used by OrderProvider)", "http": {"path": "/invite", "verb": "post"}, "accepts": [{"arg": "profile", "type": "object", "required": true, "description": "{ familyName, givenName, mobile, email, birth }"}, {"arg": "tierLevel", "type": "number", "required": true}, {"arg": "options", "type": "object", "description": "{ through, identity }"}], "returns": {"type": "object", "root": true}}, "prototype.joinMembership": {"description": "Join Membership for Member", "http": {"path": "/membership/join", "verb": "post"}, "accepts": [{"arg": "memberId", "type": "any", "required": true}, {"arg": "tierLevel", "type": "number", "required": true}, {"arg": "options", "type": "object"}], "returns": {"type": "Membership", "root": true}}, "prototype.renewMembership": {"description": "Renew Membership for Member", "http": {"path": "/membership/renew", "verb": "post"}, "accepts": [{"arg": "memberId", "type": "any", "required": true}, {"arg": "tierLevel", "type": "number", "required": true}, {"arg": "options", "type": "object"}], "returns": {"type": "Membership", "root": true}}, "prototype.upgradeMembership": {"description": "Upgrade Membership for Member", "http": {"path": "/membership/upgrade", "verb": "post"}, "accepts": [{"arg": "memberId", "type": "any", "required": true}, {"arg": "tierLevel", "type": "number", "required": true}, {"arg": "options", "type": "object"}], "returns": {"type": "Membership", "root": true}}, "prototype.downgradeMembership": {"description": "Downgrade Membership for Member", "http": {"path": "/membership/downgrade", "verb": "post"}, "accepts": [{"arg": "memberId", "type": "any", "required": true}, {"arg": "tierLevel", "type": "number", "required": true}, {"arg": "options", "type": "object"}], "returns": {"type": "object", "root": true}}, "prototype.changeMembershipTier": {"description": "Change Membership Tier for Member", "http": {"path": "/membership/changetier", "verb": "post"}, "accepts": [{"arg": "memberId", "type": "any", "required": true}, {"arg": "tierLevel", "type": "number", "required": true}, {"arg": "options", "type": "object"}], "returns": {"type": "Membership", "root": true}}, "prototype.qualify": {"description": "Qualify for memberships of program", "http": {"path": "/qualify", "verb": "post"}, "accepts": [{"arg": "memberId", "type": "any", "required": true}, {"arg": "options", "type": "object"}], "returns": {"type": "array", "root": true}}, "prototype.qualifiers": {"description": "Get qualified list for Member", "http": {"path": "/qualifies", "verb": "get"}, "accepts": [{"arg": "profile", "type": "object", "required": true, "description": "Qualify Profile by calling qualifyProfile()"}, {"arg": "options", "type": "object"}], "returns": {"type": "object", "root": true}}, "prototype.createMembership": {"description": "Create Membership for Member", "http": {"path": "/membership/create", "verb": "post"}, "accepts": [{"arg": "memberId", "type": "any", "required": true}, {"arg": "qualifier", "type": "string", "required": true}, {"arg": "tierLevel", "type": "number", "required": true}, {"arg": "options", "type": "object"}], "returns": {"type": "Membership", "root": true}}, "prototype.nextCardNumber": {"description": "Issue next Card Number for Tier", "http": {"path": "/tier/:tierLevel/cardnumber/next", "verb": "get"}, "accepts": [{"arg": "tierLevel", "type": "Number", "required": true}], "returns": {"type": "boolean", "root": true}}}}