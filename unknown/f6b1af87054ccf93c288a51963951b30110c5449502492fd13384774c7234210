/**
 *  @module Mixin:Search<PERSON>erson
 *  	- used by backend UI, search instance by text strings
 * 		- Dependent on mongodb text index
 */

const { isEmail, isMobilePhone, isNumeric, isDate, isEAN, isISBN, isMongoId, isAlphanumeric } = require('validator'),
	{ DOSEARCH } = require('@perkd/errors/dist/service')

const { ZERO_RESULTS, TIMEOUT } = DOSEARCH,
	OK = 'OK',
	DEFAULT_LIMIT = 50,
	MAX_TIME = 5000			// millisecond

module.exports = function(Person) {

	/**
	 * Search instances of the model matched by text string from the data source
	 * @param  {string} textString - keyword1 keyword2 keyword3... (AND)
	 * @param {Object} options
	 * 		{boolean} list - indicate return list or total number
	 * 		{number} searchThreshold // deprecated
     *  	--- mongo query ---
	 * 		{object} where: {"gender": "f", "createdAt": {$gt: ...}},
	 * 		{object} fields: {"name": 1},
	 * 		{number} limit: 1
	 * 		{object} sort: {"name": 1}
	 * 		{number} skip: 10
	 * @return {promise} [instances] | count
	 */
	Person.search = async function(textString, options = {}) {
		const { list, where = {}, fields = {}, limit = DEFAULT_LIMIT, sort = { modifiedAt: -1 }, skip = 0 } = options

		return list
			? doFind(textString, { where, fields, limit, sort, skip })
			: doCount(textString, { where, fields, limit, sort, skip })
	}

	// -----  Prviate Functions -----
	async function doCount(queryString, options = {}) {
		const { where } = options,
			query = buildQuery(queryString, where),
			count = await Person.collection().countDocuments(query, { maxTimeMS: MAX_TIME })
				.catch(err => {
					if (err.message === 'operation exceeded time limit') {
						return Promise.resolve({ TIMEOUT })
					}
					appLog('search', { searchString: queryString, error: err.message }, 'error')
				})

		// TODO @anne add tags
		// appStats(Stats.search.latency, start, { tags: { tenant: getTenantCode(), keyword: tokens.text, source } })
		// appStats(Stats.search.latency, start, { tags: { tenant: getTenantCode(), keyword: tokens.text } })  // TODO: got Stats not exists error
		// const timeDiff = bm.diff(start)
		return count
	}

	async function doFind(queryString, options = {}) {
		const { where, fields, limit, sort, skip } = options,
			query = buildQuery(queryString, where),
			results = await Person.collection()
				.find(query, { projections: fields, sort, skip, limit, maxTimeMS: MAX_TIME })
				.toArray()
				.catch(err => {
					if (err.message === 'operation exceeded time limit') {
						return Promise.resolve({ results: [], count: 0, status: TIMEOUT })
					}
					appLog('search', { searchString: queryString, error: err.message }, 'error')
				})

		// TODO @anne add tags
		// appStats(Stats.search.latency, start, { tags: { tenant: getTenantCode(), keyword: tokens.text, source } })
		// appStats(Stats.search.latency, start, { tags: { tenant: getTenantCode(), keyword: tokens.text } })  // TODO: got Stats not exists error
		// const timeDiff = bm.diff(start)
		return { results, count: results.length, status: results.length > 0 ? OK : ZERO_RESULTS }
	}

	// -----  Prviate Functions -----
	function buildQuery(queryString, where) {
		const tokens = parseQuery(queryString),
			{ email, date, mobile, number, barcode, mongoId, cardNumber, externalId, text } = tokens,
			query = { ...where, deletedAt: null },
			and = [],
			textSearch = {}

		if (email) {
			for (const address of email) {
				and.push({ 'emailList.address': { $regex: `^${address}` } })
			}
		}
		if (date) {
			for (const aDate of date) {
				and.push({ 'dateList.date': aDate })
			}
		}
		if (mobile || number) {
			const all = [ ...(mobile || []), ...(number || []) ]
			for (const phoneNumber of all) {
				and.push({ $or: [
					{ 'phoneList.fullNumber': { $regex: `^${phoneNumber}` } },
					{ 'phoneList.number': { $regex: `^${phoneNumber}` } }
				] })
			}
		}
		if (mobile || number || barcode || mongoId || cardNumber || externalId) {
			const all = [ ...(mobile || []), ...(number || []), ...(barcode || []), ...(mongoId || []), ...(cardNumber || []), ...(externalId || []) ]
			query.$or = []
			for (const extId of all) {
				query.$or.push({ 'identityList.externalId': extId })
			}
		}
		if (text || mobile || number || barcode || cardNumber || externalId) {
			const all = [ ...(text || []), ...(mobile || []), ...(number || []), ...(barcode || []), ...(cardNumber || []), ...(externalId || []) ]
			query.$or = query.$or || []
			for (const extId of all) {
				query.$or.push({ 'tags.user': extId })
				query.$or.push({ 'tags.system': extId })
			}
		}
		if (text || externalId) { // free text search: fullName
			const all = [ ...(text || []), ...(externalId || []) ]
			textSearch.$text = { $search: `${all.map(kw => `"${kw}"`).join(' ')}` }
			query.$or.push(textSearch)
		}

		// if (textSearch.$search) query.$text = textSearch
		if (and.length) {
			if (query.$or) query.$or.push({ $and: and })
			else query.$and = and
		}

		return query
	}

	function parseQuery(query = '') {
		const words = query.trim().split(' '),
			tokens = {}

		for (const w of words) {
			const word = w.trim()
			if (!word) continue

			const type = dataType(word)
			tokens[type] = tokens[type] || []
			let data = word
			if (type === 'date') data = new Date(word + ' UTC')			// FIXME
			if (type === 'cardNumber') data = word.toUpperCase()
			tokens[type].push(data)
		}
		return tokens
	}

	function dataType(word) {
		if (isEmail(word)) return 'email'
		else if (isDate(word, { format: 'D/M/YYYY' })) return 'date'
		else if (isDate(word, { format: 'D/MM/YYYY' })) return 'date'
		else if (isDate(word, { format: 'DD/M/YYYY' })) return 'date'
		else if (isDate(word, { format: 'DD/MM/YYYY' })) return 'date'
		else if (isDate(word, { format: 'M/D/YYYY' })) return 'date'
		else if (isDate(word, { format: 'M/DD/YYYY' })) return 'date'
		else if (isDate(word, { format: 'MM/D/YYYY' })) return 'date'
		else if (isDate(word, { format: 'MM/DD/YYYY' })) return 'date'
		else if (isDate(word, { format: 'YYYY/M/D' })) return 'date'
		else if (isDate(word, { format: 'YYYY/MM/D' })) return 'date'
		else if (isDate(word, { format: 'YYYY/M/DD' })) return 'date'
		else if (isDate(word, { format: 'YYYY/MM/DD' })) return 'date'
		else if (isMobilePhone(word)) return 'mobile'
		else if (isNumeric(word)) return 'number'
		else if (isEAN(word)) return 'barcode'
		else if (isISBN(word)) return 'barcode'
		else if (isMongoId(word)) return 'mongoId'
		else if (/^[A-Za-z]+\d+$/.test(word)) return 'cardNumber'
		else if (isAlphanumeric(word)) return 'externalId'
		return 'text'
	}

	// -----  Remote Methods  -----

	Person.remoteMethod('search', {
		description: 'Search persons matched by text string from the data source.',
		http: { path: '/search', verb: 'get' },
		accepts: [
			{ arg: 'textString', type: 'string', required: true },
			{ arg: 'options', type: 'object' },
		],
		returns: { type: 'any', root: true },
	})
}
