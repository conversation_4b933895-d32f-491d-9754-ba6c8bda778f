{"name": "Provider", "plural": "Providers", "base": "PersistedModel", "idInjection": true, "strict": false, "mixins": {"MultitenantRemote": true, "DisableAllRemotes": {"find": true, "findOne": true, "prototype.patchAttributes": true}}, "properties": {"id": {"type": "string", "id": true, "generated": true}}, "hidden": [], "validations": [], "relations": {}, "acls": [], "scopes": {}, "methods": {"getByName": {"description": "Get providers (including credentials) by name", "http": {"path": "/name", "verb": "get"}, "accepts": [{"arg": "name", "type": "string", "required": true}], "returns": {"type": ["Provider"], "root": true}}, "getByService": {"description": "Get providers (including credentials) by service name", "http": {"path": "/service", "verb": "get"}, "accepts": [{"arg": "service", "type": "string", "required": true}], "returns": {"type": "object", "root": true}}}}