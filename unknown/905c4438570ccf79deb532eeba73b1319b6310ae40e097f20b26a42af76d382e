{"accountregistered": {"event": "xaccount.account.registered", "collection": "account", "property": "id", "model": "TimeseriesValue", "timestamp": "registeredAt", "dimensions": {"country": {"property": "mobileCountryCode"}, "app": {"property": "install.app.version"}, "os": {"property": "install.os.name"}}}, "accountverified": {"event": "xaccount.account.verified", "collection": "account", "property": "id", "model": "TimeseriesValue", "timestamp": "registeredAt", "dimensions": {"country": {"property": "mobileCountryCode"}, "app": {"property": "install.app.version"}, "os": {"property": "install.os.name"}}}, "accountdeleted": {"event": "xaccount.account.deleted", "collection": "account", "property": "id", "model": "TimeseriesValue", "timestamp": "registeredAt", "dimensions": {"country": {"property": "mobileCountryCode"}}}, "accountlaunched": {"event": "xaccount.account.launched", "collection": "account", "property": "id", "dimensions": {"country": {"property": "install.locale.country"}, "app": {"property": "install.app.version"}, "os": {"property": "install.os.name"}}}, "accountlogin": {"event": "xaccount.account.login", "collection": "account", "property": "id", "dimensions": {"country": {"property": "install.locale.country"}, "app": {"property": "install.app.version"}, "os": {"property": "install.os.name"}}}, "activemembers": {"event": "sales.order.paid", "model": "TimeseriesValue", "property": "memberId", "timestamp": "when.paid", "dimensions": {"program": {"property": "program.id"}, "tier": {"property": "program.tierLevel"}, "location": {"property": ["acquiredThrough.location.id", "acquired.location.id"]}, "context": {"property": "acquired.attributedTo.context.key", "default": "default"}}}, "cardrecommended": {"event": "card.card.issued", "collection": "card", "property": "id", "dimensions": {"campaign": {"property": "acquired.attributedTo.id"}, "master": {"property": "masterId"}}}, "cardrecommendaccepted": {"event": "card.card.accepted", "collection": "card", "property": "id", "dimensions": {"campaign": {"property": "acquired.attributedTo.id"}, "master": {"property": "masterId"}}}, "cardrecommenddeclined": {"event": "card.card.declined", "collection": "card", "property": "id", "dimensions": {"campaign": {"property": "acquired.attributedTo.id"}, "master": {"property": "masterId"}}}, "cardrecommendregistered": {"event": "card.card.registered", "collection": "card", "property": "id", "dimensions": {"campaign": {"property": "acquired.attributedTo.id"}, "master": {"property": "masterId"}}}, "cpmembershipnewjoin": {"event": "campaign.membership.join", "collection": "membership", "property": "memberId", "timestamp": "startTime", "dimensions": {"program": {"property": "programId"}, "tier": {"property": "tierLevel"}, "channel": {"property": "acquired.channel"}, "channelValue": {"property": "acquired.channelId"}, "isLocal": {"property": "person.isLocal"}}}, "cpmembershiprenew": {"event": "campaign.membership.renew", "collection": "membership", "property": "memberId", "timestamp": "startTime", "dimensions": {"program": {"property": "programId"}, "tier": {"property": "tierLevel"}, "isLocal": {"property": "person.isLocal"}}}, "cpmembershipregistered": {"event": "campaign.membership.register", "collection": "membership", "property": "memberId", "timestamp": "startTime", "dimensions": {"program": {"property": "programId"}, "tier": {"property": "tierLevel"}, "channel": {"property": "acquired.channel"}, "channelValue": {"property": "acquired.channelId"}, "isLocal": {"property": "person.isLocal"}}}, "cpsalecustomers": {"event": "sales.order.paid", "model": "TimeseriesValue", "property": "personId", "dimensions": {"campaign": {"property": ["tag.campaignId", "track.campaignId"]}}}, "cpsalerevenue": {"event": "campaign.order.created", "collection": "order", "property": "amount", "dimensions": {"touchpoint": {"property": ["acquiredThrough.type", "acquired.type"]}, "touchId": {"property": ["acquiredThrough.id", "acquired.id"]}, "attribute": {"property": ["acquiredThrough.attributedTo.type", "acquired.attributedTo.type"]}, "attributeId": {"property": ["acquiredThrough.attributedTo.id", "acquired.attributedTo.id"]}, "location": {"property": ["acquiredThrough.location.id", "acquired.location.id"]}, "firstPurchase": {"property": "firstPurchase", "asBoolean": ["notfirstpurchase", "firstpurchase"]}, "campaign": {"property": ["tag.campaignId", "track.campaignId"]}}}, "cpsalevisits": {"event": "campaign.order.created", "collection": "order", "property": "id", "dimensions": {"touchpoint": {"property": ["acquiredThrough.type", "acquired.type"]}, "touchId": {"property": ["acquiredThrough.id", "acquired.id"]}, "attribute": {"property": ["acquiredThrough.attributedTo.type", "acquired.attributedTo.type"]}, "attributeId": {"property": ["acquiredThrough.attributedTo.id", "acquired.attributedTo.id"]}, "location": {"property": ["acquiredThrough.location.id", "acquired.location.id"]}, "firstPurchase": {"property": "firstPurchase", "asBoolean": ["notfirstpurchase", "firstpurchase"]}, "campaign": {"property": ["tag.campaignId", "track.campaignId"]}}}, "cpsaleunits": {"event": "campaign.order.created", "collection": "order", "property": "quantity", "dimensions": {"touchpoint": {"property": ["acquiredThrough.type", "acquired.type"]}, "touchId": {"property": ["acquiredThrough.id", "acquired.id"]}, "attribute": {"property": ["acquiredThrough.attributedTo.type", "acquired.attributedTo.type"]}, "attributeId": {"property": ["acquiredThrough.attributedTo.id", "acquired.attributedTo.id"]}, "location": {"property": ["acquiredThrough.location.id", "acquired.location.id"]}, "firstPurchase": {"property": "firstPurchase", "asBoolean": ["notfirstpurchase", "firstpurchase"]}, "campaign": {"property": ["tag.campaignId", "track.campaignId"]}}}, "cpordercreated": {"event": "sales.order.created.collect", "collection": "order", "property": "id", "dimensions": {"touchpoint": {"property": ["acquiredThrough.type", "acquired.type"]}, "touchId": {"property": ["acquiredThrough.id", "acquired.id"]}, "location": {"property": ["acquiredThrough.location.id", "acquired.location.id"]}, "campaign": {"property": "external.campaignId"}}}, "cporderaccepted": {"event": "sales.order.accepted.collect", "collection": "order", "property": "id", "dimensions": {"touchpoint": {"property": ["acquiredThrough.type", "acquired.type"]}, "touchId": {"property": ["acquiredThrough.id", "acquired.id"]}, "location": {"property": ["acquiredThrough.location.id", "acquired.location.id"]}, "campaign": {"property": "external.campaignId"}}}, "cporderdeclined": {"event": "sales.order.declined.collect", "collection": "order", "property": "id", "dimensions": {"touchpoint": {"property": ["acquiredThrough.type", "acquired.type"]}, "touchId": {"property": ["acquiredThrough.id", "acquired.id"]}, "location": {"property": ["acquiredThrough.location.id", "acquired.location.id"]}, "campaign": {"property": "external.campaignId"}}}, "cpordercancelled": {"event": "sales.order.cancelled.collect", "collection": "order", "property": "id", "dimensions": {"touchpoint": {"property": ["acquiredThrough.type", "acquired.type"]}, "touchId": {"property": ["acquiredThrough.id", "acquired.id"]}, "location": {"property": ["acquiredThrough.location.id", "acquired.location.id"]}, "campaign": {"property": "external.campaignId"}}}, "cporderpacked": {"event": "sales.order.packed.collect", "collection": "order", "property": "id", "dimensions": {"touchpoint": {"property": ["acquiredThrough.type", "acquired.type"]}, "touchId": {"property": ["acquiredThrough.id", "acquired.id"]}, "location": {"property": ["acquiredThrough.location.id", "acquired.location.id"]}, "campaign": {"property": "external.campaignId"}}}, "cpordercollected": {"event": "sales.order.collected.collect", "collection": "order", "property": "id", "dimensions": {"touchpoint": {"property": ["acquiredThrough.type", "acquired.type"]}, "touchId": {"property": ["acquiredThrough.id", "acquired.id"]}, "location": {"property": ["acquiredThrough.location.id", "acquired.location.id"]}, "campaign": {"property": "external.campaignId"}}}, "emailsent": {"event": "message.email.sent", "collection": "email", "property": "count", "dimensions": {"provider": {"property": "provider.name"}, "campaign": {"property": ["tag.campaignId", "track.campaignId"]}, "fee": {"property": "billing.fee.rate"}, "fee-tag": {"property": "billing.fee.tag"}}}, "emailsenterror": {"event": "message.email.sent.error", "collection": "email", "property": "count", "dimensions": {"provider": {"property": "provider.name"}, "campaign": {"property": ["tag.campaignId", "track.campaignId"]}, "error": {"property": "error.code"}}}, "emailfailed": {"event": "campaign.email.status.failed", "collection": "email", "property": "count", "dimensions": {"provider": {"property": "provider.name"}, "campaign": {"property": ["tag.campaignId", "track.campaignId"]}, "error": {"property": "error.code"}}}, "emaildelivered": {"event": "campaign.email.status.delivered", "collection": "email", "property": "count", "dimensions": {"provider": {"property": "provider.name"}, "campaign": {"property": ["tag.campaignId", "track.campaignId"]}}}, "emailopened": {"event": "campaign.email.status.opened", "collection": "email", "property": "count", "dimensions": {"campaign": {"property": ["tag.campaignId", "track.campaignId"]}}}, "emailclickthru": {"event": "campaign.email.status.clickthru", "collection": "email", "property": "count", "dimensions": {"campaign": {"property": ["tag.campaignId", "track.campaignId"]}}}, "membercreated": {"event": "membership.member.created", "collection": "member"}, "membershipjoined": {"events": {"membership.membership.joined": {"multiples": 1}, "membership.membership.recovered": {"multiples": 1}, "membership.membership.cancelled": {"multiples": -1}, "membership.membership.terminated": {"multiples": -1}}, "collection": "membership", "property": "membershipId", "timestamp": "startTime", "dimensions": {"program": {"property": "programId"}, "tier": {"property": "tierLevel"}, "touchpoint": {"property": ["acquiredThrough.type", "acquired.type"]}, "touchId": {"property": ["acquiredThrough.id", "acquired.id"]}, "attribute": {"property": ["acquiredThrough.attributedTo.type", "acquired.attributedTo.type"]}, "attributeId": {"property": ["acquiredThrough.attributedTo.id", "acquired.attributedTo.id"]}, "attributeName": {"property": ["acquiredThrough.attributedTo.name", "acquired.attributedTo.name"]}, "locationType": {"property": ["acquiredThrough.location.type", "acquired.location.type"]}, "location": {"property": ["acquiredThrough.location.id", "acquired.location.id"]}, "context": {"property": "acquired.attributedTo.context.key", "default": "default"}, "campaign": {"property": ["tag.campaignId", "track.campaignId"]}, "residence": {"property": "person.residence"}, "fee": {"property": "context.billing.fee.rate"}, "fee-tag": {"property": "context.billing.fee.tag"}}}, "membershipcancelled": {"event": "membership.membership.cancelled", "collection": "membership", "property": "membershipId", "timestamp": "startTime", "dimensions": {"program": {"property": "programId"}, "tier": {"property": "tierLevel"}, "touchpoint": {"property": ["acquiredThrough.type", "acquired.type"]}, "touchId": {"property": ["acquiredThrough.id", "acquired.id"]}, "attribute": {"property": ["acquiredThrough.attributedTo.type", "acquired.attributedTo.type"]}, "attributeId": {"property": ["acquiredThrough.attributedTo.id", "acquired.attributedTo.id"]}, "attributeName": {"property": ["acquiredThrough.attributedTo.name", "acquired.attributedTo.name"]}, "locationType": {"property": ["acquiredThrough.location.type", "acquired.location.type"]}, "location": {"property": ["acquiredThrough.location.id", "acquired.location.id"]}, "context": {"property": "acquired.attributedTo.context.key", "default": "default"}, "campaign": {"property": ["tag.campaignId", "track.campaignId"]}, "residence": {"property": "person.residence"}}}, "membershiprecovered": {"event": "membership.membership.recovered", "collection": "membership", "property": "membershipId", "timestamp": "startTime", "dimensions": {"program": {"property": "programId"}, "tier": {"property": "tierLevel"}, "touchpoint": {"property": ["acquiredThrough.type", "acquired.type"]}, "touchId": {"property": ["acquiredThrough.id", "acquired.id"]}, "attribute": {"property": ["acquiredThrough.attributedTo.type", "acquired.attributedTo.type"]}, "attributeId": {"property": ["acquiredThrough.attributedTo.id", "acquired.attributedTo.id"]}, "attributeName": {"property": ["acquiredThrough.attributedTo.name", "acquired.attributedTo.name"]}, "location": {"property": ["acquiredThrough.location.id", "acquired.location.id"]}, "context": {"property": "acquired.attributedTo.context.key", "default": "default"}, "campaign": {"property": ["tag.campaignId", "track.campaignId"]}}}, "membershipregistered": {"events": {"membership.card.registered": {"multiples": 1}, "membership.membership.recovered": {"multiples": 1, "filter": {"digitalCard_registeredAt": {"neq": null}}}, "membership.membership.cancelled": {"multiples": -1, "filter": {"digitalCard_registeredAt": {"neq": null}}}, "membership.membership.terminated": {"multiples": -1, "filter": {"digitalCard_registeredAt": {"neq": null}}}}, "collection": "membership", "property": "membershipId", "timestamp": "startTime", "dimensions": {"program": {"property": "programId"}, "tier": {"property": "tierLevel"}, "touchpoint": {"property": ["acquiredThrough.type", "acquired.type"]}, "touchId": {"property": ["acquiredThrough.id", "acquired.id"]}, "attribute": {"property": ["acquiredThrough.attributedTo.type", "acquired.attributedTo.type"]}, "attributeId": {"property": ["acquiredThrough.attributedTo.id", "acquired.attributedTo.id"]}, "attributeName": {"property": ["acquiredThrough.attributedTo.name", "acquired.attributedTo.name"]}, "locationType": {"property": ["acquiredThrough.location.type", "acquired.location.type"]}, "location": {"property": ["acquiredThrough.location.id", "acquired.location.id"]}, "context": {"property": "acquired.attributedTo.context.key", "default": "default"}, "campaign": {"property": ["tag.campaignId", "track.campaignId"]}, "residence": {"property": "person.residence"}}}, "membershipregistercancelled": {"event": "membership.membership.registercancelled", "collection": "membership", "property": "membershipId", "timestamp": "startTime", "dimensions": {"program": {"property": "programId"}, "tier": {"property": "tierLevel"}, "touchpoint": {"property": ["acquiredThrough.type", "acquired.type"]}, "touchId": {"property": ["acquiredThrough.id", "acquired.id"]}, "attribute": {"property": ["acquiredThrough.attributedTo.type", "acquired.attributedTo.type"]}, "attributeId": {"property": ["acquiredThrough.attributedTo.id", "acquired.attributedTo.id"]}, "attributeName": {"property": ["acquiredThrough.attributedTo.name", "acquired.attributedTo.name"]}, "locationType": {"property": ["acquiredThrough.location.type", "acquired.location.type"]}, "location": {"property": ["acquiredThrough.location.id", "acquired.location.id"]}, "context": {"property": "acquired.attributedTo.context.key", "default": "default"}, "campaign": {"property": ["tag.campaignId", "track.campaignId"]}, "residence": {"property": "person.residence"}}}, "membershiprenewed": {"event": "membership.membership.renewed", "collection": "membership", "property": "membershipId", "timestamp": "startTime", "dimensions": {"program": {"property": "programId"}, "tier": {"property": "tierLevel"}, "touchpoint": {"property": ["acquiredThrough.type", "acquired.type"]}, "touchId": {"property": ["acquiredThrough.id", "acquired.id"]}, "attribute": {"property": ["acquiredThrough.attributedTo.type", "acquired.attributedTo.type"]}, "attributeId": {"property": ["acquiredThrough.attributedTo.id", "acquired.attributedTo.id"]}, "attributeName": {"property": ["acquiredThrough.attributedTo.name", "acquired.attributedTo.name"]}, "location": {"property": ["acquiredThrough.location.id", "acquired.location.id"]}, "context": {"property": "acquired.attributedTo.context.key", "default": "default"}, "campaign": {"property": ["tag.campaignId", "track.campaignId"]}, "fee": {"property": "context.billing.fee.rate"}, "fee-tag": {"property": "context.billing.fee.tag"}}}, "membershipupgraded": {"event": "membership.membership.upgraded", "collection": "membership", "property": "membershipId", "timestamp": "startTime", "dimensions": {"program": {"property": "programId"}, "tier": {"property": "tierLevel"}, "touchpoint": {"property": ["acquiredThrough.type", "acquired.type"]}, "touchId": {"property": ["acquiredThrough.id", "acquired.id"]}, "attribute": {"property": ["acquiredThrough.attributedTo.type", "acquired.attributedTo.type"]}, "attributeId": {"property": ["acquiredThrough.attributedTo.id", "acquired.attributedTo.id"]}, "attributeName": {"property": ["acquiredThrough.attributedTo.name", "acquired.attributedTo.name"]}, "location": {"property": ["acquiredThrough.location.id", "acquired.location.id"]}, "context": {"property": "acquired.attributedTo.context.key", "default": "default"}, "campaign": {"property": ["tag.campaignId", "track.campaignId"]}, "fee": {"property": "context.billing.fee.rate"}, "fee-tag": {"property": "context.billing.fee.tag"}}}, "membershipdowngraded": {"event": "membership.membership.downgraded", "collection": "membership", "property": "membershipId", "timestamp": "startTime", "dimensions": {"program": {"property": "programId"}, "tier": {"property": "tierLevel"}, "touchpoint": {"property": ["acquiredThrough.type", "acquired.type"]}, "touchId": {"property": ["acquiredThrough.id", "acquired.id"]}, "attribute": {"property": ["acquiredThrough.attributedTo.type", "acquired.attributedTo.type"]}, "attributeId": {"property": ["acquiredThrough.attributedTo.id", "acquired.attributedTo.id"]}, "attributeName": {"property": ["acquiredThrough.attributedTo.name", "acquired.attributedTo.name"]}, "location": {"property": ["acquiredThrough.location.id", "acquired.location.id"]}, "context": {"property": "acquired.attributedTo.context.key", "default": "default"}, "campaign": {"property": ["tag.campaignId", "track.campaignId"]}, "fee": {"property": "context.billing.fee.rate"}, "fee-tag": {"property": "context.billing.fee.tag"}}}, "membershipextended": {"event": "membership.membership.extended", "collection": "membership", "property": "membershipId", "timestamp": "startTime", "dimensions": {"program": {"property": "programId"}, "tier": {"property": "tierLevel"}, "touchpoint": {"property": ["acquiredThrough.type", "acquired.type"]}, "touchId": {"property": ["acquiredThrough.id", "acquired.id"]}, "attribute": {"property": ["acquiredThrough.attributedTo.type", "acquired.attributedTo.type"]}, "attributeId": {"property": ["acquiredThrough.attributedTo.id", "acquired.attributedTo.id"]}, "attributeName": {"property": ["acquiredThrough.attributedTo.name", "acquired.attributedTo.name"]}, "location": {"property": ["acquiredThrough.location.id", "acquired.location.id"]}, "context": {"property": "acquired.attributedTo.context.key", "default": "default"}, "campaign": {"property": ["tag.campaignId", "track.campaignId"]}}}, "membershipterminated": {"event": "membership.membership.terminated", "collection": "membership", "property": "membershipId", "timestamp": "startTime", "dimensions": {"program": {"property": "programId"}, "tier": {"property": "tierLevel"}, "touchpoint": {"property": ["acquiredThrough.type", "acquired.type"]}, "touchId": {"property": ["acquiredThrough.id", "acquired.id"]}, "attribute": {"property": ["acquiredThrough.attributedTo.type", "acquired.attributedTo.type"]}, "attributeId": {"property": ["acquiredThrough.attributedTo.id", "acquired.attributedTo.id"]}, "attributeName": {"property": ["acquiredThrough.attributedTo.name", "acquired.attributedTo.name"]}, "location": {"property": ["acquiredThrough.location.id", "acquired.location.id"]}, "context": {"property": "acquired.attributedTo.context.key", "default": "default"}, "campaign": {"property": ["tag.campaignId", "track.campaignId"]}}}, "membershipcarddeleted": {"event": "membership.card.deleted", "collection": "membership", "property": "membershipId", "timestamp": "startTime", "dimensions": {"program": {"property": "programId"}, "tier": {"property": "tierLevel"}, "touchpoint": {"property": ["acquiredThrough.type", "acquired.type"]}, "touchId": {"property": ["acquiredThrough.id", "acquired.id"]}, "attribute": {"property": ["acquiredThrough.attributedTo.type", "acquired.attributedTo.type"]}, "attributeId": {"property": ["acquiredThrough.attributedTo.id", "acquired.attributedTo.id"]}, "attributeName": {"property": ["acquiredThrough.attributedTo.name", "acquired.attributedTo.name"]}, "location": {"property": ["acquiredThrough.location.id", "acquired.location.id"]}, "context": {"property": "acquired.attributedTo.context.key", "default": "default"}, "campaign": {"property": ["tag.campaignId", "track.campaignId"]}}}, "membershipcheckin": {"event": "membership.membership.checkin", "model": "TimeseriesValue", "property": "cardNumber", "timestamp": "checkInTime", "dimensions": {"program": {"property": "programId"}, "tier": {"property": "tierLevel"}, "touchpoint": {"property": ["acquiredThrough.type", "acquired.type"]}, "touchId": {"property": ["acquiredThrough.id", "acquired.id"]}, "attribute": {"property": ["acquiredThrough.attributedTo.type", "acquired.attributedTo.type"]}, "attributeId": {"property": ["acquiredThrough.attributedTo.id", "acquired.attributedTo.id"]}, "attributeName": {"property": ["acquiredThrough.attributedTo.name", "acquired.attributedTo.name"]}, "location": {"property": ["acquiredThrough.location.id", "acquired.location.id"]}, "campaign": {"property": ["tag.campaignId", "track.campaignId"]}, "context": {"property": "acquired.attributedTo.context.key", "default": "default"}, "digital": {"property": "cardRegisteredAt", "asBoolean": ["nondigital", "digital"]}}}, "membershipcheckinregistered": {"event": "membership.membership.checkinregistered", "collection": "membership", "property": "membershipId", "timestamp": "checkInTime", "dimensions": {"program": {"property": "programId"}, "tier": {"property": "tierLevel"}, "touchpoint": {"property": ["acquiredThrough.type", "acquired.type"]}, "touchId": {"property": ["acquiredThrough.id", "acquired.id"]}, "attribute": {"property": ["acquiredThrough.attributedTo.type", "acquired.attributedTo.type"]}, "attributeId": {"property": ["acquiredThrough.attributedTo.id", "acquired.attributedTo.id"]}, "attributeName": {"property": ["acquiredThrough.attributedTo.name", "acquired.attributedTo.name"]}, "location": {"property": ["acquiredThrough.location.id", "acquired.location.id"]}, "context": {"property": "acquired.attributedTo.context.key", "default": "default"}, "campaign": {"property": ["tag.campaignId", "track.campaignId"]}}}, "offerissued": {"event": "offer.offer.issued", "collection": "offer", "property": "id", "timestamp": "when.issued", "dimensions": {"campaign": {"property": "campaignId"}, "fee": {"property": "context.billing.fee.rate"}, "fee-tag": {"property": "context.billing.fee.tag"}, "master": {"property": "masterId"}}}, "offerredeemed": {"event": "offer.offer.redeemed", "collection": "offer", "property": "id", "timestamp": "when.redeemed", "dimensions": {"campaign": {"property": "campaignId"}, "master": {"property": "masterId"}}}, "offerdeleted": {"event": "offer.offer.deleted", "collection": "offer", "property": "quantity", "timestamp": "deletedAt", "dimensions": {"campaign": {"property": "campaignId"}, "master": {"property": "masterId"}}}, "offercancelled": {"event": "offer.offer.cancelled", "collection": "offer", "property": "id", "timestamp": "when.cancelled", "dimensions": {"campaign": {"property": "campaignId"}, "master": {"property": "masterId"}}}, "offerviewed": {"event": "offer.offer.viewed", "collection": "offer", "property": "id", "dimensions": {"campaign": {"property": "campaignId"}, "master": {"property": "masterId"}}}, "offerfirstviewed": {"event": "offer.offer.viewed.first", "collection": "offer", "property": "id", "dimensions": {"campaign": {"property": "campaignId"}, "master": {"property": "masterId"}}}, "offerextended": {"event": "offer.offer.extended", "collection": "offer", "property": "id", "dimensions": {"campaign": {"property": "campaignId"}, "master": {"property": "masterId"}}}, "personcreated": {"event": "person.person.created", "collection": "person", "dimensions": {"touchpoint": {"property": ["acquiredThrough.type", "acquired.type"]}, "touchId": {"property": ["acquiredThrough.id", "acquired.id"]}, "attribute": {"property": ["acquiredThrough.attributedTo.type", "acquired.attributedTo.type"]}, "attributeId": {"property": ["acquiredThrough.attributedTo.id", "acquired.attributedTo.id"]}, "attributeName": {"property": ["acquiredThrough.attributedTo.name", "acquired.attributedTo.name"]}, "location": {"property": ["acquiredThrough.location.id", "acquired.location.id"]}, "context": {"property": "acquired.attributedTo.context.key", "default": "default"}}}, "pushsent": {"event": "message.push.sent", "collection": "push", "property": "count", "dimensions": {"provider": {"property": "provider.name"}, "campaign": {"property": "track.campaignId"}, "fee": {"property": "billing.fee.rate"}, "fee-tag": {"property": "billing.fee.tag"}}}, "pushsenterror": {"event": "message.push.sent.error", "collection": "push", "property": "count", "dimensions": {"provider": {"property": "provider.name"}, "campaign": {"property": "track.campaignId"}, "error": {"property": "error.code"}}}, "pushdelivered": {"event": "message.push.status.delivered", "collection": "push", "property": "count", "dimensions": {"provider": {"property": "provider.name"}, "campaign": {"property": "track.campaignId"}}}, "pushfailed": {"event": "message.push.status.failed", "collection": "push", "property": "count", "dimensions": {"provider": {"property": "provider.name"}, "campaign": {"property": "track.campaignId"}}}, "pushinvalidtoken": {"event": "message.push.invalidtoken", "collection": "push", "property": "count", "dimensions": {"provider": {"property": "provider.name"}, "campaign": {"property": "track.campaignId"}, "error": {"property": "error.code"}}}, "pushuninstalled": {"event": "message.push.uninstalled", "collection": "push", "property": "count", "dimensions": {"provider": {"property": "provider.name"}, "campaign": {"property": "track.campaignId"}, "error": {"property": "error.code"}}}, "rewardsetcompleted": {"event": "reward.reward.completed", "collection": "reward", "property": "id", "dimensions": {"master": {"property": "masterId"}}}, "rewardsetnetcompleted": {"events": {"reward.reward.completed": {"multiples": 1}, "reward.reward.completed.reverted": {"multiples": -1}}, "collection": "reward", "property": "id", "dimensions": {"master": {"property": "masterId"}}}, "rewardsetcancelcompleted": {"event": "reward.reward.completed.reverted", "collection": "reward", "property": "id", "dimensions": {"master": {"property": "masterId"}}}, "rewardlevelcompleted": {"event": "reward.reward.level.completed", "collection": "reward", "property": "id", "dimensions": {"master": {"property": "masterId"}, "level": {"property": "level"}}}, "rewardlevelnetcompleted": {"events": {"reward.reward.level.completed": {"multiples": 1}, "reward.reward.level.completed.reverted": {"multiples": -1}}, "collection": "reward", "property": "id", "dimensions": {"master": {"property": "masterId"}, "level": {"property": "level"}}}, "rewardlevelcancelcompleted": {"event": "reward.reward.level.completed.reverted", "collection": "reward", "property": "id", "dimensions": {"master": {"property": "masterId"}, "level": {"property": "level"}}}, "richsent": {"event": "message.rich.sent", "collection": "rich", "property": "count", "dimensions": {"provider": {"property": "provider"}, "campaign": {"property": "track.campaignId"}, "fee": {"property": "billing.fee.rate"}, "fee-tag": {"property": "billing.fee.tag"}}}, "richsenterror": {"event": "message.rich.sent.error", "collection": "rich", "property": "count", "dimensions": {"provider": {"property": "provider"}, "campaign": {"property": "track.campaignId"}}}, "richfailed": {"event": "message.rich.sent.error", "collection": "rich", "property": "count", "dimensions": {"provider": {"property": "provider.name"}, "campaign": {"property": "track.campaignId"}}}, "richdelivered": {"event": "perkd.message.received", "collection": "rich", "property": "count", "dimensions": {"provider": {"property": "provider.name"}, "campaign": {"property": "track.campaignId"}}}, "richviewed": {"event": "perkd.message.read", "collection": "rich", "property": "count", "dimensions": {"campaign": {"property": "track.campaignId"}}}, "xrichdelivered": {"event": "rich.message.received", "collection": "rich", "property": "count", "dimensions": {"provider": {"property": "provider.name"}, "campaign": {"property": "track.campaignId"}}}, "xrichviewed": {"event": "rich.message.read", "collection": "rich", "property": "count", "dimensions": {"campaign": {"property": "track.campaignId"}}}, "richclickthru": {"event": "message.rich.clickthru", "collection": "rich", "property": "count", "dimensions": {"campaign": {"property": "track.campaignId"}, "link": {"property": "clickedUrl"}}}, "rsvpsuccess": {"event": "campaign.rsvp.success", "collection": "rsvp", "property": "quantity", "dimensions": {"slot": {"property": "slot"}, "campaign": {"property": ["tag.campaignId", "track.campaignId"]}}}, "rsvpfailed": {"event": "campaign.rsvp.failed", "collection": "rsvp", "property": "quantity", "dimensions": {"slot": {"property": "slot"}, "campaign": {"property": ["tag.campaignId", "track.campaignId"]}}}, "salerevenue": {"event": "sales.order.paid", "collection": "order", "property": "amount", "timestamp": "when.paid", "dimensions": {"touchpoint": {"property": ["acquiredThrough.type", "acquired.type"]}, "touchId": {"property": ["acquiredThrough.id", "acquired.id"]}, "attribute": {"property": ["acquiredThrough.attributedTo.type", "acquired.attributedTo.type"]}, "attributeId": {"property": ["acquiredThrough.attributedTo.id", "acquired.attributedTo.id"]}, "location": {"property": ["acquiredThrough.location.id", "acquired.location.id"]}, "firstPurchase": {"property": "context.firstPurchase", "asBoolean": ["notfirstpurchase", "firstpurchase"]}, "campaign": {"property": ["tag.campaignId", "track.campaignId"]}}}, "salevisits": {"event": "sales.order.paid", "collection": "order", "property": "id", "timestamp": "when.paid", "dimensions": {"touchpoint": {"property": ["acquiredThrough.type", "acquired.type"]}, "touchId": {"property": ["acquiredThrough.id", "acquired.id"]}, "attribute": {"property": ["acquiredThrough.attributedTo.type", "acquired.attributedTo.type"]}, "attributeId": {"property": ["acquiredThrough.attributedTo.id", "acquired.attributedTo.id"]}, "location": {"property": ["acquiredThrough.location.id", "acquired.location.id"]}, "firstPurchase": {"property": "context.firstPurchase", "asBoolean": ["notfirstpurchase", "firstpurchase"]}, "campaign": {"property": ["tag.campaignId", "track.campaignId"]}}}, "saleunits": {"event": "sales.order.paid", "collection": "order", "property": "quantity", "timestamp": "when.paid", "dimensions": {"touchpoint": {"property": ["acquiredThrough.type", "acquired.type"]}, "touchId": {"property": ["acquiredThrough.id", "acquired.id"]}, "attribute": {"property": ["acquiredThrough.attributedTo.type", "acquired.attributedTo.type"]}, "attributeId": {"property": ["acquiredThrough.attributedTo.id", "acquired.attributedTo.id"]}, "location": {"property": ["acquiredThrough.location.id", "acquired.location.id"]}, "firstPurchase": {"property": "context.firstPurchase", "asBoolean": ["notfirstpurchase", "firstpurchase"]}, "campaign": {"property": ["tag.campaignId", "track.campaignId"]}}}, "saleshoppers": {"event": "sales.order.paid", "model": "TimeseriesValue", "property": "personId", "timestamp": "when.paid", "dimensions": {"touchpoint": {"property": ["acquiredThrough.type", "acquired.type"]}, "touchId": {"property": ["acquiredThrough.id", "acquired.id"]}, "attribute": {"property": ["acquiredThrough.attributedTo.type", "acquired.attributedTo.type"]}, "attributeId": {"property": ["acquiredThrough.attributedTo.id", "acquired.attributedTo.id"]}, "location": {"property": ["acquiredThrough.location.id", "acquired.location.id"]}, "firstPurchase": {"property": "context.firstPurchase", "asBoolean": ["notfirstpurchase", "firstpurchase"]}, "campaign": {"property": ["tag.campaignId", "track.campaignId"]}}}, "salerevenuecancelled": {"event": "sales.order.cancelled", "collection": "order", "property": "amount", "timestamp": "when.paid", "dimensions": {"touchpoint": {"property": ["acquiredThrough.type", "acquired.type"]}, "touchId": {"property": ["acquiredThrough.id", "acquired.id"]}, "attribute": {"property": ["acquiredThrough.attributedTo.type", "acquired.attributedTo.type"]}, "attributeId": {"property": ["acquiredThrough.attributedTo.id", "acquired.attributedTo.id"]}, "location": {"property": ["acquiredThrough.location.id", "acquired.location.id"]}, "firstPurchase": {"property": "context.firstPurchase", "asBoolean": ["notfirstpurchase", "firstpurchase"]}, "campaign": {"property": ["tag.campaignId", "track.campaignId"]}}}, "salevisitscancelled": {"event": "sales.order.cancelled", "collection": "order", "property": "id", "timestamp": "when.paid", "dimensions": {"touchpoint": {"property": ["acquiredThrough.type", "acquired.type"]}, "touchId": {"property": ["acquiredThrough.id", "acquired.id"]}, "attribute": {"property": ["acquiredThrough.attributedTo.type", "acquired.attributedTo.type"]}, "attributeId": {"property": ["acquiredThrough.attributedTo.id", "acquired.attributedTo.id"]}, "location": {"property": ["acquiredThrough.location.id", "acquired.location.id"]}, "firstPurchase": {"property": "context.firstPurchase", "asBoolean": ["notfirstpurchase", "firstpurchase"]}, "campaign": {"property": ["tag.campaignId", "track.campaignId"]}}}, "saleunitscancelled": {"event": "sales.order.cancelled", "collection": "order", "property": "quantity", "timestamp": "when.paid", "dimensions": {"touchpoint": {"property": ["acquiredThrough.type", "acquired.type"]}, "touchId": {"property": ["acquiredThrough.id", "acquired.id"]}, "attribute": {"property": ["acquiredThrough.attributedTo.type", "acquired.attributedTo.type"]}, "attributeId": {"property": ["acquiredThrough.attributedTo.id", "acquired.attributedTo.id"]}, "location": {"property": ["acquiredThrough.location.id", "acquired.location.id"]}, "firstPurchase": {"property": "context.firstPurchase", "asBoolean": ["notfirstpurchase", "firstpurchase"]}, "campaign": {"property": ["tag.campaignId", "track.campaignId"]}}}, "saleshopperscancelled": {"event": "sales.order.cancelled", "model": "TimeseriesValue", "property": "personId", "timestamp": "when.paid", "dimensions": {"touchpoint": {"property": ["acquiredThrough.type", "acquired.type"]}, "touchId": {"property": ["acquiredThrough.id", "acquired.id"]}, "attribute": {"property": ["acquiredThrough.attributedTo.type", "acquired.attributedTo.type"]}, "attributeId": {"property": ["acquiredThrough.attributedTo.id", "acquired.attributedTo.id"]}, "location": {"property": ["acquiredThrough.location.id", "acquired.location.id"]}, "firstPurchase": {"property": "context.firstPurchase", "asBoolean": ["notfirstpurchase", "firstpurchase"]}, "campaign": {"property": ["tag.campaignId", "track.campaignId"]}}}, "stampissued": {"event": "reward.reward.stamp.issued", "collection": "reward", "property": "quantity", "dimensions": {"master": {"property": "masterId"}, "location": {"property": "through.location.id"}}}, "stampnetissued": {"events": {"reward.reward.stamp.issued": {"multiples": 1}, "reward.reward.stamp.deducted": {"multiples": -1}}, "collection": "reward", "property": "quantity", "dimensions": {"master": {"property": "masterId"}, "location": {"property": "through.location.id"}}}, "stampdeducted": {"event": "reward.reward.stamp.deducted", "collection": "reward", "property": "quantity", "dimensions": {"master": {"property": "masterId"}, "location": {"property": "through.location.id"}}}, "smssent": {"event": "message.sms.sent", "collection": "sms", "property": "count", "dimensions": {"provider": {"property": "provider.name"}, "country": {"property": "to.countryCode"}, "campaign": {"property": ["tag.campaignId", "track.campaignId"]}, "fee": {"property": "billing.fee.rate"}, "fee-tag": {"property": "billing.fee.tag"}}}, "smssenterror": {"event": "message.sms.sent.error", "collection": "sms", "property": "count", "dimensions": {"provider": {"property": "provider.name"}, "country": {"property": "to.countryCode"}, "campaign": {"property": ["tag.campaignId", "track.campaignId"]}, "error": {"property": "error.code"}}}, "smsdelivered": {"event": "campaign.sms.status.delivered", "collection": "sms", "property": "count", "dimensions": {"provider": {"property": "provider.name"}, "country": {"property": "to.countryCode"}, "campaign": {"property": ["tag.campaignId", "track.campaignId"]}}}, "smsfailed": {"event": "campaign.sms.status.failed", "collection": "sms", "property": "count", "dimensions": {"provider": {"property": "provider.name"}, "country": {"property": "to.countryCode"}, "campaign": {"property": ["tag.campaignId", "track.campaignId"]}, "error": {"property": "error.code"}}}}