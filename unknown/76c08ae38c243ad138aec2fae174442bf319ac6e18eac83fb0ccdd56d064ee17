{"name": "Service", "plural": "Services", "base": "PersistedModel", "description": "", "idInjection": false, "strict": false, "mixins": {"Timestamp": true, "DisableAllRemotes": true, "Queue": true}, "properties": {"name": {"type": "string", "id": true, "generated": false}, "version": {"type": "string"}, "description": {"type": "string"}, "tenants": {"type": "object"}, "appPath": {"type": "string"}, "settings": {"type": "object"}, "config": {"type": "object"}, "autoStart": {"type": "Boolean"}, "dependencies": {"type": "object"}, "state": {"type": "object"}, "properShutdown": {"type": "boolean"}, "lastHeartbeat": {"type": "date"}, "createdAt": {"type": "date"}, "modifiedAt": {"type": "date"}}, "validations": [], "relations": {}, "acls": [], "scopes": {}, "indexes": {}, "methods": {"ready": {"description": "Force service to READY state", "http": {"path": "/:id/ready", "verb": "post"}, "accepts": [{"arg": "id", "type": "string", "required": true}], "returns": {"arg": "status"}}, "start": {"description": "Start service", "http": {"path": "/:id/start", "verb": "post"}, "accepts": [{"arg": "id", "type": "string", "required": true}], "returns": {"arg": "status"}}, "stop": {"description": "Stop service", "http": {"path": "/:id/stop", "verb": "post"}, "accepts": [{"arg": "id", "type": "string", "required": true}], "returns": {"arg": "status"}}, "pause": {"description": "Pause service", "http": {"path": "/:id/pause", "verb": "get"}, "accepts": [{"arg": "id", "type": "string", "required": true}], "returns": {"arg": "status"}}, "resume": {"description": "Resume service", "http": {"path": "/:id/resume", "verb": "get"}, "accepts": [{"arg": "id", "type": "string", "required": true}], "returns": {"arg": "status"}}, "register": {"description": "Register a service", "http": {"path": "/:id/register", "verb": "put"}, "accepts": [{"arg": "id", "type": "string", "required": true}, {"arg": "data", "type": "object", "http": {"source": "body"}, "required": true}], "returns": {"arg": "dependencies"}}, "status": {"description": "Status of services", "http": {"path": "/status", "verb": "get"}, "returns": {"arg": "status"}}, "tenants": {"description": "Tenants in this service", "http": {"path": "/tenants", "verb": "get"}, "returns": {"arg": "tenants"}}, "events": {"description": "Events subscribed within this service", "http": {"path": "/events", "verb": "get"}, "accepts": [{"arg": "tenantCode", "type": "string"}, {"arg": "eventName", "type": "string"}], "returns": {"type": "object", "root": true}}, "ping": {"description": "Ping a service", "http": {"path": "/ping", "verb": "get"}, "returns": {"type": "string", "root": true}}}, "http": {}}