/**
  *  @module idempotency 	middleware
  *
  *  Setup idempotency  in LB Context from request Header
  */
const { Context } = require('@perkd/multitenant-context'),
	{ Apis } = require('@crm/types')

const { IDEMPOTENCY_KEY } = Apis.Headers

module.exports = function() {

	return function injectIdempotency(req, res, next) {
		const idempotencyKey = req.headers[IDEMPOTENCY_KEY]

		if (idempotencyKey) {
			Context.idempotencyKey = idempotencyKey
		}
		next()
	}
}
