{"name": "Tier", "plural": "Tiers", "description": "", "base": "Model", "idInjection": true, "strict": false, "mixins": {"MultitenantRemote": true}, "properties": {"level": {"type": "number", "id": true}, "name": {"type": "string", "length": 32, "required": true, "unique": true}, "description": {"type": "string"}, "type": {"type": "string", "enum": ["free", "paid", "earned", "invited"], "description": "only required if program is 'mixed'"}, "tenure": {"type": {"join": {"type": {"start": "object", "end": "object"}, "default": null}, "renew": {"type": {"start": "object", "end": "object"}, "default": null}, "upgrade": {"type": {"start": "object", "end": "object"}, "default": null}, "downgrade": {"type": {"start": "object", "end": "object"}, "default": null}}, "default": {}, "description": "eg. { join: { start: { base: 'realtime' }, end: { duration: 'P354D' } } } (ISO 8601)"}, "qualifiers": {"type": "object", "default": {}, "description": "key = qualifier name, value = filter  eg. join: { purchase_times: { gt: 5 }}"}, "roles": {"type": ["string"], "description": "Staff with ANY of the roles qualifies for this tier"}, "toQualify": {"type": [{"type": {"name": {"type": "string"}, "tierLevel": {"type": "number"}, "kind": {"type": "string", "enum": ["amount", "transaction"]}, "value": {"type": "number", "description": "Spend 'amount' / 'transaction' required to qualify"}, "currency": {"type": "String", "max": 3}, "end": {"type": "object", "description": "The rule used to generate time to qualify by"}}}], "description": "Outstanding spend needed for each Qualifier", "default": []}, "digitalCard": {"type": {"masterId": {"type": "String", "description": "Cardmaster I<PERSON> of Perkd"}, "updateStartTime": {"type": {"join": {"type": "boolean", "description": "true => show current membership start time on card"}, "renew": {"type": "boolean", "description": "true => show current membership start time on card"}, "upgrade": {"type": "boolean", "description": "true => show current membership start time on card"}, "downgrade": {"type": "boolean", "description": "true => show current membership start time on card"}}}, "concatEndTime": {"type": "boolean", "description": "true => show last endTime of concatenated memberships on card, include pending membership"}, "discover": {"type": {"enabled": {"type": "boolean", "default": true, "description": "Whether to list in Card Library"}, "regions": {"type": [{"name": {"type": "String", "required": true}, "kind": {"type": "String", "default": "country", "enum": ["country", "state", "city"]}, "country": {"type": "String", "length": 2, "description": "ISO 3166-1 alpha-2"}}], "default": [], "description": "Discoverable in which regions in Card Library"}, "applet": {"type": "string", "description": "Applet key"}}}, "barcodeType": {"type": "string", "enum": ["NONE", "QRCODE", "CODE39", "ISBN10", "CODE128", "UPCA", "UPCE", "EAN13", "EAN8", "ITF", "DATABAR", "DATABAREXP", "ISBN13", "PDF417", "CODABAR", "AZTEC", "DATAMATRIX"]}, "numberFormat": {"type": "string", "max": 64, "description": "Format string for card number display"}, "forms": {"type": [{"name": {"type": "String", "max": 16, "required": true}, "schema": {"type": "Object", "description": "List of field schema, ref: http://schemaform.io/examples/bootstrap-example.html (Kitchen Sink Example)"}, "view": {"type": "Object", "description": "List of field schema, ref: http://schemaform.io/examples/bootstrap-example.html (Kitchen Sink Example)"}, "mapping": {"type": "Object", "description": "Mapping data to field value"}}]}, "sharePolicies": {"type": [{"type": "Share"}], "description": "Share modes and policies"}, "widgets": {"type": ["WidgetConfig"], "description": "Widgets for cards of this tier"}, "features": {"type": {"BLE": {"type": "boolean", "description": "Enable BLE card recognition", "default": false}, "photo": {"type": "boolean", "description": "Enable profile photo on card", "default": false}}}, "globalize": {"type": "Globalize"}}}, "products": {"type": [{"type": {"variantId": {"type": "string"}, "qualifier": {"type": "string", "description": "'join', 'renew', etc"}}}], "default": []}, "billing": {"type": {"fee": {"type": {"rate": {"type": "string", "enum": ["standard", "free"]}}}}}, "numberingId": {"type": "string"}}}