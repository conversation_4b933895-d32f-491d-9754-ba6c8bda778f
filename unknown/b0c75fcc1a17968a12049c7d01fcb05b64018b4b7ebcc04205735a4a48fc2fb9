/* jshint esversion: 6 */
/**
 *  @module Mixin:Common
 */

module.exports = function(Model) {

	Model.schema = async function() {
		const modelDefinition = Model.getDefinition(),
			schema = {}

		schema[this.modelName] = {
			properties: modelDefinition.rawProperties,
			relations: modelDefinition.settings.relations,
			settings: modelDefinition.settings,
		}

		return schema
	}

	Model.doFind = async function(filter = {}) {
		// must have filter, prevent to find all - ZJ
		return (!filter.where) ? [] : Model.find(filter)
	}

	Model.doFindOne = async function(filter) {
		return Model.findOne(filter)
	}

	Model.doCount = async function(where) {
		return Model.count(where)
	}

	// -----  Private Static -----

	Model.getInstance = async function(id, filter) {
		const { modelName } = this

		if (id === null || id === undefined) {
			throw new Error(modelName + ' not found, (id: ' + id + ')')
		}

		const instance = await Model.findById(id, filter)
		if (!instance) {
			throw new Error(modelName + ' not found, (id: ' + id + ')')
		}
		return instance
	}

	Model.getDefinition = function() {
		return this.dataSource.getModelDefinition(this.modelName)
	}

	// -----  Remote Methods  -----

	Model.remoteMethod('schema', {
		description: 'Returns Model definition',
		http: { path: '/schema', verb: 'get' },
		returns: { type: 'object', root: true },
	})

	Model.remoteMethod('doFind', {
		description: 'Find all instances of the model matched by filter from the data source.',
		http: { path: '/find', verb: 'post' },
		accepts: { arg: 'filter', type: 'object' },
		returns: { type: 'array', root: true },
	})

	Model.remoteMethod('doFindOne', {
		description: 'Find first instance of the model matched by filter from the data source.',
		http: { path: '/findOne', verb: 'post' },
		accepts: { arg: 'filter', type: 'object' },
		returns: { type: 'object', root: true },
	})

	Model.remoteMethod('doCount', {
		description: 'Count instances of the model matched by where from the data source.',
		http: { path: '/count', verb: 'post' },
		accepts: { arg: 'where', type: 'object' },
		returns: { type: 'number', root: true },
	})
}
