/**
 *  @module card-profile	middleware
 *
 *  Setup "card" info in LB Context from request Header:
 * 		{
 * 			"id": "5d1b026145828f10b6c5eedb"
 *		}
 */

const { Context } = require('@perkd/multitenant-context'),
	{ card } = require('@perkd/wallet')

module.exports = function() {

	return function injectCardProfile(req, res, next) {
		const cardProfile = card(req.headers)

		if (cardProfile) {
			Context.cardProfile = cardProfile
		}

		next()
	}
}
