{"name": "Membership", "plural": "Membership", "base": "PersistedModel", "idInjection": true, "strict": false, "mixins": {"MultitenantRemote": true}, "properties": {"id": {"type": "string", "id": true, "generated": true}, "tierLevel": {"type": "number", "required": true}, "qualifier": {"type": "string", "enum": ["join", "renew", "upgrade", "downgrade", "tierchange", "extend", "terminate", "transfer"]}, "qualifyMethod": {"type": "string", "description": "Qualifier method name, eg. 'complimentary', 'manual'"}, "startTime": {"type": "date"}, "endTime": {"type": "date"}, "state": {"type": "String", "default": "pending", "enum": ["pending", "active", "renewed", "expired", "upgraded", "downgraded", "tierChanged", "suspended", "cancelled", "terminated", "transferred", "blacklisted"]}, "programPriority": {"type": "number"}, "cardNumber": {"type": "string"}, "digitalCard": {"type": {"id": {"type": "string", "description": "Card instance id in X", "default": null}, "registeredAt": {"type": "date", "description": "Time card was registered", "default": null}, "hiddenAt": {"type": "date", "default": null}}, "default": {}}, "storedValue": {"type": {"balance": {"type": "number", "default": 0, "description": "Balance stored value (integer)"}, "currency": {"type": {"code": {"type": "string", "max": 8, "description": "ISO 4217 currency code, 'CREDIT' or 'POINT'"}, "precision": {"type": "number", "default": 0, "description": "Decimal precision, ie. 2 => amount of 1000 = $10.00"}}, "required": true}}}, "toQualify": {"type": [{"type": {"name": {"type": "string"}, "tierLevel": {"type": "number"}, "kind": {"type": "string", "enum": ["amount", "transaction"]}, "value": {"type": "number", "description": "Spend 'amount' / 'transaction' required to qualify"}, "currency": {"type": "String", "max": 3}, "by": {"type": "date", "description": "Time to qualify by"}}}], "description": "Outstanding spend needed for each Qualifier", "default": []}, "behaviors": {"type": "object", "default": {}}, "acquired": {"type": "TouchPoint"}, "upgradedAt": {"type": "date"}, "terminatedAt": {"type": "date"}, "transferredAt": {"type": "date", "description": "Time membership was transferred to another member"}, "createdAt": {"type": "date"}, "modifiedAt": {"type": "date"}}, "validations": [], "relations": {"payments": {"type": "hasMany", "model": "Payment", "polymorphic": {"selector": "source"}}}, "acls": [], "indexes": {}, "scopes": {}, "methods": {"doFind": {"description": "Find all instances of the model matched by filter from the data source.", "http": {"path": "/find", "verb": "post"}, "accepts": {"arg": "filter", "type": "object"}, "returns": {"type": "array", "root": true}}, "doFindOne": {"description": "Find first instance of the model matched by filter from the data source.", "http": {"path": "/findOne", "verb": "post"}, "accepts": {"arg": "filter", "type": "object"}, "returns": {"type": "object", "root": true}}, "doCount": {"description": "Count instances of the model matched by where from the data source.", "http": {"path": "/count", "verb": "post"}, "accepts": {"arg": "where", "type": "object"}, "returns": {"type": "number", "root": true}}, "issueCard": {"description": "Issue card (Merchant API)", "http": {"path": "/merchant/card/issue", "verb": "post"}, "accepts": [{"arg": "card", "type": "object", "required": true}, {"arg": "options", "type": "object"}], "returns": {"type": "object", "root": true}}, "extend": {"description": "Extend Membership of card number", "http": {"path": "/extend", "verb": "post"}, "accepts": [{"arg": "options", "type": "object", "required": true, "description": "{ cardNumber }"}], "returns": {"type": "object", "root": true}}, "terminate": {"description": "Terminate Membership of card number", "http": {"path": "/terminate", "verb": "post"}, "accepts": [{"arg": "options", "type": "object", "required": true, "description": "{ cardNumber }"}], "returns": {"type": "object", "root": true}}, "findLastJoinByCardId": {"description": "Find most recently joined membership (ACTIVE or EXPIRED)", "http": {"path": "/recent/join/card/:cardId", "verb": "get"}, "accepts": [{"arg": "cardId", "type": "any", "http": {"source": "path"}, "required": true}], "returns": {"type": "Membership", "root": true}}, "findActiveByCardId": {"description": "Find active membership (last to expire)", "http": {"path": "/active/card/:cardId", "verb": "get"}, "accepts": [{"arg": "cardId", "type": "any", "http": {"source": "path"}, "required": true}], "returns": {"type": "Membership", "root": true}}, "findLastExpireByCardId": {"description": "Find last to expire membership (ACTIVE or EXPIRED)", "http": {"path": "/last/expire/card/:cardId", "verb": "get"}, "accepts": [{"arg": "cardId", "type": "any", "http": {"source": "path"}, "required": true}], "returns": {"type": "Membership", "root": true}}, "findOneWithPaymentByCardId": {"description": "Find active Membership with associated payment type) of Card", "http": {"path": "/active/card/:cardId/payment/:type", "verb": "get"}, "accepts": [{"arg": "cardId", "type": "any", "http": {"source": "path"}, "required": true}, {"arg": "type", "type": "string", "http": {"source": "path"}, "required": true}], "returns": {"type": "Membership", "root": true}}, "findActiveAndPersonIdByCardNumber": {"description": "Find active Membership & personId with CardNumber", "http": {"path": "/activeWithPerson/cardNumber/:cardNumber", "verb": "get"}, "accepts": [{"arg": "cardNumber", "type": "string", "http": {"source": "path"}, "required": true}], "returns": {"type": "Membership", "root": true}}, "findManyActiveByPersonId": {"description": "Find active Memberships with personId", "http": {"path": "/test/active/:personId", "verb": "get"}, "accepts": [{"arg": "personId", "type": "any", "http": {"source": "path"}, "required": true}], "returns": {"type": ["Membership"], "root": true}}, "findActiveByPersonId": {"description": "Best effert match Membership with personId", "http": {"path": "/active/:personId", "verb": "get"}, "accepts": [{"arg": "personId", "type": "any", "http": {"source": "path"}, "required": true}, {"arg": "programIds", "type": "array"}], "returns": {"type": "Membership", "root": true}}, "handleExpiry": {"description": "Do qualify when membership goes expired", "http": {"path": "/expiry/handle", "verb": "post"}, "accepts": [{"arg": "daysAgo", "type": "number", "description": "Days ago of expired memberships.", "default": 1}], "returns": {"type": "Object", "root": true}}, "notify": {"description": "Send push notification to card of membership", "http": {"path": "/card/notify", "verb": "post"}, "accepts": [{"arg": "membershipId", "type": "string", "required": true}, {"arg": "template", "type": "object", "required": true, "description": "{ name, widget? }"}, {"arg": "personalize", "type": "object", "description": "Personalization data"}, {"arg": "options", "type": "object", "description": "{ image, payload }"}], "returns": {"type": "Object", "root": true}}, "setCredentials": {"description": "Set Card credentials for Membership", "http": {"path": "/card/credentials", "verb": "post"}, "accepts": [{"arg": "id", "type": "any", "required": true}, {"arg": "kind", "type": "string", "required": true}, {"arg": "name", "type": "string", "required": true}, {"arg": "credentials", "type": "object", "required": true}], "returns": {"type": "object", "root": true}}, "prototype.isActive": {"description": "Is Membership active?", "http": {"path": "/isActive", "verb": "get"}, "returns": {"type": "boolean", "root": true}}, "prototype.renew": {"description": "Renew Membership", "http": {"path": "/renew", "verb": "post"}, "accepts": [{"arg": "options", "type": "object"}], "returns": {"type": "object", "root": true}}, "prototype.extend": {"description": "Extend Membership", "http": {"path": "/extend", "verb": "post"}, "accepts": [{"arg": "options", "type": "object"}], "returns": {"type": "object", "root": true}}, "prototype.upgrade": {"description": "Upgrade Membership to (next) Tier", "http": {"path": "/upgrade", "verb": "post"}, "accepts": [{"arg": "tierLevel", "type": "number"}, {"arg": "options", "type": "object"}], "returns": {"type": "object", "root": true}}, "prototype.downgrade": {"description": "Downgrade Membership to (lower) Tier", "http": {"path": "/downgrade", "verb": "post"}, "accepts": [{"arg": "tierLevel", "type": "number"}, {"arg": "options", "type": "object"}], "returns": {"type": "object", "root": true}}, "prototype.changeTier": {"description": "Change Membership to Tier", "http": {"path": "/changeTier", "verb": "post"}, "accepts": [{"arg": "tierLevel", "type": "number", "required": true}, {"arg": "options", "type": "object"}], "returns": {"type": "object", "root": true}}, "prototype.suspend": {"description": "Suspend Membership temporarily", "http": {"path": "/suspend", "verb": "post"}, "accepts": [{"arg": "options", "type": "object"}], "returns": {"type": "object", "root": true}}, "prototype.cancel": {"description": "Cancel Membership", "http": {"path": "/cancel", "verb": "post"}, "accepts": [{"arg": "options", "type": "object"}], "returns": {"type": "object", "root": true}}, "prototype.recover": {"description": "Recover a cancelled Membership", "http": {"path": "/recover", "verb": "post"}, "accepts": [{"arg": "options", "type": "object"}], "returns": {"type": "object", "root": true}}, "prototype.terminate": {"description": "Pre-schedule or immediately Terminate Membership", "http": {"path": "/terminate", "verb": "post"}, "accepts": [{"arg": "options", "type": "object"}], "returns": {"type": "object", "root": true}}, "prototype.expire": {"description": "Expire membership", "http": {"path": "/expire", "verb": "post"}, "returns": {"type": "object", "root": true}}, "prototype.cancelPreTerminate": {"description": "Cancel pre-scheduled Termination", "http": {"path": "/canelPreTerminate", "verb": "post"}, "accepts": [{"arg": "options", "type": "object"}], "returns": {"type": "object", "root": true}}, "prototype.issueCard": {"description": "Issue Card for Membership", "http": {"path": "/card/issue", "verb": "post"}, "accepts": [{"arg": "options", "type": "object"}], "returns": {"type": "object", "root": true}}, "prototype.updateCard": {"description": "Update Card for Membership", "http": {"path": "/card/update", "verb": "post"}, "accepts": [{"arg": "qualifier", "type": "string"}], "returns": {"type": "object", "root": true}}, "prototype.refreshCard": {"description": "Refresh digital card with membership data", "http": {"path": "/card/refresh", "verb": "post"}, "accepts": [{"arg": "options", "type": "object"}], "returns": {"type": "object", "root": true}}, "prototype.cancelCard": {"description": "Cancel Card for Membership", "http": {"path": "/card/cancel", "verb": "post"}, "accepts": [{"arg": "options", "type": "object"}], "returns": {"type": "object", "root": true}}, "prototype.revokeCard": {"description": "Revoke Card for Membership", "http": {"path": "/card/revoke", "verb": "post"}, "accepts": [{"arg": "options", "type": "object"}], "returns": {"type": "object", "root": true}}, "prototype.recoverCard": {"description": "Recover Card for Membership", "http": {"path": "/card/recover", "verb": "post"}, "accepts": [{"arg": "options", "type": "object"}], "returns": {"type": "object", "root": true}}, "prototype.getCardUserProfile": {"description": "Get User Profile of card (Merchant API)", "http": {"path": "/card/profile", "verb": "get"}, "accepts": [], "returns": {"type": "object", "root": true}}}}