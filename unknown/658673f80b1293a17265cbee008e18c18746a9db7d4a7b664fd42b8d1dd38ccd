{"name": "TouchPoint", "plural": "TouchPoints", "description": "Partial Activity model (instrument + location + attributedTo)", "base": "Model", "idInjection": false, "strict": false, "mixins": {}, "options": {}, "properties": {"type": {"type": "string", "enum": ["crm", "merchant", "perkd", "qrcode", "advertisement", "pos", "app", "website", "email", "sms", "ecommerce", "unknown"], "description": "Corresponds to types of Instrument of Activities"}, "format": {"type": "string", "description": "channel / medium"}, "location": {"type": "Spot"}, "attributedTo": {"type": {"type": "", "id": {"type": "string"}, "name": {"type": "string", "max": 80}}, "description": "Person, staff, system function"}, "touchedAt": {"type": "Date"}}, "validations": [], "relations": {}, "acls": [], "indexes": {"type_index": {"keys": {"type": 1}}}, "scopes": {}, "methods": {}}