/**
 * @module Mixin:Embed<PERSON><PERSON>mon
 */

const { promisify } = require('node:util'),
	{ isEmptyObj, cloneDeep } = require('@perkd/utils'),
	{ JSONPatch } = require('@perkd/sync'),
	{ diffArray } = appRequire('lib/common/deltaHelper'),
	{ compare } = JSONPatch

/**
 * @description This class is used in
				01 Date
				02 Phone
				03 Email
				04 Address
				05 Permission
				06 Tag
				07 Note
				08 Identity
				09 Location
 *  Note: please update the list in the future
 */
class EmbedLib {
	/**
	 * @param {Object} Model			eg: Person
	 * @param {string} relationName		eg: phones
	 * @param {Object} options
	 * @param {String} idName
	 */
	constructor(Model, relationName, options = {}, idName = 'id') {
		this._model = Model
		this._idName = idName // should get from model definition - ZJ
		this._relationName = relationName
		this._definition = cloneDeep(Model.dataSource.getModelDefinition(Model.modelName).settings.relations[relationName])
		this._subModelName = this._definition.model
		this._propertyName = this._definition.property
		this._options = options
		this.diff = diffArray(this._propertyName, this._idName)
	}

	setup() {
		this._setupMethods(this._model, this._relationName, this._definition, this._options)

		// ---  Remote & Operation Hooks  ---
		this._setupRemoteHooks(this._model, this._relationName, this._options)
		this._setupRemoteMethods(this._model, this._relationName, this._definition, this._options)
	}

	_setupMethods(Model, relationName, definition, options) {
		const self = this,
			Modelname = definition.model,
			Relationname = relationName.charAt(0).toUpperCase() + relationName.slice(1)

		Model.prototype['upsert' + Relationname] = function(data, through, options) {
			return self._upsert(this, data, through, Object.assign({}, options, { through }))
		}
		if (self._definition.type === 'embedsMany') {
			Model.prototype['delete' + Modelname] = function(data, through, options) {
				return self._delete(this, data, through, Object.assign({}, options, { through }))
			}
			Model.prototype['default' + Modelname] = function(id) {
				return self._default(this, id)
			}
		}
	}

	_setupRemoteHooks(Model, relationName, options) {
		const self = this

		if (self._definition.type === 'embedsMany') {
			Model.beforeRemote('prototype.__destroyById__' + relationName, (ctx, _modelInstance_, next) => {
				self.remoteMethodBeforeHook(ctx, options, next)
			})

			Model.afterRemote('prototype.__destroyById__' + relationName, (ctx, _modelInstance_, next) => {
				self.remoteMethodAfterHook(ctx, options, next)
			})

			Model.beforeRemote('prototype.__updateById__' + relationName, (ctx, _modelInstance_, next) => {
				self.remoteMethodBeforeHook(ctx, options, next)
			})

			Model.afterRemote('prototype.__updateById__' + relationName, (ctx, _modelInstance_, next) => {
				self.remoteMethodAfterHook(ctx, options, next)
			})
		}
	}

	_setupRemoteMethods(Model, relationName, definition, options) {
		// Relationname = relationName.charAt(0).toUpperCase() + relationName.slice(1);

		if (this._definition.type === 'embedsMany') {
			// Model.remoteMethod('prototype.upsert' + Relationname, {
			// 	description: 'Create or update ' + Relationname,
			// 	http: { verb: 'post' },
			// 	accepts: [
			// 		{ arg: 'data', type: 'object', required: true },
			// 		{ arg: 'options', type: 'object'},
			// 	],
			// 	returns: { type: 'array', root: true },
			// });
			Model.remoteMethod('prototype.default' + definition.model, {
				description: 'Set default ' + definition.model.toLowerCase(),
				http: { path: '/' + relationName + '/:fk/default', verb: 'post' },
				accepts: [ { arg: 'fk', type: 'string', required: true } ],
				returns: { type: 'object', root: true },
			})
			Model.remoteMethod('prototype.delete' + definition.model, {
				description: 'Delete ' + definition.model.toLowerCase(),
				http: { path: '/delete' + definition.model, verb: 'post' },
				accepts: [
					{ arg: definition.model.toLowerCase(), type: 'object', required: true },
					{ arg: 'through', type: 'object' },
				],
				returns: { type: 'object', root: true },
			})
		}
	}

	/**
	 * @description get/set default row to first index
	 * @param {Object} Instance  Reference to parent model in mixins
	 * @param {Object} id      Unique id value
	 */
	async _default(Instance, id) {
		if (!id) { // getter
			return Instance[this._propertyName][0] || null
		} // setter
		if (typeof id !== 'string') id = id.toString()
		const pos = Instance[this._propertyName].findIndex(row => row[this._idName].toString() === id)
		if (pos > 0) {
			const moved = Instance[this._propertyName].splice(pos, 1)[0]
			Instance[this._propertyName].unshift(moved)

			const changes = {}
			changes[this._propertyName] = Instance[this._propertyName]
			await Instance.updateAttributes(changes)
			return moved
		}
		return null // TODO - ZJ what 0 means?
	}

	/**
	 * @description Delete model
	 * @param {Object} Instance
	 * @param {Object} model		model object containing id or uniq
	 * @param {Object} through
	 * @param {Object} options		Parent function options
	 * @return deleted model
	 */
	async _delete(Instance, model, through, options = {}) {
		const _options = Object.assign({}, this._options, options),
			found = model.id
				? this._findById(Instance, model.id)
				: this._findByUniq(Instance, model)

		if (!found) return null

		const delta = this.diff(found, {})

		await promisify(Instance[this._relationName].unset)(found[this._idName])

		this._fireModelEvent('deleted', Instance, found, delta, through, _options)
		this._fireAppEvent(Instance, delta, through, _options)

		return found
	}

	async _create(Instance, data, through = {}, options = {})	{
		const _options = Object.assign({}, this._options, options)

		try {
			const updated = await Instance[this._relationName].create(data),
				updatedData = updated.toJSON(),
				delta = this.diff({}, updatedData)

			this._fireModelEvent('created', Instance, updatedData, delta, through, _options)

			const withDelta = Object.assign({}, updatedData, { _delta: delta })
			if (this._definition.type === 'embedsMany' && options.upsertAsDefault) await this._default(Instance, updated.id)

			return withDelta
		}
		catch (err) {
			appLog('create ' + this._subModelName, {
				error: err,
				modelName: this._model.modelName,
				data,
			})
			return {}
		}
	}

	/**
	 *
	 * @param {Object} found Embed Instance to be replace
	 * @param {Object} model Embed model instance
	 * @param {Object} paramsObj contain delta which updates by referencing
	 * @param {Object} Instance Persisted model instance
	 * @param {Object} options Object to update
	 * @param {boolean} createLog default=false
	 */
	async _update(Instance, data, through, options = {}) {
		const self = this,
			theKey = self._getIdValue(data),
			before = options.before || {},
			_options = Object.assign({}, self._options, options)

		if (self._definition.type === 'embedsMany') {
			const res = await promisify(Instance[self._relationName].updateById)(theKey, data)
			return _done(res)
		}

		const res = await Instance[self._relationName].update(data)
		return _done(res)

		async function _done(updated) {
			const updatedData = updated.toJSON(),
				after = Object.assign({}, before, updatedData),
				delta = compare(before, after) ? self.diff(before, after) : null

			delta && self._fireModelEvent('changed', Instance, updated, delta, through, _options)
			// if submodel has special event, fire... eg: permission changed
			_options.isNew || (delta && _options.event && self._fireAppEvent(Instance, delta, through, _options))

			const withDelta = Object.assign({}, updatedData, { _delta: delta })
			if (self._definition.type === 'embedsMany' && options.upsertAsDefault) await self._default(Instance, updated.id)

			return withDelta
		}
	}

	/**
	 * @description upsert : update and insert, nothing more
	 * @param {Object} Instance	Reference to parent model in mixins
	 * @param {Embed Model} data	Either an embed model or a list of embed model to be update, created or deleted
	 * @param {Object} options	Parent function options
	 */
	// _upsert(Instance, modelList, options = {}) {
	async _upsert(Instance, data, through = {}, options = {}) {
		const _options = Object.assign({}, this._options, options)

		try {
			if (this._definition.type === 'embedsMany') {
				const dataList = Array.isArray(data) ? data : [ data ],
					result = []

				for (const d of dataList) {
					const key = this._getIdValue(d),
						found = key ? this._findById(Instance, key) : this._findByUniq(Instance, d),
						res = found
							? await this._update(Instance, Object.assign({}, d, { id: found.id }), through, Object.assign({}, _options, { before: found.toJSON() }))
							: await this._create(Instance, d, through, _options)
					result.push(res)
				}

				return result
			}

			const found = this._findByUniq(Instance, data)
			return found
				? this._update(Instance, data, through, Object.assign({ before: found.toJSON() }, _options))
				: this._create(Instance, data, through, _options)
		}
		catch (err) {
			// watchdog show error
			appLog('upsert' + this._subModelName, {
				error: err,
				modelName: this._model.modelName,
				data,
			})
			return this._definition.type === 'embedsMany' ? [] : {}
		}
	}

	/**
	 * @description to accommodate mutiple and single unique key query
	 * 				if multiple unique -> return entire model from getKeyValue,
	 * 				_findById function will get the entire model instance ( see Address.js )
	 * @param {Object} model
	 */
	_getIdValue(model) {
		return model[this._idName] || null
	}

	/**
	 * @param {Object} Instance   Reference to parent model in mixins
	 * @param {Object} id     Unique key value to search from embed model list
	 */
	_findById(Instance, id) {
		if (!id || !Instance[this._propertyName]) {
			return null
		}

		return Instance[this._propertyName].find(model => model[this._idName] === id)
	}

	_findByUniq(Instance, data) {
		if (this._definition.type === 'embedsMany') {
			const funcName = '_' + this._subModelName.toLowerCase()
			return (typeof Instance[funcName] === 'function') ? Instance[funcName](data) : undefined
		}
		// embedsOne
		return isEmptyObj(this[this._propertyName] || {}) ? false : this[this._propertyName]
	}

	/**
	 * @description broadcast event which linked to language, locale, permission change
	 * @param {Object} updated 	 updated model instance
	 * @param {Object} Instance  instance to update model
	 * @param {Object} _delta 	 delta obj
	 * @param {Object} options   optional value
	 * @param {string} eventName updated / created
	 */
	_fireModelEvent(eventName, Instance, data, delta, through = {}, options = {}) {
		if (data && delta) {
			// console.log('✅ fireModelEvent %s', self._subModelName + '.' + eventName);
			this._model.emit(this._subModelName + '.' + eventName, {
				instance: Instance,
				data,
				through,
				options,
			})
		}
	}

	/**
	 * @description Broadcast event to generate activity log
	 * @param {Object} delta     Delta value to stored
	 * @param {Object} Instance  Main model instance
	 * @param {Object} options   optional value for activity
	 */
	_fireAppEvent(Instance, delta, through = {}, options = {}) {
		const modelName = this._model.modelName.toLowerCase(),
			App = this._model.app,
			eventName = options.event || 'updated'

		if (delta && App.Event[modelName] && App.Event[modelName][eventName]) {
			const updateAct = Object.assign(Instance.toJSON(), { context: { delta } }, through ? { through } : {})
			// console.log('✅ fireAppEvent %s', App.Event[modelName][eventName]);
			App.emit(App.Event[modelName][eventName], updateAct)
		}
	}

	/**
	 *
	 * @param {Object} ctx           current context
	 * @param {Object} modelInstance Main model instance
	 * @param {function} next		 callback to execute next function
	 */
	remoteMethodBeforeHook(ctx, options, next) {
		const { args, instance } = ctx

		if (instance) {
			ctx._before = clone(this._findById(instance, args.fk.toString()))
		}
		next()
	}

	/**
	 * @description https://github.com/strongloop/loopback/issues/766
	 * @param {Object} ctx				current context
	 * @param {Object} modelInstance	Main model instance
	 * @param {function} next			callback to execute next function
	 * @param {String}   eventName		remote method event name, (deleted, updated, created)
	 */
	remoteMethodAfterHook(ctx, options, next) {
		if (ctx._before) {
			const before = (ctx._before ? ctx._before.toJSON() : ctx._before) || {},
				after = (ctx.result ? ctx.result.toJSON() : ctx.result) || {}

			const delta = compare(before, after) ? this.diff(before, after || {}) : null
			delta && ctx.method.name.includes('__updateById__') && this._fireModelEvent('changed', ctx.instance, after, delta, options)
			delta && ctx.method.name.includes('__destroyById__') && this._fireModelEvent('deleted', ctx.instance, after, delta, options)
			options.isNew || (delta && this._fireAppEvent(ctx.instance, delta, options))
		}
		next()
	}
}

module.exports.EmbedLib = EmbedLib
