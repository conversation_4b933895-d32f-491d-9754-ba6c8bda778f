{"name": "Points", "plural": "Points", "description": "", "base": "PersistedModel", "idInjection": true, "strict": false, "mixins": {"MultitenantRemote": true}, "properties": {"id": {"type": "String", "id": true, "generated": true}, "collected": {"type": "Number", "default": 0, "description": "Total points collected"}, "redeemed": {"type": "Number", "default": 0, "description": "Total points redeemed"}, "available": {"type": "Number", "default": 0, "description": "Net balance available"}, "bonus": {"type": "Number", "default": 0, "description": "Total bonus points"}, "adjusted": {"type": "Number", "default": 0, "description": "Total points adjusted"}, "forfeited": {"type": "Number", "default": 0, "description": "Total points forfeited"}, "forwarded": {"type": "Number", "default": 0, "description": "Total points forwarded to next membership"}, "carried": {"type": "Number", "default": 0, "description": "Total points carried from previous membership"}, "expired": {"type": "Number", "default": 0, "description": "Total points expired"}, "qualified": {"type": [{"type": {"name": {"type": "string"}, "amount": {"type": "number"}, "currency": {"type": "string", "max": 3}, "toRedeem": {"type": "number", "description": "Points needed to redeem"}}}], "default": [], "description": "Cash value redemptions already qualified"}, "toQualify": {"type": [{"name": {"type": "string"}, "amount": {"type": "number"}, "currency": {"type": "string", "max": 3}, "toRedeem": {"type": "number", "description": "Points needed to redeem"}, "pointsGap": {"type": "number", "description": "Additional points needed to qualify"}}], "default": [], "description": "Redemptions to qualify with additional points"}, "visible": {"type": "Boolean", "default": true}, "endTime": {"type": "Date", "description": "Time point balance expires", "default": null}, "createdAt": {"type": "Date"}, "modifiedAt": {"type": "Date"}, "deletedAt": {"type": "Date"}}, "hidden": [], "validations": [], "relations": {}, "acls": [], "methods": {}}