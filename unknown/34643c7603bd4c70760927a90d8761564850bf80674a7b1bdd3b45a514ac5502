{"name": "StoredValueConfig", "plural": "StoredValueConfig", "idInjection": false, "strict": true, "options": {"validateUpsert": false}, "properties": {"id": false, "kind": {"type": "string", "enum": ["giftcard", "storecredits"], "required": true}, "name": {"type": "string", "description": "Unique wallet name"}, "enabled": {"type": "boolean"}, "currency": {"type": {"code": {"type": "string", "max": 8, "description": "ISO 4217 currency code, 'CREDIT' or 'POINT'"}, "precision": {"type": "number", "default": 0, "description": "Decimal precision, ie. 2 => amount of 1000 = $10.00"}}}, "style": {"color": {"type": "string", "description": "RGB Hex string"}, "format": {"type": "string", "description": "Regex format string for Balance display"}, "fontSize": {"type": "number", "description": "Font size for Balance display"}, "container": {"type": "object", "description": "Container Style for Balance display, ref: https://reactnative.dev/docs/view-style-props"}, "text": {"type": "object", "description": "Text Style for Balance display, ref: https://reactnative.dev/docs/text-style-props"}}, "singleUse": {"type": "Boolean", "description": "True = balance to be deducted in single payment"}, "taxIncluded": {"type": "Boolean", "description": "True => tax paid at top up"}, "sharedOnly": {"type": "Boolean", "description": "True = card must be shared after purchase, used by recipient only"}, "external": {"type": "object", "description": "External params passed to provider during account create, optional (eg. FlexM giftCardId)"}}, "scopes": {}, "methods": {}}