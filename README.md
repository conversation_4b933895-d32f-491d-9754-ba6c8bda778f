# Business Service

A microservice for managing business entities and their related operations in the CRM platform.

## Table of Contents
- [Overview](#overview)
- [System Architecture](#system-architecture)
- [Core Features](#core-features)
- [Service Integration](#service-integration)
- [Provisioning Architecture](#provisioning-architecture)
- [Environment Variables](#environment-variables)
- [Data Models](#data-models)
- [API Endpoints](#api-endpoints)
- [Events](#events)


## Overview
This service is a core component within the CRM ecosystem that provides comprehensive business entity management, including:

- Business profile and settings management
- Multi-tenant configuration and access control
- Provider integration framework with shared resource optimization
- Payment processing and wallet management
- Staff and store operations
- Event-driven business operations

### System Architecture
```mermaid
graph TD
    subgraph Business Service
        A[Core Service] --> B[Account Provisioning]
        A --> C[Business Provisioning]
        A --> D[Feature Provisioning]
        A --> E[Resource Management]
    end

    subgraph Resource Management
        E --> E1[Tenant Resources]
        E --> E2[Shared Resources]
        E1 --> F1[Custom Pool]
        E2 --> F2[Native Pool]
    end

    B --> B1[Core Settings]
    B --> B2[Admin Setup]
    B --> B3[Redis Sync]

    C --> C1[Payment Integration]
    C --> C2[Provider Setup]
    C --> C3[Multi-tenant Config]

    D --> D1[Membership]
    D --> D2[Fulfillment]
    D --> D3[Stored Value]

    classDef resource fill:#4299e1,stroke:#2b6cb0,stroke-width:2px,color:#fff
    class E1,E2,F1,F2 resource
```

### Core Features
- Multi-tenant business profile management with Redis-backed settings and MongoDB storage
- Multi-level provisioning (Account, Business, Feature) with automated setup
- Provider integration system with modular service enablement and health monitoring
- Optimized resource management with connection pooling and tenant isolation
- Event-driven business operations with Redis Streams
- Secure payment processing with multi-provider support
- Staff and store operations management
- Comprehensive business settings synchronization

### Business Management
- Business profile and settings management with Redis synchronization
- Multi-tenant configuration and access control via JWT
- Theme and branding customization (light/dark modes)
- Logo and style management
- Business identification and registration
- Locale and regional settings with multi-language support

### Payment Processing
- Multi-provider payment gateway integration (Apple Pay, Google Pay, Alipay)
- Configurable fee structure with percentage and fixed fees
- Multi-currency support
- Secure payment processing and settlement
- Payment method management and configuration
- Real-time payment status tracking
- Comprehensive refund handling

#### Payment Architecture
The Business service provides payment capabilities to other microservices and the Perkd platform through a layered architecture:

1. **Base Layer (PayPrimitives)**: Core payment operations that interact with payment providers
2. **Business Logic Layer (Pay, Payment)**: Business logic for payment processing
3. **API Layer (PayApi, PaymentApi, StoredValueApi)**: Remote API endpoints for external systems

**Key Concepts:**
- **Store Configuration vs. Store Payment Settings**:
  - *Store Configuration*: The mechanism (functions like `getStorePaymentSetting()`) used to retrieve payment settings for a specific store
  - *Store Payment Settings*: The actual data structure containing merchant IDs and provider-specific settings for each store

For detailed documentation, see [Business Payments Documentation](docs/business-payments.md).

### Provider System
- Modular provider integration framework with name-based identification
- Service-based capability management with health monitoring
- Environment-aware configurations (live/test) with Redis sync
- Secure credential handling via Secrets Manager
- Provider health monitoring and validation
- Multi-provider support for:
  - Payments (Apple Pay, Google Pay, Alipay)
  - Sales channels (Shopify, GrabFood, GrabMart, UberEats)
  - Operations (Print, Invoice)
  - Cloud services (AWS, Google)
- Provider types:
  - Shared: Platform-wide providers (AWS, Google)
  - Tenanted: Tenant-specific providers (Shopify)
  - Instance: Model instance-specific providers (payment wallets)

See [Provider Documentation](docs/providers.md) for detailed information about specific providers.


## Service Integration
This service integrates with multiple microservices and external providers through APIs and events:

```mermaid
graph LR
    BS[Business Service]

    BS <--> AS[Action Service]
    BS <--> PS[Place Service]
    BS <--> PE[Person Service]
    BS <--> MS[Membership Service]
    BS <--> PA[Payment Service]
    BS <--> PR[Product Service]
    BS <--> CS[Campaign Service]

    subgraph External Providers
        SP[Sales Providers]
        OP[Operation Providers]
        CP[Cloud Providers]
    end

    BS <--> SP
    BS <--> OP
    BS <--> CP
```

### Core Service Integration

- Action Service
	- Event tracking and logging
	- Business activity monitoring
	- Audit trail management
	- System health checks

- Place Service
	- Store location management
	- Geographic data handling
	- Operating hours configuration
	- Store capacity monitoring
	- Place closure handling
	- Provisioning state management

- Person Service
	- Staff profile management
	- Role-based access control
	- Authentication and authorization
	- User preference management
	- Staff notification handling

- Membership Service
	- Staff membership card issuance

- Payment Service
	- Transaction processing
	- Payment method management
	- Fee calculation and processing
	- Settlement handling
	- Multi-provider payment support
	- Stored value operations
	- Multi-currency support
	- Payment reconciliation
	- Fee structure management

- Product Service
	- Inventory management
	- Product catalog handling
	- Price and promotion management
	- Stock level monitoring

- Campaign Service
	- Marketing campaign management
	- Promotion configuration
	- Discount handling
	- Campaign performance tracking

### External Service Integration
- **Core Provider Model**: Name-based provider identification with service-based capability declaration
- **Sales Channel Providers**: Shopify, GrabFood, GrabMart, UberEats integrations
- **Operations Providers**: Print and Invoice provider integrations
- **Cloud Service Providers**: AWS and Google service integrations

## Provisioning Architecture

The system uses a multi-level provisioning architecture to manage business setup and configuration:

1. Account Provisioning (Level 0) - Initial account and core settings setup
2. Business Provisioning (Level 1) - Business-specific integrations and configurations
3. Feature Provisioning (Level 2) - Feature-specific enablement and setup

```mermaid
graph TD
    A[Business Service] --> B[Account Provisioning]
    A --> C[Business Provisioning]
    A --> D[Feature Provisioning]

    B --> B1[Core Settings]
    B --> B2[Admin Setup]
    B --> B3[Redis Sync]

    C --> C1[Payment Integration]
    C --> C2[Provider Setup]
    C --> C3[Multi-tenant Config]

    D --> D1[Membership]
    D --> D2[Fulfillment]
    D --> D3[Stored Value]
```

For detailed information about the provisioning system, including provider management, settings management, and deprovisioning procedures, see [Provisioning Documentation](docs/provision.md).


## Environment Variables

For Shopify Staff App:
```sh
ACTION_SECRET       MUST be PRE-getHashSha256(), to be compatible with BUG in app
```

## Data Models
The key entities and their relationships:

```mermaid
erDiagram
    Business ||--o{ Place : has
    Business ||--o{ Staff : employs
    Business ||--o{ Setting : configures
    Business ||--o{ LogoImage : contains
    Business ||--o{ Dashboard : displays
    Business ||--|| Gateway : uses
    Business ||--|| Person : owned-by
    Business ||--|| User : managed-by

    Place {
        string id
        string name
        string category
        object location
        boolean active
        datetime operatingHours
    }

    Staff {
        string id
        string[] roles
        datetime joinedAt
        boolean active
    }

    Setting {
        string name
        object value
        string type
        datetime updatedAt
    }

    Payment {
        string id
        string type
        string method
        number amount
        string status
        object details
    }

    Gateway {
        string id
        string provider
        object credentials
        object settings
    }

    Business {
        string id
        string name
        object brand
        object style
        object locale
        boolean isMain
        string merchantCode
    }
```

### Key Entities

- Business - Core business profile and configuration management
	- Business profiles and registration
	- Settings and configurations
	- Contact information and branding
	- Integration configurations

- Service - Service lifecycle and configuration management
	- Service states and health
	- Multi-tenant configuration
	- Event subscriptions
	- Service dependencies

- Provider - External service provider management
	- Provider credentials and configuration
	- Service enablement states
	- Integration settings
	- Environment configurations

- Settings - Business and service settings management
	- Feature configurations
	- Integration settings
	- Locale and regional settings
	- Operational preferences

- Event - Event tracking and management
	- Event publishing and subscription
	- Event history and metrics
	- Event routing configuration
	- Tenant-specific events

- Place - Store and location management
	- Store profiles and details
	- Operating hours and capacity
	- Geographic information
	- Queue management

- Staff - Staff management and operations
	- Staff profiles and roles
	- Check-in/out tracking
	- Notification preferences
	- Service assignments

- Payment - Payment processing and configuration
	- Payment methods and gateways
	- Transaction processing
	- Fee configurations
	- Settlement handling

- Provision - Feature provisioning and lifecycle
	- Account provisioning
	- Feature enablement
	- Provider setup
	- Resource allocation

### Model Properties

- Basic properties (name, type, brand)
- Business identifiers and registration
- Contact information (phone, email, address)
- Locale settings and internationalization
- Commerce settings
- WiFi settings
- Style configuration for light/dark themes
- Payment configuration
- External integrations
- Social media integrations

## API Endpoints

For detailed API documentation, see [API.md](docs/API.md).

### Business Operations
- `GET /Businesses/main` - Get main business profile
- `POST /Businesses/settings` - Add business settings
- `GET /Businesses/settings/:name` - Get settings by name
- `DELETE /Businesses/settings/:name` - Remove settings
- `POST /Businesses/settings/:name` - Update settings
- `POST /Businesses/settings/redis/sync` - Sync settings to Redis cache

### Staff Management
- `GET /Businesses/getStaff` - Get staff by roles
- `POST /Businesses/staff/checkin` - Staff store check-in
- `POST /Businesses/staff/checkout` - Staff store check-out
- `POST /Businesses/staff/notify` - Send notifications to staff
- `POST /Businesses/staff/card/issue` - Issue staff membership card

### Store Operations
- `GET /Businesses/getStores` - Get all operating stores
- `POST /Businesses/places/findOrCreate/provider` - Create/find provider store
- `GET /Businesses/places/provider/:provider/:storeId` - Get store by provider ID

### Payment Operations

#### Payment Flow
- `POST /Businesses/payment/request` - Initialize payment request
- `POST /Businesses/payment/authorize` - Authorize payment
- `POST /Businesses/payment/capture` - Capture authorized payment
- `DELETE /Businesses/payment/cancel` - Cancel payment request
- `POST /Businesses/payment/refund` - Process refund

#### Wallet Management
- `POST /Businesses/payment/wallet` - Create wallet
- `GET /Businesses/payment/wallet` - Get wallet details
- `GET /Businesses/payment/balance` - Get wallet balance

#### Stored Value
- `POST /Businesses/payment/storedvalue/init` - Initialize wallet with amount
- `POST /Businesses/payment/storedvalue/topup` - Top up wallet with amount
- `POST /Businesses/payment/storedvalue/deduct` - Deduct amount from wallet
- `POST /Businesses/payment/storedvalue/transfer` - Transfer amount between wallets

#### Perkd Callback Endpoints
- `POST /Businesses/perkd/pay` - Make payment to business
- `POST /Businesses/perkd/pay/commit` - Commit pending payment
- `POST /Businesses/perkd/pay/cancel` - Cancel pending payment
- `POST /Businesses/perkd/pay/send` - Transfer amount between cards
- `GET /Businesses/perkd/pay/balance` - Retrieve balance
- `POST /Businesses/perkd/pay/method` - Store payment method for future use
- `DELETE /Businesses/perkd/pay/method` - Remove stored payment method

#### Perkd Pay API Endpoints (for vending machines)
- `POST /Businesses/pay/commit` - Capture authorized payment
- `POST /Businesses/pay/cancel` - Cancel authorized payment

#### Payment Methods
Supported payment methods with percentage and fixed fee support:
- Apple Pay
- Google Pay
- Alipay

Each payment method supports:
- Percentage-based fees
- Fixed amount fees
- Method-specific configurations

#### Fee Structure
Each payment method can have:
- Percentage fee (decimal format, e.g., 0.1 = 10%)
- Fixed amount fee (in native precision, e.g., 0.10 = 10 cents)

### Tenant Management
Endpoints for managing multi-tenant functionality:

- `GET /tenants` - List tenants
- `POST /tenants` - Add tenant
- `DELETE /tenants/{code}` - Remove tenant
- `GET /tenants/{code}/settings` - Get tenant settings
- `POST /tenants/{code}/settings` - Update tenant settings


### Settings Management
Endpoints for managing service and business settings:

- `GET /settings` - List settings
- `POST /settings` - Add setting
- `GET /settings/{key}` - Get setting by key
- `POST /settings/{key}` - Update setting
- `DELETE /settings/{key}` - Remove setting


### System Operations
Endpoints for health check and monitoring:
- `GET /status` - Service health check
- `GET /config` - Get service configuration


## API Response Examples

### Success Response
```json
{
  "success": true,
  "data": {
    "id": "business123",
    "name": "Sample Business",
    "settings": {
      "locale": "en-US",
      "timezone": "UTC+8"
    }
  }
}
```

### Error Response
```json
{
  "success": false,
  "error": {
    "code": "INVALID_REQUEST",
    "message": "Invalid payment amount",
    "details": {
      "field": "amount",
      "reason": "Must be greater than 0"
    }
  }
}
```


## Events

### Published Events
Events emitted by this service for other services to consume.

#### Business Events
  - `business.*` - All business-related events
  - `business.created` - New business registration
  - `business.updated` - Business profile changes
  - `business.deleted` - Business deletion
  - `business.settings.changed` - Business settings updates

#### Payment Events
  - `payment.balance.changed` - Wallet balance updates

#### Place Events
  - `place.provision.started` - Place provisioning initiated
  - `place.provision.activated` - Place activation completed

#### Applet Events
  - `applet.service.accepted` - Service request acceptance


### Subscribed Events
Events consumed by this service from other services.

#### Place Events
  - `place.place.closed` - Store closure notification

#### Payment Events
  - `payment.transaction.paid` - Payment completed
  - `payment.transaction.authorized` - Payment authorized
  - `payment.transaction.chargeable` - Payment chargeable
  - `payment.transaction.cancelled` - Payment cancelled
  - `payment.transaction.failed` - Payment failed
  - `payment.transaction.event` - Generic payment event

#### Sales Events
  - `sales.fulfillment.requested.kitchen` - Kitchen fulfillment request
  - `sales.fulfillment.packed.kitchen` - Kitchen fulfillment packed
  - `sales.fulfillment.allocated.deliver` - Delivery allocation
  - `sales.fulfillment.arrived.deliver` - Delivery arrival
  - `sales.order.created.manual` - Manual order creation

#### Applet Events
  - `applet.service.request` - Service request received

#### Shopify Events
  - `shopify.shop.redact` - Shopify data redaction


### Event Structure
```json
{
  "id": "unique_event_id",
  "name": "event.name",
  "domain": "service_domain",
  "actor": "entity_type",
  "action": "operation",
  "data": {
    // Event specific payload
  },
  "tenantCode": "tenant_identifier",
  "timezone": "Asia/Singapore",
  "published": "timestamp"
}
```
