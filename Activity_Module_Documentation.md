# Activity Module Documentation

## Overview

The Activity Module is a core component of the CRM Platform's Business Service that provides event-driven activity tracking and logging functionality. It extends Node.js EventEmitter to listen for system events and automatically create activity records based on predefined configurations.

## Architecture

### Core Components

1. **ActivityModule Class** - Main module extending EventEmitter
2. **Activity Model** - Database model for storing activity records
3. **Activity Mixin** - Provides `createActivity` method to models
4. **Activity Definitions** - External registry defining activity configurations
5. **NodeCache** - In-memory caching for activity merging

### Key Dependencies

- `node:events` - EventEmitter base class
- `get-value` - Deep object property access
- `node-cache` - In-memory caching
- `p-limit` - Concurrency control
- `@perkd/utils` - Utility functions (pick2, delay, satisfyCondition)

## Configuration

### Module Configuration (`server/config/activity.json`)

```json
{
  "definitions": "@perkd/activity-registry-crm",
  "options": {
    "maxListeners": 0
  },
  "presets": [
    "auth-login",
    "auth-logout", 
    "staff-checkin",
    "staff-checkout",
    "fulfill-item",
    "order-markpaid",
    "order-relocate",
    "offer-issue-staff"
  ]
}
```

### Constructor Parameters

- **app** - Application emitter instance
- **settings** - Configuration object containing:
  - `enabled` - Boolean flag to enable/disable module
  - `presets` - Array of activity definition names to initialize
  - `options` - Module options:
    - `maxListeners` - Maximum event listeners (default: unlimited)
    - `stdTTL` - Cache TTL in seconds (default: 100)
    - `checkperiod` - Cache cleanup interval (default: 120)
  - `definitions` - Path to activity definitions registry

## Activity Definitions Structure

Each activity definition contains:

```javascript
{
  "activity-name": {
    "event": "event.name.pattern",        // Event(s) to listen for
    "actor": "ModelName",                 // Model that performs the activity
    "property": "path.to.actorId",        // Path to extract actor ID from event data
    "dataPick": ["field1", "field2"],     // Fields to extract from event data
    "filter": { /* condition object */ }, // Optional filter conditions
    "options": {
      "merge": true,                      // Whether to merge multiple activities
      "delay": 1000                       // Delay before processing (ms)
    }
  }
}
```

## Core Functionality

### 1. Initialization (`ready()`)

- Processes all preset activity definitions
- Uses concurrency control (limit of 5) to prevent event loop blocking
- Registers event listeners for each activity definition

### 2. Event Listening (`add()`)

For each activity definition:
- Registers event listeners for specified events
- Applies filters to determine if event should trigger activity
- Extracts relevant data using `dataPick` configuration
- Handles immediate save or caching based on merge settings

### 3. Activity Processing

#### Immediate Processing (merge: false)
```javascript
if (!merge) {
  await self.save({ actor, actorId, data: activity })
  return
}
```

#### Delayed/Merged Processing (merge: true)
```javascript
// Cache activity
const cache = myCache.get(actorId)
if (cache) {
  cache.push(activity)
  myCache.set(actorId, cache)
} else {
  myCache.set(actorId, [activity])
}

// Wait for delay period
await delay(waitTime)

// Process cached activities
const refreshedCache = myCache.get(actorId)
if (refreshedCache) {
  await self.mergeAndSave(actor, actorId, refreshedCache)
  myCache.del(actorId)
}
```

### 4. Activity Persistence

#### Save Method
- Resolves actor model from app.models
- Calls model's `createActivity` method
- Handles errors gracefully without disrupting service

#### Merge and Save
- Combines multiple activities for same actor
- Merges `result.delta` arrays from multiple activities
- Saves consolidated activity record

## Data Models

### Activity Model Properties

| Property | Type | Description |
|----------|------|-------------|
| name | string | Activity name/verb (max 32 chars) |
| actor | object | Entity performing the activity |
| instrument | object | Object used in activity completion |
| location | object | Geographic location data |
| attributedTo | object | Attribution information |
| content | string | HTML content description |
| startTime | date | Activity start time |
| endTime | date | Activity end time |
| occurredAt | date | When activity occurred |
| event | string | Source event ID |
| undo | object | Undo configuration |
| object | object | Activity target object |
| target | object | Activity target |
| origin | object | Activity origin |
| result | object | Activity results/outcomes |
| audience | object | Activity audience |
| custom | object | Custom parameters |
| level | number | Activity level (default: 1) |
| visible | boolean | Visibility flag (default: true) |

### Specialized Activity Models

- **StaffActivity** - Extends Activity for staff-specific activities
- Uses multitenant architecture for data isolation

## Integration Points

### 1. EventBus Integration
- Receives events from external systems via EventBus module
- Events flow: External System → EventBus → Activity Module → Database

### 2. Model Integration
- Models include Activity mixin to gain `createActivity` method
- Activity records linked to model instances via `ownerId`

### 3. Multi-tenancy Support
- Activities are tenant-scoped
- Supports tenant-specific activity models (e.g., StaffActivity)

## Usage Examples

### Basic Activity Definition
```javascript
{
  "staff-login": {
    "event": "business.staff.login",
    "actor": "Staff", 
    "property": "staffId",
    "dataPick": ["timestamp", "location", "device"]
  }
}
```

### Merged Activity Definition
```javascript
{
  "order-updates": {
    "event": ["business.order.updated", "business.order.status.changed"],
    "actor": "Order",
    "property": "orderId", 
    "dataPick": ["status", "items", "total"],
    "options": {
      "merge": true,
      "delay": 2000
    }
  }
}
```

## Error Handling

- Graceful error handling in save operations
- Errors logged but don't disrupt service operation
- Failed activities return null instead of throwing

## Performance Considerations

- Concurrency control during initialization (p-limit)
- In-memory caching for activity merging
- Configurable cache TTL and cleanup intervals
- Non-blocking error handling

## Monitoring and Debugging

- Console logging during module lifecycle
- Error logging for failed activity saves
- Cache statistics available through NodeCache
- Event emission tracking through EventEmitter

## Best Practices

1. **Activity Definitions**: Keep definitions focused and specific
2. **Caching**: Use merge option judiciously to avoid memory issues
3. **Error Handling**: Monitor error logs for failed activity saves
4. **Performance**: Adjust cache settings based on activity volume
5. **Testing**: Test activity definitions with realistic event data

## Troubleshooting

### Common Issues

1. **Missing Definitions**: Ensure activity registry is properly loaded
2. **Event Not Triggering**: Verify event names match exactly
3. **Actor Model Not Found**: Check model name spelling and availability
4. **Cache Memory Issues**: Adjust TTL settings for high-volume activities
5. **Filter Not Working**: Validate filter conditions against actual event data

### Debug Steps

1. Check module initialization logs
2. Verify event emission in EventBus
3. Validate activity definition syntax
4. Monitor cache usage and cleanup
5. Review error logs for save failures
